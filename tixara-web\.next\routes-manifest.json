{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [{"source": "/api/:path*", "headers": [{"key": "Access-Control-Allow-Credentials", "value": "true"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT"}, {"key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version"}], "regex": "^/api(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}], "dynamicRoutes": [{"page": "/api/admin/artposure/orders/[id]", "regex": "^/api/admin/artposure/orders/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/artposure/orders/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/artposure/services/[id]", "regex": "^/api/admin/artposure/services/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/artposure/services/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/badges/plans/[id]", "regex": "^/api/admin/badges/plans/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/badges/plans/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/booster/boosts/[id]", "regex": "^/api/admin/booster/boosts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/booster/boosts/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/booster/packages/[id]", "regex": "^/api/admin/booster/packages/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/booster/packages/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/users/[id]", "regex": "^/api/admin/users/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/users/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/admin/users/[id]/verify", "regex": "^/api/admin/users/([^/]+?)/verify(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/admin/users/(?<nxtPid>[^/]+?)/verify(?:/)?$"}, {"page": "/api/auth/[...next<PERSON>h]", "regex": "^/api/auth/(.+?)(?:/)?$", "routeKeys": {"nxtPnextauth": "nxtPnextauth"}, "namedRegex": "^/api/auth/(?<nxtPnextauth>.+?)(?:/)?$"}, {"page": "/api/categories/[id]", "regex": "^/api/categories/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/categories/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/events/[id]", "regex": "^/api/events/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/events/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/ticket-templates/[id]", "regex": "^/api/ticket\\-templates/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/ticket\\-templates/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/api/tickets/[id]", "regex": "^/api/tickets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/api/tickets/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/events/[id]", "regex": "^/events/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/events/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/my-tickets/[id]", "regex": "^/my\\-tickets/([^/]+?)(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/my\\-tickets/(?<nxtPid>[^/]+?)(?:/)?$"}, {"page": "/organizer/events/[id]/edit", "regex": "^/organizer/events/([^/]+?)/edit(?:/)?$", "routeKeys": {"nxtPid": "nxtPid"}, "namedRegex": "^/organizer/events/(?<nxtPid>[^/]+?)/edit(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/admin", "regex": "^/admin(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin(?:/)?$"}, {"page": "/admin/analytics", "regex": "^/admin/analytics(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/analytics(?:/)?$"}, {"page": "/admin/artposure", "regex": "^/admin/artposure(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/artposure(?:/)?$"}, {"page": "/admin/badges", "regex": "^/admin/badges(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/badges(?:/)?$"}, {"page": "/admin/booster", "regex": "^/admin/booster(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/booster(?:/)?$"}, {"page": "/admin/categories", "regex": "^/admin/categories(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/categories(?:/)?$"}, {"page": "/admin/settings/platform", "regex": "^/admin/settings/platform(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/settings/platform(?:/)?$"}, {"page": "/admin/ticket-templates", "regex": "^/admin/ticket\\-templates(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ticket\\-templates(?:/)?$"}, {"page": "/admin/ticket-templates/create", "regex": "^/admin/ticket\\-templates/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/ticket\\-templates/create(?:/)?$"}, {"page": "/admin/users", "regex": "^/admin/users(?:/)?$", "routeKeys": {}, "namedRegex": "^/admin/users(?:/)?$"}, {"page": "/auth/login", "regex": "^/auth/login(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/login(?:/)?$"}, {"page": "/auth/register", "regex": "^/auth/register(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/register(?:/)?$"}, {"page": "/badges", "regex": "^/badges(?:/)?$", "routeKeys": {}, "namedRegex": "^/badges(?:/)?$"}, {"page": "/events", "regex": "^/events(?:/)?$", "routeKeys": {}, "namedRegex": "^/events(?:/)?$"}, {"page": "/my-tickets", "regex": "^/my\\-tickets(?:/)?$", "routeKeys": {}, "namedRegex": "^/my\\-tickets(?:/)?$"}, {"page": "/organizer", "regex": "^/organizer(?:/)?$", "routeKeys": {}, "namedRegex": "^/organizer(?:/)?$"}, {"page": "/organizer/artposure", "regex": "^/organizer/artposure(?:/)?$", "routeKeys": {}, "namedRegex": "^/organizer/artposure(?:/)?$"}, {"page": "/organizer/boost", "regex": "^/organizer/boost(?:/)?$", "routeKeys": {}, "namedRegex": "^/organizer/boost(?:/)?$"}, {"page": "/organizer/events", "regex": "^/organizer/events(?:/)?$", "routeKeys": {}, "namedRegex": "^/organizer/events(?:/)?$"}, {"page": "/organizer/events/create", "regex": "^/organizer/events/create(?:/)?$", "routeKeys": {}, "namedRegex": "^/organizer/events/create(?:/)?$"}, {"page": "/staff/validate-ticket", "regex": "^/staff/validate\\-ticket(?:/)?$", "routeKeys": {}, "namedRegex": "^/staff/validate\\-ticket(?:/)?$"}, {"page": "/uangtix", "regex": "^/uangtix(?:/)?$", "routeKeys": {}, "namedRegex": "^/uangtix(?:/)?$"}, {"page": "/uangtix/deposit", "regex": "^/uangtix/deposit(?:/)?$", "routeKeys": {}, "namedRegex": "^/uangtix/deposit(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": [{"source": "/admin/:path*", "destination": "/dashboard/admin/:path*", "regex": "^/admin(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/organizer/:path*", "destination": "/dashboard/organizer/:path*", "regex": "^/organizer(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}, {"source": "/staff/:path*", "destination": "/dashboard/staff/:path*", "regex": "^/staff(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))?(?:/)?$"}]}