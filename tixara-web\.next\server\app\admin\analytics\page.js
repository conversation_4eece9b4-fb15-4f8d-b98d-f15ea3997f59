(()=>{var e={};e.id=8668,e.ids=[8668],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},59731:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>c});var n=t(50482),o=t(69108),a=t(62563),s=t.n(a),i=t(68300),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let c=["",{children:["admin",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53155)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx"],u="/admin/analytics/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/admin/analytics/page",pathname:"/admin/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},69970:(e,r,t)=>{Promise.resolve().then(t.bind(t,7402))},9559:(e,r,t)=>{Promise.resolve().then(t.bind(t,45778))},16509:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},23978:()=>{},7402:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>T});var n=t(95344),o=t(3729),a=t(53267),s=t.n(a),i=t(42739),d=t(33733),c=t(96885),l=t(48411),u=t(46064);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,t(69224).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var h=t(76196),O=t(55794),f=t(89895);(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}();let x=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),p=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),j=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),N=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),v=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),D=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),_=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),U=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),w=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),E=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),b=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),y=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),g=s()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1});function T(){let{toast:e}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[r,t]=(0,o.useState)(!0),[a,s]=(0,o.useState)(null),[T,M]=(0,o.useState)("30d"),[C,L]=(0,o.useState)(!1);(0,o.useEffect)(()=>{F()},[T]);let F=async()=>{try{t(!0);let e=await fetch(`/api/admin/analytics?range=${T}`);if(e.ok){let r=await e.json();s(r)}else throw Error("Failed to fetch analytics")}catch(r){console.error("Error fetching analytics:",r),e({title:"Error",description:"Gagal memuat data analytics",variant:"destructive"})}finally{t(!1)}},k=async r=>{try{L(!0);let t=await fetch(`/api/admin/analytics/export?type=${r}&range=${T}`);if(t.ok){let n=await t.blob(),o=window.URL.createObjectURL(n),a=document.createElement("a");a.href=o,a.download=`analytics-${T}.${r}`,document.body.appendChild(a),a.click(),window.URL.revokeObjectURL(o),document.body.removeChild(a),e({title:"Berhasil",description:`Data analytics berhasil diexport ke ${r.toUpperCase()}`})}else throw Error("Failed to export data")}catch(r){console.error("Error exporting data:",r),e({title:"Error",description:"Gagal mengexport data analytics",variant:"destructive"})}finally{L(!1)}},P=["#0ea5e9","#10b981","#f59e0b","#ef4444","#8b5cf6","#06b6d4"];return r?n.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:n.jsx(i.Z,{className:"h-8 w-8 animate-spin"})}):a?(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Analytics & Reports"}),n.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Analisis performa dan laporan platform TiXara"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:T,onValueChange:M,children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-32",children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"7d",children:"7 Hari"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"30d",children:"30 Hari"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"90d",children:"90 Hari"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"1y",children:"1 Tahun"})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:F,variant:"outline",children:[n.jsx(d.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>k("csv"),variant:"outline",disabled:C,children:[C?n.jsx(i.Z,{className:"h-4 w-4 mr-2 animate-spin"}):n.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>k("pdf"),disabled:C,children:[C?n.jsx(i.Z,{className:"h-4 w-4 mr-2 animate-spin"}):n.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Export PDF"]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium",children:"Total Revenue"}),n.jsx(l.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx("div",{className:"text-2xl font-bold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(a.overview.totalRevenue)}),(0,n.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[a.overview.revenueGrowth>=0?n.jsx(u.Z,{className:"h-3 w-3 mr-1 text-green-600"}):n.jsx(m,{className:"h-3 w-3 mr-1 text-red-600"}),(0,n.jsxs)("span",{className:a.overview.revenueGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(a.overview.revenueGrowth),"%"]}),n.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium",children:"Tiket Terjual"}),n.jsx(h.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx("div",{className:"text-2xl font-bold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(a.overview.totalTicketsSold)}),(0,n.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[a.overview.ticketsGrowth>=0?n.jsx(u.Z,{className:"h-3 w-3 mr-1 text-green-600"}):n.jsx(m,{className:"h-3 w-3 mr-1 text-red-600"}),(0,n.jsxs)("span",{className:a.overview.ticketsGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(a.overview.ticketsGrowth),"%"]}),n.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium",children:"Total Events"}),n.jsx(O.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx("div",{className:"text-2xl font-bold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(a.overview.totalEvents)}),(0,n.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[a.overview.eventsGrowth>=0?n.jsx(u.Z,{className:"h-3 w-3 mr-1 text-green-600"}):n.jsx(m,{className:"h-3 w-3 mr-1 text-red-600"}),(0,n.jsxs)("span",{className:a.overview.eventsGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(a.overview.eventsGrowth),"%"]}),n.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium",children:"Total Users"}),n.jsx(f.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx("div",{className:"text-2xl font-bold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(a.overview.totalUsers)}),(0,n.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[a.overview.usersGrowth>=0?n.jsx(u.Z,{className:"h-3 w-3 mr-1 text-green-600"}):n.jsx(m,{className:"h-3 w-3 mr-1 text-red-600"}),(0,n.jsxs)("span",{className:a.overview.usersGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(a.overview.usersGrowth),"%"]}),n.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"revenue",className:"space-y-6",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid w-full grid-cols-4",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"revenue",children:"Revenue & Sales"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"users",children:"User Growth"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"events",children:"Event Analytics"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"performance",children:"Top Performance"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"revenue",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Revenue Trend"}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["Tren pendapatan dalam ","7d"===T?"7 hari":"30d"===T?"30 hari":"90d"===T?"90 hari":"1 tahun"," terakhir"]})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(g,{width:"100%",height:300,children:(0,n.jsxs)(j,{data:a.revenueChart,children:[n.jsx(E,{strokeDasharray:"3 3"}),n.jsx(U,{dataKey:"date"}),n.jsx(w,{}),n.jsx(b,{formatter:e=>Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(Number(e))}),n.jsx(y,{}),n.jsx(N,{type:"monotone",dataKey:"revenue",stroke:"#0ea5e9",strokeWidth:2})]})})})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Tickets Sold"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Jumlah tiket terjual per periode"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(g,{width:"100%",height:300,children:(0,n.jsxs)(x,{data:a.revenueChart,children:[n.jsx(E,{strokeDasharray:"3 3"}),n.jsx(U,{dataKey:"date"}),n.jsx(w,{}),n.jsx(b,{}),n.jsx(y,{}),n.jsx(p,{dataKey:"tickets",fill:"#10b981"})]})})})]})]})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"users",children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"User Growth"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Pertumbuhan user dan organizer"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(g,{width:"100%",height:400,children:(0,n.jsxs)(j,{data:a.userGrowthChart,children:[n.jsx(E,{strokeDasharray:"3 3"}),n.jsx(U,{dataKey:"date"}),n.jsx(w,{}),n.jsx(b,{}),n.jsx(y,{}),n.jsx(N,{type:"monotone",dataKey:"users",stroke:"#0ea5e9",strokeWidth:2,name:"Total Users"}),n.jsx(N,{type:"monotone",dataKey:"organizers",stroke:"#10b981",strokeWidth:2,name:"Organizers"})]})})})]})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"events",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Event Categories"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Distribusi event berdasarkan kategori"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(g,{width:"100%",height:300,children:(0,n.jsxs)(v,{children:[n.jsx(D,{data:a.eventCategoriesChart,cx:"50%",cy:"50%",labelLine:!1,label:({category:e,percent:r})=>`${e} ${(100*r).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"count",children:a.eventCategoriesChart.map((e,r)=>n.jsx(_,{fill:P[r%P.length]},`cell-${r}`))}),n.jsx(b,{})]})})})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Revenue by Category"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Revenue berdasarkan kategori event"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(g,{width:"100%",height:300,children:(0,n.jsxs)(x,{data:a.eventCategoriesChart,children:[n.jsx(E,{strokeDasharray:"3 3"}),n.jsx(U,{dataKey:"category"}),n.jsx(w,{}),n.jsx(b,{formatter:e=>Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(Number(e))}),n.jsx(p,{dataKey:"revenue",fill:"#0ea5e9"})]})})})]})]})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"performance",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Top Events"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Event dengan penjualan tiket terbanyak"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("div",{className:"space-y-4",children:a.topEvents.map((e,r)=>(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"w-8 h-8 rounded-full flex items-center justify-center",children:r+1}),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"font-medium",children:e.title}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:["by ",e.organizer]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[(0,n.jsxs)("p",{className:"font-medium",children:[Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.ticketsSold)," tiket"]}),n.jsx("p",{className:"text-sm text-gray-500",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.revenue)})]})]},e.id))})})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Top Organizers"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Organizer dengan performa terbaik"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx("div",{className:"space-y-4",children:a.topOrganizers.map((e,r)=>(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"w-8 h-8 rounded-full flex items-center justify-center",children:r+1}),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"font-medium",children:e.name}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:[e.eventsCount," events"]})]})]}),(0,n.jsxs)("div",{className:"text-right",children:[n.jsx("p",{className:"font-medium",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.totalRevenue)}),(0,n.jsxs)("p",{className:"text-sm text-gray-500",children:[Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.totalTickets)," tiket"]})]})]},e.id))})})]})]})})]})]}):n.jsx("div",{className:"text-center py-8",children:n.jsx("p",{className:"text-gray-500",children:"Gagal memuat data analytics"})})}},45778:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var n=t(95344),o=t(47674),a=t(8428);(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}();var s=t(42739);function i({children:e}){let{data:r,status:t}=(0,o.useSession)(),i=(0,a.useRouter)();return"loading"===t?n.jsx("div",{className:"flex items-center justify-center min-h-screen",children:n.jsx(s.Z,{className:"h-8 w-8 animate-spin"})}):r?.user&&"ADMIN"===r.user.role?n.jsx(Object(function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,n.jsxs)("div",{className:"lg:pl-64",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),n.jsx("main",{className:"py-6",children:n.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]})}):(i.push("/dashboard"),null)}},55794:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},48411:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},96885:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33733:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},76196:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("Ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]])},46064:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},89895:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},53267:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return a}});let n=t(39694);t(95344),t(3729);let o=n._(t(60546));function a(e,r){let t={loading:e=>{let{error:r,isLoading:t,pastDelay:n}=e;return null}};return"function"==typeof e&&(t.loader=e),(0,o.default)({...t,...r})}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},38354:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let n=t(63689);function o(e){let{reason:r,children:t}=e;throw new n.BailoutToCSRError(r)}},60546:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return d}});let n=t(95344),o=t(3729),a=t(38354);function s(e){var r;return{default:null!=(r=null==e?void 0:e.default)?r:e}}let i={loader:()=>Promise.resolve(s(()=>null)),loading:null,ssr:!0},d=function(e){let r={...i,...e},t=(0,o.lazy)(()=>r.loader().then(s)),d=r.loading;function c(e){let s=d?(0,n.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,i=r.ssr?(0,n.jsx)(t,{...e}):(0,n.jsx)(a.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(t,{...e})});return(0,n.jsx)(o.Suspense,{fallback:s,children:i})}return c.displayName="LoadableComponent",c}},53155:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>a,__esModule:()=>o,default:()=>s});let n=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\analytics\page.tsx`),{__esModule:o,$$typeof:a}=n,s=n.default},66294:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>a,__esModule:()=>o,default:()=>s});let n=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\layout.tsx`),{__esModule:o,$$typeof:a}=n,s=n.default},82917:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c,metadata:()=>d});var n=t(25036),o=t(450),a=t.n(o),s=t(14824),i=t.n(s);t(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let d={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function c({children:e}){return n.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:n.jsx("body",{className:`${a().variable} ${i().variable} font-sans antialiased`,children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),n.jsx("main",{className:"flex-1",children:e}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},67272:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[1638,3293,5504],()=>t(59731));module.exports=n})();