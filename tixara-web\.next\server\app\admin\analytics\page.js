(()=>{var e={};e.id=8668,e.ids=[8668],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},59731:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var s=t(50482),r=t(69108),n=t(62563),i=t.n(n),l=t(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(a,d);let o=["",{children:["admin",{children:["analytics",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53155)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx"],x="/admin/analytics/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/analytics/page",pathname:"/admin/analytics",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},69970:(e,a,t)=>{Promise.resolve().then(t.bind(t,7402))},7402:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>R});var s=t(95344),r=t(3729),n=t(16212),i=t(61351),l=t(69436),d=t(17470),o=t(15746),c=t(53267),x=t.n(c),u=t(42739),m=t(33733),h=t(96885),p=t(48411),f=t(46064);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,t(69224).Z)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var j=t(76196),v=t(55794),g=t(89895),w=t(91626),b=t(30692);let N=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),k=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),D=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),C=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),O=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),_=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),U=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),Z=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),E=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),M=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),T=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),P=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1}),G=x()(async()=>{!function(){var e=Error("Cannot find module 'recharts'");throw e.code="MODULE_NOT_FOUND",e}()},{loadableGenerated:{modules:["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\analytics\\page.tsx -> recharts"]},ssr:!1});function R(){let{toast:e}=(0,b.pm)(),[a,t]=(0,r.useState)(!0),[c,x]=(0,r.useState)(null),[R,q]=(0,r.useState)("30d"),[S,z]=(0,r.useState)(!1);(0,r.useEffect)(()=>{L()},[R]);let L=async()=>{try{t(!0);let e=await fetch(`/api/admin/analytics?range=${R}`);if(e.ok){let a=await e.json();x(a)}else throw Error("Failed to fetch analytics")}catch(a){console.error("Error fetching analytics:",a),e({title:"Error",description:"Gagal memuat data analytics",variant:"destructive"})}finally{t(!1)}},F=async a=>{try{z(!0);let t=await fetch(`/api/admin/analytics/export?type=${a}&range=${R}`);if(t.ok){let s=await t.blob(),r=window.URL.createObjectURL(s),n=document.createElement("a");n.href=r,n.download=`analytics-${R}.${a}`,document.body.appendChild(n),n.click(),window.URL.revokeObjectURL(r),document.body.removeChild(n),e({title:"Berhasil",description:`Data analytics berhasil diexport ke ${a.toUpperCase()}`})}else throw Error("Failed to export data")}catch(a){console.error("Error exporting data:",a),e({title:"Error",description:"Gagal mengexport data analytics",variant:"destructive"})}finally{z(!1)}},V=["#0ea5e9","#10b981","#f59e0b","#ef4444","#8b5cf6","#06b6d4"];return a?s.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:s.jsx(u.Z,{className:"h-8 w-8 animate-spin"})}):c?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Analytics & Reports"}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Analisis performa dan laporan platform TiXara"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(d.Ph,{value:R,onValueChange:q,children:[s.jsx(d.i4,{className:"w-32",children:s.jsx(d.ki,{})}),(0,s.jsxs)(d.Bw,{children:[s.jsx(d.Ql,{value:"7d",children:"7 Hari"}),s.jsx(d.Ql,{value:"30d",children:"30 Hari"}),s.jsx(d.Ql,{value:"90d",children:"90 Hari"}),s.jsx(d.Ql,{value:"1y",children:"1 Tahun"})]})]}),(0,s.jsxs)(n.z,{onClick:L,variant:"outline",children:[s.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,s.jsxs)(n.z,{onClick:()=>F("csv"),variant:"outline",disabled:S,children:[S?s.jsx(u.Z,{className:"h-4 w-4 mr-2 animate-spin"}):s.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),(0,s.jsxs)(n.z,{onClick:()=>F("pdf"),disabled:S,children:[S?s.jsx(u.Z,{className:"h-4 w-4 mr-2 animate-spin"}):s.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Export PDF"]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Total Revenue"}),s.jsx(p.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:(0,w.formatCurrency)(c.overview.totalRevenue)}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[c.overview.revenueGrowth>=0?s.jsx(f.Z,{className:"h-3 w-3 mr-1 text-green-600"}):s.jsx(y,{className:"h-3 w-3 mr-1 text-red-600"}),(0,s.jsxs)("span",{className:c.overview.revenueGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(c.overview.revenueGrowth),"%"]}),s.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Tiket Terjual"}),s.jsx(j.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:(0,w.formatNumber)(c.overview.totalTicketsSold)}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[c.overview.ticketsGrowth>=0?s.jsx(f.Z,{className:"h-3 w-3 mr-1 text-green-600"}):s.jsx(y,{className:"h-3 w-3 mr-1 text-red-600"}),(0,s.jsxs)("span",{className:c.overview.ticketsGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(c.overview.ticketsGrowth),"%"]}),s.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Total Events"}),s.jsx(v.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:(0,w.formatNumber)(c.overview.totalEvents)}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[c.overview.eventsGrowth>=0?s.jsx(f.Z,{className:"h-3 w-3 mr-1 text-green-600"}):s.jsx(y,{className:"h-3 w-3 mr-1 text-red-600"}),(0,s.jsxs)("span",{className:c.overview.eventsGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(c.overview.eventsGrowth),"%"]}),s.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[s.jsx(i.ll,{className:"text-sm font-medium",children:"Total Users"}),s.jsx(g.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(i.aY,{children:[s.jsx("div",{className:"text-2xl font-bold",children:(0,w.formatNumber)(c.overview.totalUsers)}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-muted-foreground",children:[c.overview.usersGrowth>=0?s.jsx(f.Z,{className:"h-3 w-3 mr-1 text-green-600"}):s.jsx(y,{className:"h-3 w-3 mr-1 text-red-600"}),(0,s.jsxs)("span",{className:c.overview.usersGrowth>=0?"text-green-600":"text-red-600",children:[Math.abs(c.overview.usersGrowth),"%"]}),s.jsx("span",{className:"ml-1",children:"dari periode sebelumnya"})]})]})]})]}),(0,s.jsxs)(o.mQ,{defaultValue:"revenue",className:"space-y-6",children:[(0,s.jsxs)(o.dr,{className:"grid w-full grid-cols-4",children:[s.jsx(o.SP,{value:"revenue",children:"Revenue & Sales"}),s.jsx(o.SP,{value:"users",children:"User Growth"}),s.jsx(o.SP,{value:"events",children:"Event Analytics"}),s.jsx(o.SP,{value:"performance",children:"Top Performance"})]}),s.jsx(o.nU,{value:"revenue",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Revenue Trend"}),(0,s.jsxs)(i.SZ,{children:["Tren pendapatan dalam ","7d"===R?"7 hari":"30d"===R?"30 hari":"90d"===R?"90 hari":"1 tahun"," terakhir"]})]}),s.jsx(i.aY,{children:s.jsx(G,{width:"100%",height:300,children:(0,s.jsxs)(D,{data:c.revenueChart,children:[s.jsx(M,{strokeDasharray:"3 3"}),s.jsx(Z,{dataKey:"date"}),s.jsx(E,{}),s.jsx(T,{formatter:e=>(0,w.formatCurrency)(Number(e))}),s.jsx(P,{}),s.jsx(C,{type:"monotone",dataKey:"revenue",stroke:"#0ea5e9",strokeWidth:2})]})})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Tickets Sold"}),s.jsx(i.SZ,{children:"Jumlah tiket terjual per periode"})]}),s.jsx(i.aY,{children:s.jsx(G,{width:"100%",height:300,children:(0,s.jsxs)(N,{data:c.revenueChart,children:[s.jsx(M,{strokeDasharray:"3 3"}),s.jsx(Z,{dataKey:"date"}),s.jsx(E,{}),s.jsx(T,{}),s.jsx(P,{}),s.jsx(k,{dataKey:"tickets",fill:"#10b981"})]})})})]})]})}),s.jsx(o.nU,{value:"users",children:(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"User Growth"}),s.jsx(i.SZ,{children:"Pertumbuhan user dan organizer"})]}),s.jsx(i.aY,{children:s.jsx(G,{width:"100%",height:400,children:(0,s.jsxs)(D,{data:c.userGrowthChart,children:[s.jsx(M,{strokeDasharray:"3 3"}),s.jsx(Z,{dataKey:"date"}),s.jsx(E,{}),s.jsx(T,{}),s.jsx(P,{}),s.jsx(C,{type:"monotone",dataKey:"users",stroke:"#0ea5e9",strokeWidth:2,name:"Total Users"}),s.jsx(C,{type:"monotone",dataKey:"organizers",stroke:"#10b981",strokeWidth:2,name:"Organizers"})]})})})]})}),s.jsx(o.nU,{value:"events",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Event Categories"}),s.jsx(i.SZ,{children:"Distribusi event berdasarkan kategori"})]}),s.jsx(i.aY,{children:s.jsx(G,{width:"100%",height:300,children:(0,s.jsxs)(O,{children:[s.jsx(_,{data:c.eventCategoriesChart,cx:"50%",cy:"50%",labelLine:!1,label:({category:e,percent:a})=>`${e} ${(100*a).toFixed(0)}%`,outerRadius:80,fill:"#8884d8",dataKey:"count",children:c.eventCategoriesChart.map((e,a)=>s.jsx(U,{fill:V[a%V.length]},`cell-${a}`))}),s.jsx(T,{})]})})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Revenue by Category"}),s.jsx(i.SZ,{children:"Revenue berdasarkan kategori event"})]}),s.jsx(i.aY,{children:s.jsx(G,{width:"100%",height:300,children:(0,s.jsxs)(N,{data:c.eventCategoriesChart,children:[s.jsx(M,{strokeDasharray:"3 3"}),s.jsx(Z,{dataKey:"category"}),s.jsx(E,{}),s.jsx(T,{formatter:e=>(0,w.formatCurrency)(Number(e))}),s.jsx(k,{dataKey:"revenue",fill:"#0ea5e9"})]})})})]})]})}),s.jsx(o.nU,{value:"performance",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Top Events"}),s.jsx(i.SZ,{children:"Event dengan penjualan tiket terbanyak"})]}),s.jsx(i.aY,{children:s.jsx("div",{className:"space-y-4",children:c.topEvents.map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx(l.C,{variant:"outline",className:"w-8 h-8 rounded-full flex items-center justify-center",children:a+1}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium",children:e.title}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["by ",e.organizer]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("p",{className:"font-medium",children:[(0,w.formatNumber)(e.ticketsSold)," tiket"]}),s.jsx("p",{className:"text-sm text-gray-500",children:(0,w.formatCurrency)(e.revenue)})]})]},e.id))})})]}),(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{children:[s.jsx(i.ll,{children:"Top Organizers"}),s.jsx(i.SZ,{children:"Organizer dengan performa terbaik"})]}),s.jsx(i.aY,{children:s.jsx("div",{className:"space-y-4",children:c.topOrganizers.map((e,a)=>(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx(l.C,{variant:"outline",className:"w-8 h-8 rounded-full flex items-center justify-center",children:a+1}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[e.eventsCount," events"]})]})]}),(0,s.jsxs)("div",{className:"text-right",children:[s.jsx("p",{className:"font-medium",children:(0,w.formatCurrency)(e.totalRevenue)}),(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[(0,w.formatNumber)(e.totalTickets)," tiket"]})]})]},e.id))})})]})]})})]})]}):s.jsx("div",{className:"text-center py-8",children:s.jsx("p",{className:"text-gray-500",children:"Gagal memuat data analytics"})})}},17470:(e,a,t)=>{"use strict";t.d(a,{Bw:()=>p,Ph:()=>c,Ql:()=>f,i4:()=>u,ki:()=>x});var s=t(95344),r=t(3729),n=t(32116),i=t(25390),l=t(12704),d=t(62312),o=t(91626);let c=n.fC;n.ZA;let x=n.B4,u=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(n.xz,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,s.jsx(n.JO,{asChild:!0,children:s.jsx(i.Z,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=n.xz.displayName;let m=r.forwardRef(({className:e,...a},t)=>s.jsx(n.u_,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:s.jsx(l.Z,{className:"h-4 w-4"})}));m.displayName=n.u_.displayName;let h=r.forwardRef(({className:e,...a},t)=>s.jsx(n.$G,{ref:t,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:s.jsx(i.Z,{className:"h-4 w-4"})}));h.displayName=n.$G.displayName;let p=r.forwardRef(({className:e,children:a,position:t="popper",...r},i)=>s.jsx(n.h_,{children:(0,s.jsxs)(n.VY,{ref:i,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...r,children:[s.jsx(m,{}),s.jsx(n.l_,{className:(0,o.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),s.jsx(h,{})]})}));p.displayName=n.VY.displayName,r.forwardRef(({className:e,...a},t)=>s.jsx(n.__,{ref:t,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=n.__.displayName;let f=r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(n.ck,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(n.wU,{children:s.jsx(d.Z,{className:"h-4 w-4"})})}),s.jsx(n.eT,{children:a})]}));f.displayName=n.ck.displayName,r.forwardRef(({className:e,...a},t)=>s.jsx(n.Z0,{ref:t,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=n.Z0.displayName},15746:(e,a,t)=>{"use strict";t.d(a,{SP:()=>o,dr:()=>d,mQ:()=>l,nU:()=>c});var s=t(95344),r=t(3729),n=t(89128),i=t(91626);let l=n.fC,d=r.forwardRef(({className:e,...a},t)=>s.jsx(n.aV,{ref:t,className:(0,i.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));d.displayName=n.aV.displayName;let o=r.forwardRef(({className:e,...a},t)=>s.jsx(n.xz,{ref:t,className:(0,i.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));o.displayName=n.xz.displayName;let c=r.forwardRef(({className:e,...a},t)=>s.jsx(n.VY,{ref:t,className:(0,i.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));c.displayName=n.VY.displayName},50340:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},85674:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48411:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},96885:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2273:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},70009:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},33733:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},46064:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},53267:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"default",{enumerable:!0,get:function(){return n}});let s=t(39694);t(95344),t(3729);let r=s._(t(60546));function n(e,a){let t={loading:e=>{let{error:a,isLoading:t,pastDelay:s}=e;return null}};return"function"==typeof e&&(t.loader=e),(0,r.default)({...t,...a})}("function"==typeof a.default||"object"==typeof a.default&&null!==a.default)&&void 0===a.default.__esModule&&(Object.defineProperty(a.default,"__esModule",{value:!0}),Object.assign(a.default,a),e.exports=a.default)},38354:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"BailoutToCSR",{enumerable:!0,get:function(){return r}});let s=t(63689);function r(e){let{reason:a,children:t}=e;throw new s.BailoutToCSRError(a)}},60546:(e,a,t)=>{"use strict";Object.defineProperty(a,"__esModule",{value:!0}),Object.defineProperty(a,"default",{enumerable:!0,get:function(){return d}});let s=t(95344),r=t(3729),n=t(38354);function i(e){var a;return{default:null!=(a=null==e?void 0:e.default)?a:e}}let l={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let a={...l,...e},t=(0,r.lazy)(()=>a.loader().then(i)),d=a.loading;function o(e){let i=d?(0,s.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,l=a.ssr?(0,s.jsx)(t,{...e}):(0,s.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(t,{...e})});return(0,s.jsx)(r.Suspense,{fallback:i,children:l})}return o.displayName="LoadableComponent",o}},53155:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>n,__esModule:()=>r,default:()=>i});let s=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\analytics\page.tsx`),{__esModule:r,$$typeof:n}=s,i=s.default},89128:(e,a,t)=>{"use strict";t.d(a,{VY:()=>E,aV:()=>U,fC:()=>_,xz:()=>Z});var s=t(3729),r=t(85222),n=t(98462),i=t(34504),l=t(43234),d=t(62409),o=t(3975),c=t(33183),x=t(99048),u=t(95344),m="Tabs",[h,p]=(0,n.b)(m,[i.Pc]),f=(0,i.Pc)(),[y,j]=h(m),v=s.forwardRef((e,a)=>{let{__scopeTabs:t,value:s,onValueChange:r,defaultValue:n,orientation:i="horizontal",dir:l,activationMode:h="automatic",...p}=e,f=(0,o.gm)(l),[j,v]=(0,c.T)({prop:s,onChange:r,defaultProp:n??"",caller:m});return(0,u.jsx)(y,{scope:t,baseId:(0,x.M)(),value:j,onValueChange:v,orientation:i,dir:f,activationMode:h,children:(0,u.jsx)(d.WV.div,{dir:f,"data-orientation":i,...p,ref:a})})});v.displayName=m;var g="TabsList",w=s.forwardRef((e,a)=>{let{__scopeTabs:t,loop:s=!0,...r}=e,n=j(g,t),l=f(t);return(0,u.jsx)(i.fC,{asChild:!0,...l,orientation:n.orientation,dir:n.dir,loop:s,children:(0,u.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:a})})});w.displayName=g;var b="TabsTrigger",N=s.forwardRef((e,a)=>{let{__scopeTabs:t,value:s,disabled:n=!1,...l}=e,o=j(b,t),c=f(t),x=C(o.baseId,s),m=O(o.baseId,s),h=s===o.value;return(0,u.jsx)(i.ck,{asChild:!0,...c,focusable:!n,active:h,children:(0,u.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":m,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:x,...l,ref:a,onMouseDown:(0,r.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(s)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(s)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;h||n||!e||o.onValueChange(s)})})})});N.displayName=b;var k="TabsContent",D=s.forwardRef((e,a)=>{let{__scopeTabs:t,value:r,forceMount:n,children:i,...o}=e,c=j(k,t),x=C(c.baseId,r),m=O(c.baseId,r),h=r===c.value,p=s.useRef(h);return s.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(l.z,{present:n||h,children:({present:t})=>(0,u.jsx)(d.WV.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":x,hidden:!t,id:m,tabIndex:0,...o,ref:a,style:{...e.style,animationDuration:p.current?"0s":void 0},children:t&&i})})});function C(e,a){return`${e}-trigger-${a}`}function O(e,a){return`${e}-content-${a}`}D.displayName=k;var _=v,U=w,Z=N,E=D}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[1638,3088,4739,9205,2295],()=>t(59731));module.exports=s})();