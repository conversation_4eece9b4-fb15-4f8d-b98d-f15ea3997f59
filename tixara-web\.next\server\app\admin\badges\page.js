(()=>{var e={};e.id=1665,e.ids=[1665],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},12198:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>o,routeModule:()=>p,tree:()=>c});var t=a(50482),r=a(69108),i=a(62563),n=a.n(i),l=a(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(s,d);let c=["",{children:["admin",{children:["badges",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,46747)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\badges\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\badges\\page.tsx"],u="/admin/badges/page",x={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/badges/page",pathname:"/admin/badges",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},59243:(e,s,a)=>{Promise.resolve().then(a.bind(a,6919))},6919:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>S});var t=a(95344),r=a(3729),i=a(16212),n=a(61351),l=a(92549),d=a(54572),c=a(93601),o=a(71809),u=a(69436),x=a(81036),p=a(16802),m=a(20886),h=a(25757),f=a(1750),j=a(23485),g=a(79200),v=a(76755),b=a(42739),y=a(33733),N=a(51838),w=a(38271),k=a(31498),C=a(89895),D=a(62093),E=a(46327),P=a(30692),Z=a(91626);function S(){let{toast:e}=(0,P.pm)(),[s,a]=(0,r.useState)(!0),[S,R]=(0,r.useState)(!1),[T,_]=(0,r.useState)([]),[M,A]=(0,r.useState)([]),[q,B]=(0,r.useState)(null),[I,V]=(0,r.useState)(!1),[z,F]=(0,r.useState)({name:"",description:"",price:0,duration:30,features:[""],maxEvents:0,maxTicketsPerEvent:0,commissionDiscount:0,prioritySupport:!1,customBranding:!1,analytics:!1,isActive:!0,color:"#0ea5e9",icon:"star"});(0,r.useEffect)(()=>{$()},[]);let $=async()=>{try{a(!0);let[e,s]=await Promise.all([fetch("/api/admin/badges/plans"),fetch("/api/admin/badges/subscriptions")]);if(e.ok){let s=await e.json();_(s)}if(s.ok){let e=await s.json();A(e)}}catch(s){console.error("Error fetching data:",s),e({title:"Error",description:"Gagal memuat data badge",variant:"destructive"})}finally{a(!1)}},H=async()=>{try{R(!0);let s=q?`/api/admin/badges/plans/${q.id}`:"/api/admin/badges/plans";if((await fetch(s,{method:q?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...z,features:z.features.filter(e=>""!==e.trim())})})).ok)e({title:"Berhasil",description:`Badge plan berhasil ${q?"diupdate":"dibuat"}`}),V(!1),L(),$();else throw Error("Failed to save plan")}catch(s){console.error("Error saving plan:",s),e({title:"Error",description:"Gagal menyimpan badge plan",variant:"destructive"})}finally{R(!1)}},U=e=>{B(e),F({name:e.name,description:e.description,price:e.price,duration:e.duration,features:e.features.length>0?e.features:[""],maxEvents:e.maxEvents,maxTicketsPerEvent:e.maxTicketsPerEvent,commissionDiscount:e.commissionDiscount,prioritySupport:e.prioritySupport,customBranding:e.customBranding,analytics:e.analytics,isActive:e.isActive,color:e.color,icon:e.icon}),V(!0)},K=async s=>{if(confirm("Apakah Anda yakin ingin menghapus badge plan ini?"))try{if((await fetch(`/api/admin/badges/plans/${s}`,{method:"DELETE"})).ok)e({title:"Berhasil",description:"Badge plan berhasil dihapus"}),$();else throw Error("Failed to delete plan")}catch(s){console.error("Error deleting plan:",s),e({title:"Error",description:"Gagal menghapus badge plan",variant:"destructive"})}},L=()=>{B(null),F({name:"",description:"",price:0,duration:30,features:[""],maxEvents:0,maxTicketsPerEvent:0,commissionDiscount:0,prioritySupport:!1,customBranding:!1,analytics:!1,isActive:!0,color:"#0ea5e9",icon:"star"})},W=(e,s)=>{F(a=>({...a,features:a.features.map((a,t)=>t===e?s:a)}))},G=e=>{F(s=>({...s,features:s.features.filter((s,a)=>a!==e)}))},O=e=>{switch(e){case"crown":return t.jsx(f.Z,{className:"h-4 w-4"});case"shield":return t.jsx(j.Z,{className:"h-4 w-4"});case"zap":return t.jsx(g.Z,{className:"h-4 w-4"});default:return t.jsx(v.Z,{className:"h-4 w-4"})}},X=e=>{switch(e){case"ACTIVE":return t.jsx(u.C,{className:"bg-green-100 text-green-800",children:"Active"});case"EXPIRED":return t.jsx(u.C,{className:"bg-red-100 text-red-800",children:"Expired"});case"CANCELLED":return t.jsx(u.C,{className:"bg-gray-100 text-gray-800",children:"Cancelled"});default:return t.jsx(u.C,{children:e})}};return s?t.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:t.jsx(b.Z,{className:"h-8 w-8 animate-spin"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Badge & Subscription Management"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola badge plans dan subscription user"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(i.z,{onClick:$,variant:"outline",children:[t.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,t.jsxs)(p.Vq,{open:I,onOpenChange:V,children:[t.jsx(p.hg,{asChild:!0,children:(0,t.jsxs)(i.z,{onClick:L,children:[t.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Tambah Badge Plan"]})}),(0,t.jsxs)(p.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(p.fK,{children:[t.jsx(p.$N,{children:q?"Edit Badge Plan":"Tambah Badge Plan"}),t.jsx(p.Be,{children:"Konfigurasi badge plan untuk subscription user"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"name",children:"Nama Badge"}),t.jsx(l.I,{id:"name",value:z.name,onChange:e=>F(s=>({...s,name:e.target.value})),placeholder:"e.g., Gold Plan"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"price",children:"Harga (Rp)"}),t.jsx(l.I,{id:"price",type:"number",value:z.price,onChange:e=>F(s=>({...s,price:parseInt(e.target.value)}))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"description",children:"Deskripsi"}),t.jsx(c.g,{id:"description",value:z.description,onChange:e=>F(s=>({...s,description:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"duration",children:"Durasi (hari)"}),t.jsx(l.I,{id:"duration",type:"number",value:z.duration,onChange:e=>F(s=>({...s,duration:parseInt(e.target.value)}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"commissionDiscount",children:"Diskon Komisi (%)"}),t.jsx(l.I,{id:"commissionDiscount",type:"number",min:"0",max:"100",value:z.commissionDiscount,onChange:e=>F(s=>({...s,commissionDiscount:parseFloat(e.target.value)}))})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"maxEvents",children:"Max Events"}),t.jsx(l.I,{id:"maxEvents",type:"number",value:z.maxEvents,onChange:e=>F(s=>({...s,maxEvents:parseInt(e.target.value)}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"maxTicketsPerEvent",children:"Max Tiket per Event"}),t.jsx(l.I,{id:"maxTicketsPerEvent",type:"number",value:z.maxTicketsPerEvent,onChange:e=>F(s=>({...s,maxTicketsPerEvent:parseInt(e.target.value)}))})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx(d._,{children:"Features"}),z.features.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(l.I,{value:e,onChange:e=>W(s,e.target.value),placeholder:"Feature description"}),t.jsx(i.z,{type:"button",variant:"outline",size:"sm",onClick:()=>G(s),disabled:1===z.features.length,children:t.jsx(w.Z,{className:"h-4 w-4"})})]},s)),(0,t.jsxs)(i.z,{type:"button",variant:"outline",onClick:()=>{F(e=>({...e,features:[...e.features,""]}))},children:[t.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Tambah Feature"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(d._,{children:"Priority Support"}),t.jsx(o.r,{checked:z.prioritySupport,onCheckedChange:e=>F(s=>({...s,prioritySupport:e}))})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(d._,{children:"Custom Branding"}),t.jsx(o.r,{checked:z.customBranding,onCheckedChange:e=>F(s=>({...s,customBranding:e}))})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(d._,{children:"Advanced Analytics"}),t.jsx(o.r,{checked:z.analytics,onCheckedChange:e=>F(s=>({...s,analytics:e}))})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(d._,{children:"Plan Aktif"}),t.jsx(o.r,{checked:z.isActive,onCheckedChange:e=>F(s=>({...s,isActive:e}))})]})]})]}),(0,t.jsxs)(p.cN,{children:[t.jsx(i.z,{variant:"outline",onClick:()=>V(!1),children:"Batal"}),(0,t.jsxs)(i.z,{onClick:H,disabled:S,children:[S?t.jsx(b.Z,{className:"h-4 w-4 mr-2 animate-spin"}):t.jsx(k.Z,{className:"h-4 w-4 mr-2"}),q?"Update":"Simpan"]})]})]})]})]})]}),(0,t.jsxs)(h.mQ,{defaultValue:"plans",className:"space-y-6",children:[(0,t.jsxs)(h.dr,{children:[t.jsx(h.SP,{value:"plans",children:"Badge Plans"}),t.jsx(h.SP,{value:"subscriptions",children:"Active Subscriptions"})]}),t.jsx(h.nU,{value:"plans",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{children:["Badge Plans (",T.length,")"]}),t.jsx(n.SZ,{children:"Kelola badge plans yang tersedia untuk subscription"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)(x.iA,{children:[t.jsx(x.xD,{children:(0,t.jsxs)(x.SC,{children:[t.jsx(x.ss,{children:"Badge"}),t.jsx(x.ss,{children:"Harga"}),t.jsx(x.ss,{children:"Durasi"}),t.jsx(x.ss,{children:"Subscribers"}),t.jsx(x.ss,{children:"Status"}),t.jsx(x.ss,{children:"Aksi"})]})}),t.jsx(x.RM,{children:T.map(e=>(0,t.jsxs)(x.SC,{children:[t.jsx(x.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx("div",{className:"flex items-center justify-center w-8 h-8 rounded-full",style:{backgroundColor:e.color},children:O(e.icon)}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:e.name}),t.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})]})}),t.jsx(x.pj,{children:t.jsx("div",{className:"font-medium",children:(0,Z.formatCurrency)(e.price)})}),(0,t.jsxs)(x.pj,{children:[e.duration," hari"]}),t.jsx(x.pj,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(C.Z,{className:"h-4 w-4 text-gray-400"}),t.jsx("span",{children:e._count.subscriptions})]})}),t.jsx(x.pj,{children:e.isActive?t.jsx(u.C,{className:"bg-green-100 text-green-800",children:"Active"}):t.jsx(u.C,{className:"bg-gray-100 text-gray-800",children:"Inactive"})}),t.jsx(x.pj,{children:(0,t.jsxs)(m.h_,{children:[t.jsx(m.$F,{asChild:!0,children:t.jsx(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:t.jsx(D.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(m.AW,{align:"end",children:[t.jsx(m.Ju,{children:"Aksi"}),(0,t.jsxs)(m.Xi,{onClick:()=>U(e),children:[t.jsx(E.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),t.jsx(m.VD,{}),(0,t.jsxs)(m.Xi,{className:"text-red-600",onClick:()=>K(e.id),children:[t.jsx(w.Z,{className:"mr-2 h-4 w-4"}),"Hapus"]})]})]})})]},e.id))})]}),0===T.length&&t.jsx("div",{className:"text-center py-8",children:t.jsx("p",{className:"text-gray-500",children:"Belum ada badge plan yang dibuat"})})]})]})}),t.jsx(h.nU,{value:"subscriptions",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[(0,t.jsxs)(n.ll,{children:["Active Subscriptions (",M.length,")"]}),t.jsx(n.SZ,{children:"Daftar subscription badge yang sedang aktif"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)(x.iA,{children:[t.jsx(x.xD,{children:(0,t.jsxs)(x.SC,{children:[t.jsx(x.ss,{children:"User"}),t.jsx(x.ss,{children:"Badge"}),t.jsx(x.ss,{children:"Status"}),t.jsx(x.ss,{children:"Start Date"}),t.jsx(x.ss,{children:"End Date"}),t.jsx(x.ss,{children:"Aksi"})]})}),t.jsx(x.RM,{children:M.map(e=>(0,t.jsxs)(x.SC,{children:[t.jsx(x.pj,{children:(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:e.user.name}),t.jsx("div",{className:"text-sm text-gray-500",children:e.user.email})]})}),t.jsx(x.pj,{children:(0,t.jsxs)(u.C,{className:"bg-yellow-100 text-yellow-800",children:[t.jsx(v.Z,{className:"h-3 w-3 mr-1"}),e.badge]})}),t.jsx(x.pj,{children:X(e.status)}),t.jsx(x.pj,{children:(0,Z.formatDate)(e.startDate)}),t.jsx(x.pj,{children:(0,Z.formatDate)(e.endDate)}),t.jsx(x.pj,{children:(0,t.jsxs)(m.h_,{children:[t.jsx(m.$F,{asChild:!0,children:t.jsx(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:t.jsx(D.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(m.AW,{align:"end",children:[t.jsx(m.Ju,{children:"Aksi"}),(0,t.jsxs)(m.Xi,{children:[t.jsx(E.Z,{className:"mr-2 h-4 w-4"}),"Detail"]}),"ACTIVE"===e.status&&(0,t.jsxs)(m.Xi,{className:"text-red-600",children:[t.jsx(w.Z,{className:"mr-2 h-4 w-4"}),"Cancel"]})]})]})})]},e.id))})]}),0===M.length&&t.jsx("div",{className:"text-center py-8",children:t.jsx("p",{className:"text-gray-500",children:"Belum ada subscription aktif"})})]})]})})]})]})}},16802:(e,s,a)=>{"use strict";a.d(s,{$N:()=>h,Be:()=>f,Vq:()=>d,cN:()=>m,cZ:()=>x,fK:()=>p,hg:()=>c});var t=a(95344),r=a(3729),i=a(88794),n=a(14513),l=a(91626);let d=i.fC,c=i.xz,o=i.h_;i.x8;let u=r.forwardRef(({className:e,...s},a)=>t.jsx(i.aV,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));u.displayName=i.aV.displayName;let x=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(o,{children:[t.jsx(u,{}),(0,t.jsxs)(i.VY,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[s,(0,t.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(n.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));x.displayName=i.VY.displayName;let p=({className:e,...s})=>t.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});p.displayName="DialogHeader";let m=({className:e,...s})=>t.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});m.displayName="DialogFooter";let h=r.forwardRef(({className:e,...s},a)=>t.jsx(i.Dx,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));h.displayName=i.Dx.displayName;let f=r.forwardRef(({className:e,...s},a)=>t.jsx(i.dk,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));f.displayName=i.dk.displayName},54572:(e,s,a)=>{"use strict";a.d(s,{_:()=>o});var t=a(95344),r=a(3729),i=a(62409),n=r.forwardRef((e,s)=>(0,t.jsx)(i.WV.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=a(92193),d=a(91626);let c=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...s},a)=>t.jsx(n,{ref:a,className:(0,d.cn)(c(),e),...s}));o.displayName=n.displayName},71809:(e,s,a)=>{"use strict";a.d(s,{r:()=>w});var t=a(95344),r=a(3729),i=a(85222),n=a(31405),l=a(98462),d=a(33183),c=a(92062),o=a(63085),u=a(62409),x="Switch",[p,m]=(0,l.b)(x),[h,f]=p(x),j=r.forwardRef((e,s)=>{let{__scopeSwitch:a,name:l,checked:c,defaultChecked:o,required:p,disabled:m,value:f="on",onCheckedChange:j,form:g,...v}=e,[N,w]=r.useState(null),k=(0,n.e)(s,e=>w(e)),C=r.useRef(!1),D=!N||g||!!N.closest("form"),[E,P]=(0,d.T)({prop:c,defaultProp:o??!1,onChange:j,caller:x});return(0,t.jsxs)(h,{scope:a,checked:E,disabled:m,children:[(0,t.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":E,"aria-required":p,"data-state":y(E),"data-disabled":m?"":void 0,disabled:m,value:f,...v,ref:k,onClick:(0,i.M)(e.onClick,e=>{P(e=>!e),D&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),D&&(0,t.jsx)(b,{control:N,bubbles:!C.current,name:l,value:f,checked:E,required:p,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});j.displayName=x;var g="SwitchThumb",v=r.forwardRef((e,s)=>{let{__scopeSwitch:a,...r}=e,i=f(g,a);return(0,t.jsx)(u.WV.span,{"data-state":y(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:s})});v.displayName=g;var b=r.forwardRef(({__scopeSwitch:e,control:s,checked:a,bubbles:i=!0,...l},d)=>{let u=r.useRef(null),x=(0,n.e)(u,d),p=(0,c.D)(a),m=(0,o.t)(s);return r.useEffect(()=>{let e=u.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(p!==a&&s){let t=new Event("click",{bubbles:i});s.call(e,a),e.dispatchEvent(t)}},[p,a,i]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:x,style:{...l.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function y(e){return e?"checked":"unchecked"}b.displayName="SwitchBubbleInput";var N=a(91626);let w=r.forwardRef(({className:e,...s},a)=>t.jsx(j,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...s,ref:a,children:t.jsx(v,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=j.displayName},81036:(e,s,a)=>{"use strict";a.d(s,{RM:()=>d,SC:()=>c,iA:()=>n,pj:()=>u,ss:()=>o,xD:()=>l});var t=a(95344),r=a(3729),i=a(91626);let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:a,className:(0,i.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let l=r.forwardRef(({className:e,...s},a)=>t.jsx("thead",{ref:a,className:(0,i.cn)("[&_tr]:border-b",e),...s}));l.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("tbody",{ref:a,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},a)=>t.jsx("tfoot",{ref:a,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=r.forwardRef(({className:e,...s},a)=>t.jsx("tr",{ref:a,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("th",{ref:a,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let u=r.forwardRef(({className:e,...s},a)=>t.jsx("td",{ref:a,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));u.displayName="TableCell",r.forwardRef(({className:e,...s},a)=>t.jsx("caption",{ref:a,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},25757:(e,s,a)=>{"use strict";a.d(s,{mQ:()=>Z,nU:()=>T,dr:()=>S,SP:()=>R});var t=a(95344),r=a(3729),i=a(85222),n=a(98462),l=a(34504),d=a(43234),c=a(62409),o=a(3975),u=a(33183),x=a(99048),p="Tabs",[m,h]=(0,n.b)(p,[l.Pc]),f=(0,l.Pc)(),[j,g]=m(p),v=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:d,activationMode:m="automatic",...h}=e,f=(0,o.gm)(d),[g,v]=(0,u.T)({prop:r,onChange:i,defaultProp:n??"",caller:p});return(0,t.jsx)(j,{scope:a,baseId:(0,x.M)(),value:g,onValueChange:v,orientation:l,dir:f,activationMode:m,children:(0,t.jsx)(c.WV.div,{dir:f,"data-orientation":l,...h,ref:s})})});v.displayName=p;var b="TabsList",y=r.forwardRef((e,s)=>{let{__scopeTabs:a,loop:r=!0,...i}=e,n=g(b,a),d=f(a);return(0,t.jsx)(l.fC,{asChild:!0,...d,orientation:n.orientation,dir:n.dir,loop:r,children:(0,t.jsx)(c.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:s})})});y.displayName=b;var N="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,disabled:n=!1,...d}=e,o=g(N,a),u=f(a),x=D(o.baseId,r),p=E(o.baseId,r),m=r===o.value;return(0,t.jsx)(l.ck,{asChild:!0,...u,focusable:!n,active:m,children:(0,t.jsx)(c.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:x,...d,ref:s,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==o.activationMode;m||n||!e||o.onValueChange(r)})})})});w.displayName=N;var k="TabsContent",C=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:i,forceMount:n,children:l,...o}=e,u=g(k,a),x=D(u.baseId,i),p=E(u.baseId,i),m=i===u.value,h=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(d.z,{present:n||m,children:({present:a})=>(0,t.jsx)(c.WV.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":x,hidden:!a,id:p,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&l})})});function D(e,s){return`${e}-trigger-${s}`}function E(e,s){return`${e}-content-${s}`}C.displayName=k;var P=a(91626);let Z=v,S=r.forwardRef(({className:e,...s},a)=>t.jsx(y,{ref:a,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));S.displayName=y.displayName;let R=r.forwardRef(({className:e,...s},a)=>t.jsx(w,{ref:a,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));R.displayName=w.displayName;let T=r.forwardRef(({className:e,...s},a)=>t.jsx(C,{ref:a,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));T.displayName=C.displayName},93601:(e,s,a)=>{"use strict";a.d(s,{g:()=>n});var t=a(95344),r=a(3729),i=a(91626);let n=r.forwardRef(({className:e,...s},a)=>t.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));n.displayName="Textarea"},25390:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},1750:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},62093:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},51838:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33733:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31498:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},46327:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},38271:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},79200:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},46747:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let t=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\badges\page.tsx`),{__esModule:r,$$typeof:i}=t,n=t.default},92062:(e,s,a)=>{"use strict";a.d(s,{D:()=>r});var t=a(3729);function r(e){let s=t.useRef({value:e,previous:e});return t.useMemo(()=>(s.current.value!==e&&(s.current.previous=s.current.value,s.current.value=e),s.current.previous),[e])}}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,3088,9253,9205,2295],()=>a(12198));module.exports=t})();