(()=>{var e={};e.id=7678,e.ids=[7678],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},80534:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=s(50482),r=s(69108),i=s(62563),n=s.n(i),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(a,o);let d=["",{children:["admin",{children:["booster",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,20490)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\booster\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\booster\\page.tsx"],u="/admin/booster/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/booster/page",pathname:"/admin/booster",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},30491:(e,a,s)=>{Promise.resolve().then(s.bind(s,28077))},28077:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>_});var t=s(95344),r=s(3729),i=s(16212),n=s(61351),l=s(92549),o=s(54572),d=s(93601),c=s(71809),u=s(69436),p=s(81036),x=s(16802),m=s(20886),h=s(25757),f=s(76755),j=s(79200),g=s(17910),y=s(42739),v=s(33733),b=s(51838),k=s(38271),N=s(31498),w=s(25545),C=s(88534),Z=s(62093),P=s(46327);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let D=(0,s(69224).Z)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);var R=s(46064),E=s(30692),M=s(91626);let S=[{value:"ACTIVE",label:"Active",color:"bg-green-100 text-green-800"},{value:"EXPIRED",label:"Expired",color:"bg-gray-100 text-gray-800"},{value:"CANCELLED",label:"Cancelled",color:"bg-red-100 text-red-800"}];function _(){let{toast:e}=(0,E.pm)(),[a,s]=(0,r.useState)(!0),[_,A]=(0,r.useState)(!1),[T,q]=(0,r.useState)([]),[z,B]=(0,r.useState)([]),[V,I]=(0,r.useState)(null),[F,H]=(0,r.useState)(!1),[$,L]=(0,r.useState)({name:"",description:"",duration:3,price:0,features:[""],priority:1,isActive:!0});(0,r.useEffect)(()=>{U()},[]);let U=async()=>{try{s(!0);let[e,a]=await Promise.all([fetch("/api/admin/booster/packages"),fetch("/api/admin/booster/boosts")]);if(e.ok){let a=await e.json();q(a.data||[])}if(a.ok){let e=await a.json();B(e.data||[])}}catch(a){console.error("Error fetching data:",a),e({title:"Error",description:"Gagal memuat data Booster",variant:"destructive"})}finally{s(!1)}},O=async()=>{try{A(!0);let a=V?`/api/admin/booster/packages/${V.id}`:"/api/admin/booster/packages";if((await fetch(a,{method:V?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...$,features:$.features.filter(e=>""!==e.trim())})})).ok)e({title:"Berhasil",description:`Paket Booster berhasil ${V?"diupdate":"dibuat"}`}),H(!1),X(),U();else throw Error("Failed to save package")}catch(a){console.error("Error saving package:",a),e({title:"Error",description:"Gagal menyimpan paket Booster",variant:"destructive"})}finally{A(!1)}},W=e=>{I(e),L({name:e.name,description:e.description,duration:e.duration,price:e.price,features:e.features.length>0?e.features:[""],priority:e.priority,isActive:e.isActive}),H(!0)},G=async a=>{if(confirm("Apakah Anda yakin ingin menghapus paket ini?"))try{if((await fetch(`/api/admin/booster/packages/${a}`,{method:"DELETE"})).ok)e({title:"Berhasil",description:"Paket Booster berhasil dihapus"}),U();else throw Error("Failed to delete package")}catch(a){console.error("Error deleting package:",a),e({title:"Error",description:"Gagal menghapus paket Booster",variant:"destructive"})}},K=async(a,s)=>{try{if((await fetch(`/api/admin/booster/boosts/${a}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:s})})).ok)e({title:"Berhasil",description:"Status boost berhasil diupdate"}),U();else throw Error("Failed to update boost status")}catch(a){console.error("Error updating boost status:",a),e({title:"Error",description:"Gagal mengupdate status boost",variant:"destructive"})}},X=()=>{I(null),L({name:"",description:"",duration:3,price:0,features:[""],priority:1,isActive:!0})},J=(e,a)=>{L(s=>({...s,features:s.features.map((s,t)=>t===e?a:s)}))},Y=e=>{L(a=>({...a,features:a.features.filter((a,s)=>s!==e)}))},Q=e=>{let a=S.find(a=>a.value===e);return t.jsx(u.C,{className:a?.color||"bg-gray-100 text-gray-800",children:a?.label||e})},ee=e=>e>=3?t.jsx(f.Z,{className:"h-4 w-4 text-yellow-500"}):e>=2?t.jsx(j.Z,{className:"h-4 w-4 text-blue-500"}):t.jsx(g.Z,{className:"h-4 w-4 text-gray-500"});return a?t.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:t.jsx(y.Z,{className:"h-8 w-8 animate-spin"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Event Booster Management"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola paket boost dan promosi event"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)(i.z,{onClick:U,variant:"outline",children:[t.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,t.jsxs)(x.Vq,{open:F,onOpenChange:H,children:[t.jsx(x.hg,{asChild:!0,children:(0,t.jsxs)(i.z,{onClick:X,children:[t.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Tambah Paket"]})}),(0,t.jsxs)(x.cZ,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[(0,t.jsxs)(x.fK,{children:[t.jsx(x.$N,{children:V?"Edit Paket Booster":"Tambah Paket Booster"}),t.jsx(x.Be,{children:"Konfigurasi paket promosi untuk event organizer"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(o._,{htmlFor:"name",children:"Nama Paket"}),t.jsx(l.I,{id:"name",value:$.name,onChange:e=>L(a=>({...a,name:e.target.value})),placeholder:"e.g., Boost Premium"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(o._,{htmlFor:"priority",children:"Prioritas (1-5)"}),t.jsx(l.I,{id:"priority",type:"number",min:"1",max:"5",value:$.priority,onChange:e=>L(a=>({...a,priority:parseInt(e.target.value)}))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(o._,{htmlFor:"description",children:"Deskripsi"}),t.jsx(d.g,{id:"description",value:$.description,onChange:e=>L(a=>({...a,description:e.target.value})),rows:3,placeholder:"Deskripsi detail paket boost..."})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(o._,{htmlFor:"price",children:"Harga (Rp)"}),t.jsx(l.I,{id:"price",type:"number",value:$.price,onChange:e=>L(a=>({...a,price:parseInt(e.target.value)}))})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(o._,{htmlFor:"duration",children:"Durasi (hari)"}),t.jsx(l.I,{id:"duration",type:"number",value:$.duration,onChange:e=>L(a=>({...a,duration:parseInt(e.target.value)}))})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[t.jsx(o._,{children:"Fitur Paket"}),$.features.map((e,a)=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(l.I,{value:e,onChange:e=>J(a,e.target.value),placeholder:"Fitur paket boost"}),t.jsx(i.z,{type:"button",variant:"outline",size:"sm",onClick:()=>Y(a),disabled:1===$.features.length,children:t.jsx(k.Z,{className:"h-4 w-4"})})]},a)),(0,t.jsxs)(i.z,{type:"button",variant:"outline",onClick:()=>{L(e=>({...e,features:[...e.features,""]}))},children:[t.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Tambah Fitur"]})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[t.jsx(o._,{children:"Paket Aktif"}),t.jsx(c.r,{checked:$.isActive,onCheckedChange:e=>L(a=>({...a,isActive:e}))})]})]}),(0,t.jsxs)(x.cN,{children:[t.jsx(i.z,{variant:"outline",onClick:()=>H(!1),children:"Batal"}),(0,t.jsxs)(i.z,{onClick:O,disabled:_,children:[_?t.jsx(y.Z,{className:"h-4 w-4 mr-2 animate-spin"}):t.jsx(N.Z,{className:"h-4 w-4 mr-2"}),V?"Update":"Simpan"]})]})]})]})]})]}),(0,t.jsxs)(h.mQ,{defaultValue:"packages",className:"space-y-6",children:[(0,t.jsxs)(h.dr,{children:[(0,t.jsxs)(h.SP,{value:"packages",children:["Paket Boost (",T.length,")"]}),(0,t.jsxs)(h.SP,{value:"boosts",children:["Active Boosts (",z.length,")"]})]}),t.jsx(h.nU,{value:"packages",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.ll,{children:"Booster Packages"}),t.jsx(n.SZ,{children:"Kelola paket promosi yang tersedia untuk organizer"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)(p.iA,{children:[t.jsx(p.xD,{children:(0,t.jsxs)(p.SC,{children:[t.jsx(p.ss,{children:"Paket"}),t.jsx(p.ss,{children:"Harga"}),t.jsx(p.ss,{children:"Durasi"}),t.jsx(p.ss,{children:"Prioritas"}),t.jsx(p.ss,{children:"Boosts"}),t.jsx(p.ss,{children:"Status"}),t.jsx(p.ss,{children:"Aksi"})]})}),t.jsx(p.RM,{children:T.map(e=>(0,t.jsxs)(p.SC,{children:[t.jsx(p.pj,{children:(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-medium flex items-center gap-2",children:[ee(e.priority),e.name]}),t.jsx("div",{className:"text-sm text-gray-500 line-clamp-2",children:e.description}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-1 mt-2",children:[e.features.slice(0,2).map((e,a)=>t.jsx(u.C,{variant:"outline",className:"text-xs",children:e},a)),e.features.length>2&&(0,t.jsxs)(u.C,{variant:"outline",className:"text-xs",children:["+",e.features.length-2," more"]})]})]})}),t.jsx(p.pj,{children:t.jsx("div",{className:"font-medium",children:(0,M.formatCurrency)(e.price)})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(w.Z,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{children:[e.duration," hari"]})]})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[ee(e.priority),(0,t.jsxs)("span",{children:["Level ",e.priority]})]})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(C.Z,{className:"h-4 w-4 text-gray-400"}),t.jsx("span",{children:e._count.boosts})]})}),t.jsx(p.pj,{children:e.isActive?t.jsx(u.C,{className:"bg-green-100 text-green-800",children:"Active"}):t.jsx(u.C,{className:"bg-gray-100 text-gray-800",children:"Inactive"})}),t.jsx(p.pj,{children:(0,t.jsxs)(m.h_,{children:[t.jsx(m.$F,{asChild:!0,children:t.jsx(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:t.jsx(Z.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(m.AW,{align:"end",children:[t.jsx(m.Ju,{children:"Aksi"}),(0,t.jsxs)(m.Xi,{onClick:()=>W(e),children:[t.jsx(P.Z,{className:"mr-2 h-4 w-4"}),"Edit"]}),t.jsx(m.VD,{}),(0,t.jsxs)(m.Xi,{className:"text-red-600",onClick:()=>G(e.id),children:[t.jsx(k.Z,{className:"mr-2 h-4 w-4"}),"Hapus"]})]})]})})]},e.id))})]}),0===T.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx(D,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:"Belum ada paket booster yang dibuat"})]})]})]})}),t.jsx(h.nU,{value:"boosts",children:(0,t.jsxs)(n.Zb,{children:[(0,t.jsxs)(n.Ol,{children:[t.jsx(n.ll,{children:"Active Event Boosts"}),t.jsx(n.SZ,{children:"Monitor dan kelola boost event yang sedang aktif"})]}),(0,t.jsxs)(n.aY,{children:[(0,t.jsxs)(p.iA,{children:[t.jsx(p.xD,{children:(0,t.jsxs)(p.SC,{children:[t.jsx(p.ss,{children:"Event"}),t.jsx(p.ss,{children:"Organizer"}),t.jsx(p.ss,{children:"Paket"}),t.jsx(p.ss,{children:"Periode"}),t.jsx(p.ss,{children:"Harga"}),t.jsx(p.ss,{children:"Status"}),t.jsx(p.ss,{children:"Aksi"})]})}),t.jsx(p.RM,{children:z.map(e=>(0,t.jsxs)(p.SC,{children:[t.jsx(p.pj,{children:(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:e.event.title}),t.jsx("div",{className:"text-sm text-gray-500",children:(0,M.formatDate)(e.event.startDate)})]})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:e.organizer.name}),t.jsx("div",{className:"text-sm text-gray-500",children:e.organizer.email})]})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[ee(e.package.priority),t.jsx("span",{children:e.package.name})]})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"text-sm",children:[t.jsx("div",{children:(0,M.formatDate)(e.startDate)}),(0,t.jsxs)("div",{className:"text-gray-500",children:["s/d ",(0,M.formatDate)(e.endDate)]})]})}),t.jsx(p.pj,{children:t.jsx("div",{className:"font-medium",children:(0,M.formatCurrency)(e.price)})}),t.jsx(p.pj,{children:Q(e.status)}),t.jsx(p.pj,{children:(0,t.jsxs)(m.h_,{children:[t.jsx(m.$F,{asChild:!0,children:t.jsx(i.z,{variant:"ghost",className:"h-8 w-8 p-0",children:t.jsx(Z.Z,{className:"h-4 w-4"})})}),(0,t.jsxs)(m.AW,{align:"end",children:[t.jsx(m.Ju,{children:"Update Status"}),S.map(a=>t.jsx(m.Xi,{onClick:()=>K(e.id,a.value),disabled:e.status===a.value,children:a.label},a.value))]})]})})]},e.id))})]}),0===z.length&&(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx(R.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:"Belum ada boost event yang aktif"})]})]})]})})]})]})}},16802:(e,a,s)=>{"use strict";s.d(a,{$N:()=>h,Be:()=>f,Vq:()=>o,cN:()=>m,cZ:()=>p,fK:()=>x,hg:()=>d});var t=s(95344),r=s(3729),i=s(88794),n=s(14513),l=s(91626);let o=i.fC,d=i.xz,c=i.h_;i.x8;let u=r.forwardRef(({className:e,...a},s)=>t.jsx(i.aV,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));u.displayName=i.aV.displayName;let p=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(c,{children:[t.jsx(u,{}),(0,t.jsxs)(i.VY,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[a,(0,t.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(n.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));p.displayName=i.VY.displayName;let x=({className:e,...a})=>t.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});x.displayName="DialogHeader";let m=({className:e,...a})=>t.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});m.displayName="DialogFooter";let h=r.forwardRef(({className:e,...a},s)=>t.jsx(i.Dx,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));h.displayName=i.Dx.displayName;let f=r.forwardRef(({className:e,...a},s)=>t.jsx(i.dk,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));f.displayName=i.dk.displayName},54572:(e,a,s)=>{"use strict";s.d(a,{_:()=>c});var t=s(95344),r=s(3729),i=s(62409),n=r.forwardRef((e,a)=>(0,t.jsx)(i.WV.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));n.displayName="Label";var l=s(92193),o=s(91626);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...a},s)=>t.jsx(n,{ref:s,className:(0,o.cn)(d(),e),...a}));c.displayName=n.displayName},71809:(e,a,s)=>{"use strict";s.d(a,{r:()=>N});var t=s(95344),r=s(3729),i=s(85222),n=s(31405),l=s(98462),o=s(33183),d=s(92062),c=s(63085),u=s(62409),p="Switch",[x,m]=(0,l.b)(p),[h,f]=x(p),j=r.forwardRef((e,a)=>{let{__scopeSwitch:s,name:l,checked:d,defaultChecked:c,required:x,disabled:m,value:f="on",onCheckedChange:j,form:g,...y}=e,[k,N]=r.useState(null),w=(0,n.e)(a,e=>N(e)),C=r.useRef(!1),Z=!k||g||!!k.closest("form"),[P,D]=(0,o.T)({prop:d,defaultProp:c??!1,onChange:j,caller:p});return(0,t.jsxs)(h,{scope:s,checked:P,disabled:m,children:[(0,t.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":P,"aria-required":x,"data-state":b(P),"data-disabled":m?"":void 0,disabled:m,value:f,...y,ref:w,onClick:(0,i.M)(e.onClick,e=>{D(e=>!e),Z&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),Z&&(0,t.jsx)(v,{control:k,bubbles:!C.current,name:l,value:f,checked:P,required:x,disabled:m,form:g,style:{transform:"translateX(-100%)"}})]})});j.displayName=p;var g="SwitchThumb",y=r.forwardRef((e,a)=>{let{__scopeSwitch:s,...r}=e,i=f(g,s);return(0,t.jsx)(u.WV.span,{"data-state":b(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:a})});y.displayName=g;var v=r.forwardRef(({__scopeSwitch:e,control:a,checked:s,bubbles:i=!0,...l},o)=>{let u=r.useRef(null),p=(0,n.e)(u,o),x=(0,d.D)(s),m=(0,c.t)(a);return r.useEffect(()=>{let e=u.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==s&&a){let t=new Event("click",{bubbles:i});a.call(e,s),e.dispatchEvent(t)}},[x,s,i]),(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...l,tabIndex:-1,ref:p,style:{...l.style,...m,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function b(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var k=s(91626);let N=r.forwardRef(({className:e,...a},s)=>t.jsx(j,{className:(0,k.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...a,ref:s,children:t.jsx(y,{className:(0,k.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));N.displayName=j.displayName},81036:(e,a,s)=>{"use strict";s.d(a,{RM:()=>o,SC:()=>d,iA:()=>n,pj:()=>u,ss:()=>c,xD:()=>l});var t=s(95344),r=s(3729),i=s(91626);let n=r.forwardRef(({className:e,...a},s)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",e),...a})}));n.displayName="Table";let l=r.forwardRef(({className:e,...a},s)=>t.jsx("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",e),...a}));l.displayName="TableHeader";let o=r.forwardRef(({className:e,...a},s)=>t.jsx("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...a}));o.displayName="TableBody",r.forwardRef(({className:e,...a},s)=>t.jsx("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let d=r.forwardRef(({className:e,...a},s)=>t.jsx("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));d.displayName="TableRow";let c=r.forwardRef(({className:e,...a},s)=>t.jsx("th",{ref:s,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));c.displayName="TableHead";let u=r.forwardRef(({className:e,...a},s)=>t.jsx("td",{ref:s,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));u.displayName="TableCell",r.forwardRef(({className:e,...a},s)=>t.jsx("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},25757:(e,a,s)=>{"use strict";s.d(a,{mQ:()=>R,nU:()=>S,dr:()=>E,SP:()=>M});var t=s(95344),r=s(3729),i=s(85222),n=s(98462),l=s(34504),o=s(43234),d=s(62409),c=s(3975),u=s(33183),p=s(99048),x="Tabs",[m,h]=(0,n.b)(x,[l.Pc]),f=(0,l.Pc)(),[j,g]=m(x),y=r.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:o,activationMode:m="automatic",...h}=e,f=(0,c.gm)(o),[g,y]=(0,u.T)({prop:r,onChange:i,defaultProp:n??"",caller:x});return(0,t.jsx)(j,{scope:s,baseId:(0,p.M)(),value:g,onValueChange:y,orientation:l,dir:f,activationMode:m,children:(0,t.jsx)(d.WV.div,{dir:f,"data-orientation":l,...h,ref:a})})});y.displayName=x;var v="TabsList",b=r.forwardRef((e,a)=>{let{__scopeTabs:s,loop:r=!0,...i}=e,n=g(v,s),o=f(s);return(0,t.jsx)(l.fC,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:r,children:(0,t.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:a})})});b.displayName=v;var k="TabsTrigger",N=r.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,disabled:n=!1,...o}=e,c=g(k,s),u=f(s),p=Z(c.baseId,r),x=P(c.baseId,r),m=r===c.value;return(0,t.jsx)(l.ck,{asChild:!0,...u,focusable:!n,active:m,children:(0,t.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":x,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:p,...o,ref:a,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;m||n||!e||c.onValueChange(r)})})})});N.displayName=k;var w="TabsContent",C=r.forwardRef((e,a)=>{let{__scopeTabs:s,value:i,forceMount:n,children:l,...c}=e,u=g(w,s),p=Z(u.baseId,i),x=P(u.baseId,i),m=i===u.value,h=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(o.z,{present:n||m,children:({present:s})=>(0,t.jsx)(d.WV.div,{"data-state":m?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":p,hidden:!s,id:x,tabIndex:0,...c,ref:a,style:{...e.style,animationDuration:h.current?"0s":void 0},children:s&&l})})});function Z(e,a){return`${e}-trigger-${a}`}function P(e,a){return`${e}-content-${a}`}C.displayName=w;var D=s(91626);let R=y,E=r.forwardRef(({className:e,...a},s)=>t.jsx(b,{ref:s,className:(0,D.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));E.displayName=b.displayName;let M=r.forwardRef(({className:e,...a},s)=>t.jsx(N,{ref:s,className:(0,D.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));M.displayName=N.displayName;let S=r.forwardRef(({className:e,...a},s)=>t.jsx(C,{ref:s,className:(0,D.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));S.displayName=C.displayName},93601:(e,a,s)=>{"use strict";s.d(a,{g:()=>n});var t=s(95344),r=s(3729),i=s(91626);let n=r.forwardRef(({className:e,...a},s)=>t.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));n.displayName="Textarea"},88534:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},25390:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},25545:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},62093:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("MoreHorizontal",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},51838:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33733:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31498:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},46327:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},17910:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},38271:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},46064:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},79200:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},20490:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let t=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\booster\page.tsx`),{__esModule:r,$$typeof:i}=t,n=t.default},92062:(e,a,s)=>{"use strict";s.d(a,{D:()=>r});var t=s(3729);function r(e){let a=t.useRef({value:e,previous:e});return t.useMemo(()=>(a.current.value!==e&&(a.current.previous=a.current.value,a.current.value=e),a.current.previous),[e])}}};var a=require("../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[1638,3088,9253,9205,2295],()=>s(80534));module.exports=t})();