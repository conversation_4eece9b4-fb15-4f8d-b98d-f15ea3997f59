(()=>{var e={};e.id=4331,e.ids=[4331],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},41016:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var t=s(50482),r=s(69108),i=s(62563),l=s.n(i),n=s(68300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(a,o);let c=["",{children:["admin",{children:["categories",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9247)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\categories\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\categories\\page.tsx"],m="/admin/categories/page",x={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/categories/page",pathname:"/admin/categories",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},66424:(e,a,s)=>{Promise.resolve().then(s.bind(s,72563))},72563:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>w});var t=s(95344),r=s(3729),i=s(47674),l=s(8428),n=s(16212),o=s(61351),c=s(92549),d=s(54572),m=s(69436),x=s(16802),p=s(81036),u=s(42739),h=s(51838),f=s(28765),g=s(36341),j=s(66138),y=s(89895),v=s(46327),N=s(38271),b=s(30692),k=s(91626);function w(){let{data:e,status:a}=(0,i.useSession)(),s=(0,l.useRouter)(),{toast:w}=(0,b.pm)(),[C,D]=(0,r.useState)([]),[_,q]=(0,r.useState)(!0),[Z,A]=(0,r.useState)(""),[P,T]=(0,r.useState)(!1),[M,R]=(0,r.useState)(null),[S,E]=(0,r.useState)({name:"",description:"",icon:"",color:"#3B82F6",isActive:!0}),[z,B]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if("loading"!==a&&(!e||"ADMIN"!==e.user.role)){s.push("/auth/login");return}},[e,a,s]);let F=async()=>{try{let e=await fetch("/api/categories"),a=await e.json();a.success?D(a.data):w({title:"Error",description:a.message||"Gagal mengambil data kategori",variant:"destructive"})}catch(e){w({title:"Error",description:"Terjadi kesalahan saat mengambil data",variant:"destructive"})}finally{q(!1)}};(0,r.useEffect)(()=>{e?.user.role==="ADMIN"&&F()},[e]);let K=async e=>{e.preventDefault(),B(!0);try{let e=M?`/api/categories/${M.id}`:"/api/categories",a=await fetch(e,{method:M?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(S)}),s=await a.json();s.success?(w({title:"Berhasil",description:s.message,variant:"success"}),T(!1),V(),F()):w({title:"Error",description:s.message||"Terjadi kesalahan",variant:"destructive"})}catch(e){w({title:"Error",description:"Terjadi kesalahan saat menyimpan data",variant:"destructive"})}finally{B(!1)}},I=async e=>{if(confirm(`Apakah Anda yakin ingin menghapus kategori "${e.name}"?`))try{let a=await fetch(`/api/categories/${e.id}`,{method:"DELETE"}),s=await a.json();s.success?(w({title:"Berhasil",description:s.message,variant:"success"}),F()):w({title:"Error",description:s.message||"Gagal menghapus kategori",variant:"destructive"})}catch(e){w({title:"Error",description:"Terjadi kesalahan saat menghapus kategori",variant:"destructive"})}},V=()=>{E({name:"",description:"",icon:"",color:"#3B82F6",isActive:!0}),R(null)},L=e=>{R(e),E({name:e.name,description:e.description||"",icon:e.icon||"",color:e.color||"#3B82F6",isActive:e.isActive}),T(!0)},$=C.filter(e=>e.name.toLowerCase().includes(Z.toLowerCase())||e.description?.toLowerCase().includes(Z.toLowerCase()));return"loading"===a||_?t.jsx("div",{className:"flex items-center justify-center min-h-screen",children:t.jsx(u.Z,{className:"h-8 w-8 animate-spin"})}):e&&"ADMIN"===e.user.role?(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Manajemen Kategori"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola kategori event untuk platform TiXara"})]}),(0,t.jsxs)(x.Vq,{open:P,onOpenChange:T,children:[t.jsx(x.hg,{asChild:!0,children:(0,t.jsxs)(n.z,{onClick:V,className:"flex items-center gap-2",children:[t.jsx(h.Z,{className:"h-4 w-4"}),"Tambah Kategori"]})}),(0,t.jsxs)(x.cZ,{children:[(0,t.jsxs)(x.fK,{children:[t.jsx(x.$N,{children:M?"Edit Kategori":"Tambah Kategori Baru"}),t.jsx(x.Be,{children:M?"Perbarui informasi kategori":"Buat kategori baru untuk mengorganisir event"})]}),(0,t.jsxs)("form",{onSubmit:K,className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"name",children:"Nama Kategori"}),t.jsx(c.I,{id:"name",value:S.name,onChange:e=>E(a=>({...a,name:e.target.value})),placeholder:"Masukkan nama kategori",required:!0})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"description",children:"Deskripsi"}),t.jsx(c.I,{id:"description",value:S.description,onChange:e=>E(a=>({...a,description:e.target.value})),placeholder:"Deskripsi kategori (opsional)"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"icon",children:"Icon"}),t.jsx(c.I,{id:"icon",value:S.icon,onChange:e=>E(a=>({...a,icon:e.target.value})),placeholder:"\uD83C\uDFB5"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(d._,{htmlFor:"color",children:"Warna"}),t.jsx(c.I,{id:"color",type:"color",value:S.color,onChange:e=>E(a=>({...a,color:e.target.value}))})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx("input",{type:"checkbox",id:"isActive",checked:S.isActive,onChange:e=>E(a=>({...a,isActive:e.target.checked})),className:"rounded"}),t.jsx(d._,{htmlFor:"isActive",children:"Kategori aktif"})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-4",children:[t.jsx(n.z,{type:"button",variant:"outline",onClick:()=>T(!1),children:"Batal"}),(0,t.jsxs)(n.z,{type:"submit",disabled:z,children:[z&&t.jsx(u.Z,{className:"mr-2 h-4 w-4 animate-spin"}),M?"Perbarui":"Simpan"]})]})]})]})]})]}),t.jsx(o.Zb,{className:"mb-6",children:t.jsx(o.aY,{className:"pt-6",children:(0,t.jsxs)("div",{className:"relative",children:[t.jsx(f.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),t.jsx(c.I,{placeholder:"Cari kategori...",value:Z,onChange:e=>A(e.target.value),className:"pl-10"})]})})}),(0,t.jsxs)(o.Zb,{children:[t.jsx(o.Ol,{children:(0,t.jsxs)(o.ll,{className:"flex items-center gap-2",children:[t.jsx(g.Z,{className:"h-5 w-5"}),"Daftar Kategori (",$.length,")"]})}),t.jsx(o.aY,{children:0===$.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[t.jsx(j.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t.jsx("p",{className:"text-gray-500",children:Z?"Tidak ada kategori yang ditemukan":"Belum ada kategori"})]}):(0,t.jsxs)(p.iA,{children:[t.jsx(p.xD,{children:(0,t.jsxs)(p.SC,{children:[t.jsx(p.ss,{children:"Kategori"}),t.jsx(p.ss,{children:"Deskripsi"}),t.jsx(p.ss,{children:"Event"}),t.jsx(p.ss,{children:"Status"}),t.jsx(p.ss,{children:"Dibuat"}),t.jsx(p.ss,{children:"Aksi"})]})}),t.jsx(p.RM,{children:$.map(e=>(0,t.jsxs)(p.SC,{children:[t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[e.icon&&t.jsx("span",{className:"text-lg",children:e.icon}),(0,t.jsxs)("div",{children:[t.jsx("div",{className:"font-medium",children:e.name}),e.color&&t.jsx("div",{className:"w-4 h-4 rounded-full mt-1",style:{backgroundColor:e.color}})]})]})}),t.jsx(p.pj,{children:t.jsx("span",{className:"text-sm text-gray-600",children:e.description||"-"})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(y.Z,{className:"h-4 w-4 text-gray-400"}),t.jsx("span",{children:e._count.events})]})}),t.jsx(p.pj,{children:t.jsx(m.C,{variant:e.isActive?"success":"secondary",children:e.isActive?"Aktif":"Nonaktif"})}),t.jsx(p.pj,{children:t.jsx("span",{className:"text-sm text-gray-600",children:(0,k.formatDate)(e.createdAt)})}),t.jsx(p.pj,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx(n.z,{size:"sm",variant:"outline",onClick:()=>L(e),children:t.jsx(v.Z,{className:"h-4 w-4"})}),t.jsx(n.z,{size:"sm",variant:"outline",onClick:()=>I(e),disabled:e._count.events>0,children:t.jsx(N.Z,{className:"h-4 w-4"})})]})})]},e.id))})]})})]})]}):null}},16802:(e,a,s)=>{"use strict";s.d(a,{$N:()=>h,Be:()=>f,Vq:()=>o,cN:()=>u,cZ:()=>x,fK:()=>p,hg:()=>c});var t=s(95344),r=s(3729),i=s(88794),l=s(14513),n=s(91626);let o=i.fC,c=i.xz,d=i.h_;i.x8;let m=r.forwardRef(({className:e,...a},s)=>t.jsx(i.aV,{ref:s,className:(0,n.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));m.displayName=i.aV.displayName;let x=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(d,{children:[t.jsx(m,{}),(0,t.jsxs)(i.VY,{ref:r,className:(0,n.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[a,(0,t.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[t.jsx(l.Z,{className:"h-4 w-4"}),t.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));x.displayName=i.VY.displayName;let p=({className:e,...a})=>t.jsx("div",{className:(0,n.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});p.displayName="DialogHeader";let u=({className:e,...a})=>t.jsx("div",{className:(0,n.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...a});u.displayName="DialogFooter";let h=r.forwardRef(({className:e,...a},s)=>t.jsx(i.Dx,{ref:s,className:(0,n.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));h.displayName=i.Dx.displayName;let f=r.forwardRef(({className:e,...a},s)=>t.jsx(i.dk,{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...a}));f.displayName=i.dk.displayName},54572:(e,a,s)=>{"use strict";s.d(a,{_:()=>d});var t=s(95344),r=s(3729),i=s(62409),l=r.forwardRef((e,a)=>(0,t.jsx)(i.WV.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));l.displayName="Label";var n=s(92193),o=s(91626);let c=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...a},s)=>t.jsx(l,{ref:s,className:(0,o.cn)(c(),e),...a}));d.displayName=l.displayName},81036:(e,a,s)=>{"use strict";s.d(a,{RM:()=>o,SC:()=>c,iA:()=>l,pj:()=>m,ss:()=>d,xD:()=>n});var t=s(95344),r=s(3729),i=s(91626);let l=r.forwardRef(({className:e,...a},s)=>t.jsx("div",{className:"relative w-full overflow-auto",children:t.jsx("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",e),...a})}));l.displayName="Table";let n=r.forwardRef(({className:e,...a},s)=>t.jsx("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",e),...a}));n.displayName="TableHeader";let o=r.forwardRef(({className:e,...a},s)=>t.jsx("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...a}));o.displayName="TableBody",r.forwardRef(({className:e,...a},s)=>t.jsx("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let c=r.forwardRef(({className:e,...a},s)=>t.jsx("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));c.displayName="TableRow";let d=r.forwardRef(({className:e,...a},s)=>t.jsx("th",{ref:s,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));d.displayName="TableHead";let m=r.forwardRef(({className:e,...a},s)=>t.jsx("td",{ref:s,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));m.displayName="TableCell",r.forwardRef(({className:e,...a},s)=>t.jsx("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},66138:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},25390:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},51838:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},46327:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},36341:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},38271:(e,a,s)=>{"use strict";s.d(a,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,s(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},9247:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let t=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\categories\page.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default}};var a=require("../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[1638,3088,9253,9205,2295],()=>s(41016));module.exports=t})();