(()=>{var e={};e.id=3,e.ids=[3],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},48337:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var r=t(50482),a=t(69108),l=t(62563),i=t.n(l),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,89481)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\page.tsx"],x="/admin/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},84864:(e,s,t)=>{Promise.resolve().then(t.bind(t,90199))},90199:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var r=t(95344),a=t(3729),l=t(61351),i=t(69436),n=t(16212),c=t(89895),d=t(55794),o=t(76196),x=t(76755),u=t(88534),m=t(7060),h=t(45961),p=t(48411),f=t(15366),y=t(85674),j=t(46064),g=t(91626);function b(){let[e,s]=(0,a.useState)({totalUsers:0,totalEvents:0,totalTicketsSold:0,totalRevenue:0,activeSubscriptions:0,pendingVerifications:0,recentTransactions:0,systemStatus:"healthy"}),[t,b]=(0,a.useState)([]),[N,v]=(0,a.useState)(!0);(0,a.useEffect)(()=>{w()},[]);let w=async()=>{try{v(!0);let e=await fetch("/api/admin/dashboard/stats");if(e.ok){let t=await e.json();s(t)}let t=await fetch("/api/admin/dashboard/activity");if(t.ok){let e=await t.json();b(e)}}catch(e){console.error("Error fetching dashboard data:",e)}finally{v(!1)}},k=e=>{switch(e){case"user_registration":return r.jsx(c.Z,{className:"h-4 w-4"});case"event_created":return r.jsx(d.Z,{className:"h-4 w-4"});case"ticket_sold":return r.jsx(o.Z,{className:"h-4 w-4"});case"subscription_created":return r.jsx(x.Z,{className:"h-4 w-4"});default:return r.jsx(u.Z,{className:"h-4 w-4"})}};return N?r.jsx("div",{className:"space-y-6",children:r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[...Array(8)].map((e,s)=>(0,r.jsxs)(l.Zb,{className:"animate-pulse",children:[r.jsx(l.Ol,{className:"pb-2",children:r.jsx("div",{className:"h-4 bg-gray-200 rounded w-3/4"})}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"h-8 bg-gray-200 rounded w-1/2 mb-2"}),r.jsx("div",{className:"h-3 bg-gray-200 rounded w-full"})]})]},s))})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Dashboard Admin"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Overview dan statistik platform TiXara"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)(i.C,{className:(e=>{switch(e){case"healthy":return"text-green-600 bg-green-100";case"warning":return"text-yellow-600 bg-yellow-100";case"error":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}})(e.systemStatus),children:["healthy"===e.systemStatus&&r.jsx(m.Z,{className:"h-3 w-3 mr-1"}),"warning"===e.systemStatus&&r.jsx(h.Z,{className:"h-3 w-3 mr-1"}),"error"===e.systemStatus&&r.jsx(h.Z,{className:"h-3 w-3 mr-1"}),"System ","healthy"===e.systemStatus?"Healthy":e.systemStatus]}),r.jsx(n.z,{onClick:w,variant:"outline",size:"sm",children:"Refresh Data"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"Total Users"}),r.jsx(c.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,g.formatNumber)(e.totalUsers)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+12% dari bulan lalu"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"Total Events"}),r.jsx(d.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,g.formatNumber)(e.totalEvents)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+8% dari bulan lalu"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"Tiket Terjual"}),r.jsx(o.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,g.formatNumber)(e.totalTicketsSold)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+23% dari bulan lalu"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"Total Revenue"}),r.jsx(p.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,g.formatCurrency)(e.totalRevenue)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+15% dari bulan lalu"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"Active Subscriptions"}),r.jsx(x.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,g.formatNumber)(e.activeSubscriptions)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Badge subscriptions aktif"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"Pending Verifications"}),r.jsx(f.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,g.formatNumber)(e.pendingVerifications)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Organizer menunggu verifikasi"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"Recent Transactions"}),r.jsx(y.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,g.formatNumber)(e.recentTransactions)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Transaksi 24 jam terakhir"})]})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(l.ll,{className:"text-sm font-medium",children:"System Performance"}),r.jsx(j.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(l.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:"98.5%"}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"Uptime bulan ini"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[r.jsx(l.ll,{children:"Recent Activity"}),r.jsx(l.SZ,{children:"Aktivitas terbaru di platform"})]}),r.jsx(l.aY,{children:r.jsx("div",{className:"space-y-4",children:t.length>0?t.slice(0,5).map(e=>(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:"flex-shrink-0",children:k(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx("p",{className:"text-sm text-gray-900 dark:text-gray-100",children:e.description}),r.jsx("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.timestamp})]})]},e.id)):r.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Belum ada aktivitas terbaru"})})})]}),(0,r.jsxs)(l.Zb,{children:[(0,r.jsxs)(l.Ol,{children:[r.jsx(l.ll,{children:"Quick Actions"}),r.jsx(l.SZ,{children:"Aksi cepat untuk admin"})]}),r.jsx(l.aY,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)(n.z,{variant:"outline",className:"h-20 flex flex-col items-center justify-center",children:[r.jsx(c.Z,{className:"h-6 w-6 mb-2"}),r.jsx("span",{className:"text-sm",children:"Kelola User"})]}),(0,r.jsxs)(n.z,{variant:"outline",className:"h-20 flex flex-col items-center justify-center",children:[r.jsx(d.Z,{className:"h-6 w-6 mb-2"}),r.jsx("span",{className:"text-sm",children:"Kelola Event"})]}),(0,r.jsxs)(n.z,{variant:"outline",className:"h-20 flex flex-col items-center justify-center",children:[r.jsx(x.Z,{className:"h-6 w-6 mb-2"}),r.jsx("span",{className:"text-sm",children:"Badge Plans"})]}),(0,r.jsxs)(n.z,{variant:"outline",className:"h-20 flex flex-col items-center justify-center",children:[r.jsx(j.Z,{className:"h-6 w-6 mb-2"}),r.jsx("span",{className:"text-sm",children:"Analytics"})]})]})})]})]})]})}},88534:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},50340:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},7060:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25390:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},85674:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},48411:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},2273:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},70009:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},46064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},15366:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},30080:(e,s,t)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var r=t(3729);"function"==typeof Object.is&&Object.is,r.useState,r.useEffect,r.useLayoutEffect,r.useDebugValue,s.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:function(e,s){return s()}},8145:(e,s,t)=>{"use strict";e.exports=t(30080)},89481:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>a,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\page.tsx`),{__esModule:a,$$typeof:l}=r,i=r.default},15480:(e,s,t)=>{"use strict";t.d(s,{NY:()=>k,Ee:()=>w,fC:()=>v});var r=t(3729),a=t(98462),l=t(2256),i=t(16069),n=t(62409),c=t(8145);function d(){return()=>{}}var o=t(95344),x="Avatar",[u,m]=(0,a.b)(x),[h,p]=u(x),f=r.forwardRef((e,s)=>{let{__scopeAvatar:t,...a}=e,[l,i]=r.useState("idle");return(0,o.jsx)(h,{scope:t,imageLoadingStatus:l,onImageLoadingStatusChange:i,children:(0,o.jsx)(n.WV.span,{...a,ref:s})})});f.displayName=x;var y="AvatarImage",j=r.forwardRef((e,s)=>{let{__scopeAvatar:t,src:a,onLoadingStatusChange:x=()=>{},...u}=e,m=p(y,t),h=function(e,{referrerPolicy:s,crossOrigin:t}){let a=(0,c.useSyncExternalStore)(d,()=>!0,()=>!1),l=r.useRef(null),n=a?(l.current||(l.current=new window.Image),l.current):null,[o,x]=r.useState(()=>N(n,e));return(0,i.b)(()=>{x(N(n,e))},[n,e]),(0,i.b)(()=>{let e=e=>()=>{x(e)};if(!n)return;let r=e("loaded"),a=e("error");return n.addEventListener("load",r),n.addEventListener("error",a),s&&(n.referrerPolicy=s),"string"==typeof t&&(n.crossOrigin=t),()=>{n.removeEventListener("load",r),n.removeEventListener("error",a)}},[n,t,s]),o}(a,u),f=(0,l.W)(e=>{x(e),m.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==h&&f(h)},[h,f]),"loaded"===h?(0,o.jsx)(n.WV.img,{...u,ref:s,src:a}):null});j.displayName=y;var g="AvatarFallback",b=r.forwardRef((e,s)=>{let{__scopeAvatar:t,delayMs:a,...l}=e,i=p(g,t),[c,d]=r.useState(void 0===a);return r.useEffect(()=>{if(void 0!==a){let e=window.setTimeout(()=>d(!0),a);return()=>window.clearTimeout(e)}},[a]),c&&"loaded"!==i.imageLoadingStatus?(0,o.jsx)(n.WV.span,{...l,ref:s}):null});function N(e,s){return e?s?(e.src!==s&&(e.src=s),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=g;var v=f,w=j,k=b}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[1638,3088,9205,2295],()=>t(48337));module.exports=r})();