(()=>{var e={};e.id=4986,e.ids=[4986],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12403:(e,n,o)=>{"use strict";o.r(n),o.d(n,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>l,routeModule:()=>O,tree:()=>d});var r=o(50482),t=o(69108),a=o(62563),i=o.n(a),s=o(68300),c={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);o.d(n,c);let d=["",{children:["admin",{children:["settings",{children:["platform",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(o.bind(o,37030)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\settings\\platform\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(o.bind(o,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(o.bind(o,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(o.t.bind(o,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\settings\\platform\\page.tsx"],m="/admin/settings/platform/page",u={require:o,loadChunk:()=>Promise.resolve()},O=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/admin/settings/platform/page",pathname:"/admin/settings/platform",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},9559:(e,n,o)=>{Promise.resolve().then(o.bind(o,45778))},61971:(e,n,o)=>{Promise.resolve().then(o.bind(o,55353))},16509:(e,n,o)=>{Promise.resolve().then(o.t.bind(o,2583,23)),Promise.resolve().then(o.t.bind(o,26840,23)),Promise.resolve().then(o.t.bind(o,38771,23)),Promise.resolve().then(o.t.bind(o,13225,23)),Promise.resolve().then(o.t.bind(o,9295,23)),Promise.resolve().then(o.t.bind(o,43982,23))},23978:()=>{},45778:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>s});var r=o(95344),t=o(47674),a=o(8428);(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}();var i=o(42739);function s({children:e}){let{data:n,status:o}=(0,t.useSession)(),s=(0,a.useRouter)();return"loading"===o?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(i.Z,{className:"h-8 w-8 animate-spin"})}):n?.user&&"ADMIN"===n.user.role?r.jsx(Object(function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"py-6",children:r.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]})}):(s.push("/dashboard"),null)}},55353:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>f});var r=o(95344),t=o(3729),a=o(42739),i=o(45961),s=o(33733),c=o(31498),d=o(69224);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,d.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var m=o(23485),u=o(85674),O=o(7060),h=o(70009);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,d.Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);function f(){let{toast:e}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[n,o]=(0,t.useState)(!0),[d,f]=(0,t.useState)(!1),[N,j]=(0,t.useState)({platformName:"TiXara",platformDescription:"Platform e-ticketing terpercaya untuk event Anda",platformUrl:"https://tixara.my.id",supportEmail:"<EMAIL>",maintenanceMode:!1,maintenanceMessage:"Platform sedang dalam maintenance. Silakan coba lagi nanti.",defaultCommissionRate:5,defaultTaxRate:0,withdrawalFee:2500,minimumWithdrawal:5e4,allowUserRegistration:!0,requireEmailVerification:!0,allowOrganizerSelfVerification:!1,enableNotifications:!0,primaryColor:"#0ea5e9",secondaryColor:"#10b981",logoUrl:"",faviconUrl:"",metaTitle:"TiXara - Platform E-Ticketing Terpercaya",metaDescription:"Jual dan beli tiket event dengan mudah di TiXara. Platform e-ticketing terpercaya untuk konser, workshop, seminar, dan event lainnya.",metaKeywords:"tiket, event, konser, workshop, seminar, e-ticketing",facebookUrl:"https://facebook.com/tixara-id",twitterUrl:"https://twitter.com/tixara_id",instagramUrl:"https://instagram.com/tixara.id",linkedinUrl:"https://linkedin.com/company/tixara"});(0,t.useEffect)(()=>{v()},[]);let v=async()=>{try{o(!0);let e=await fetch("/api/admin/settings/platform");if(e.ok){let n=await e.json();j(e=>({...e,...n}))}}catch(n){console.error("Error fetching settings:",n),e({title:"Error",description:"Gagal memuat pengaturan platform",variant:"destructive"})}finally{o(!1)}},U=async()=>{try{if(f(!0),(await fetch("/api/admin/settings/platform",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(N)})).ok)e({title:"Berhasil",description:"Pengaturan platform berhasil disimpan"});else throw Error("Failed to save settings")}catch(n){console.error("Error saving settings:",n),e({title:"Error",description:"Gagal menyimpan pengaturan platform",variant:"destructive"})}finally{f(!1)}},x=(e,n)=>{j(o=>({...o,[e]:n}))};return n?r.jsx("div",{className:"flex items-center justify-center min-h-[400px]",children:r.jsx(a.Z,{className:"h-8 w-8 animate-spin"})}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Pengaturan Platform"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola konfigurasi dan pengaturan platform TiXara"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[N.maintenanceMode&&(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"destructive",children:[r.jsx(i.Z,{className:"h-3 w-3 mr-1"}),"Maintenance Mode"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:v,variant:"outline",children:[r.jsx(s.Z,{className:"h-4 w-4 mr-2"}),"Refresh"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:U,disabled:d,children:[d?r.jsx(a.Z,{className:"h-4 w-4 mr-2 animate-spin"}):r.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Simpan"]})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{defaultValue:"general",className:"space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid w-full grid-cols-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"general",children:[r.jsx(l,{className:"h-4 w-4 mr-2"}),"General"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"maintenance",children:[r.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Maintenance"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"commission",children:[r.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Commission"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"features",children:[r.jsx(O.Z,{className:"h-4 w-4 mr-2"}),"Features"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"branding",children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Branding"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"seo",children:[r.jsx(p,{className:"h-4 w-4 mr-2"}),"SEO & Social"]})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"general",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Pengaturan Umum"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Konfigurasi dasar platform TiXara"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"platformName",children:"Nama Platform"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"platformName",value:N.platformName,onChange:e=>x("platformName",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"platformUrl",children:"URL Platform"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"platformUrl",value:N.platformUrl,onChange:e=>x("platformUrl",e.target.value)})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"platformDescription",children:"Deskripsi Platform"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"platformDescription",value:N.platformDescription,onChange:e=>x("platformDescription",e.target.value),rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"supportEmail",children:"Email Support"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"supportEmail",type:"email",value:N.supportEmail,onChange:e=>x("supportEmail",e.target.value)})]})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"maintenance",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Mode Maintenance"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Kontrol akses platform saat maintenance"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Aktifkan Mode Maintenance"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Platform akan tidak dapat diakses oleh user biasa"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:N.maintenanceMode,onCheckedChange:e=>x("maintenanceMode",e)})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/separator'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"maintenanceMessage",children:"Pesan Maintenance"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"maintenanceMessage",value:N.maintenanceMessage,onChange:e=>x("maintenanceMessage",e.target.value),rows:3,placeholder:"Pesan yang akan ditampilkan saat maintenance"})]}),N.maintenanceMode&&r.jsx("div",{className:"p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(i.Z,{className:"h-5 w-5 text-yellow-600 mr-2"}),r.jsx("p",{className:"text-sm text-yellow-800",children:"Mode maintenance sedang aktif. Hanya admin yang dapat mengakses platform."})]})})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"commission",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Komisi & Biaya"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Pengaturan komisi platform dan biaya transaksi"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"defaultCommissionRate",children:"Komisi Default (%)"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"defaultCommissionRate",type:"number",min:"0",max:"100",step:"0.1",value:N.defaultCommissionRate,onChange:e=>x("defaultCommissionRate",parseFloat(e.target.value))})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"defaultTaxRate",children:"Pajak Default (%)"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"defaultTaxRate",type:"number",min:"0",max:"100",step:"0.1",value:N.defaultTaxRate,onChange:e=>x("defaultTaxRate",parseFloat(e.target.value))})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"withdrawalFee",children:"Biaya Withdrawal (Rp)"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"withdrawalFee",type:"number",min:"0",value:N.withdrawalFee,onChange:e=>x("withdrawalFee",parseInt(e.target.value))})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"minimumWithdrawal",children:"Minimum Withdrawal (Rp)"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"minimumWithdrawal",type:"number",min:"0",value:N.minimumWithdrawal,onChange:e=>x("minimumWithdrawal",parseInt(e.target.value))})]})]})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"features",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Fitur Platform"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Kontrol fitur yang tersedia di platform"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Registrasi User Baru"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Izinkan user baru mendaftar ke platform"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:N.allowUserRegistration,onCheckedChange:e=>x("allowUserRegistration",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Verifikasi Email Wajib"}),r.jsx("p",{className:"text-sm text-gray-500",children:"User harus verifikasi email sebelum dapat menggunakan platform"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:N.requireEmailVerification,onCheckedChange:e=>x("requireEmailVerification",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Self-Verification Organizer"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Organizer dapat memverifikasi diri sendiri tanpa persetujuan admin"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:N.allowOrganizerSelfVerification,onCheckedChange:e=>x("allowOrganizerSelfVerification",e)})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"space-y-0.5",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Notifikasi Push"}),r.jsx("p",{className:"text-sm text-gray-500",children:"Aktifkan sistem notifikasi push untuk user"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{checked:N.enableNotifications,onCheckedChange:e=>x("enableNotifications",e)})]})]})})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"branding",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Branding & Tema"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Kustomisasi tampilan dan branding platform"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"primaryColor",children:"Warna Primary"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"primaryColor",type:"color",value:N.primaryColor,onChange:e=>x("primaryColor",e.target.value),className:"w-16 h-10"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{value:N.primaryColor,onChange:e=>x("primaryColor",e.target.value),placeholder:"#0ea5e9"})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"secondaryColor",children:"Warna Secondary"}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"secondaryColor",type:"color",value:N.secondaryColor,onChange:e=>x("secondaryColor",e.target.value),className:"w-16 h-10"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{value:N.secondaryColor,onChange:e=>x("secondaryColor",e.target.value),placeholder:"#10b981"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"logoUrl",children:"URL Logo"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"logoUrl",value:N.logoUrl,onChange:e=>x("logoUrl",e.target.value),placeholder:"https://example.com/logo.png"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"faviconUrl",children:"URL Favicon"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"faviconUrl",value:N.faviconUrl,onChange:e=>x("faviconUrl",e.target.value),placeholder:"https://example.com/favicon.ico"})]})]})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"seo",children:(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"SEO Settings"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Optimasi mesin pencari untuk platform"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"metaTitle",children:"Meta Title"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"metaTitle",value:N.metaTitle,onChange:e=>x("metaTitle",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"metaDescription",children:"Meta Description"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"metaDescription",value:N.metaDescription,onChange:e=>x("metaDescription",e.target.value),rows:3})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"metaKeywords",children:"Meta Keywords"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"metaKeywords",value:N.metaKeywords,onChange:e=>x("metaKeywords",e.target.value),placeholder:"keyword1, keyword2, keyword3"})]})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Social Media"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Link media sosial platform"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"facebookUrl",children:"Facebook URL"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"facebookUrl",value:N.facebookUrl,onChange:e=>x("facebookUrl",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"twitterUrl",children:"Twitter URL"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"twitterUrl",value:N.twitterUrl,onChange:e=>x("twitterUrl",e.target.value)})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"instagramUrl",children:"Instagram URL"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"instagramUrl",value:N.instagramUrl,onChange:e=>x("instagramUrl",e.target.value)})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"linkedinUrl",children:"LinkedIn URL"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"linkedinUrl",value:N.linkedinUrl,onChange:e=>x("linkedinUrl",e.target.value)})]})]})]})]})]})})]})]})}(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/separator'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}()},45961:(e,n,o)=>{"use strict";o.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},7060:(e,n,o)=>{"use strict";o.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},85674:(e,n,o)=>{"use strict";o.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},70009:(e,n,o)=>{"use strict";o.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},33733:(e,n,o)=>{"use strict";o.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},31498:(e,n,o)=>{"use strict";o.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},23485:(e,n,o)=>{"use strict";o.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,o(69224).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},66294:(e,n,o)=>{"use strict";o.r(n),o.d(n,{$$typeof:()=>a,__esModule:()=>t,default:()=>i});let r=(0,o(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\layout.tsx`),{__esModule:t,$$typeof:a}=r,i=r.default},37030:(e,n,o)=>{"use strict";o.r(n),o.d(n,{$$typeof:()=>a,__esModule:()=>t,default:()=>i});let r=(0,o(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\settings\platform\page.tsx`),{__esModule:t,$$typeof:a}=r,i=r.default},82917:(e,n,o)=>{"use strict";o.r(n),o.d(n,{default:()=>d,metadata:()=>c});var r=o(25036),t=o(450),a=o.n(t),i=o(14824),s=o.n(i);o(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${a().variable} ${s().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},67272:()=>{}};var n=require("../../../../webpack-runtime.js");n.C(e);var o=e=>n(n.s=e),r=n.X(0,[1638,3293,5504],()=>o(12403));module.exports=r})();