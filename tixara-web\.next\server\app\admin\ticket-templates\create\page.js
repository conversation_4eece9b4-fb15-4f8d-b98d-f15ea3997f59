(()=>{var t={};t.id=4886,t.ids=[4886],t.modules={47849:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external")},72934:t=>{"use strict";t.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external")},54580:t=>{"use strict";t.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:t=>{"use strict";t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39491:t=>{"use strict";t.exports=require("assert")},50852:t=>{"use strict";t.exports=require("async_hooks")},14300:t=>{"use strict";t.exports=require("buffer")},32081:t=>{"use strict";t.exports=require("child_process")},82361:t=>{"use strict";t.exports=require("events")},57147:t=>{"use strict";t.exports=require("fs")},73292:t=>{"use strict";t.exports=require("fs/promises")},22037:t=>{"use strict";t.exports=require("os")},71017:t=>{"use strict";t.exports=require("path")},12781:t=>{"use strict";t.exports=require("stream")},76224:t=>{"use strict";t.exports=require("tty")},73837:t=>{"use strict";t.exports=require("util")},59796:t=>{"use strict";t.exports=require("zlib")},19442:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>o.a,__next_app__:()=>d,originalPathname:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>h});var i=r(50482),n=r(69108),s=r(62563),o=r.n(s),a=r(68300),l={};for(let t in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(t)&&(l[t]=()=>a[t]);r.d(e,l);let h=["",{children:["admin",{children:["ticket-templates",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,71977)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],u=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\create\\page.tsx"],c="/admin/ticket-templates/create/page",d={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/admin/ticket-templates/create/page",pathname:"/admin/ticket-templates/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:h}})},85947:(t,e,r)=>{Promise.resolve().then(r.bind(r,37934))},37934:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>x});var i=r(95344),n=r(3729),s=r(47674),o=r(8428),a=r(16212),l=r(61351),h=r(92549),u=r(54572),c=r(93601),d=r(71809),f=r(42739),p=r(63024);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let g=(0,r(69224).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);var m=r(53148),_=r(31498),y=r(30692);r(8035);let b=`<temptix>
  <ticket-header>🎫 TiXara</ticket-header>
  <ticket-body>
    <event>{{eventName}}</event>
    <category>{{category}}</category>
    <buyer>{{buyerName}}</buyer>
    <qr-code src="{{qr}}" />
    <verified>{{isVerified}}</verified>
    <admin-fee>Biaya Admin: {{adminFee}}</admin-fee>
  </ticket-body>
</temptix>`;function x(){let t;let{data:e,status:r}=(0,s.useSession)(),x=(0,o.useRouter)(),{toast:w}=(0,y.pm)(),[E,T]=(0,n.useState)(!1),[v,C]=(0,n.useState)(!1),[k,A]=(0,n.useState)({name:"",description:"",templateCode:b,category:"general",isPremium:!1,requiredBadge:"",price:0,isActive:!0});if("loading"===r)return i.jsx("div",{className:"flex items-center justify-center min-h-screen",children:i.jsx(f.Z,{className:"h-8 w-8 animate-spin"})});if(!e?.user||"ADMIN"!==e.user.role)return x.push("/dashboard"),null;let I=async t=>{t.preventDefault(),T(!0);try{let t=await fetch("/api/ticket-templates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(k)}),e=await t.json();e.success?(w({title:"Berhasil",description:"Template berhasil dibuat"}),x.push("/admin/ticket-templates")):w({title:"Error",description:e.message||"Gagal membuat template",variant:"destructive"})}catch(t){w({title:"Error",description:"Terjadi kesalahan saat membuat template",variant:"destructive"})}finally{T(!1)}},P=(t,e)=>{A(r=>({...r,[t]:e}))};return(0,i.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,i.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,i.jsxs)(a.z,{variant:"outline",size:"sm",onClick:()=>x.back(),className:"flex items-center gap-2",children:[i.jsx(p.Z,{className:"h-4 w-4"}),"Kembali"]}),(0,i.jsxs)("div",{children:[i.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Buat Template Tiket"}),i.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Buat template tiket baru dengan format .temptix"})]})]}),(0,i.jsxs)("form",{onSubmit:I,className:"space-y-6",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[i.jsx("div",{className:"space-y-6",children:(0,i.jsxs)(l.Zb,{children:[(0,i.jsxs)(l.Ol,{children:[i.jsx(l.ll,{children:"Informasi Template"}),i.jsx(l.SZ,{children:"Atur informasi dasar template tiket"})]}),(0,i.jsxs)(l.aY,{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"name",children:"Nama Template *"}),i.jsx(h.I,{id:"name",value:k.name,onChange:t=>P("name",t.target.value),placeholder:"Masukkan nama template",required:!0})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"description",children:"Deskripsi"}),i.jsx(c.g,{id:"description",value:k.description,onChange:t=>P("description",t.target.value),placeholder:"Deskripsi template (opsional)",rows:3})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"category",children:"Kategori"}),(0,i.jsxs)("select",{id:"category",value:k.category,onChange:t=>P("category",t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[i.jsx("option",{value:"general",children:"General"}),i.jsx("option",{value:"concert",children:"Konser"}),i.jsx("option",{value:"conference",children:"Konferensi"}),i.jsx("option",{value:"workshop",children:"Workshop"}),i.jsx("option",{value:"sports",children:"Olahraga"}),i.jsx("option",{value:"exhibition",children:"Pameran"})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[i.jsx(d.r,{id:"isPremium",checked:k.isPremium,onCheckedChange:t=>P("isPremium",t)}),i.jsx(u._,{htmlFor:"isPremium",children:"Template Premium"})]}),k.isPremium&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"price",children:"Harga (Rp)"}),i.jsx(h.I,{id:"price",type:"number",value:k.price,onChange:t=>P("price",parseInt(t.target.value)||0),placeholder:"0",min:"0"})]}),(0,i.jsxs)("div",{children:[i.jsx(u._,{htmlFor:"requiredBadge",children:"Badge yang Diperlukan"}),(0,i.jsxs)("select",{id:"requiredBadge",value:k.requiredBadge,onChange:t=>P("requiredBadge",t.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[i.jsx("option",{value:"",children:"Tidak ada"}),i.jsx("option",{value:"BRONZE",children:"Bronze"}),i.jsx("option",{value:"SILVER",children:"Silver"}),i.jsx("option",{value:"GOLD",children:"Gold"}),i.jsx("option",{value:"TITANIUM",children:"Titanium"})]})]})]}),(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[i.jsx(d.r,{id:"isActive",checked:k.isActive,onCheckedChange:t=>P("isActive",t)}),i.jsx(u._,{htmlFor:"isActive",children:"Aktif"})]})]})]})}),(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)(l.Zb,{children:[i.jsx(l.Ol,{children:(0,i.jsxs)("div",{className:"flex items-center justify-between",children:[(0,i.jsxs)("div",{children:[i.jsx(l.ll,{children:"Kode Template"}),i.jsx(l.SZ,{children:"Tulis kode .temptix untuk template tiket"})]}),(0,i.jsxs)(a.z,{type:"button",variant:"outline",size:"sm",onClick:()=>C(!v),children:[v?i.jsx(g,{className:"h-4 w-4 mr-1"}):i.jsx(m.Z,{className:"h-4 w-4 mr-1"}),v?"Kode":"Preview"]})]})}),i.jsx(l.aY,{children:v?(t=k.templateCode,Object.entries({"{{eventName}}":"Sample Event Name","{{category}}":"Konser","{{buyerName}}":"John Doe","{{qr}}":"[QR CODE]","{{isVerified}}":"✓ Verified","{{adminFee}}":"Rp 5.000","{{ticketCode}}":"TIX-ABC123","{{eventDate}}":"25 Desember 2024","{{eventLocation}}":"Jakarta Convention Center","{{organizerName}}":"Event Organizer","{{price}}":"Rp 100.000"}).forEach(([e,r])=>{t=t.replace(RegExp(e,"g"),r)}),i.jsx("div",{className:"border rounded-lg p-4 bg-gray-50 dark:bg-gray-900",children:i.jsx("div",{dangerouslySetInnerHTML:{__html:t}})})):i.jsx(c.g,{value:k.templateCode,onChange:t=>P("templateCode",t.target.value),placeholder:"Masukkan kode .temptix",rows:20,className:"font-mono text-sm",required:!0})})]}),(0,i.jsxs)(l.Zb,{children:[i.jsx(l.Ol,{children:i.jsx(l.ll,{className:"text-sm",children:"Variabel yang Tersedia"})}),i.jsx(l.aY,{children:(0,i.jsxs)("div",{className:"text-xs space-y-1 text-gray-600",children:[(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{eventName}}"})," - Nama event"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{category}}"})," - Kategori event"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{buyerName}}"})," - Nama pembeli"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{qr}}"})," - QR code (wajib)"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{isVerified}}"})," - Status verifikasi"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{adminFee}}"})," - Biaya admin"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{ticketCode}}"})," - Kode tiket"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{eventDate}}"})," - Tanggal event"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{eventLocation}}"})," - Lokasi event"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{organizerName}}"})," - Nama organizer"]}),(0,i.jsxs)("div",{children:[i.jsx("code",{children:"{{price}}"})," - Harga tiket"]})]})})]})]})]}),(0,i.jsxs)("div",{className:"flex justify-end gap-4",children:[i.jsx(a.z,{type:"button",variant:"outline",onClick:()=>x.back(),children:"Batal"}),(0,i.jsxs)(a.z,{type:"submit",disabled:E,children:[E?i.jsx(f.Z,{className:"h-4 w-4 mr-2 animate-spin"}):i.jsx(_.Z,{className:"h-4 w-4 mr-2"}),"Simpan Template"]})]})]})]})}},54572:(t,e,r)=>{"use strict";r.d(e,{_:()=>u});var i=r(95344),n=r(3729),s=r(62409),o=n.forwardRef((t,e)=>(0,i.jsx)(s.WV.label,{...t,ref:e,onMouseDown:e=>{e.target.closest("button, input, select, textarea")||(t.onMouseDown?.(e),!e.defaultPrevented&&e.detail>1&&e.preventDefault())}}));o.displayName="Label";var a=r(92193),l=r(91626);let h=(0,a.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),u=n.forwardRef(({className:t,...e},r)=>i.jsx(o,{ref:r,className:(0,l.cn)(h(),t),...e}));u.displayName=o.displayName},71809:(t,e,r)=>{"use strict";r.d(e,{r:()=>T});var i=r(95344),n=r(3729),s=r(85222),o=r(31405),a=r(98462),l=r(33183),h=r(92062),u=r(63085),c=r(62409),d="Switch",[f,p]=(0,a.b)(d),[g,m]=f(d),_=n.forwardRef((t,e)=>{let{__scopeSwitch:r,name:a,checked:h,defaultChecked:u,required:f,disabled:p,value:m="on",onCheckedChange:_,form:y,...b}=t,[E,T]=n.useState(null),v=(0,o.e)(e,t=>T(t)),C=n.useRef(!1),k=!E||y||!!E.closest("form"),[A,I]=(0,l.T)({prop:h,defaultProp:u??!1,onChange:_,caller:d});return(0,i.jsxs)(g,{scope:r,checked:A,disabled:p,children:[(0,i.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":A,"aria-required":f,"data-state":w(A),"data-disabled":p?"":void 0,disabled:p,value:m,...b,ref:v,onClick:(0,s.M)(t.onClick,t=>{I(t=>!t),k&&(C.current=t.isPropagationStopped(),C.current||t.stopPropagation())})}),k&&(0,i.jsx)(x,{control:E,bubbles:!C.current,name:a,value:m,checked:A,required:f,disabled:p,form:y,style:{transform:"translateX(-100%)"}})]})});_.displayName=d;var y="SwitchThumb",b=n.forwardRef((t,e)=>{let{__scopeSwitch:r,...n}=t,s=m(y,r);return(0,i.jsx)(c.WV.span,{"data-state":w(s.checked),"data-disabled":s.disabled?"":void 0,...n,ref:e})});b.displayName=y;var x=n.forwardRef(({__scopeSwitch:t,control:e,checked:r,bubbles:s=!0,...a},l)=>{let c=n.useRef(null),d=(0,o.e)(c,l),f=(0,h.D)(r),p=(0,u.t)(e);return n.useEffect(()=>{let t=c.current;if(!t)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==r&&e){let i=new Event("click",{bubbles:s});e.call(t,r),t.dispatchEvent(i)}},[f,r,s]),(0,i.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...a,tabIndex:-1,ref:d,style:{...a.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function w(t){return t?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var E=r(91626);let T=n.forwardRef(({className:t,...e},r)=>i.jsx(_,{className:(0,E.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...e,ref:r,children:i.jsx(b,{className:(0,E.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));T.displayName=_.displayName},93601:(t,e,r)=>{"use strict";r.d(e,{g:()=>o});var i=r(95344),n=r(3729),s=r(91626);let o=n.forwardRef(({className:t,...e},r)=>i.jsx("textarea",{className:(0,s.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...e}));o.displayName="Textarea"},17354:t=>{"use strict";var e={single_source_shortest_paths:function(t,r,i){var n,s,o,a,l,h,u,c={},d={};d[r]=0;var f=e.PriorityQueue.make();for(f.push(r,0);!f.empty();)for(o in s=(n=f.pop()).value,a=n.cost,l=t[s]||{})l.hasOwnProperty(o)&&(h=a+l[o],u=d[o],(void 0===d[o]||u>h)&&(d[o]=h,f.push(o,h),c[o]=s));if(void 0!==i&&void 0===d[i])throw Error(["Could not find a path from ",r," to ",i,"."].join(""));return c},extract_shortest_path_from_predecessor_list:function(t,e){for(var r=[],i=e;i;)r.push(i),t[i],i=t[i];return r.reverse(),r},find_path:function(t,r,i){var n=e.single_source_shortest_paths(t,r,i);return e.extract_shortest_path_from_predecessor_list(n,i)},PriorityQueue:{make:function(t){var r,i=e.PriorityQueue,n={};for(r in t=t||{},i)i.hasOwnProperty(r)&&(n[r]=i[r]);return n.queue=[],n.sorter=t.sorter||i.default_sorter,n},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){this.queue.push({value:t,cost:e}),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=e},63024:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},50340:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},25390:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},85674:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},53148:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2273:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},70009:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},31498:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},73852:(t,e,r)=>{"use strict";let i=r(32899),n=[function(){},function(t,e,r,i){if(i===e.length)throw Error("Ran out of data");let n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=255},function(t,e,r,i){if(i+1>=e.length)throw Error("Ran out of data");let n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=e[i+1]},function(t,e,r,i){if(i+2>=e.length)throw Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=255},function(t,e,r,i){if(i+3>=e.length)throw Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=e[i+3]}],s=[function(){},function(t,e,r,i){let n=e[0];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=i},function(t,e,r){let i=e[0];t[r]=i,t[r+1]=i,t[r+2]=i,t[r+3]=e[1]},function(t,e,r,i){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=i},function(t,e,r){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=e[3]}];e.dataToBitMap=function(t,e){let r,o,a,l,h=e.width,u=e.height,c=e.depth,d=e.bpp,f=e.interlace;if(8!==c){let e,i;e=[],i=0,r={get:function(r){for(;e.length<r;)!function(){let r,n,s,o;if(i===t.length)throw Error("Ran out of data");let a=t[i];switch(i++,c){default:throw Error("unrecognised depth");case 16:s=t[i],i++,e.push((a<<8)+s);break;case 4:s=15&a,o=a>>4,e.push(o,s);break;case 2:r=3&a,n=a>>2&3,s=a>>4&3,o=a>>6&3,e.push(o,s,n,r);break;case 1:r=a>>4&1,n=a>>5&1,s=a>>6&1,o=a>>7&1,e.push(o,s,n,r,a>>3&1,a>>2&1,a>>1&1,1&a)}}();let n=e.slice(0,r);return e=e.slice(r),n},resetAfterLine:function(){e.length=0},end:function(){if(i!==t.length)throw Error("extra data found")}}}o=c<=8?Buffer.alloc(h*u*4):new Uint16Array(h*u*4);let p=Math.pow(2,c)-1,g=0;if(f)a=i.getImagePasses(h,u),l=i.getInterlaceIterator(h,u);else{let t=0;l=function(){let e=t;return t+=4,e},a=[{width:h,height:u}]}for(let e=0;e<a.length;e++)8===c?g=function(t,e,r,i,s,o){let a=t.width,l=t.height,h=t.index;for(let t=0;t<l;t++)for(let l=0;l<a;l++){let a=r(l,t,h);n[i](e,s,a,o),o+=i}return o}(a[e],o,l,d,t,g):function(t,e,r,i,n,o){let a=t.width,l=t.height,h=t.index;for(let t=0;t<l;t++){for(let l=0;l<a;l++){let a=n.get(i),u=r(l,t,h);s[i](e,a,u,o)}n.resetAfterLine()}}(a[e],o,l,d,r,p);if(8===c){if(g!==t.length)throw Error("extra data found")}else r.end();return o}},24646:(t,e,r)=>{"use strict";let i=r(74014);t.exports=function(t,e,r,n){let s=-1!==[i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(n.colorType);if(n.colorType===n.inputColorType){let e;let r=(e=new ArrayBuffer(2),new DataView(e).setInt16(0,256,!0),256!==new Int16Array(e)[0]);if(8===n.bitDepth||16===n.bitDepth&&r)return t}let o=16!==n.bitDepth?t:new Uint16Array(t.buffer),a=255,l=i.COLORTYPE_TO_BPP_MAP[n.inputColorType];4!==l||n.inputHasAlpha||(l=3);let h=i.COLORTYPE_TO_BPP_MAP[n.colorType];16===n.bitDepth&&(a=65535,h*=2);let u=Buffer.alloc(e*r*h),c=0,d=0,f=n.bgColor||{};void 0===f.red&&(f.red=a),void 0===f.green&&(f.green=a),void 0===f.blue&&(f.blue=a);for(let t=0;t<r;t++)for(let t=0;t<e;t++){let t=function(){let t,e,r;let l=a;switch(n.inputColorType){case i.COLORTYPE_COLOR_ALPHA:l=o[c+3],t=o[c],e=o[c+1],r=o[c+2];break;case i.COLORTYPE_COLOR:t=o[c],e=o[c+1],r=o[c+2];break;case i.COLORTYPE_ALPHA:l=o[c+1],e=t=o[c],r=t;break;case i.COLORTYPE_GRAYSCALE:e=t=o[c],r=t;break;default:throw Error("input color type:"+n.inputColorType+" is not supported at present")}return n.inputHasAlpha&&!s&&(l/=a,t=Math.min(Math.max(Math.round((1-l)*f.red+l*t),0),a),e=Math.min(Math.max(Math.round((1-l)*f.green+l*e),0),a),r=Math.min(Math.max(Math.round((1-l)*f.blue+l*r),0),a)),{red:t,green:e,blue:r,alpha:l}}(o,c);switch(n.colorType){case i.COLORTYPE_COLOR_ALPHA:case i.COLORTYPE_COLOR:8===n.bitDepth?(u[d]=t.red,u[d+1]=t.green,u[d+2]=t.blue,s&&(u[d+3]=t.alpha)):(u.writeUInt16BE(t.red,d),u.writeUInt16BE(t.green,d+2),u.writeUInt16BE(t.blue,d+4),s&&u.writeUInt16BE(t.alpha,d+6));break;case i.COLORTYPE_ALPHA:case i.COLORTYPE_GRAYSCALE:{let e=(t.red+t.green+t.blue)/3;8===n.bitDepth?(u[d]=e,s&&(u[d+1]=t.alpha)):(u.writeUInt16BE(e,d),s&&u.writeUInt16BE(t.alpha,d+2));break}default:throw Error("unrecognised color Type "+n.colorType)}c+=l,d+=h}return u}},96150:(t,e,r)=>{"use strict";let i=r(73837),n=r(12781),s=t.exports=function(){n.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};i.inherits(s,n),s.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick((function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}).bind(this))},s.prototype.write=function(t,e){let r;return this.writable?(r=Buffer.isBuffer(t)?t:Buffer.from(t,e||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",Error("Stream not writable")),!1)},s.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},s.prototype.destroySoon=s.prototype.end,s.prototype._end=function(){this._reads.length>0&&this.emit("error",Error("Unexpected end of input")),this.destroy()},s.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},s.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))},s.prototype._processRead=function(t){this._reads.shift();let e=0,r=0,i=Buffer.alloc(t.length);for(;e<t.length;){let n=this._buffers[r++],s=Math.min(n.length,t.length-e);n.copy(i,e,0,s),e+=s,s!==n.length&&(this._buffers[--r]=n.slice(s))}r>0&&this._buffers.splice(0,r),this._buffered-=t.length,t.func.call(this,i)},s.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else if(this._buffered>=t.length)this._processRead(t);else break}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}},74014:t=>{"use strict";t.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},28750:t=>{"use strict";let e=[];!function(){for(let t=0;t<256;t++){let r=t;for(let t=0;t<8;t++)1&r?r=3988292384^r>>>1:r>>>=1;e[t]=r}}();let r=t.exports=function(){this._crc=-1};r.prototype.write=function(t){for(let r=0;r<t.length;r++)this._crc=e[(this._crc^t[r])&255]^this._crc>>>8;return!0},r.prototype.crc32=function(){return -1^this._crc},r.crc32=function(t){let r=-1;for(let i=0;i<t.length;i++)r=e[(r^t[i])&255]^r>>>8;return -1^r}},94194:(t,e,r)=>{"use strict";let i=r(67215),n={0:function(t,e,r,i,n){for(let s=0;s<r;s++)i[n+s]=t[e+s]},1:function(t,e,r,i,n,s){for(let o=0;o<r;o++){let r=o>=s?t[e+o-s]:0,a=t[e+o]-r;i[n+o]=a}},2:function(t,e,r,i,n){for(let s=0;s<r;s++){let o=e>0?t[e+s-r]:0,a=t[e+s]-o;i[n+s]=a}},3:function(t,e,r,i,n,s){for(let o=0;o<r;o++){let a=o>=s?t[e+o-s]:0,l=e>0?t[e+o-r]:0,h=t[e+o]-(a+l>>1);i[n+o]=h}},4:function(t,e,r,n,s,o){for(let a=0;a<r;a++){let l=a>=o?t[e+a-o]:0,h=e>0?t[e+a-r]:0,u=e>0&&a>=o?t[e+a-(r+o)]:0,c=t[e+a]-i(l,h,u);n[s+a]=c}}},s={0:function(t,e,r){let i=0,n=e+r;for(let r=e;r<n;r++)i+=Math.abs(t[r]);return i},1:function(t,e,r,i){let n=0;for(let s=0;s<r;s++){let r=s>=i?t[e+s-i]:0;n+=Math.abs(t[e+s]-r)}return n},2:function(t,e,r){let i=0,n=e+r;for(let s=e;s<n;s++){let n=e>0?t[s-r]:0;i+=Math.abs(t[s]-n)}return i},3:function(t,e,r,i){let n=0;for(let s=0;s<r;s++){let o=s>=i?t[e+s-i]:0,a=e>0?t[e+s-r]:0;n+=Math.abs(t[e+s]-(o+a>>1))}return n},4:function(t,e,r,n){let s=0;for(let o=0;o<r;o++){let a=o>=n?t[e+o-n]:0,l=e>0?t[e+o-r]:0,h=e>0&&o>=n?t[e+o-(r+n)]:0;s+=Math.abs(t[e+o]-i(a,l,h))}return s}};t.exports=function(t,e,r,i,o){let a;if("filterType"in i&&-1!==i.filterType){if("number"==typeof i.filterType)a=[i.filterType];else throw Error("unrecognised filter types")}else a=[0,1,2,3,4];16===i.bitDepth&&(o*=2);let l=e*o,h=0,u=0,c=Buffer.alloc((l+1)*r),d=a[0];for(let e=0;e<r;e++){if(a.length>1){let e=1/0;for(let r=0;r<a.length;r++){let i=s[a[r]](t,u,l,o);i<e&&(d=a[r],e=i)}}c[h]=d,h++,n[d](t,u,l,c,h,o),h+=l,u+=l}return c}},14375:(t,e,r)=>{"use strict";let i=r(73837),n=r(96150),s=r(9284),o=t.exports=function(t){n.call(this);let e=[],r=this;this._filter=new s(t,{read:this.read.bind(this),write:function(t){e.push(t)},complete:function(){r.emit("complete",Buffer.concat(e))}}),this._filter.start()};i.inherits(o,n)},8672:(t,e,r)=>{"use strict";let i=r(40643),n=r(9284);e.process=function(t,e){let r=[],s=new i(t);return new n(e,{read:s.read.bind(s),write:function(t){r.push(t)},complete:function(){}}).start(),s.process(),Buffer.concat(r)}},9284:(t,e,r)=>{"use strict";let i=r(32899),n=r(67215);function s(t,e,r){let i=t*e;return 8!==r&&(i=Math.ceil(i/(8/r))),i}let o=t.exports=function(t,e){let r=t.width,n=t.height,o=t.interlace,a=t.bpp,l=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],o){let t=i.getImagePasses(r,n);for(let e=0;e<t.length;e++)this._images.push({byteWidth:s(t[e].width,a,l),height:t[e].height,lineIndex:0})}else this._images.push({byteWidth:s(r,a,l),height:n,lineIndex:0});8===l?this._xComparison=a:16===l?this._xComparison=2*a:this._xComparison=1};o.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},o.prototype._unFilterType1=function(t,e,r){let i=this._xComparison,n=i-1;for(let s=0;s<r;s++){let r=t[1+s],o=s>n?e[s-i]:0;e[s]=r+o}},o.prototype._unFilterType2=function(t,e,r){let i=this._lastLine;for(let n=0;n<r;n++){let r=t[1+n],s=i?i[n]:0;e[n]=r+s}},o.prototype._unFilterType3=function(t,e,r){let i=this._xComparison,n=i-1,s=this._lastLine;for(let o=0;o<r;o++){let r=t[1+o],a=s?s[o]:0,l=Math.floor(((o>n?e[o-i]:0)+a)/2);e[o]=r+l}},o.prototype._unFilterType4=function(t,e,r){let i=this._xComparison,s=i-1,o=this._lastLine;for(let a=0;a<r;a++){let r=t[1+a],l=o?o[a]:0,h=n(a>s?e[a-i]:0,l,a>s&&o?o[a-i]:0);e[a]=r+h}},o.prototype._reverseFilterLine=function(t){let e,r=t[0],i=this._images[this._imageIndex],n=i.byteWidth;if(0===r)e=t.slice(1,n+1);else switch(e=Buffer.alloc(n),r){case 1:this._unFilterType1(t,e,n);break;case 2:this._unFilterType2(t,e,n);break;case 3:this._unFilterType3(t,e,n);break;case 4:this._unFilterType4(t,e,n);break;default:throw Error("Unrecognised filter type - "+r)}this.write(e),i.lineIndex++,i.lineIndex>=i.height?(this._lastLine=null,this._imageIndex++,i=this._images[this._imageIndex]):this._lastLine=e,i?this.read(i.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},43529:t=>{"use strict";t.exports=function(t,e){let r=e.depth,i=e.width,n=e.height,s=e.colorType,o=e.transColor,a=e.palette,l=t;return 3===s?function(t,e,r,i,n){let s=0;for(let o=0;o<i;o++)for(let i=0;i<r;i++){let r=n[t[s]];if(!r)throw Error("index "+t[s]+" not in palette");for(let t=0;t<4;t++)e[s+t]=r[t];s+=4}}(t,l,i,n,a):(o&&function(t,e,r,i,n){let s=0;for(let o=0;o<i;o++)for(let i=0;i<r;i++){let r=!1;if(1===n.length?n[0]===t[s]&&(r=!0):n[0]===t[s]&&n[1]===t[s+1]&&n[2]===t[s+2]&&(r=!0),r)for(let t=0;t<4;t++)e[s+t]=0;s+=4}}(t,l,i,n,o),8!==r&&(16===r&&(l=Buffer.alloc(i*n*4)),function(t,e,r,i,n){let s=Math.pow(2,n)-1,o=0;for(let n=0;n<i;n++)for(let i=0;i<r;i++){for(let r=0;r<4;r++)e[o+r]=Math.floor(255*t[o+r]/s+.5);o+=4}}(t,l,i,n,r))),l}},32899:(t,e)=>{"use strict";let r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];e.getImagePasses=function(t,e){let i=[],n=t%8,s=e%8,o=(t-n)/8,a=(e-s)/8;for(let t=0;t<r.length;t++){let e=r[t],l=o*e.x.length,h=a*e.y.length;for(let t=0;t<e.x.length;t++)if(e.x[t]<n)l++;else break;for(let t=0;t<e.y.length;t++)if(e.y[t]<s)h++;else break;l>0&&h>0&&i.push({width:l,height:h,index:t})}return i},e.getInterlaceIterator=function(t){return function(e,i,n){let s=e%r[n].x.length,o=(e-s)/r[n].x.length*8+r[n].x[s],a=i%r[n].y.length;return 4*o+((i-a)/r[n].y.length*8+r[n].y[a])*t*4}}},91867:(t,e,r)=>{"use strict";let i=r(73837),n=r(12781),s=r(74014),o=r(59826),a=t.exports=function(t){n.call(this),this._packer=new o(t||{}),this._deflate=this._packer.createDeflate(),this.readable=!0};i.inherits(a,n),a.prototype.pack=function(t,e,r,i){this.emit("data",Buffer.from(s.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,r)),i&&this.emit("data",this._packer.packGAMA(i));let n=this._packer.filterData(t,e,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",(function(t){this.emit("data",this._packer.packIDAT(t))}).bind(this)),this._deflate.on("end",(function(){this.emit("data",this._packer.packIEND()),this.emit("end")}).bind(this)),this._deflate.end(n)}},88975:(t,e,r)=>{"use strict";let i=!0,n=r(59796);n.deflateSync||(i=!1);let s=r(74014),o=r(59826);t.exports=function(t,e){if(!i)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let r=new o(e||{}),a=[];a.push(Buffer.from(s.PNG_SIGNATURE)),a.push(r.packIHDR(t.width,t.height)),t.gamma&&a.push(r.packGAMA(t.gamma));let l=r.filterData(t.data,t.width,t.height),h=n.deflateSync(l,r.getDeflateOptions());if(l=null,!h||!h.length)throw Error("bad png - invalid compressed data response");return a.push(r.packIDAT(h)),a.push(r.packIEND()),Buffer.concat(a)}},59826:(t,e,r)=>{"use strict";let i=r(74014),n=r(28750),s=r(24646),o=r(94194),a=r(59796),l=t.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32768,t.deflateLevel=null!=t.deflateLevel?t.deflateLevel:9,t.deflateStrategy=null!=t.deflateStrategy?t.deflateStrategy:3,t.inputHasAlpha=null==t.inputHasAlpha||t.inputHasAlpha,t.deflateFactory=t.deflateFactory||a.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType="number"==typeof t.colorType?t.colorType:i.COLORTYPE_COLOR_ALPHA,t.inputColorType="number"==typeof t.inputColorType?t.inputColorType:i.COLORTYPE_COLOR_ALPHA,-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(t.colorType))throw Error("option color type:"+t.colorType+" is not supported at present");if(-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(t.inputColorType))throw Error("option input color type:"+t.inputColorType+" is not supported at present");if(8!==t.bitDepth&&16!==t.bitDepth)throw Error("option bit depth:"+t.bitDepth+" is not supported at present")};l.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},l.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},l.prototype.filterData=function(t,e,r){let n=s(t,e,r,this._options),a=i.COLORTYPE_TO_BPP_MAP[this._options.colorType];return o(n,e,r,this._options,a)},l.prototype._packChunk=function(t,e){let r=e?e.length:0,i=Buffer.alloc(r+12);return i.writeUInt32BE(r,0),i.writeUInt32BE(t,4),e&&e.copy(i,8),i.writeInt32BE(n.crc32(i.slice(4,i.length-4)),i.length-4),i},l.prototype.packGAMA=function(t){let e=Buffer.alloc(4);return e.writeUInt32BE(Math.floor(t*i.GAMMA_DIVISION),0),this._packChunk(i.TYPE_gAMA,e)},l.prototype.packIHDR=function(t,e){let r=Buffer.alloc(13);return r.writeUInt32BE(t,0),r.writeUInt32BE(e,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(i.TYPE_IHDR,r)},l.prototype.packIDAT=function(t){return this._packChunk(i.TYPE_IDAT,t)},l.prototype.packIEND=function(){return this._packChunk(i.TYPE_IEND,null)}},67215:t=>{"use strict";t.exports=function(t,e,r){let i=t+e-r,n=Math.abs(i-t),s=Math.abs(i-e),o=Math.abs(i-r);return n<=s&&n<=o?t:s<=o?e:r}},15487:(t,e,r)=>{"use strict";let i=r(73837),n=r(59796),s=r(96150),o=r(14375),a=r(68836),l=r(73852),h=r(43529),u=t.exports=function(t){s.call(this),this._parser=new a(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};i.inherits(u,s),u.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",function(){})),this.errord=!0},u.prototype._inflateData=function(t){if(!this._inflate){if(this._bitmapInfo.interlace)this._inflate=n.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let t=((this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3)+1)*this._bitmapInfo.height,e=Math.max(t,n.Z_MIN_CHUNK);this._inflate=n.createInflate({chunkSize:e});let r=t,i=this.emit.bind(this,"error");this._inflate.on("error",function(t){r&&i(t)}),this._filter.on("complete",this._complete.bind(this));let s=this._filter.write.bind(this._filter);this._inflate.on("data",function(t){r&&(t.length>r&&(t=t.slice(0,r)),r-=t.length,s(t))}),this._inflate.on("end",this._filter.end.bind(this._filter))}}this._inflate.write(t)},u.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new o(this._bitmapInfo)},u.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t},u.prototype._handlePalette=function(t){this._bitmapInfo.palette=t},u.prototype._simpleTransparency=function(){this._metaData.alpha=!0},u.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},u.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},u.prototype._complete=function(t){let e;if(!this.errord){try{let r=l.dataToBitMap(t,this._bitmapInfo);e=h(r,this._bitmapInfo),r=null}catch(t){this._handleError(t);return}this.emit("parsed",e)}}},65100:(t,e,r)=>{"use strict";let i=!0,n=r(59796),s=r(56934);n.deflateSync||(i=!1);let o=r(40643),a=r(8672),l=r(68836),h=r(73852),u=r(43529);t.exports=function(t,e){let r,c,d,f;if(!i)throw Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let p=[],g=new o(t);if(new l(e,{read:g.read.bind(g),error:function(t){r=t},metadata:function(t){c=t},gamma:function(t){d=t},palette:function(t){c.palette=t},transColor:function(t){c.transColor=t},inflateData:function(t){p.push(t)},simpleTransparency:function(){c.alpha=!0}}).start(),g.process(),r)throw r;let m=Buffer.concat(p);if(p.length=0,c.interlace)f=n.inflateSync(m);else{let t=((c.width*c.bpp*c.depth+7>>3)+1)*c.height;f=s(m,{chunkSize:t,maxLength:t})}if(m=null,!f||!f.length)throw Error("bad png - invalid inflate data response");let _=a.process(f,c);m=null;let y=h.dataToBitMap(_,c);_=null;let b=u(y,c);return c.data=b,c.gamma=d||0,c}},68836:(t,e,r)=>{"use strict";let i=r(74014),n=r(28750),s=t.exports=function(t,e){this._options=t,t.checkCRC=!1!==t.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[i.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[i.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[i.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[i.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[i.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[i.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};s.prototype.start=function(){this.read(i.PNG_SIGNATURE.length,this._parseSignature.bind(this))},s.prototype._parseSignature=function(t){let e=i.PNG_SIGNATURE;for(let r=0;r<e.length;r++)if(t[r]!==e[r]){this.error(Error("Invalid file signature"));return}this.read(8,this._parseChunkBegin.bind(this))},s.prototype._parseChunkBegin=function(t){let e=t.readUInt32BE(0),r=t.readUInt32BE(4),s="";for(let e=4;e<8;e++)s+=String.fromCharCode(t[e]);let o=!!(32&t[4]);if(!this._hasIHDR&&r!==i.TYPE_IHDR){this.error(Error("Expected IHDR on beggining"));return}if(this._crc=new n,this._crc.write(Buffer.from(s)),this._chunks[r])return this._chunks[r](e);if(!o){this.error(Error("Unsupported critical chunk type "+s));return}this.read(e+4,this._skipChunk.bind(this))},s.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},s.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},s.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),r=this._crc.crc32();if(this._options.checkCRC&&r!==e){this.error(Error("Crc error - "+e+" - "+r));return}this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},s.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))},s.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),r=t.readUInt32BE(4),n=t[8],s=t[9],o=t[10],a=t[11],l=t[12];if(8!==n&&4!==n&&2!==n&&1!==n&&16!==n){this.error(Error("Unsupported bit depth "+n));return}if(!(s in i.COLORTYPE_TO_BPP_MAP)){this.error(Error("Unsupported color type"));return}if(0!==o){this.error(Error("Unsupported compression method"));return}if(0!==a){this.error(Error("Unsupported filter method"));return}if(0!==l&&1!==l){this.error(Error("Unsupported interlace method"));return}this._colorType=s;let h=i.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:r,depth:n,interlace:!!l,palette:!!(s&i.COLORTYPE_PALETTE),color:!!(s&i.COLORTYPE_COLOR),alpha:!!(s&i.COLORTYPE_ALPHA),bpp:h,colorType:s}),this._handleChunkEnd()},s.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))},s.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let r=0;r<e;r++)this._palette.push([t[3*r],t[3*r+1],t[3*r+2],255]);this.palette(this._palette),this._handleChunkEnd()},s.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))},s.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===i.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length){this.error(Error("Transparency chunk must be after palette"));return}if(t.length>this._palette.length){this.error(Error("More transparent colors than palette size"));return}for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===i.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===i.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()},s.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))},s.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/i.GAMMA_DIVISION),this._handleChunkEnd()},s.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))},s.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===i.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw Error("Expected palette not found");this.inflateData(e);let r=t-e.length;r>0?this._handleIDAT(r):this._handleChunkEnd()},s.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))},s.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}},39278:(t,e,r)=>{"use strict";let i=r(65100),n=r(88975);e.read=function(t,e){return i(t,e||{})},e.write=function(t,e){return n(t,e)}},83269:(t,e,r)=>{"use strict";let i=r(73837),n=r(12781),s=r(15487),o=r(91867),a=r(39278),l=e.y=function(t){n.call(this),t=t||{},this.width=0|t.width,this.height=0|t.height,this.data=this.width>0&&this.height>0?Buffer.alloc(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new s(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",(function(t){this.data=t,this.emit("parsed",t)}).bind(this)),this._packer=new o(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};i.inherits(l,n),l.sync=a,l.prototype.pack=function(){return this.data&&this.data.length?process.nextTick((function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}).bind(this)):this.emit("error","No data provided"),this},l.prototype.parse=function(t,e){if(e){let t,r;t=(function(t){this.removeListener("error",r),this.data=t,e(null,this)}).bind(this),r=(function(r){this.removeListener("parsed",t),e(r,null)}).bind(this),this.once("parsed",t),this.once("error",r)}return this.end(t),this},l.prototype.write=function(t){return this._parser.write(t),!0},l.prototype.end=function(t){this._parser.end(t)},l.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)},l.prototype._gamma=function(t){this.gamma=t},l.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},l.bitblt=function(t,e,r,i,n,s,o,a){if(i|=0,n|=0,s|=0,o|=0,a|=0,(r|=0)>t.width||i>t.height||r+n>t.width||i+s>t.height)throw Error("bitblt reading outside image");if(o>e.width||a>e.height||o+n>e.width||a+s>e.height)throw Error("bitblt writing outside image");for(let l=0;l<s;l++)t.data.copy(e.data,(a+l)*e.width+o<<2,(i+l)*t.width+r<<2,(i+l)*t.width+r+n<<2)},l.prototype.bitblt=function(t,e,r,i,n,s,o){return l.bitblt(this,t,e,r,i,n,s,o),this},l.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let r=0;r<t.width;r++){let i=t.width*e+r<<2;for(let e=0;e<3;e++){let r=t.data[i+e]/255;r=Math.pow(r,1/2.2/t.gamma),t.data[i+e]=Math.round(255*r)}}t.gamma=0}},l.prototype.adjustGamma=function(){l.adjustGamma(this)}},56934:(t,e,r)=>{"use strict";let i=r(39491).ok,n=r(59796),s=r(73837),o=r(14300).kMaxLength;function a(t){if(!(this instanceof a))return new a(t);t&&t.chunkSize<n.Z_MIN_CHUNK&&(t.chunkSize=n.Z_MIN_CHUNK),n.Inflate.call(this,t),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&null!=t.maxLength&&(this._maxLength=t.maxLength)}function l(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function h(t,e){return function(t,e){if("string"==typeof e&&(e=Buffer.from(e)),!(e instanceof Buffer))throw TypeError("Not a string or buffer");let r=t._finishFlushFlag;return null==r&&(r=n.Z_FINISH),t._processChunk(e,r)}(new a(e),t)}a.prototype._processChunk=function(t,e,r){let s,a;if("function"==typeof r)return n.Inflate._processChunk.call(this,t,e,r);let h=this,u=t&&t.length,c=this._chunkSize-this._offset,d=this._maxLength,f=0,p=[],g=0;this.on("error",function(t){s=t}),i(this._handle,"zlib binding closed");do a=(a=this._handle.writeSync(e,t,f,u,this._buffer,this._offset,c))||this._writeState;while(!this._hadError&&function(t,e){if(h._hadError)return;let r=c-e;if(i(r>=0,"have should not go down"),r>0){let t=h._buffer.slice(h._offset,h._offset+r);if(h._offset+=r,t.length>d&&(t=t.slice(0,d)),p.push(t),g+=t.length,0==(d-=t.length))return!1}return(0===e||h._offset>=h._chunkSize)&&(c=h._chunkSize,h._offset=0,h._buffer=Buffer.allocUnsafe(h._chunkSize)),0===e&&(f+=u-t,u=t,!0)}(a[0],a[1]));if(this._hadError)throw s;if(g>=o)throw l(this),RangeError("Cannot create final Buffer. It would be larger than 0x"+o.toString(16)+" bytes");let m=Buffer.concat(p,g);return l(this),m},s.inherits(a,n.Inflate),t.exports=e=h,e.Inflate=a,e.createInflate=function(t){return new a(t)},e.inflateSync=h},40643:t=>{"use strict";let e=t.exports=function(t){this._buffer=t,this._reads=[]};e.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})},e.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){let t=this._reads[0];if(this._buffer.length&&(this._buffer.length>=t.length||t.allowLess)){this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}else break}return this._reads.length>0?Error("There are some read requests waitng on finished stream"):this._buffer.length>0?Error("unrecognised content at end of stream"):void 0}},5131:(t,e,r)=>{let i=r(22665),n=r(70394),s=r(2438),o=r(40935);function a(t,e,r,s,o){let a=[].slice.call(arguments,1),l=a.length,h="function"==typeof a[l-1];if(!h&&!i())throw Error("Callback required as last argument");if(h){if(l<2)throw Error("Too few arguments provided");2===l?(o=r,r=e,e=s=void 0):3===l&&(e.getContext&&void 0===o?(o=s,s=void 0):(o=s,s=r,r=e,e=void 0))}else{if(l<1)throw Error("Too few arguments provided");return 1===l?(r=e,e=s=void 0):2!==l||e.getContext||(s=r,r=e,e=void 0),new Promise(function(i,o){try{let o=n.create(r,s);i(t(o,e,s))}catch(t){o(t)}})}try{let i=n.create(r,s);o(null,t(i,e,s))}catch(t){o(t)}}n.create,a.bind(null,s.render),a.bind(null,s.renderToDataURL),a.bind(null,function(t,e,r){return o.render(t,r)})},22665:t=>{"use strict";t.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},62332:(t,e,r)=>{let i=r(36096).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];let e=Math.floor(t/7)+2,r=i(t),n=145===r?26:2*Math.ceil((r-13)/(2*e-2)),s=[r-7];for(let t=1;t<e-1;t++)s[t]=s[t-1]-n;return s.push(6),s.reverse()},e.getPositions=function(t){let r=[],i=e.getRowColCoords(t),n=i.length;for(let t=0;t<n;t++)for(let e=0;e<n;e++)(0!==t||0!==e)&&(0!==t||e!==n-1)&&(t!==n-1||0!==e)&&r.push([i[t],i[e]]);return r}},84152:(t,e,r)=>{"use strict";let i=r(73758),n=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function s(t){this.mode=i.ALPHANUMERIC,this.data=t}s.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let r=45*n.indexOf(this.data[e]);r+=n.indexOf(this.data[e+1]),t.put(r,11)}this.data.length%2&&t.put(n.indexOf(this.data[e]),6)},t.exports=s},33060:t=>{"use strict";function e(){this.buffer=[],this.length=0}e.prototype={get:function(t){return(this.buffer[Math.floor(t/8)]>>>7-t%8&1)==1},put:function(t,e){for(let r=0;r<e;r++)this.putBit((t>>>e-r-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(t){let e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=e},19934:t=>{"use strict";function e(t){if(!t||t<1)throw Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}e.prototype.set=function(t,e,r,i){let n=t*this.size+e;this.data[n]=r,i&&(this.reservedBit[n]=!0)},e.prototype.get=function(t,e){return this.data[t*this.size+e]},e.prototype.xor=function(t,e,r){this.data[t*this.size+e]^=r},e.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=e},27327:(t,e,r)=>{"use strict";let i=r(73758);function n(t){this.mode=i.BYTE,"string"==typeof t?this.data=new TextEncoder().encode(t):this.data=new Uint8Array(t)}n.getBitsLength=function(t){return 8*t},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(t){for(let e=0,r=this.data.length;e<r;e++)t.put(this.data[e],8)},t.exports=n},59797:(t,e,r)=>{let i=r(13862),n=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],s=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case i.L:return n[(t-1)*4+0];case i.M:return n[(t-1)*4+1];case i.Q:return n[(t-1)*4+2];case i.H:return n[(t-1)*4+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case i.L:return s[(t-1)*4+0];case i.M:return s[(t-1)*4+1];case i.Q:return s[(t-1)*4+2];case i.H:return s[(t-1)*4+3];default:return}}},13862:(t,e)=>{e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw Error("Unknown EC Level: "+t)}}(t)}catch(t){return r}}},42708:(t,e,r)=>{let i=r(36096).getSymbolSize;e.getPositions=function(t){let e=i(t);return[[0,0],[e-7,0],[0,e-7]]}},84248:(t,e,r)=>{let i=r(36096),n=i.getBCHDigit(1335);e.getEncodedBits=function(t,e){let r=t.bit<<3|e,s=r<<10;for(;i.getBCHDigit(s)-n>=0;)s^=1335<<i.getBCHDigit(s)-n;return(r<<10|s)^21522}},52143:(t,e)=>{let r=new Uint8Array(512),i=new Uint8Array(256);(function(){let t=1;for(let e=0;e<255;e++)r[e]=t,i[t]=e,256&(t<<=1)&&(t^=285);for(let t=255;t<512;t++)r[t]=r[t-255]})(),e.log=function(t){if(t<1)throw Error("log("+t+")");return i[t]},e.exp=function(t){return r[t]},e.mul=function(t,e){return 0===t||0===e?0:r[i[t]+i[e]]}},88330:(t,e,r)=>{"use strict";let i=r(73758),n=r(36096);function s(t){this.mode=i.KANJI,this.data=t}s.getBitsLength=function(t){return 13*t},s.prototype.getLength=function(){return this.data.length},s.prototype.getBitsLength=function(){return s.getBitsLength(this.data.length)},s.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let r=n.toSJIS(this.data[e]);if(r>=33088&&r<=40956)r-=33088;else if(r>=57408&&r<=60351)r-=49472;else throw Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");r=(r>>>8&255)*192+(255&r),t.put(r,13)}},t.exports=s},85126:(t,e)=>{e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};let r={N1:3,N2:3,N3:40,N4:10};e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){let e=t.size,i=0,n=0,s=0,o=null,a=null;for(let l=0;l<e;l++){n=s=0,o=a=null;for(let h=0;h<e;h++){let e=t.get(l,h);e===o?n++:(n>=5&&(i+=r.N1+(n-5)),o=e,n=1),(e=t.get(h,l))===a?s++:(s>=5&&(i+=r.N1+(s-5)),a=e,s=1)}n>=5&&(i+=r.N1+(n-5)),s>=5&&(i+=r.N1+(s-5))}return i},e.getPenaltyN2=function(t){let e=t.size,i=0;for(let r=0;r<e-1;r++)for(let n=0;n<e-1;n++){let e=t.get(r,n)+t.get(r,n+1)+t.get(r+1,n)+t.get(r+1,n+1);(4===e||0===e)&&i++}return i*r.N2},e.getPenaltyN3=function(t){let e=t.size,i=0,n=0,s=0;for(let r=0;r<e;r++){n=s=0;for(let o=0;o<e;o++)n=n<<1&2047|t.get(r,o),o>=10&&(1488===n||93===n)&&i++,s=s<<1&2047|t.get(o,r),o>=10&&(1488===s||93===s)&&i++}return i*r.N3},e.getPenaltyN4=function(t){let e=0,i=t.data.length;for(let r=0;r<i;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/i/5)-10)*r.N4},e.applyMask=function(t,r){let i=r.size;for(let n=0;n<i;n++)for(let s=0;s<i;s++)r.isReserved(s,n)||r.xor(s,n,function(t,r,i){switch(t){case e.Patterns.PATTERN000:return(r+i)%2==0;case e.Patterns.PATTERN001:return r%2==0;case e.Patterns.PATTERN010:return i%3==0;case e.Patterns.PATTERN011:return(r+i)%3==0;case e.Patterns.PATTERN100:return(Math.floor(r/2)+Math.floor(i/3))%2==0;case e.Patterns.PATTERN101:return r*i%2+r*i%3==0;case e.Patterns.PATTERN110:return(r*i%2+r*i%3)%2==0;case e.Patterns.PATTERN111:return(r*i%3+(r+i)%2)%2==0;default:throw Error("bad maskPattern:"+t)}}(t,s,n))},e.getBestMask=function(t,r){let i=Object.keys(e.Patterns).length,n=0,s=1/0;for(let o=0;o<i;o++){r(o),e.applyMask(o,t);let i=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(o,t),i<s&&(s=i,n=o)}return n}},73758:(t,e,r)=>{let i=r(3517),n=r(85917);e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw Error("Invalid mode: "+t);if(!i.isValid(e))throw Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return n.testNumeric(t)?e.NUMERIC:n.testAlphanumeric(t)?e.ALPHANUMERIC:n.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,r){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw Error("Unknown mode: "+t)}}(t)}catch(t){return r}}},11986:(t,e,r)=>{"use strict";let i=r(73758);function n(t){this.mode=i.NUMERIC,this.data=t.toString()}n.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},n.prototype.getLength=function(){return this.data.length},n.prototype.getBitsLength=function(){return n.getBitsLength(this.data.length)},n.prototype.write=function(t){let e,r;for(e=0;e+3<=this.data.length;e+=3)r=parseInt(this.data.substr(e,3),10),t.put(r,10);let i=this.data.length-e;i>0&&(r=parseInt(this.data.substr(e),10),t.put(r,3*i+1))},t.exports=n},39322:(t,e,r)=>{let i=r(52143);e.mul=function(t,e){let r=new Uint8Array(t.length+e.length-1);for(let n=0;n<t.length;n++)for(let s=0;s<e.length;s++)r[n+s]^=i.mul(t[n],e[s]);return r},e.mod=function(t,e){let r=new Uint8Array(t);for(;r.length-e.length>=0;){let t=r[0];for(let n=0;n<e.length;n++)r[n]^=i.mul(e[n],t);let n=0;for(;n<r.length&&0===r[n];)n++;r=r.slice(n)}return r},e.generateECPolynomial=function(t){let r=new Uint8Array([1]);for(let n=0;n<t;n++)r=e.mul(r,new Uint8Array([1,i.exp(n)]));return r}},70394:(t,e,r)=>{let i=r(36096),n=r(13862),s=r(33060),o=r(19934),a=r(62332),l=r(42708),h=r(85126),u=r(59797),c=r(59574),d=r(50814),f=r(84248),p=r(73758),g=r(29105);function m(t,e,r){let i,n;let s=t.size,o=f.getEncodedBits(e,r);for(i=0;i<15;i++)n=(o>>i&1)==1,i<6?t.set(i,8,n,!0):i<8?t.set(i+1,8,n,!0):t.set(s-15+i,8,n,!0),i<8?t.set(8,s-i-1,n,!0):i<9?t.set(8,15-i-1+1,n,!0):t.set(8,15-i-1,n,!0);t.set(s-8,8,1,!0)}e.create=function(t,e){let r,f;if(void 0===t||""===t)throw Error("No input text");let _=n.M;return void 0!==e&&(_=n.from(e.errorCorrectionLevel,n.M),r=d.from(e.version),f=h.from(e.maskPattern),e.toSJISFunc&&i.setToSJISFunction(e.toSJISFunc)),function(t,e,r,n){let f;if(Array.isArray(t))f=g.fromArray(t);else if("string"==typeof t){let i=e;if(!i){let e=g.rawSplit(t);i=d.getBestVersionForData(e,r)}f=g.fromString(t,i||40)}else throw Error("Invalid data");let _=d.getBestVersionForData(f,r);if(!_)throw Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<_)throw Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+_+".\n")}else e=_;let y=function(t,e,r){let n=new s;r.forEach(function(e){n.put(e.mode.bit,4),n.put(e.getLength(),p.getCharCountIndicator(e.mode,t)),e.write(n)});let o=(i.getSymbolTotalCodewords(t)-u.getTotalCodewordsCount(t,e))*8;for(n.getLengthInBits()+4<=o&&n.put(0,4);n.getLengthInBits()%8!=0;)n.putBit(0);let a=(o-n.getLengthInBits())/8;for(let t=0;t<a;t++)n.put(t%2?17:236,8);return function(t,e,r){let n,s;let o=i.getSymbolTotalCodewords(e),a=o-u.getTotalCodewordsCount(e,r),l=u.getBlocksCount(e,r),h=o%l,d=l-h,f=Math.floor(o/l),p=Math.floor(a/l),g=p+1,m=f-p,_=new c(m),y=0,b=Array(l),x=Array(l),w=0,E=new Uint8Array(t.buffer);for(let t=0;t<l;t++){let e=t<d?p:g;b[t]=E.slice(y,y+e),x[t]=_.encode(b[t]),y+=e,w=Math.max(w,e)}let T=new Uint8Array(o),v=0;for(n=0;n<w;n++)for(s=0;s<l;s++)n<b[s].length&&(T[v++]=b[s][n]);for(n=0;n<m;n++)for(s=0;s<l;s++)T[v++]=x[s][n];return T}(n,t,e)}(e,r,f),b=new o(i.getSymbolSize(e));return function(t,e){let r=t.size,i=l.getPositions(e);for(let e=0;e<i.length;e++){let n=i[e][0],s=i[e][1];for(let e=-1;e<=7;e++)if(!(n+e<=-1)&&!(r<=n+e))for(let i=-1;i<=7;i++)s+i<=-1||r<=s+i||(e>=0&&e<=6&&(0===i||6===i)||i>=0&&i<=6&&(0===e||6===e)||e>=2&&e<=4&&i>=2&&i<=4?t.set(n+e,s+i,!0,!0):t.set(n+e,s+i,!1,!0))}}(b,e),function(t){let e=t.size;for(let r=8;r<e-8;r++){let e=r%2==0;t.set(r,6,e,!0),t.set(6,r,e,!0)}}(b),function(t,e){let r=a.getPositions(e);for(let e=0;e<r.length;e++){let i=r[e][0],n=r[e][1];for(let e=-2;e<=2;e++)for(let r=-2;r<=2;r++)-2===e||2===e||-2===r||2===r||0===e&&0===r?t.set(i+e,n+r,!0,!0):t.set(i+e,n+r,!1,!0)}}(b,e),m(b,r,0),e>=7&&function(t,e){let r,i,n;let s=t.size,o=d.getEncodedBits(e);for(let e=0;e<18;e++)r=Math.floor(e/3),i=e%3+s-8-3,n=(o>>e&1)==1,t.set(r,i,n,!0),t.set(i,r,n,!0)}(b,e),function(t,e){let r=t.size,i=-1,n=r-1,s=7,o=0;for(let a=r-1;a>0;a-=2)for(6===a&&a--;;){for(let r=0;r<2;r++)if(!t.isReserved(n,a-r)){let i=!1;o<e.length&&(i=(e[o]>>>s&1)==1),t.set(n,a-r,i),-1==--s&&(o++,s=7)}if((n+=i)<0||r<=n){n-=i,i=-i;break}}}(b,y),isNaN(n)&&(n=h.getBestMask(b,m.bind(null,b,r))),h.applyMask(n,b),m(b,r,n),{modules:b,version:e,errorCorrectionLevel:r,maskPattern:n,segments:f}}(t,r,_,f)}},59574:(t,e,r)=>{"use strict";let i=r(39322);function n(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}n.prototype.initialize=function(t){this.degree=t,this.genPoly=i.generateECPolynomial(this.degree)},n.prototype.encode=function(t){if(!this.genPoly)throw Error("Encoder not initialized");let e=new Uint8Array(t.length+this.degree);e.set(t);let r=i.mod(e,this.genPoly),n=this.degree-r.length;if(n>0){let t=new Uint8Array(this.degree);return t.set(r,n),t}return r},t.exports=n},85917:(t,e)=>{let r="[0-9]+",i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",n="(?:(?![A-Z0-9 $%*+\\-./:]|"+(i=i.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";e.KANJI=RegExp(i,"g"),e.BYTE_KANJI=RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=RegExp(n,"g"),e.NUMERIC=RegExp(r,"g"),e.ALPHANUMERIC=RegExp("[A-Z $%*+\\-./:]+","g");let s=RegExp("^"+i+"$"),o=RegExp("^"+r+"$"),a=RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return s.test(t)},e.testNumeric=function(t){return o.test(t)},e.testAlphanumeric=function(t){return a.test(t)}},29105:(t,e,r)=>{let i=r(73758),n=r(11986),s=r(84152),o=r(27327),a=r(88330),l=r(85917),h=r(36096),u=r(17354);function c(t){return unescape(encodeURIComponent(t)).length}function d(t,e,r){let i;let n=[];for(;null!==(i=t.exec(r));)n.push({data:i[0],index:i.index,mode:e,length:i[0].length});return n}function f(t){let e,r;let n=d(l.NUMERIC,i.NUMERIC,t),s=d(l.ALPHANUMERIC,i.ALPHANUMERIC,t);return h.isKanjiModeEnabled()?(e=d(l.BYTE,i.BYTE,t),r=d(l.KANJI,i.KANJI,t)):(e=d(l.BYTE_KANJI,i.BYTE,t),r=[]),n.concat(s,e,r).sort(function(t,e){return t.index-e.index}).map(function(t){return{data:t.data,mode:t.mode,length:t.length}})}function p(t,e){switch(e){case i.NUMERIC:return n.getBitsLength(t);case i.ALPHANUMERIC:return s.getBitsLength(t);case i.KANJI:return a.getBitsLength(t);case i.BYTE:return o.getBitsLength(t)}}function g(t,e){let r;let l=i.getBestModeForData(t);if((r=i.from(e,l))!==i.BYTE&&r.bit<l.bit)throw Error('"'+t+'" cannot be encoded with mode '+i.toString(r)+".\n Suggested mode is: "+i.toString(l));switch(r!==i.KANJI||h.isKanjiModeEnabled()||(r=i.BYTE),r){case i.NUMERIC:return new n(t);case i.ALPHANUMERIC:return new s(t);case i.KANJI:return new a(t);case i.BYTE:return new o(t)}}e.fromArray=function(t){return t.reduce(function(t,e){return"string"==typeof e?t.push(g(e,null)):e.data&&t.push(g(e.data,e.mode)),t},[])},e.fromString=function(t,r){let n=function(t,e){let r={},n={start:{}},s=["start"];for(let o=0;o<t.length;o++){let a=t[o],l=[];for(let t=0;t<a.length;t++){let h=a[t],u=""+o+t;l.push(u),r[u]={node:h,lastCount:0},n[u]={};for(let t=0;t<s.length;t++){let o=s[t];r[o]&&r[o].node.mode===h.mode?(n[o][u]=p(r[o].lastCount+h.length,h.mode)-p(r[o].lastCount,h.mode),r[o].lastCount+=h.length):(r[o]&&(r[o].lastCount=h.length),n[o][u]=p(h.length,h.mode)+4+i.getCharCountIndicator(h.mode,e))}}s=l}for(let t=0;t<s.length;t++)n[s[t]].end=0;return{map:n,table:r}}(function(t){let e=[];for(let r=0;r<t.length;r++){let n=t[r];switch(n.mode){case i.NUMERIC:e.push([n,{data:n.data,mode:i.ALPHANUMERIC,length:n.length},{data:n.data,mode:i.BYTE,length:n.length}]);break;case i.ALPHANUMERIC:e.push([n,{data:n.data,mode:i.BYTE,length:n.length}]);break;case i.KANJI:e.push([n,{data:n.data,mode:i.BYTE,length:c(n.data)}]);break;case i.BYTE:e.push([{data:n.data,mode:i.BYTE,length:c(n.data)}])}}return e}(f(t,h.isKanjiModeEnabled())),r),s=u.find_path(n.map,"start","end"),o=[];for(let t=1;t<s.length-1;t++)o.push(n.table[s[t]].node);return e.fromArray(o.reduce(function(t,e){let r=t.length-1>=0?t[t.length-1]:null;return r&&r.mode===e.mode?t[t.length-1].data+=e.data:t.push(e),t},[]))},e.rawSplit=function(t){return e.fromArray(f(t,h.isKanjiModeEnabled()))}},36096:(t,e)=>{let r;let i=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw Error('"version" cannot be null or undefined');if(t<1||t>40)throw Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return i[t]},e.getBCHDigit=function(t){let e=0;for(;0!==t;)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!=typeof t)throw Error('"toSJISFunc" is not a valid function.');r=t},e.isKanjiModeEnabled=function(){return void 0!==r},e.toSJIS=function(t){return r(t)}},3517:(t,e)=>{e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},50814:(t,e,r)=>{let i=r(36096),n=r(59797),s=r(13862),o=r(73758),a=r(3517),l=i.getBCHDigit(7973);function h(t,e){return o.getCharCountIndicator(t,e)+4}e.from=function(t,e){return a.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,r){if(!a.isValid(t))throw Error("Invalid QR Code version");void 0===r&&(r=o.BYTE);let s=(i.getSymbolTotalCodewords(t)-n.getTotalCodewordsCount(t,e))*8;if(r===o.MIXED)return s;let l=s-h(r,t);switch(r){case o.NUMERIC:return Math.floor(l/10*3);case o.ALPHANUMERIC:return Math.floor(l/11*2);case o.KANJI:return Math.floor(l/13);case o.BYTE:default:return Math.floor(l/8)}},e.getBestVersionForData=function(t,r){let i;let n=s.from(r,s.M);if(Array.isArray(t)){if(t.length>1)return function(t,r){for(let i=1;i<=40;i++)if(function(t,e){let r=0;return t.forEach(function(t){let i=h(t.mode,e);r+=i+t.getBitsLength()}),r}(t,i)<=e.getCapacity(i,r,o.MIXED))return i}(t,n);if(0===t.length)return 1;i=t[0]}else i=t;return function(t,r,i){for(let n=1;n<=40;n++)if(r<=e.getCapacity(n,i,t))return n}(i.mode,i.getLength(),n)},e.getEncodedBits=function(t){if(!a.isValid(t)||t<7)throw Error("Invalid QR Code version");let e=t<<12;for(;i.getBCHDigit(e)-l>=0;)e^=7973<<i.getBCHDigit(e)-l;return t<<12|e}},8035:(t,e,r)=>{"use strict";r(28541)},2438:(t,e,r)=>{let i=r(78564);e.render=function(t,e,r){var n;let s=r,o=e;void 0!==s||e&&e.getContext||(s=e,e=void 0),e||(o=function(){try{return document.createElement("canvas")}catch(t){throw Error("You need to specify a canvas element")}}()),s=i.getOptions(s);let a=i.getImageWidth(t.modules.size,s),l=o.getContext("2d"),h=l.createImageData(a,a);return i.qrToImageData(h.data,t,s),n=o,l.clearRect(0,0,n.width,n.height),n.style||(n.style={}),n.height=a,n.width=a,n.style.height=a+"px",n.style.width=a+"px",l.putImageData(h,0,0),o},e.renderToDataURL=function(t,r,i){let n=i;void 0!==n||r&&r.getContext||(n=r,r=void 0),n||(n={});let s=e.render(t,r,n),o=n.type||"image/png",a=n.rendererOpts||{};return s.toDataURL(o,a.quality)}},19644:(t,e,r)=>{let i=r(57147),n=r(83269).y,s=r(78564);e.render=function(t,e){let r=s.getOptions(e),i=r.rendererOpts,o=s.getImageWidth(t.modules.size,r);i.width=o,i.height=o;let a=new n(i);return s.qrToImageData(a.data,t,r),a},e.renderToDataURL=function(t,r,i){void 0===i&&(i=r,r=void 0),e.renderToBuffer(t,r,function(t,e){t&&i(t);let r="data:image/png;base64,";r+=e.toString("base64"),i(null,r)})},e.renderToBuffer=function(t,r,i){void 0===i&&(i=r,r=void 0);let n=e.render(t,r),s=[];n.on("error",i),n.on("data",function(t){s.push(t)}),n.on("end",function(){i(null,Buffer.concat(s))}),n.pack()},e.renderToFile=function(t,r,n,s){void 0===s&&(s=n,n=void 0);let o=!1,a=(...t)=>{o||(o=!0,s.apply(null,t))},l=i.createWriteStream(t);l.on("error",a),l.on("close",a),e.renderToFileStream(l,r,n)},e.renderToFileStream=function(t,r,i){e.render(r,i).pack().pipe(t)}},40935:(t,e,r)=>{let i=r(78564);function n(t,e){let r=t.a/255,i=e+'="'+t.hex+'"';return r<1?i+" "+e+'-opacity="'+r.toFixed(2).slice(1)+'"':i}function s(t,e,r){let i=t+e;return void 0!==r&&(i+=" "+r),i}e.render=function(t,e,r){let o=i.getOptions(e),a=t.modules.size,l=t.modules.data,h=a+2*o.margin,u=o.color.light.a?"<path "+n(o.color.light,"fill")+' d="M0 0h'+h+"v"+h+'H0z"/>':"",c="<path "+n(o.color.dark,"stroke")+' d="'+function(t,e,r){let i="",n=0,o=!1,a=0;for(let l=0;l<t.length;l++){let h=Math.floor(l%e),u=Math.floor(l/e);h||o||(o=!0),t[l]?(a++,l>0&&h>0&&t[l-1]||(i+=o?s("M",h+r,.5+u+r):s("m",n,0),n=0,o=!1),h+1<e&&t[l+1]||(i+=s("h",a),a=0)):n++}return i}(l,a,o.margin)+'"/>',d='<svg xmlns="http://www.w3.org/2000/svg" '+(o.width?'width="'+o.width+'" height="'+o.width+'" ':"")+('viewBox="0 0 '+h)+" "+h+'" shape-rendering="crispEdges">'+u+c+"</svg>\n";return"function"==typeof r&&r(null,d),d}},134:(t,e,r)=>{let i=r(40935);e.render=i.render,e.renderToFile=function(t,i,n,s){void 0===s&&(s=n,n=void 0);let o=r(57147),a=e.render(i,n);o.writeFile(t,'<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+a,s)}},51224:(t,e,r)=>{let i=r(53537),n=r(27570);e.render=function(t,e,r){return e&&e.small?n.render(t,e,r):i.render(t,e,r)}},27570:(t,e)=>{let r="\x1b[37m",i="\x1b[30m",n="\x1b[0m",s="\x1b[47m"+i,o="\x1b[40m"+r,a=function(t,e,r,i){let n=e+1;return r>=n||i>=n||i<-1||r<-1?"0":r>=e||i>=e||i<0||r<0?"1":t[i*e+r]?"2":"1"},l=function(t,e,r,i){return a(t,e,r,i)+a(t,e,r,i+1)};e.render=function(t,e,a){var h,u;let c=t.modules.size,d=t.modules.data,f=!!(e&&e.inverse),p=e&&e.inverse?o:s,g={"00":n+" "+p,"01":n+(h=f?i:r)+"▄"+p,"02":n+(u=f?r:i)+"▄"+p,10:n+h+"▀"+p,11:" ",12:"▄",20:n+u+"▀"+p,21:"▀",22:"█"},m=n+"\n"+p,_=p;for(let t=-1;t<c+1;t+=2){for(let e=-1;e<c;e++)_+=g[l(d,c,e,t)];_+=g[l(d,c,c,t)]+m}return _+=n,"function"==typeof a&&a(null,_),_}},53537:(t,e)=>{e.render=function(t,e,r){let i=t.modules.size,n=t.modules.data,s="\x1b[47m  \x1b[0m",o="",a=Array(i+3).join(s),l=[,,].join(s);o+=a+"\n";for(let t=0;t<i;++t){o+=s;for(let e=0;e<i;e++)o+=n[t*i+e]?"\x1b[40m  \x1b[0m":s;o+=l+"\n"}return o+=a+"\n","function"==typeof r&&r(null,o),o}},51850:(t,e,r)=>{let i=r(78564),n={WW:" ",WB:"▄",BB:"█",BW:"▀"},s={BB:" ",BW:"▄",WW:"█",WB:"▀"};e.render=function(t,e,r){let o=i.getOptions(e),a=n;("#ffffff"===o.color.dark.hex||"#000000"===o.color.light.hex)&&(a=s);let l=t.modules.size,h=t.modules.data,u="",c=Array(l+2*o.margin+1).join(a.WW);c=Array(o.margin/2+1).join(c+"\n");let d=Array(o.margin+1).join(a.WW);u+=c;for(let t=0;t<l;t+=2){u+=d;for(let e=0;e<l;e++){var f;let r=h[t*l+e],i=h[(t+1)*l+e];u+=(f=a,r&&i?f.BB:r&&!i?f.BW:!r&&i?f.WB:f.WW)}u+=d+"\n"}return u+=c.slice(0,-1),"function"==typeof r&&r(null,u),u},e.renderToFile=function(t,i,n,s){void 0===s&&(s=n,n=void 0);let o=r(57147),a=e.render(i,n);o.writeFile(t,a,s)}},78564:(t,e)=>{function r(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw Error("Invalid hex color: "+t);(3===e.length||4===e.length)&&(e=Array.prototype.concat.apply([],e.map(function(t){return[t,t]}))),6===e.length&&e.push("F","F");let r=parseInt(e.join(""),16);return{r:r>>24&255,g:r>>16&255,b:r>>8&255,a:255&r,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});let e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,i=t.width&&t.width>=21?t.width:void 0,n=t.scale||4;return{width:i,scale:i?4:n,margin:e,color:{dark:r(t.color.dark||"#000000ff"),light:r(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,r){let i=e.getScale(t,r);return Math.floor((t+2*r.margin)*i)},e.qrToImageData=function(t,r,i){let n=r.modules.size,s=r.modules.data,o=e.getScale(n,i),a=Math.floor((n+2*i.margin)*o),l=i.margin*o,h=[i.color.light,i.color.dark];for(let e=0;e<a;e++)for(let r=0;r<a;r++){let u=(e*a+r)*4,c=i.color.light;e>=l&&r>=l&&e<a-l&&r<a-l&&(c=h[s[Math.floor((e-l)/o)*n+Math.floor((r-l)/o)]?1:0]),t[u++]=c.r,t[u++]=c.g,t[u++]=c.b,t[u]=c.a}}},28541:(t,e,r)=>{r(22665);let i=r(70394);r(19644),r(51850),r(51224),r(134),i.create,r(5131)},30080:(t,e,r)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var i=r(3729);"function"==typeof Object.is&&Object.is,i.useState,i.useEffect,i.useLayoutEffect,i.useDebugValue,e.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:function(t,e){return e()}},8145:(t,e,r)=>{"use strict";t.exports=r(30080)},71977:(t,e,r)=>{"use strict";r.r(e),r.d(e,{$$typeof:()=>s,__esModule:()=>n,default:()=>o});let i=(0,r(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\ticket-templates\create\page.tsx`),{__esModule:n,$$typeof:s}=i,o=i.default},15480:(t,e,r)=>{"use strict";r.d(e,{NY:()=>v,Ee:()=>T,fC:()=>E});var i=r(3729),n=r(98462),s=r(2256),o=r(16069),a=r(62409),l=r(8145);function h(){return()=>{}}var u=r(95344),c="Avatar",[d,f]=(0,n.b)(c),[p,g]=d(c),m=i.forwardRef((t,e)=>{let{__scopeAvatar:r,...n}=t,[s,o]=i.useState("idle");return(0,u.jsx)(p,{scope:r,imageLoadingStatus:s,onImageLoadingStatusChange:o,children:(0,u.jsx)(a.WV.span,{...n,ref:e})})});m.displayName=c;var _="AvatarImage",y=i.forwardRef((t,e)=>{let{__scopeAvatar:r,src:n,onLoadingStatusChange:c=()=>{},...d}=t,f=g(_,r),p=function(t,{referrerPolicy:e,crossOrigin:r}){let n=(0,l.useSyncExternalStore)(h,()=>!0,()=>!1),s=i.useRef(null),a=n?(s.current||(s.current=new window.Image),s.current):null,[u,c]=i.useState(()=>w(a,t));return(0,o.b)(()=>{c(w(a,t))},[a,t]),(0,o.b)(()=>{let t=t=>()=>{c(t)};if(!a)return;let i=t("loaded"),n=t("error");return a.addEventListener("load",i),a.addEventListener("error",n),e&&(a.referrerPolicy=e),"string"==typeof r&&(a.crossOrigin=r),()=>{a.removeEventListener("load",i),a.removeEventListener("error",n)}},[a,r,e]),u}(n,d),m=(0,s.W)(t=>{c(t),f.onImageLoadingStatusChange(t)});return(0,o.b)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,u.jsx)(a.WV.img,{...d,ref:e,src:n}):null});y.displayName=_;var b="AvatarFallback",x=i.forwardRef((t,e)=>{let{__scopeAvatar:r,delayMs:n,...s}=t,o=g(b,r),[l,h]=i.useState(void 0===n);return i.useEffect(()=>{if(void 0!==n){let t=window.setTimeout(()=>h(!0),n);return()=>window.clearTimeout(t)}},[n]),l&&"loaded"!==o.imageLoadingStatus?(0,u.jsx)(a.WV.span,{...s,ref:e}):null});function w(t,e){return t?e?(t.src!==e&&(t.src=e),t.complete&&t.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=b;var E=m,T=y,v=x},92062:(t,e,r)=>{"use strict";r.d(e,{D:()=>n});var i=r(3729);function n(t){let e=i.useRef({value:t,previous:t});return i.useMemo(()=>(e.current.value!==t&&(e.current.previous=e.current.value,e.current.value=t),e.current.previous),[t])}}};var e=require("../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),i=e.X(0,[1638,3088,9205,2295],()=>r(19442));module.exports=i})();