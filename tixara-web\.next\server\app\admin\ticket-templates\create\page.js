(()=>{var e={};e.id=4886,e.ids=[4886],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19442:(e,n,t)=>{"use strict";t.r(n),t.d(n,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>l,routeModule:()=>p,tree:()=>c});var r=t(50482),o=t(69108),a=t(62563),i=t.n(a),s=t(68300),d={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);t.d(n,d);let c=["",{children:["admin",{children:["ticket-templates",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,71977)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\create\\page.tsx"],m="/admin/ticket-templates/create/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/admin/ticket-templates/create/page",pathname:"/admin/ticket-templates/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9559:(e,n,t)=>{Promise.resolve().then(t.bind(t,45778))},85947:(e,n,t)=>{Promise.resolve().then(t.bind(t,89709))},16509:(e,n,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},23978:()=>{},45778:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>s});var r=t(95344),o=t(47674),a=t(8428);(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}();var i=t(42739);function s({children:e}){let{data:n,status:t}=(0,o.useSession)(),s=(0,a.useRouter)();return"loading"===t?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(i.Z,{className:"h-8 w-8 animate-spin"})}):n?.user&&"ADMIN"===n.user.role?r.jsx(Object(function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"py-6",children:r.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]})}):(s.push("/dashboard"),null)}},89709:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>u});var r=t(95344),o=t(3729),a=t(47674),i=t(8428),s=t(42739),d=t(63024);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,t(69224).Z)("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);var l=t(53148),m=t(31498);function u(){let e;let{data:n,status:t}=(0,a.useSession)(),u=(0,i.useRouter)(),{toast:p}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[h,O]=(0,o.useState)(!1),[x,f]=(0,o.useState)(!1),[v,j]=(0,o.useState)({name:"",description:"",templateCode:Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}()),category:"general",isPremium:!1,requiredBadge:"",price:0,isActive:!0});if("loading"===t)return r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(s.Z,{className:"h-8 w-8 animate-spin"})});if(!n?.user||"ADMIN"!==n.user.role)return u.push("/dashboard"),null;let N=async e=>{e.preventDefault(),O(!0);try{let e=await fetch("/api/ticket-templates",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(v)}),n=await e.json();n.success?(p({title:"Berhasil",description:"Template berhasil dibuat"}),u.push("/admin/ticket-templates")):p({title:"Error",description:n.message||"Gagal membuat template",variant:"destructive"})}catch(e){p({title:"Error",description:"Terjadi kesalahan saat membuat template",variant:"destructive"})}finally{O(!1)}},g=(e,n)=>{j(t=>({...t,[e]:n}))};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>u.back(),className:"flex items-center gap-2",children:[r.jsx(d.Z,{className:"h-4 w-4"}),"Kembali"]}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Buat Template Tiket"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Buat template tiket baru dengan format .temptix"})]})]}),(0,r.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[r.jsx("div",{className:"space-y-6",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Informasi Template"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Atur informasi dasar template tiket"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"name",children:"Nama Template *"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"name",value:v.name,onChange:e=>g("name",e.target.value),placeholder:"Masukkan nama template",required:!0})]}),(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"description",children:"Deskripsi"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"description",value:v.description,onChange:e=>g("description",e.target.value),placeholder:"Deskripsi template (opsional)",rows:3})]}),(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"category",children:"Kategori"}),(0,r.jsxs)("select",{id:"category",value:v.category,onChange:e=>g("category",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[r.jsx("option",{value:"general",children:"General"}),r.jsx("option",{value:"concert",children:"Konser"}),r.jsx("option",{value:"conference",children:"Konferensi"}),r.jsx("option",{value:"workshop",children:"Workshop"}),r.jsx("option",{value:"sports",children:"Olahraga"}),r.jsx("option",{value:"exhibition",children:"Pameran"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"isPremium",checked:v.isPremium,onCheckedChange:e=>g("isPremium",e)}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"isPremium",children:"Template Premium"})]}),v.isPremium&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"price",children:"Harga (Rp)"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"price",type:"number",value:v.price,onChange:e=>g("price",parseInt(e.target.value)||0),placeholder:"0",min:"0"})]}),(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"requiredBadge",children:"Badge yang Diperlukan"}),(0,r.jsxs)("select",{id:"requiredBadge",value:v.requiredBadge,onChange:e=>g("requiredBadge",e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[r.jsx("option",{value:"",children:"Tidak ada"}),r.jsx("option",{value:"BRONZE",children:"Bronze"}),r.jsx("option",{value:"SILVER",children:"Silver"}),r.jsx("option",{value:"GOLD",children:"Gold"}),r.jsx("option",{value:"TITANIUM",children:"Titanium"})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"isActive",checked:v.isActive,onCheckedChange:e=>g("isActive",e)}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"isActive",children:"Aktif"})]})]})]})}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Kode Template"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Tulis kode .temptix untuk template tiket"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"button",variant:"outline",size:"sm",onClick:()=>f(!x),children:[x?r.jsx(c,{className:"h-4 w-4 mr-1"}):r.jsx(l.Z,{className:"h-4 w-4 mr-1"}),x?"Kode":"Preview"]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:x?(e=v.templateCode,Object.entries({"{{eventName}}":"Sample Event Name","{{category}}":"Konser","{{buyerName}}":"John Doe","{{qr}}":"[QR CODE]","{{isVerified}}":"✓ Verified","{{adminFee}}":"Rp 5.000","{{ticketCode}}":"TIX-ABC123","{{eventDate}}":"25 Desember 2024","{{eventLocation}}":"Jakarta Convention Center","{{organizerName}}":"Event Organizer","{{price}}":"Rp 100.000"}).forEach(([n,t])=>{e=e.replace(RegExp(n,"g"),t)}),r.jsx("div",{className:"border rounded-lg p-4 bg-gray-50 dark:bg-gray-900",children:r.jsx("div",{dangerouslySetInnerHTML:{__html:e}})})):r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{value:v.templateCode,onChange:e=>g("templateCode",e.target.value),placeholder:"Masukkan kode .temptix",rows:20,className:"font-mono text-sm",required:!0})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm",children:"Variabel yang Tersedia"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"text-xs space-y-1 text-gray-600",children:[(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{eventName}}"})," - Nama event"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{category}}"})," - Kategori event"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{buyerName}}"})," - Nama pembeli"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{qr}}"})," - QR code (wajib)"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{isVerified}}"})," - Status verifikasi"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{adminFee}}"})," - Biaya admin"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{ticketCode}}"})," - Kode tiket"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{eventDate}}"})," - Tanggal event"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{eventLocation}}"})," - Lokasi event"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{organizerName}}"})," - Nama organizer"]}),(0,r.jsxs)("div",{children:[r.jsx("code",{children:"{{price}}"})," - Harga tiket"]})]})})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end gap-4",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"button",variant:"outline",onClick:()=>u.back(),children:"Batal"}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"submit",disabled:h,children:[h?r.jsx(s.Z,{className:"h-4 w-4 mr-2 animate-spin"}):r.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Simpan Template"]})]})]})]})}(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/switch'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}()},63024:(e,n,t)=>{"use strict";t.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},53148:(e,n,t)=>{"use strict";t.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},31498:(e,n,t)=>{"use strict";t.d(n,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Save",[["path",{d:"M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z",key:"1owoqh"}],["polyline",{points:"17 21 17 13 7 13 7 21",key:"1md35c"}],["polyline",{points:"7 3 7 8 15 8",key:"8nz8an"}]])},66294:(e,n,t)=>{"use strict";t.r(n),t.d(n,{$$typeof:()=>a,__esModule:()=>o,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\layout.tsx`),{__esModule:o,$$typeof:a}=r,i=r.default},71977:(e,n,t)=>{"use strict";t.r(n),t.d(n,{$$typeof:()=>a,__esModule:()=>o,default:()=>i});let r=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\ticket-templates\create\page.tsx`),{__esModule:o,$$typeof:a}=r,i=r.default},82917:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>c,metadata:()=>d});var r=t(25036),o=t(450),a=t.n(o),i=t(14824),s=t.n(i);t(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let d={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function c({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${a().variable} ${s().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},67272:()=>{}};var n=require("../../../../webpack-runtime.js");n.C(e);var t=e=>n(n.s=e),r=n.X(0,[1638,3293,5504],()=>t(19442));module.exports=r})();