(()=>{var e={};e.id=6954,e.ids=[6954],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},97761:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>o,routeModule:()=>p,tree:()=>d});var s=a(50482),r=a(69108),i=a(62563),l=a.n(i),n=a(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(t,c);let d=["",{children:["admin",{children:["ticket-templates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,47351)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\page.tsx"],u="/admin/ticket-templates/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/ticket-templates/page",pathname:"/admin/ticket-templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63340:(e,t,a)=>{Promise.resolve().then(a.bind(a,34910))},34910:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var s=a(95344),r=a(3729),i=a(47674),l=a(8428),n=a(16212),c=a(61351),d=a(69436),o=a(92549),u=a(42739),m=a(51838),p=a(28765);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let x=(0,a(69224).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var h=a(1750),y=a(89895),g=a(55794),v=a(53148),f=a(46327),j=a(38271),k=a(30692),w=a(91626);function N(){let{data:e,status:t}=(0,i.useSession)(),a=(0,l.useRouter)(),{toast:N}=(0,k.pm)(),[b,Z]=(0,r.useState)([]),[C,q]=(0,r.useState)(!0),[E,S]=(0,r.useState)(""),[M,P]=(0,r.useState)("all"),[_,z]=(0,r.useState)("all");(0,r.useEffect)(()=>{if("loading"!==t&&(!e?.user||"ADMIN"!==e.user.role)){a.push("/dashboard");return}},[e,t,a]);let A=async()=>{try{q(!0);let e=new URLSearchParams;E&&e.append("search",E),"all"!==M&&e.append("category",M),"all"!==_&&e.append("isPremium",_);let t=await fetch(`/api/ticket-templates?${e}`),a=await t.json();a.success?Z(a.data):N({title:"Error",description:a.message||"Gagal mengambil data template",variant:"destructive"})}catch(e){N({title:"Error",description:"Terjadi kesalahan saat mengambil data",variant:"destructive"})}finally{q(!1)}};(0,r.useEffect)(()=>{e?.user?.role==="ADMIN"&&A()},[e,E,M,_]);let T=async(e,t)=>{if(confirm(`Apakah Anda yakin ingin menghapus template "${t}"?`))try{let t=await fetch(`/api/ticket-templates/${e}`,{method:"DELETE"}),a=await t.json();a.success?(N({title:"Berhasil",description:"Template berhasil dihapus"}),A()):N({title:"Error",description:a.message||"Gagal menghapus template",variant:"destructive"})}catch(e){N({title:"Error",description:"Terjadi kesalahan saat menghapus template",variant:"destructive"})}},D=async(e,t)=>{try{let a=await fetch(`/api/ticket-templates/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({isActive:!t})}),s=await a.json();s.success?(N({title:"Berhasil",description:`Template ${t?"dinonaktifkan":"diaktifkan"}`}),A()):N({title:"Error",description:s.message||"Gagal mengubah status template",variant:"destructive"})}catch(e){N({title:"Error",description:"Terjadi kesalahan saat mengubah status",variant:"destructive"})}};return"loading"===t||C?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsx(u.Z,{className:"h-8 w-8 animate-spin"})}):e?.user&&"ADMIN"===e.user.role?(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-7xl",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Template Tiket"}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola template tiket untuk berbagai jenis event"})]}),(0,s.jsxs)(n.z,{onClick:()=>a.push("/admin/ticket-templates/create"),children:[s.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Buat Template"]})]}),s.jsx(c.Zb,{className:"mb-6",children:s.jsx(c.aY,{className:"pt-6",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[s.jsx("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[s.jsx(p.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),s.jsx(o.I,{placeholder:"Cari template...",value:E,onChange:e=>S(e.target.value),className:"pl-10"})]})}),(0,s.jsxs)("select",{value:M,onChange:e=>P(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[s.jsx("option",{value:"all",children:"Semua Kategori"}),s.jsx("option",{value:"general",children:"General"}),s.jsx("option",{value:"concert",children:"Konser"}),s.jsx("option",{value:"conference",children:"Konferensi"}),s.jsx("option",{value:"workshop",children:"Workshop"}),s.jsx("option",{value:"sports",children:"Olahraga"})]}),(0,s.jsxs)("select",{value:_,onChange:e=>z(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[s.jsx("option",{value:"all",children:"Semua Tipe"}),s.jsx("option",{value:"true",children:"Premium"}),s.jsx("option",{value:"false",children:"Gratis"})]})]})})}),0===b.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx(x,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Belum ada template"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Buat template tiket pertama untuk memulai"}),(0,s.jsxs)(n.z,{onClick:()=>a.push("/admin/ticket-templates/create"),children:[s.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Buat Template"]})]}):s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>(0,s.jsxs)(c.Zb,{className:"hover:shadow-lg transition-shadow",children:[s.jsx(c.Ol,{children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[s.jsx(c.ll,{className:"text-lg truncate",children:e.name}),s.jsx(c.SZ,{className:"mt-1",children:e.description||"Tidak ada deskripsi"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[e.isPremium&&s.jsx(h.Z,{className:"h-4 w-4 text-yellow-500"}),s.jsx(d.C,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Aktif":"Nonaktif"})]})]})}),s.jsx(c.aY,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"Kategori:"}),s.jsx(d.C,{variant:"outline",children:e.category})]}),e.isPremium&&(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"Harga:"}),(0,s.jsxs)("span",{className:"font-medium",children:["Rp ",e.price.toLocaleString("id-ID")]})]}),e.requiredBadge&&(0,s.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[s.jsx("span",{className:"text-gray-600",children:"Badge:"}),s.jsx(d.C,{variant:"outline",children:e.requiredBadge})]}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx(y.Z,{className:"h-3 w-3"}),(0,s.jsxs)("span",{children:[e._count.tickets," tiket"]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx(g.Z,{className:"h-3 w-3"}),(0,s.jsxs)("span",{children:[e._count.events," event"]})]})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-500",children:["Dibuat ",(0,w.formatRelativeTime)(e.createdAt)," oleh ",e.creator.name]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,s.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>a.push(`/admin/ticket-templates/${e.id}`),children:[s.jsx(v.Z,{className:"h-3 w-3 mr-1"}),"Lihat"]}),(0,s.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>a.push(`/admin/ticket-templates/${e.id}/edit`),children:[s.jsx(f.Z,{className:"h-3 w-3 mr-1"}),"Edit"]}),s.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>D(e.id,e.isActive),children:e.isActive?"Nonaktifkan":"Aktifkan"}),0===e._count.tickets&&0===e._count.events&&s.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>T(e.id,e.name),className:"text-red-600 hover:text-red-700",children:s.jsx(j.Z,{className:"h-3 w-3"})})]})]})})]},e.id))})]}):null}},50340:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},25390:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},85674:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1750:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},53148:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2273:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},70009:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},51838:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},46327:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},38271:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},30080:(e,t,a)=>{"use strict";/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var s=a(3729);"function"==typeof Object.is&&Object.is,s.useState,s.useEffect,s.useLayoutEffect,s.useDebugValue,t.useSyncExternalStore=void 0!==s.useSyncExternalStore?s.useSyncExternalStore:function(e,t){return t()}},8145:(e,t,a)=>{"use strict";e.exports=a(30080)},47351:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let s=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\ticket-templates\page.tsx`),{__esModule:r,$$typeof:i}=s,l=s.default},15480:(e,t,a)=>{"use strict";a.d(t,{NY:()=>b,Ee:()=>N,fC:()=>w});var s=a(3729),r=a(98462),i=a(2256),l=a(16069),n=a(62409),c=a(8145);function d(){return()=>{}}var o=a(95344),u="Avatar",[m,p]=(0,r.b)(u),[x,h]=m(u),y=s.forwardRef((e,t)=>{let{__scopeAvatar:a,...r}=e,[i,l]=s.useState("idle");return(0,o.jsx)(x,{scope:a,imageLoadingStatus:i,onImageLoadingStatusChange:l,children:(0,o.jsx)(n.WV.span,{...r,ref:t})})});y.displayName=u;var g="AvatarImage",v=s.forwardRef((e,t)=>{let{__scopeAvatar:a,src:r,onLoadingStatusChange:u=()=>{},...m}=e,p=h(g,a),x=function(e,{referrerPolicy:t,crossOrigin:a}){let r=(0,c.useSyncExternalStore)(d,()=>!0,()=>!1),i=s.useRef(null),n=r?(i.current||(i.current=new window.Image),i.current):null,[o,u]=s.useState(()=>k(n,e));return(0,l.b)(()=>{u(k(n,e))},[n,e]),(0,l.b)(()=>{let e=e=>()=>{u(e)};if(!n)return;let s=e("loaded"),r=e("error");return n.addEventListener("load",s),n.addEventListener("error",r),t&&(n.referrerPolicy=t),"string"==typeof a&&(n.crossOrigin=a),()=>{n.removeEventListener("load",s),n.removeEventListener("error",r)}},[n,a,t]),o}(r,m),y=(0,i.W)(e=>{u(e),p.onImageLoadingStatusChange(e)});return(0,l.b)(()=>{"idle"!==x&&y(x)},[x,y]),"loaded"===x?(0,o.jsx)(n.WV.img,{...m,ref:t,src:r}):null});v.displayName=g;var f="AvatarFallback",j=s.forwardRef((e,t)=>{let{__scopeAvatar:a,delayMs:r,...i}=e,l=h(f,a),[c,d]=s.useState(void 0===r);return s.useEffect(()=>{if(void 0!==r){let e=window.setTimeout(()=>d(!0),r);return()=>window.clearTimeout(e)}},[r]),c&&"loaded"!==l.imageLoadingStatus?(0,o.jsx)(n.WV.span,{...i,ref:t}):null});function k(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}j.displayName=f;var w=y,N=v,b=j}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,3088,9205,2295],()=>a(97761));module.exports=s})();