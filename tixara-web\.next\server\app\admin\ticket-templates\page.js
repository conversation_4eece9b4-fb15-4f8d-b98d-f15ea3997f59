(()=>{var e={};e.id=6954,e.ids=[6954],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},97761:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>p,tree:()=>d});var a=s(50482),r=s(69108),i=s(62563),l=s.n(i),n=s(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["admin",{children:["ticket-templates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,47351)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\page.tsx"],m="/admin/ticket-templates/page",x={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/admin/ticket-templates/page",pathname:"/admin/ticket-templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63340:(e,t,s)=>{Promise.resolve().then(s.bind(s,34910))},34910:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var a=s(95344),r=s(3729),i=s(47674),l=s(8428),n=s(16212),c=s(61351),d=s(69436),o=s(92549),m=s(42739),x=s(51838),p=s(28765);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,s(69224).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var h=s(1750),y=s(89895),g=s(55794),v=s(53148),k=s(46327),j=s(38271),f=s(30692),N=s(91626);function w(){let{data:e,status:t}=(0,i.useSession)(),s=(0,l.useRouter)(),{toast:w}=(0,f.pm)(),[b,Z]=(0,r.useState)([]),[q,C]=(0,r.useState)(!0),[M,P]=(0,r.useState)(""),[_,z]=(0,r.useState)("all"),[D,T]=(0,r.useState)("all");(0,r.useEffect)(()=>{if("loading"!==t&&(!e?.user||"ADMIN"!==e.user.role)){s.push("/dashboard");return}},[e,t,s]);let A=async()=>{try{C(!0);let e=new URLSearchParams;M&&e.append("search",M),"all"!==_&&e.append("category",_),"all"!==D&&e.append("isPremium",D);let t=await fetch(`/api/ticket-templates?${e}`),s=await t.json();s.success?Z(s.data):w({title:"Error",description:s.message||"Gagal mengambil data template",variant:"destructive"})}catch(e){w({title:"Error",description:"Terjadi kesalahan saat mengambil data",variant:"destructive"})}finally{C(!1)}};(0,r.useEffect)(()=>{e?.user?.role==="ADMIN"&&A()},[e,M,_,D]);let E=async(e,t)=>{if(confirm(`Apakah Anda yakin ingin menghapus template "${t}"?`))try{let t=await fetch(`/api/ticket-templates/${e}`,{method:"DELETE"}),s=await t.json();s.success?(w({title:"Berhasil",description:"Template berhasil dihapus"}),A()):w({title:"Error",description:s.message||"Gagal menghapus template",variant:"destructive"})}catch(e){w({title:"Error",description:"Terjadi kesalahan saat menghapus template",variant:"destructive"})}},S=async(e,t)=>{try{let s=await fetch(`/api/ticket-templates/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({isActive:!t})}),a=await s.json();a.success?(w({title:"Berhasil",description:`Template ${t?"dinonaktifkan":"diaktifkan"}`}),A()):w({title:"Error",description:a.message||"Gagal mengubah status template",variant:"destructive"})}catch(e){w({title:"Error",description:"Terjadi kesalahan saat mengubah status",variant:"destructive"})}};return"loading"===t||q?a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx(m.Z,{className:"h-8 w-8 animate-spin"})}):e?.user&&"ADMIN"===e.user.role?(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-7xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Template Tiket"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola template tiket untuk berbagai jenis event"})]}),(0,a.jsxs)(n.z,{onClick:()=>s.push("/admin/ticket-templates/create"),children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Buat Template"]})]}),a.jsx(c.Zb,{className:"mb-6",children:a.jsx(c.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[a.jsx("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx(p.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),a.jsx(o.I,{placeholder:"Cari template...",value:M,onChange:e=>P(e.target.value),className:"pl-10"})]})}),(0,a.jsxs)("select",{value:_,onChange:e=>z(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[a.jsx("option",{value:"all",children:"Semua Kategori"}),a.jsx("option",{value:"general",children:"General"}),a.jsx("option",{value:"concert",children:"Konser"}),a.jsx("option",{value:"conference",children:"Konferensi"}),a.jsx("option",{value:"workshop",children:"Workshop"}),a.jsx("option",{value:"sports",children:"Olahraga"})]}),(0,a.jsxs)("select",{value:D,onChange:e=>T(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[a.jsx("option",{value:"all",children:"Semua Tipe"}),a.jsx("option",{value:"true",children:"Premium"}),a.jsx("option",{value:"false",children:"Gratis"})]})]})})}),0===b.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(u,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Belum ada template"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Buat template tiket pertama untuk memulai"}),(0,a.jsxs)(n.z,{onClick:()=>s.push("/admin/ticket-templates/create"),children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Buat Template"]})]}):a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:b.map(e=>(0,a.jsxs)(c.Zb,{className:"hover:shadow-lg transition-shadow",children:[a.jsx(c.Ol,{children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[a.jsx(c.ll,{className:"text-lg truncate",children:e.name}),a.jsx(c.SZ,{className:"mt-1",children:e.description||"Tidak ada deskripsi"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[e.isPremium&&a.jsx(h.Z,{className:"h-4 w-4 text-yellow-500"}),a.jsx(d.C,{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Aktif":"Nonaktif"})]})]})}),a.jsx(c.aY,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Kategori:"}),a.jsx(d.C,{variant:"outline",children:e.category})]}),e.isPremium&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Harga:"}),(0,a.jsxs)("span",{className:"font-medium",children:["Rp ",e.price.toLocaleString("id-ID")]})]}),e.requiredBadge&&(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[a.jsx("span",{className:"text-gray-600",children:"Badge:"}),a.jsx(d.C,{variant:"outline",children:e.requiredBadge})]}),(0,a.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(y.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[e._count.tickets," tiket"]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-1",children:[a.jsx(g.Z,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[e._count.events," event"]})]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Dibuat ",(0,N.formatRelativeTime)(e.createdAt)," oleh ",e.creator.name]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,a.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>s.push(`/admin/ticket-templates/${e.id}`),children:[a.jsx(v.Z,{className:"h-3 w-3 mr-1"}),"Lihat"]}),(0,a.jsxs)(n.z,{variant:"outline",size:"sm",onClick:()=>s.push(`/admin/ticket-templates/${e.id}/edit`),children:[a.jsx(k.Z,{className:"h-3 w-3 mr-1"}),"Edit"]}),a.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>S(e.id,e.isActive),children:e.isActive?"Nonaktifkan":"Aktifkan"}),0===e._count.tickets&&0===e._count.events&&a.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>E(e.id,e.name),className:"text-red-600 hover:text-red-700",children:a.jsx(j.Z,{className:"h-3 w-3"})})]})]})})]},e.id))})]}):null}},50340:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},25390:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},85674:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1750:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},53148:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2273:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},70009:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},51838:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},46327:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},38271:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},47351:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let a=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\ticket-templates\page.tsx`),{__esModule:r,$$typeof:i}=a,l=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,3088,9205,2295],()=>s(97761));module.exports=a})();