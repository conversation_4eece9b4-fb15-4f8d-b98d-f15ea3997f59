(()=>{var e={};e.id=6954,e.ids=[6954],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},97761:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>l});var r=a(50482),n=a(69108),s=a(62563),i=a.n(s),o=a(68300),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);a.d(t,c);let l=["",{children:["admin",{children:["ticket-templates",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,47351)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,66294)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\admin\\ticket-templates\\page.tsx"],m="/admin/ticket-templates/page",u={require:a,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/admin/ticket-templates/page",pathname:"/admin/ticket-templates",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},9559:(e,t,a)=>{Promise.resolve().then(a.bind(a,45778))},63340:(e,t,a)=>{Promise.resolve().then(a.bind(a,34910))},16509:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,2583,23)),Promise.resolve().then(a.t.bind(a,26840,23)),Promise.resolve().then(a.t.bind(a,38771,23)),Promise.resolve().then(a.t.bind(a,13225,23)),Promise.resolve().then(a.t.bind(a,9295,23)),Promise.resolve().then(a.t.bind(a,43982,23))},23978:()=>{},45778:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>o});var r=a(95344),n=a(47674),s=a(8428);(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}();var i=a(42739);function o({children:e}){let{data:t,status:a}=(0,n.useSession)(),o=(0,s.useRouter)();return"loading"===a?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(i.Z,{className:"h-8 w-8 animate-spin"})}):t?.user&&"ADMIN"===t.user.role?r.jsx(Object(function(){var e=Error("Cannot find module '@/components/auth/role-guard'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-sidebar'");throw e.code="MODULE_NOT_FOUND",e}()),{}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/admin/admin-header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"py-6",children:r.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]})}):(o.push("/dashboard"),null)}},34910:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>v});var r=a(95344),n=a(3729),s=a(47674),i=a(8428),o=a(42739),c=a(51838),l=a(28765);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let d=(0,a(69224).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);var m=a(1750),u=a(89895),p=a(55794),h=a(53148),x=a(46327),f=a(38271);function v(){let{data:e,status:t}=(0,s.useSession)(),a=(0,i.useRouter)(),{toast:v}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[O,N]=(0,n.useState)([]),[j,g]=(0,n.useState)(!0),[y,k]=(0,n.useState)(""),[_,w]=(0,n.useState)("all"),[b,D]=(0,n.useState)("all");(0,n.useEffect)(()=>{if("loading"!==t&&(!e?.user||"ADMIN"!==e.user.role)){a.push("/dashboard");return}},[e,t,a]);let E=async()=>{try{g(!0);let e=new URLSearchParams;y&&e.append("search",y),"all"!==_&&e.append("category",_),"all"!==b&&e.append("isPremium",b);let t=await fetch(`/api/ticket-templates?${e}`),a=await t.json();a.success?N(a.data):v({title:"Error",description:a.message||"Gagal mengambil data template",variant:"destructive"})}catch(e){v({title:"Error",description:"Terjadi kesalahan saat mengambil data",variant:"destructive"})}finally{g(!1)}};(0,n.useEffect)(()=>{e?.user?.role==="ADMIN"&&E()},[e,y,_,b]);let U=async(e,t)=>{if(confirm(`Apakah Anda yakin ingin menghapus template "${t}"?`))try{let t=await fetch(`/api/ticket-templates/${e}`,{method:"DELETE"}),a=await t.json();a.success?(v({title:"Berhasil",description:"Template berhasil dihapus"}),E()):v({title:"Error",description:a.message||"Gagal menghapus template",variant:"destructive"})}catch(e){v({title:"Error",description:"Terjadi kesalahan saat menghapus template",variant:"destructive"})}},T=async(e,t)=>{try{let a=await fetch(`/api/ticket-templates/${e}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({isActive:!t})}),r=await a.json();r.success?(v({title:"Berhasil",description:`Template ${t?"dinonaktifkan":"diaktifkan"}`}),E()):v({title:"Error",description:r.message||"Gagal mengubah status template",variant:"destructive"})}catch(e){v({title:"Error",description:"Terjadi kesalahan saat mengubah status",variant:"destructive"})}};return"loading"===t||j?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(o.Z,{className:"h-8 w-8 animate-spin"})}):e?.user&&"ADMIN"===e.user.role?(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-7xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Template Tiket"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola template tiket untuk berbagai jenis event"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>a.push("/admin/ticket-templates/create"),children:[r.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Buat Template"]})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mb-6",children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6",children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[r.jsx("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx(l.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Cari template...",value:y,onChange:e=>k(e.target.value),className:"pl-10"})]})}),(0,r.jsxs)("select",{value:_,onChange:e=>w(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[r.jsx("option",{value:"all",children:"Semua Kategori"}),r.jsx("option",{value:"general",children:"General"}),r.jsx("option",{value:"concert",children:"Konser"}),r.jsx("option",{value:"conference",children:"Konferensi"}),r.jsx("option",{value:"workshop",children:"Workshop"}),r.jsx("option",{value:"sports",children:"Olahraga"})]}),(0,r.jsxs)("select",{value:b,onChange:e=>D(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[r.jsx("option",{value:"all",children:"Semua Tipe"}),r.jsx("option",{value:"true",children:"Premium"}),r.jsx("option",{value:"false",children:"Gratis"})]})]})})}),0===O.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(d,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Belum ada template"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Buat template tiket pertama untuk memulai"}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>a.push("/admin/ticket-templates/create"),children:[r.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Buat Template"]})]}):r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:O.map(e=>(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"hover:shadow-lg transition-shadow",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-lg truncate",children:e.name}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mt-1",children:e.description||"Tidak ada deskripsi"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[e.isPremium&&r.jsx(m.Z,{className:"h-4 w-4 text-yellow-500"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:e.isActive?"default":"secondary",className:"text-xs",children:e.isActive?"Aktif":"Nonaktif"})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[r.jsx("span",{className:"text-gray-600",children:"Kategori:"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:e.category})]}),e.isPremium&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[r.jsx("span",{className:"text-gray-600",children:"Harga:"}),(0,r.jsxs)("span",{className:"font-medium",children:["Rp ",e.price.toLocaleString("id-ID")]})]}),e.requiredBadge&&(0,r.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[r.jsx("span",{className:"text-gray-600",children:"Badge:"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:e.requiredBadge})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(u.Z,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[e._count.tickets," tiket"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(p.Z,{className:"h-3 w-3"}),(0,r.jsxs)("span",{children:[e._count.events," event"]})]})]}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Dibuat ",Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.createdAt)," oleh ",e.creator.name]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 pt-2",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>a.push(`/admin/ticket-templates/${e.id}`),children:[r.jsx(h.Z,{className:"h-3 w-3 mr-1"}),"Lihat"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>a.push(`/admin/ticket-templates/${e.id}/edit`),children:[r.jsx(x.Z,{className:"h-3 w-3 mr-1"}),"Edit"]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>T(e.id,e.isActive),children:e.isActive?"Nonaktifkan":"Aktifkan"}),0===e._count.tickets&&0===e._count.events&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>U(e.id,e.name),className:"text-red-600 hover:text-red-700",children:r.jsx(f.Z,{className:"h-3 w-3"})})]})]})})]},e.id))})]}):null}(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},55794:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},1750:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},53148:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},51838:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},28765:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},46327:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z",key:"1lpok0"}]])},38271:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89895:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},66294:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>s,__esModule:()=>n,default:()=>i});let r=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\layout.tsx`),{__esModule:n,$$typeof:s}=r,i=r.default},47351:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>s,__esModule:()=>n,default:()=>i});let r=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\ticket-templates\page.tsx`),{__esModule:n,$$typeof:s}=r,i=r.default},82917:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>l,metadata:()=>c});var r=a(25036),n=a(450),s=a.n(n),i=a(14824),o=a.n(i);a(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function l({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${s().variable} ${o().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},67272:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,3293,5504],()=>a(97761));module.exports=r})();