"use strict";(()=>{var t={};t.id=1179,t.ids=[1179],t.modules={53524:t=>{t.exports=require("@prisma/client")},72934:t=>{t.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:t=>{t.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:t=>{t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:t=>{t.exports=require("assert")},14300:t=>{t.exports=require("buffer")},6113:t=>{t.exports=require("crypto")},82361:t=>{t.exports=require("events")},13685:t=>{t.exports=require("http")},95687:t=>{t.exports=require("https")},63477:t=>{t.exports=require("querystring")},57310:t=>{t.exports=require("url")},73837:t=>{t.exports=require("util")},59796:t=>{t.exports=require("zlib")},96713:(t,e,r)=>{r.r(e),r.d(e,{headerHooks:()=>g,originalPathname:()=>y,patchFetch:()=>$,requestAsyncStorage:()=>p,routeModule:()=>u,serverHooks:()=>h,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>f});var a={};r.r(a),r.d(a,{GET:()=>l});var n=r(95419),o=r(69108),i=r(99678),s=r(78070),d=r(81355),c=r(53524);async function l(t){try{let e;let r=await (0,d.getServerSession)(Object(function(){var t=Error("Cannot find module '@/lib/auth'");throw t.code="MODULE_NOT_FOUND",t}()));if(!r?.user||r.user.role!==c.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(t.url),n=a.get("type")||"csv",o=a.get("range")||"30d",i=new Date;switch(o){case"7d":e=new Date(i.getTime()-6048e5);break;case"90d":e=new Date(i.getTime()-7776e6);break;case"1y":e=new Date(i.getTime()-31536e6);break;default:e=new Date(i.getTime()-2592e6)}let[l,u,p,m]=await Promise.all([Object(function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}()).transaction.findMany({where:{createdAt:{gte:e}},select:{id:!0,amount:!0,type:!0,status:!0,createdAt:!0,user:{select:{name:!0,email:!0}},event:{select:{title:!0,category:!0}}},orderBy:{createdAt:"desc"}}),Object(function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}()).event.findMany({where:{createdAt:{gte:e}},select:{id:!0,title:!0,category:!0,status:!0,startDate:!0,createdAt:!0,organizer:{select:{name:!0,email:!0}},_count:{select:{tickets:!0}}},orderBy:{createdAt:"desc"}}),Object(function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}()).user.findMany({where:{createdAt:{gte:e}},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,createdAt:!0,_count:{select:{events:!0,tickets:!0}}},orderBy:{createdAt:"desc"}}),Object(function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}()).ticket.findMany({where:{createdAt:{gte:e}},select:{id:!0,status:!0,createdAt:!0,user:{select:{name:!0,email:!0}},event:{select:{title:!0,category:!0}}},orderBy:{createdAt:"desc"}})]);if("csv"===n)return function(t){let{transactions:e,events:r,users:a,tickets:n,range:o}=t,i=`TiXara Analytics Report - ${o}
`;return i+=`Generated on: ${new Date().toISOString()}

TRANSACTIONS
ID,Amount,Type,Status,Date,User,Event
`,e.forEach(t=>{i+=`${t.id},${t.amount},${t.type},${t.status},${t.createdAt.toISOString()},${t.user?.name||"N/A"},${t.event?.title||"N/A"}
`}),i+=`
EVENTS
ID,Title,Category,Status,Start Date,Created Date,Organizer,Tickets Sold
`,r.forEach(t=>{i+=`${t.id},${t.title},${t.category||"N/A"},${t.status},${t.startDate?.toISOString()||"N/A"},${t.createdAt.toISOString()},${t.organizer.name},${t._count.tickets}
`}),i+=`
USERS
ID,Name,Email,Role,Badge,Verified,Created Date,Events,Tickets
`,a.forEach(t=>{i+=`${t.id},${t.name},${t.email},${t.role},${t.badge},${t.isVerified},${t.createdAt.toISOString()},${t._count.events},${t._count.tickets}
`}),i+=`
TICKETS
ID,Status,Created Date,User,Event
`,n.forEach(t=>{i+=`${t.id},${t.status},${t.createdAt.toISOString()},${t.user?.name||"N/A"},${t.event?.title||"N/A"}
`}),new s.Z(i,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="tixara-analytics-${o}.csv"`}})}({transactions:l,events:u,users:p,tickets:m,range:o});if("pdf"===n)return function(t){let{transactions:e,events:r,users:a,tickets:n,range:o}=t,i=`
    <!DOCTYPE html>
    <html>
    <head>
      <title>TiXara Analytics Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #0ea5e9; }
        h2 { color: #374151; margin-top: 30px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f3f4f6; }
        .summary { background-color: #f0f9ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <h1>TiXara Analytics Report</h1>
      <p><strong>Period:</strong> ${o}</p>
      <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
      
      <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Transactions:</strong> ${e.length}</p>
        <p><strong>Total Events:</strong> ${r.length}</p>
        <p><strong>New Users:</strong> ${a.length}</p>
        <p><strong>Tickets Sold:</strong> ${n.length}</p>
        <p><strong>Total Revenue:</strong> Rp ${e.filter(t=>"SUCCESS"===t.status).reduce((t,e)=>t+e.amount,0).toLocaleString()}</p>
      </div>

      <h2>Recent Transactions</h2>
      <table>
        <tr>
          <th>Date</th>
          <th>Amount</th>
          <th>Type</th>
          <th>Status</th>
          <th>User</th>
        </tr>
        ${e.slice(0,20).map(t=>`
          <tr>
            <td>${new Date(t.createdAt).toLocaleDateString()}</td>
            <td>Rp ${t.amount.toLocaleString()}</td>
            <td>${t.type}</td>
            <td>${t.status}</td>
            <td>${t.user?.name||"N/A"}</td>
          </tr>
        `).join("")}
      </table>

      <h2>Recent Events</h2>
      <table>
        <tr>
          <th>Title</th>
          <th>Category</th>
          <th>Organizer</th>
          <th>Tickets Sold</th>
          <th>Created</th>
        </tr>
        ${r.slice(0,20).map(t=>`
          <tr>
            <td>${t.title}</td>
            <td>${t.category||"N/A"}</td>
            <td>${t.organizer.name}</td>
            <td>${t._count.tickets}</td>
            <td>${new Date(t.createdAt).toLocaleDateString()}</td>
          </tr>
        `).join("")}
      </table>
    </body>
    </html>
  `;return new s.Z(i,{headers:{"Content-Type":"text/html","Content-Disposition":`attachment; filename="tixara-analytics-${o}.html"`}})}({transactions:l,events:u,users:p,tickets:m,range:o});return s.Z.json({error:"Invalid export type"},{status:400})}catch(t){return console.error("Error exporting analytics:",t),s.Z.json({error:"Internal server error"},{status:500})}}(function(){var t=Error("Cannot find module '@/lib/auth'");throw t.code="MODULE_NOT_FOUND",t})(),function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}();let u=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/analytics/export/route",pathname:"/api/admin/analytics/export",filename:"route",bundlePath:"app/api/admin/analytics/export/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\analytics\\export\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:h,headerHooks:g,staticGenerationBailout:f}=u,y="/api/admin/analytics/export/route";function $(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:m})}}};var e=require("../../../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),a=e.X(0,[1638,6206,1355],()=>r(96713));module.exports=a})();