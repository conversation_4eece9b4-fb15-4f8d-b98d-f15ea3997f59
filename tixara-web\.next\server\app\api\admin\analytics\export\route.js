"use strict";(()=>{var e={};e.id=1179,e.ids=[1179],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},96713:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>$,originalPathname:()=>f,patchFetch:()=>w,requestAsyncStorage:()=>m,routeModule:()=>g,serverHooks:()=>y,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{GET:()=>u});var i=r(95419),s=r(69108),n=r(99678),o=r(78070),d=r(81355),l=r(3205),c=r(3214),p=r(53524);async function u(e){try{let t;let r=await (0,d.getServerSession)(l.Lz);if(!r?.user||r.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),i=a.get("type")||"csv",s=a.get("range")||"30d",n=new Date;switch(s){case"7d":t=new Date(n.getTime()-6048e5);break;case"90d":t=new Date(n.getTime()-7776e6);break;case"1y":t=new Date(n.getTime()-31536e6);break;default:t=new Date(n.getTime()-2592e6)}let[u,g,m,h]=await Promise.all([c.prisma.transaction.findMany({where:{createdAt:{gte:t}},select:{id:!0,amount:!0,type:!0,status:!0,createdAt:!0,user:{select:{name:!0,email:!0}},event:{select:{title:!0,category:!0}}},orderBy:{createdAt:"desc"}}),c.prisma.event.findMany({where:{createdAt:{gte:t}},select:{id:!0,title:!0,category:!0,status:!0,startDate:!0,createdAt:!0,organizer:{select:{name:!0,email:!0}},_count:{select:{tickets:!0}}},orderBy:{createdAt:"desc"}}),c.prisma.user.findMany({where:{createdAt:{gte:t}},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,createdAt:!0,_count:{select:{events:!0,tickets:!0}}},orderBy:{createdAt:"desc"}}),c.prisma.ticket.findMany({where:{createdAt:{gte:t}},select:{id:!0,status:!0,createdAt:!0,user:{select:{name:!0,email:!0}},event:{select:{title:!0,category:!0}}},orderBy:{createdAt:"desc"}})]);if("csv"===i)return function(e){let{transactions:t,events:r,users:a,tickets:i,range:s}=e,n=`TiXara Analytics Report - ${s}
`;return n+=`Generated on: ${new Date().toISOString()}

TRANSACTIONS
ID,Amount,Type,Status,Date,User,Event
`,t.forEach(e=>{n+=`${e.id},${e.amount},${e.type},${e.status},${e.createdAt.toISOString()},${e.user?.name||"N/A"},${e.event?.title||"N/A"}
`}),n+=`
EVENTS
ID,Title,Category,Status,Start Date,Created Date,Organizer,Tickets Sold
`,r.forEach(e=>{n+=`${e.id},${e.title},${e.category||"N/A"},${e.status},${e.startDate?.toISOString()||"N/A"},${e.createdAt.toISOString()},${e.organizer.name},${e._count.tickets}
`}),n+=`
USERS
ID,Name,Email,Role,Badge,Verified,Created Date,Events,Tickets
`,a.forEach(e=>{n+=`${e.id},${e.name},${e.email},${e.role},${e.badge},${e.isVerified},${e.createdAt.toISOString()},${e._count.events},${e._count.tickets}
`}),n+=`
TICKETS
ID,Status,Created Date,User,Event
`,i.forEach(e=>{n+=`${e.id},${e.status},${e.createdAt.toISOString()},${e.user?.name||"N/A"},${e.event?.title||"N/A"}
`}),new o.Z(n,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="tixara-analytics-${s}.csv"`}})}({transactions:u,events:g,users:m,tickets:h,range:s});if("pdf"===i)return function(e){let{transactions:t,events:r,users:a,tickets:i,range:s}=e,n=`
    <!DOCTYPE html>
    <html>
    <head>
      <title>TiXara Analytics Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #0ea5e9; }
        h2 { color: #374151; margin-top: 30px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f3f4f6; }
        .summary { background-color: #f0f9ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <h1>TiXara Analytics Report</h1>
      <p><strong>Period:</strong> ${s}</p>
      <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
      
      <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Transactions:</strong> ${t.length}</p>
        <p><strong>Total Events:</strong> ${r.length}</p>
        <p><strong>New Users:</strong> ${a.length}</p>
        <p><strong>Tickets Sold:</strong> ${i.length}</p>
        <p><strong>Total Revenue:</strong> Rp ${t.filter(e=>"SUCCESS"===e.status).reduce((e,t)=>e+t.amount,0).toLocaleString()}</p>
      </div>

      <h2>Recent Transactions</h2>
      <table>
        <tr>
          <th>Date</th>
          <th>Amount</th>
          <th>Type</th>
          <th>Status</th>
          <th>User</th>
        </tr>
        ${t.slice(0,20).map(e=>`
          <tr>
            <td>${new Date(e.createdAt).toLocaleDateString()}</td>
            <td>Rp ${e.amount.toLocaleString()}</td>
            <td>${e.type}</td>
            <td>${e.status}</td>
            <td>${e.user?.name||"N/A"}</td>
          </tr>
        `).join("")}
      </table>

      <h2>Recent Events</h2>
      <table>
        <tr>
          <th>Title</th>
          <th>Category</th>
          <th>Organizer</th>
          <th>Tickets Sold</th>
          <th>Created</th>
        </tr>
        ${r.slice(0,20).map(e=>`
          <tr>
            <td>${e.title}</td>
            <td>${e.category||"N/A"}</td>
            <td>${e.organizer.name}</td>
            <td>${e._count.tickets}</td>
            <td>${new Date(e.createdAt).toLocaleDateString()}</td>
          </tr>
        `).join("")}
      </table>
    </body>
    </html>
  `;return new o.Z(n,{headers:{"Content-Type":"text/html","Content-Disposition":`attachment; filename="tixara-analytics-${s}.html"`}})}({transactions:u,events:g,users:m,tickets:h,range:s});return o.Z.json({error:"Invalid export type"},{status:400})}catch(e){return console.error("Error exporting analytics:",e),o.Z.json({error:"Internal server error"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/analytics/export/route",pathname:"/api/admin/analytics/export",filename:"route",bundlePath:"app/api/admin/analytics/export/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\analytics\\export\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:h,serverHooks:y,headerHooks:$,staticGenerationBailout:x}=g,f="/api/admin/analytics/export/route";function w(){return(0,n.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:h})}},3205:(e,t,r)=>{r.d(t,{Lz:()=>d});var a=r(65822),i=r(86485),s=r(98432),n=r.n(s),o=r(3214);r(53524);let d={adapter:(0,a.N)(o.prisma),providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:r,session:a})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===r&&a&&(e={...e,...a}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,r)=>{r.d(t,{prisma:()=>i});var a=r(53524);let i=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,9155],()=>r(96713));module.exports=a})();