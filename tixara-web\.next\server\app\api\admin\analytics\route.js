"use strict";(()=>{var e={};e.id=7777,e.ids=[7777],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},11997:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>C,originalPathname:()=>U,patchFetch:()=>T,requestAsyncStorage:()=>N,routeModule:()=>w,serverHooks:()=>v,staticGenerationAsyncStorage:()=>D,staticGenerationBailout:()=>h});var a={};r.r(a),r.d(a,{GET:()=>d});var n=r(95419),o=r(69108),s=r(99678),i=r(78070),u=r(81355),c=r(53524);async function d(e){try{let t,r;let a=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!a?.user||a.user.role!==c.UserRole.ADMIN)return i.Z.json({error:"Unauthorized"},{status:401});let{searchParams:n}=new URL(e.url),o=n.get("range")||"30d",s=new Date;switch(o){case"7d":t=new Date(s.getTime()-6048e5),r=new Date(s.getTime()-12096e5);break;case"90d":t=new Date(s.getTime()-7776e6),r=new Date(s.getTime()-15552e6);break;case"1y":t=new Date(s.getTime()-31536e6),r=new Date(s.getTime()-63072e6);break;default:t=new Date(s.getTime()-2592e6),r=new Date(s.getTime()-5184e6)}let[d,w,N,D,v,C,h,U,T,f,g,_,A]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).transaction.aggregate({where:{status:"SUCCESS",createdAt:{gte:t}},_sum:{amount:!0}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).transaction.aggregate({where:{status:"SUCCESS",createdAt:{gte:r,lt:t}},_sum:{amount:!0}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{status:"ACTIVE",createdAt:{gte:t}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{status:"ACTIVE",createdAt:{gte:r,lt:t}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.count({where:{createdAt:{gte:t}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.count({where:{createdAt:{gte:r,lt:t}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.count({where:{createdAt:{gte:t}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.count({where:{createdAt:{gte:r,lt:t}}}),l(t,o),m(t,o),E(t),O(t),p(t)]),b=(e,t)=>0===t?e>0?100:0:Math.round((e-t)/t*100),S={overview:{totalRevenue:d._sum.amount||0,totalTicketsSold:N,totalEvents:v,totalUsers:h,revenueGrowth:b(d._sum.amount||0,w._sum.amount||0),ticketsGrowth:b(N,D),eventsGrowth:b(v,C),usersGrowth:b(h,U)},revenueChart:T,userGrowthChart:f,eventCategoriesChart:g,topEvents:_,topOrganizers:A};return i.Z.json(S)}catch(e){return console.error("Error fetching analytics:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function l(e,t){let r="1y"===t?"%Y-%m":"%Y-%m-%d";return(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$queryRaw`
    SELECT 
      DATE_FORMAT(t.createdAt, ${r}) as date,
      COALESCE(SUM(CASE WHEN t.status = 'SUCCESS' THEN t.amount ELSE 0 END), 0) as revenue,
      COUNT(CASE WHEN tk.status = 'ACTIVE' THEN 1 END) as tickets
    FROM transactions t
    LEFT JOIN tickets tk ON DATE(t.createdAt) = DATE(tk.createdAt)
    WHERE t.createdAt >= ${e}
    GROUP BY DATE_FORMAT(t.createdAt, ${r})
    ORDER BY date ASC
  `).map(e=>({date:e.date,revenue:Number(e.revenue),tickets:Number(e.tickets)}))}async function m(e,t){let r="1y"===t?"%Y-%m":"%Y-%m-%d";return(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$queryRaw`
    SELECT 
      DATE_FORMAT(createdAt, ${r}) as date,
      COUNT(*) as users,
      COUNT(CASE WHEN role = 'ORGANIZER' THEN 1 END) as organizers
    FROM users
    WHERE createdAt >= ${e}
    GROUP BY DATE_FORMAT(createdAt, ${r})
    ORDER BY date ASC
  `).map(e=>({date:e.date,users:Number(e.users),organizers:Number(e.organizers)}))}async function E(e){return(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$queryRaw`
    SELECT 
      e.category,
      COUNT(e.id) as count,
      COALESCE(SUM(t.amount), 0) as revenue
    FROM events e
    LEFT JOIN transactions t ON e.id = t.eventId AND t.status = 'SUCCESS'
    WHERE e.createdAt >= ${e}
    GROUP BY e.category
    ORDER BY count DESC
  `).map(e=>({category:e.category||"Lainnya",count:Number(e.count),revenue:Number(e.revenue)}))}async function O(e){return(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findMany({where:{createdAt:{gte:e}},select:{id:!0,title:!0,organizer:{select:{name:!0}},_count:{select:{tickets:!0}},transactions:{where:{status:"SUCCESS"},select:{amount:!0}}},orderBy:{tickets:{_count:"desc"}},take:5})).map(e=>({id:e.id,title:e.title,organizer:e.organizer.name,ticketsSold:e._count.tickets,revenue:e.transactions.reduce((e,t)=>e+t.amount,0)}))}async function p(e){return(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findMany({where:{role:c.UserRole.ORGANIZER,events:{some:{createdAt:{gte:e}}}},select:{id:!0,name:!0,_count:{select:{events:!0}},events:{select:{transactions:{where:{status:"SUCCESS"},select:{amount:!0}},_count:{select:{tickets:!0}}}}},take:5})).map(e=>({id:e.id,name:e.name,eventsCount:e._count.events,totalRevenue:e.events.reduce((e,t)=>e+t.transactions.reduce((e,t)=>e+t.amount,0),0),totalTickets:e.events.reduce((e,t)=>e+t._count.tickets,0)})).sort((e,t)=>t.totalRevenue-e.totalRevenue)}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let w=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/analytics/route",pathname:"/api/admin/analytics",filename:"route",bundlePath:"app/api/admin/analytics/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\analytics\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:N,staticGenerationAsyncStorage:D,serverHooks:v,headerHooks:C,staticGenerationBailout:h}=w,U="/api/admin/analytics/route";function T(){return(0,s.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:D})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355],()=>r(11997));module.exports=a})();