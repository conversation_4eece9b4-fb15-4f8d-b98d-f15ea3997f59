"use strict";(()=>{var e={};e.id=7777,e.ids=[7777],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},11997:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>C,originalPathname:()=>R,patchFetch:()=>N,requestAsyncStorage:()=>h,routeModule:()=>A,serverHooks:()=>T,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>S});var r={};a.r(r),a.d(r,{GET:()=>m});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),c=a(3205),d=a(3214),l=a(53524);async function m(e){try{let t,a;let r=await (0,u.getServerSession)(c.Lz);if(!r?.user||r.user.role!==l.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:s}=new URL(e.url),i=s.get("range")||"30d",n=new Date;switch(i){case"7d":t=new Date(n.getTime()-6048e5),a=new Date(n.getTime()-12096e5);break;case"90d":t=new Date(n.getTime()-7776e6),a=new Date(n.getTime()-15552e6);break;case"1y":t=new Date(n.getTime()-31536e6),a=new Date(n.getTime()-63072e6);break;default:t=new Date(n.getTime()-2592e6),a=new Date(n.getTime()-5184e6)}let[m,A,h,y,T,C,S,R,N,k,x,b,f]=await Promise.all([d.prisma.transaction.aggregate({where:{status:"SUCCESS",createdAt:{gte:t}},_sum:{amount:!0}}),d.prisma.transaction.aggregate({where:{status:"SUCCESS",createdAt:{gte:a,lt:t}},_sum:{amount:!0}}),d.prisma.ticket.count({where:{status:"ACTIVE",createdAt:{gte:t}}}),d.prisma.ticket.count({where:{status:"ACTIVE",createdAt:{gte:a,lt:t}}}),d.prisma.event.count({where:{createdAt:{gte:t}}}),d.prisma.event.count({where:{createdAt:{gte:a,lt:t}}}),d.prisma.user.count({where:{createdAt:{gte:t}}}),d.prisma.user.count({where:{createdAt:{gte:a,lt:t}}}),p(t,i),g(t,i),E(t),w(t),v(t)]),D=(e,t)=>0===t?e>0?100:0:Math.round((e-t)/t*100),O={overview:{totalRevenue:m._sum.amount||0,totalTicketsSold:h,totalEvents:T,totalUsers:S,revenueGrowth:D(m._sum.amount||0,A._sum.amount||0),ticketsGrowth:D(h,y),eventsGrowth:D(T,C),usersGrowth:D(S,R)},revenueChart:N,userGrowthChart:k,eventCategoriesChart:x,topEvents:b,topOrganizers:f};return o.Z.json(O)}catch(e){return console.error("Error fetching analytics:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function p(e,t){let a="1y"===t?"%Y-%m":"%Y-%m-%d";return(await d.prisma.$queryRaw`
    SELECT 
      DATE_FORMAT(t.createdAt, ${a}) as date,
      COALESCE(SUM(CASE WHEN t.status = 'SUCCESS' THEN t.amount ELSE 0 END), 0) as revenue,
      COUNT(CASE WHEN tk.status = 'ACTIVE' THEN 1 END) as tickets
    FROM transactions t
    LEFT JOIN tickets tk ON DATE(t.createdAt) = DATE(tk.createdAt)
    WHERE t.createdAt >= ${e}
    GROUP BY DATE_FORMAT(t.createdAt, ${a})
    ORDER BY date ASC
  `).map(e=>({date:e.date,revenue:Number(e.revenue),tickets:Number(e.tickets)}))}async function g(e,t){let a="1y"===t?"%Y-%m":"%Y-%m-%d";return(await d.prisma.$queryRaw`
    SELECT 
      DATE_FORMAT(createdAt, ${a}) as date,
      COUNT(*) as users,
      COUNT(CASE WHEN role = 'ORGANIZER' THEN 1 END) as organizers
    FROM users
    WHERE createdAt >= ${e}
    GROUP BY DATE_FORMAT(createdAt, ${a})
    ORDER BY date ASC
  `).map(e=>({date:e.date,users:Number(e.users),organizers:Number(e.organizers)}))}async function E(e){return(await d.prisma.$queryRaw`
    SELECT 
      e.category,
      COUNT(e.id) as count,
      COALESCE(SUM(t.amount), 0) as revenue
    FROM events e
    LEFT JOIN transactions t ON e.id = t.eventId AND t.status = 'SUCCESS'
    WHERE e.createdAt >= ${e}
    GROUP BY e.category
    ORDER BY count DESC
  `).map(e=>({category:e.category||"Lainnya",count:Number(e.count),revenue:Number(e.revenue)}))}async function w(e){return(await d.prisma.event.findMany({where:{createdAt:{gte:e}},select:{id:!0,title:!0,organizer:{select:{name:!0}},_count:{select:{tickets:!0}},transactions:{where:{status:"SUCCESS"},select:{amount:!0}}},orderBy:{tickets:{_count:"desc"}},take:5})).map(e=>({id:e.id,title:e.title,organizer:e.organizer.name,ticketsSold:e._count.tickets,revenue:e.transactions.reduce((e,t)=>e+t.amount,0)}))}async function v(e){return(await d.prisma.user.findMany({where:{role:l.UserRole.ORGANIZER,events:{some:{createdAt:{gte:e}}}},select:{id:!0,name:!0,_count:{select:{events:!0}},events:{select:{transactions:{where:{status:"SUCCESS"},select:{amount:!0}},_count:{select:{tickets:!0}}}}},take:5})).map(e=>({id:e.id,name:e.name,eventsCount:e._count.events,totalRevenue:e.events.reduce((e,t)=>e+t.transactions.reduce((e,t)=>e+t.amount,0),0),totalTickets:e.events.reduce((e,t)=>e+t._count.tickets,0)})).sort((e,t)=>t.totalRevenue-e.totalRevenue)}let A=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/analytics/route",pathname:"/api/admin/analytics",filename:"route",bundlePath:"app/api/admin/analytics/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\analytics\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:T,headerHooks:C,staticGenerationBailout:S}=A,R="/api/admin/analytics/route";function N(){return(0,n.patchFetch)({serverHooks:T,staticGenerationAsyncStorage:y})}},3205:(e,t,a)=>{a.d(t,{Lz:()=>u});var r=a(65822),s=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let u={adapter:(0,r.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:a,session:r})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===a&&r&&(e={...e,...r}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,a)=>{a.d(t,{prisma:()=>s});var r=a(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,9155],()=>a(11997));module.exports=r})();