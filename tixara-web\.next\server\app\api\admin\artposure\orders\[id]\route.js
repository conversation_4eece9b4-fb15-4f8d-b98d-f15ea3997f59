"use strict";(()=>{var e={};e.id=1095,e.ids=[1095],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},77738:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>v,originalPathname:()=>g,patchFetch:()=>x,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>f,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>h});var s={};t.r(s),t.d(s,{GET:()=>c,PATCH:()=>l});var a=t(95419),i=t(69108),o=t(99678),n=t(78070),u=t(81355),d=t(53524);async function c(e,{params:r}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let t=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).artposureOrder.findUnique({where:{id:r.id},include:{service:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0,badgeType:!0}},event:{select:{id:!0,title:!0,slug:!0,startDate:!0,location:!0}}}});if(!t)return n.Z.json({success:!1,message:"Order tidak ditemukan"},{status:404});return n.Z.json({success:!0,data:t})}catch(e){return console.error("Get artposure order error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function l(e,{params:r}){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user||"ADMIN"!==t.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{status:s,result:a,feedback:i}=await e.json();if(!s)return n.Z.json({success:!1,message:"Status wajib diisi"},{status:400});if(!Object.values(d.ArtposureStatus).includes(s))return n.Z.json({success:!1,message:"Status tidak valid"},{status:400});let o=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).artposureOrder.findUnique({where:{id:r.id},include:{organizer:{select:{id:!0,name:!0,email:!0}},service:{select:{name:!0}}}});if(!o)return n.Z.json({success:!1,message:"Order tidak ditemukan"},{status:404});let c={status:s};void 0!==a&&(c.result=a),void 0!==i&&(c.feedback=i);let l=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).artposureOrder.update({where:{id:r.id},data:c,include:{service:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0}},event:{select:{id:!0,title:!0,slug:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:o.organizer.id,title:"Update Status Artposure",message:`Status order ${o.service.name} telah diupdate menjadi ${s}`,type:"ARTPOSURE_UPDATE",data:{orderId:l.id,status:s,serviceName:o.service.name}}}),n.Z.json({success:!0,data:l,message:"Status order berhasil diupdate"})}catch(e){return console.error("Update artposure order error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/artposure/orders/[id]/route",pathname:"/api/admin/artposure/orders/[id]",filename:"route",bundlePath:"app/api/admin/artposure/orders/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\artposure\\orders\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:O,serverHooks:f,headerHooks:v,staticGenerationBailout:h}=p,g="/api/admin/artposure/orders/[id]/route";function x(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:O})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6206,1355],()=>t(77738));module.exports=s})();