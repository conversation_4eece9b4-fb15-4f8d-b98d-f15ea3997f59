"use strict";(()=>{var e={};e.id=1095,e.ids=[1095],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},77738:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>f,originalPathname:()=>j,patchFetch:()=>b,requestAsyncStorage:()=>v,routeModule:()=>g,serverHooks:()=>w,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>x});var s={};a.r(s),a.d(s,{GET:()=>c,PATCH:()=>m});var t=a(95419),i=a(69108),n=a(99678),o=a(78070),d=a(81355),u=a(3205),l=a(3214),p=a(53524);async function c(e,{params:r}){try{let e=await (0,d.getServerSession)(u.Lz);if(!e?.user||"ADMIN"!==e.user.role)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let a=await l.prisma.artposureOrder.findUnique({where:{id:r.id},include:{service:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0,badgeType:!0}},event:{select:{id:!0,title:!0,slug:!0,startDate:!0,location:!0}}}});if(!a)return o.Z.json({success:!1,message:"Order tidak ditemukan"},{status:404});return o.Z.json({success:!0,data:a})}catch(e){return console.error("Get artposure order error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function m(e,{params:r}){try{let a=await (0,d.getServerSession)(u.Lz);if(!a?.user||"ADMIN"!==a.user.role)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{status:s,result:t,feedback:i}=await e.json();if(!s)return o.Z.json({success:!1,message:"Status wajib diisi"},{status:400});if(!Object.values(p.ArtposureStatus).includes(s))return o.Z.json({success:!1,message:"Status tidak valid"},{status:400});let n=await l.prisma.artposureOrder.findUnique({where:{id:r.id},include:{organizer:{select:{id:!0,name:!0,email:!0}},service:{select:{name:!0}}}});if(!n)return o.Z.json({success:!1,message:"Order tidak ditemukan"},{status:404});let c={status:s};void 0!==t&&(c.result=t),void 0!==i&&(c.feedback=i);let m=await l.prisma.artposureOrder.update({where:{id:r.id},data:c,include:{service:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0}},event:{select:{id:!0,title:!0,slug:!0}}}});return await l.prisma.notification.create({data:{userId:n.organizer.id,title:"Update Status Artposure",message:`Status order ${n.service.name} telah diupdate menjadi ${s}`,type:"ARTPOSURE_UPDATE",data:{orderId:m.id,status:s,serviceName:n.service.name}}}),o.Z.json({success:!0,data:m,message:"Status order berhasil diupdate"})}catch(e){return console.error("Update artposure order error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let g=new t.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/artposure/orders/[id]/route",pathname:"/api/admin/artposure/orders/[id]",filename:"route",bundlePath:"app/api/admin/artposure/orders/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\artposure\\orders\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:v,staticGenerationAsyncStorage:h,serverHooks:w,headerHooks:f,staticGenerationBailout:x}=g,j="/api/admin/artposure/orders/[id]/route";function b(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:h})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>d});var s=a(65822),t=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let d={adapter:(0,s.N)(o.prisma),providers:[(0,t.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:s})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&s&&(e={...e,...s}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>t});var s=a(53524);let t=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[1638,6206,9155],()=>a(77738));module.exports=s})();