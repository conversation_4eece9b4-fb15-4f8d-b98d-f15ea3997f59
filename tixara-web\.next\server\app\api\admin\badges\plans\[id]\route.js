"use strict";(()=>{var e={};e.id=570,e.ids=[570],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},53900:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>x,originalPathname:()=>j,patchFetch:()=>y,requestAsyncStorage:()=>h,routeModule:()=>b,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>v});var t={};a.r(t),a.d(t,{DELETE:()=>m,GET:()=>c,PUT:()=>g});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),d=a(81355),u=a(3205),l=a(3214),p=a(53524);async function c(e,{params:r}){try{let e=await (0,d.getServerSession)(u.Lz);if(!e?.user||e.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let a=await l.prisma.badgePlan.findUnique({where:{id:r.id},include:{_count:{select:{subscriptions:!0}},subscriptions:{include:{user:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:10}}});if(!a)return o.Z.json({error:"Badge plan not found"},{status:404});return o.Z.json(a)}catch(e){return console.error("Error fetching badge plan:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function g(e,{params:r}){try{let a=await (0,d.getServerSession)(u.Lz);if(!a?.user||a.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{name:t,description:s,price:i,duration:n,features:c,maxEvents:g,maxTicketsPerEvent:m,commissionDiscount:b,prioritySupport:h,customBranding:w,analytics:f,isActive:x,color:v,icon:j}=await e.json();if(!t||!s||i<0||n<=0)return o.Z.json({error:"Invalid input data"},{status:400});if(!await l.prisma.badgePlan.findUnique({where:{id:r.id}}))return o.Z.json({error:"Badge plan not found"},{status:404});if(await l.prisma.badgePlan.findFirst({where:{name:t,id:{not:r.id}}}))return o.Z.json({error:"Badge plan dengan nama tersebut sudah ada"},{status:400});let y=await l.prisma.badgePlan.update({where:{id:r.id},data:{name:t,description:s,price:i,duration:n,features:c||[],maxEvents:g||0,maxTicketsPerEvent:m||0,commissionDiscount:b||0,prioritySupport:h||!1,customBranding:w||!1,analytics:f||!1,isActive:!1!==x,color:v||"#0ea5e9",icon:j||"star"},include:{_count:{select:{subscriptions:!0}}}});return o.Z.json(y)}catch(e){return console.error("Error updating badge plan:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let e=await (0,d.getServerSession)(u.Lz);if(!e?.user||e.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});if(!await l.prisma.badgePlan.findUnique({where:{id:r.id},include:{_count:{select:{subscriptions:!0}}}}))return o.Z.json({error:"Badge plan not found"},{status:404});if(await l.prisma.badgeSubscription.count({where:{badgePlanId:r.id,status:"ACTIVE"}})>0)return o.Z.json({error:"Tidak dapat menghapus badge plan yang memiliki subscription aktif"},{status:400});return await l.prisma.badgePlan.delete({where:{id:r.id}}),o.Z.json({message:"Badge plan berhasil dihapus"})}catch(e){return console.error("Error deleting badge plan:",e),o.Z.json({error:"Internal server error"},{status:500})}}let b=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/badges/plans/[id]/route",pathname:"/api/admin/badges/plans/[id]",filename:"route",bundlePath:"app/api/admin/badges/plans/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\badges\\plans\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:h,staticGenerationAsyncStorage:w,serverHooks:f,headerHooks:x,staticGenerationBailout:v}=b,j="/api/admin/badges/plans/[id]/route";function y(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>d});var t=a(65822),s=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let d={adapter:(0,t.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:t})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&t&&(e={...e,...t}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>s});var t=a(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[1638,6206,9155],()=>a(53900));module.exports=t})();