"use strict";(()=>{var e={};e.id=570,e.ids=[570],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},53900:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>U,patchFetch:()=>E,requestAsyncStorage:()=>b,routeModule:()=>f,serverHooks:()=>O,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>g});var n={};t.r(n),t.d(n,{DELETE:()=>p,GET:()=>c,PUT:()=>l});var a=t(95419),o=t(69108),i=t(99678),s=t(78070),u=t(81355),d=t(53524);async function c(e,{params:r}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==d.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let t=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findUnique({where:{id:r.id},include:{_count:{select:{subscriptions:!0}},subscriptions:{include:{user:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:10}}});if(!t)return s.Z.json({error:"Badge plan not found"},{status:404});return s.Z.json(t)}catch(e){return console.error("Error fetching badge plan:",e),s.Z.json({error:"Internal server error"},{status:500})}}async function l(e,{params:r}){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user||t.user.role!==d.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let{name:n,description:a,price:o,duration:i,features:c,maxEvents:l,maxTicketsPerEvent:p,commissionDiscount:f,prioritySupport:b,customBranding:m,analytics:O,isActive:h,color:g,icon:U}=await e.json();if(!n||!a||o<0||i<=0)return s.Z.json({error:"Invalid input data"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findUnique({where:{id:r.id}}))return s.Z.json({error:"Badge plan not found"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findFirst({where:{name:n,id:{not:r.id}}}))return s.Z.json({error:"Badge plan dengan nama tersebut sudah ada"},{status:400});let E=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.update({where:{id:r.id},data:{name:n,description:a,price:o,duration:i,features:c||[],maxEvents:l||0,maxTicketsPerEvent:p||0,commissionDiscount:f||0,prioritySupport:b||!1,customBranding:m||!1,analytics:O||!1,isActive:!1!==h,color:g||"#0ea5e9",icon:U||"star"},include:{_count:{select:{subscriptions:!0}}}});return s.Z.json(E)}catch(e){return console.error("Error updating badge plan:",e),s.Z.json({error:"Internal server error"},{status:500})}}async function p(e,{params:r}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==d.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findUnique({where:{id:r.id},include:{_count:{select:{subscriptions:!0}}}}))return s.Z.json({error:"Badge plan not found"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.count({where:{badgePlanId:r.id,status:"ACTIVE"}})>0)return s.Z.json({error:"Tidak dapat menghapus badge plan yang memiliki subscription aktif"},{status:400});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.delete({where:{id:r.id}}),s.Z.json({message:"Badge plan berhasil dihapus"})}catch(e){return console.error("Error deleting badge plan:",e),s.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let f=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/badges/plans/[id]/route",pathname:"/api/admin/badges/plans/[id]",filename:"route",bundlePath:"app/api/admin/badges/plans/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\badges\\plans\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:b,staticGenerationAsyncStorage:m,serverHooks:O,headerHooks:h,staticGenerationBailout:g}=f,U="/api/admin/badges/plans/[id]/route";function E(){return(0,i.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:m})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[1638,6206,1355],()=>t(53900));module.exports=n})();