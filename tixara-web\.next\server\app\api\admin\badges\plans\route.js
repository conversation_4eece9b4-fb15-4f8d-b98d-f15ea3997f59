"use strict";(()=>{var e={};e.id=8853,e.ids=[8853],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},4420:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>x,originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>b,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>O});var n={};t.r(n),t.d(n,{GET:()=>d,POST:()=>l});var a=t(95419),o=t(69108),s=t(99678),i=t(78070),u=t(81355),c=t(53524);async function d(){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==c.UserRole.ADMIN)return i.Z.json({error:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findMany({include:{_count:{select:{subscriptions:!0}}},orderBy:{createdAt:"desc"}});return i.Z.json(r)}catch(e){return console.error("Error fetching badge plans:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function l(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||r.user.role!==c.UserRole.ADMIN)return i.Z.json({error:"Unauthorized"},{status:401});let{name:t,description:n,price:a,duration:o,features:s,maxEvents:d,maxTicketsPerEvent:l,commissionDiscount:p,prioritySupport:m,customBranding:f,analytics:b,isActive:x,color:O,icon:g}=await e.json();if(!t||!n||a<0||o<=0)return i.Z.json({error:"Invalid input data"},{status:400});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findFirst({where:{name:t}}))return i.Z.json({error:"Badge plan dengan nama tersebut sudah ada"},{status:400});let h=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.create({data:{name:t,description:n,price:a,duration:o,features:s||[],maxEvents:d||0,maxTicketsPerEvent:l||0,commissionDiscount:p||0,prioritySupport:m||!1,customBranding:f||!1,analytics:b||!1,isActive:!1!==x,color:O||"#0ea5e9",icon:g||"star"},include:{_count:{select:{subscriptions:!0}}}});return i.Z.json(h,{status:201})}catch(e){return console.error("Error creating badge plan:",e),i.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/badges/plans/route",pathname:"/api/admin/badges/plans",filename:"route",bundlePath:"app/api/admin/badges/plans/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\badges\\plans\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:b,headerHooks:x,staticGenerationBailout:O}=p,g="/api/admin/badges/plans/route";function h(){return(0,s.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:f})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[1638,6206,1355],()=>t(4420));module.exports=n})();