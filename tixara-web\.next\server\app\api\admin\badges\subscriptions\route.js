"use strict";(()=>{var e={};e.id=1209,e.ids=[1209],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},45309:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>f,originalPathname:()=>v,patchFetch:()=>q,requestAsyncStorage:()=>b,routeModule:()=>m,serverHooks:()=>h,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>x});var t={};a.r(t),a.d(t,{GET:()=>c,POST:()=>g});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),d=a(3205),l=a(3214),p=a(53524);async function c(e){try{let r=await (0,u.getServerSession)(d.Lz);if(!r?.user||r.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),t=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"20"),i=a.get("status"),n=a.get("badge"),c=a.get("search"),g=(t-1)*s,m={};i&&(m.status=i),n&&(m.badge=n),c&&(m.OR=[{user:{name:{contains:c,mode:"insensitive"}}},{user:{email:{contains:c,mode:"insensitive"}}}]);let[b,w]=await Promise.all([l.prisma.badgeSubscription.findMany({where:m,include:{user:{select:{id:!0,name:!0,email:!0,avatar:!0}},badgePlan:{select:{id:!0,name:!0,color:!0,icon:!0}}},orderBy:{createdAt:"desc"},skip:g,take:s}),l.prisma.badgeSubscription.count({where:m})]),h=b.map(e=>({id:e.id,badge:e.badgePlan?.name||e.badge,status:e.status,startDate:e.startDate.toISOString(),endDate:e.endDate.toISOString(),user:{id:e.user.id,name:e.user.name,email:e.user.email},createdAt:e.createdAt.toISOString()}));return o.Z.json({subscriptions:h,pagination:{page:t,limit:s,total:w,totalPages:Math.ceil(w/s)}})}catch(e){return console.error("Error fetching badge subscriptions:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function g(e){try{let r=await (0,u.getServerSession)(d.Lz);if(!r?.user||r.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{userId:a,badgePlanId:t,duration:s}=await e.json();if(!a||!t)return o.Z.json({error:"User ID and Badge Plan ID are required"},{status:400});if(!await l.prisma.user.findUnique({where:{id:a}}))return o.Z.json({error:"User not found"},{status:404});let i=await l.prisma.badgePlan.findUnique({where:{id:t}});if(!i)return o.Z.json({error:"Badge plan not found"},{status:404});if(await l.prisma.badgeSubscription.findFirst({where:{userId:a,status:"ACTIVE"}}))return o.Z.json({error:"User sudah memiliki subscription aktif"},{status:400});let n=new Date,c=new Date;c.setDate(n.getDate()+(s||i.duration));let g=await l.prisma.badgeSubscription.create({data:{userId:a,badgePlanId:t,badge:i.name,status:"ACTIVE",startDate:n,endDate:c},include:{user:{select:{id:!0,name:!0,email:!0}},badgePlan:{select:{id:!0,name:!0,color:!0,icon:!0}}}});return await l.prisma.user.update({where:{id:a},data:{badge:i.name}}),o.Z.json(g,{status:201})}catch(e){return console.error("Error creating badge subscription:",e),o.Z.json({error:"Internal server error"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/badges/subscriptions/route",pathname:"/api/admin/badges/subscriptions",filename:"route",bundlePath:"app/api/admin/badges/subscriptions/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\badges\\subscriptions\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:b,staticGenerationAsyncStorage:w,serverHooks:h,headerHooks:f,staticGenerationBailout:x}=m,v="/api/admin/badges/subscriptions/route";function q(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:w})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>u});var t=a(65822),s=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let u={adapter:(0,t.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:t})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&t&&(e={...e,...t}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>s});var t=a(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[1638,6206,9155],()=>a(45309));module.exports=t})();