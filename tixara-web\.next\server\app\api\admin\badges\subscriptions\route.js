"use strict";(()=>{var e={};e.id=1209,e.ids=[1209],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},45309:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>O,originalPathname:()=>h,patchFetch:()=>D,requestAsyncStorage:()=>b,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>f});var a={};t.r(a),t.d(a,{GET:()=>c,POST:()=>l});var n=t(95419),i=t(69108),o=t(99678),s=t(78070),u=t(81355),d=t(53524);async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||r.user.role!==d.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),a=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"20"),i=t.get("status"),o=t.get("badge"),c=t.get("search"),l=(a-1)*n,p={};i&&(p.status=i),o&&(p.badge=o),c&&(p.OR=[{user:{name:{contains:c,mode:"insensitive"}}},{user:{email:{contains:c,mode:"insensitive"}}}]);let[b,m]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.findMany({where:p,include:{user:{select:{id:!0,name:!0,email:!0,avatar:!0}},badgePlan:{select:{id:!0,name:!0,color:!0,icon:!0}}},orderBy:{createdAt:"desc"},skip:l,take:n}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.count({where:p})]),g=b.map(e=>({id:e.id,badge:e.badgePlan?.name||e.badge,status:e.status,startDate:e.startDate.toISOString(),endDate:e.endDate.toISOString(),user:{id:e.user.id,name:e.user.name,email:e.user.email},createdAt:e.createdAt.toISOString()}));return s.Z.json({subscriptions:g,pagination:{page:a,limit:n,total:m,totalPages:Math.ceil(m/n)}})}catch(e){return console.error("Error fetching badge subscriptions:",e),s.Z.json({error:"Internal server error"},{status:500})}}async function l(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||r.user.role!==d.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let{userId:t,badgePlanId:a,duration:n}=await e.json();if(!t||!a)return s.Z.json({error:"User ID and Badge Plan ID are required"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{id:t}}))return s.Z.json({error:"User not found"},{status:404});let i=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findUnique({where:{id:a}});if(!i)return s.Z.json({error:"Badge plan not found"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.findFirst({where:{userId:t,status:"ACTIVE"}}))return s.Z.json({error:"User sudah memiliki subscription aktif"},{status:400});let o=new Date,c=new Date;c.setDate(o.getDate()+(n||i.duration));let l=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.create({data:{userId:t,badgePlanId:a,badge:i.name,status:"ACTIVE",startDate:o,endDate:c},include:{user:{select:{id:!0,name:!0,email:!0}},badgePlan:{select:{id:!0,name:!0,color:!0,icon:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.update({where:{id:t},data:{badge:i.name}}),s.Z.json(l,{status:201})}catch(e){return console.error("Error creating badge subscription:",e),s.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/badges/subscriptions/route",pathname:"/api/admin/badges/subscriptions",filename:"route",bundlePath:"app/api/admin/badges/subscriptions/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\badges\\subscriptions\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:b,staticGenerationAsyncStorage:m,serverHooks:g,headerHooks:O,staticGenerationBailout:f}=p,h="/api/admin/badges/subscriptions/route";function D(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,1355],()=>t(45309));module.exports=a})();