"use strict";(()=>{var e={};e.id=1119,e.ids=[1119],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},76507:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>h,originalPathname:()=>f,patchFetch:()=>j,requestAsyncStorage:()=>v,routeModule:()=>g,serverHooks:()=>w,staticGenerationAsyncStorage:()=>b,staticGenerationBailout:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p,PATCH:()=>m});var r=t(95419),i=t(69108),o=t(99678),n=t(78070),d=t(81355),u=t(3205),l=t(3214),c=t(53524);async function p(e,{params:a}){try{let e=await (0,d.getServerSession)(u.Lz);if(!e?.user||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let t=await l.prisma.eventBoost.findUnique({where:{id:a.id},include:{event:{select:{id:!0,title:!0,slug:!0,startDate:!0,endDate:!0,location:!0,image:!0,soldTickets:!0,totalTickets:!0}},package:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0,badgeType:!0}}}});if(!t)return n.Z.json({success:!1,message:"Boost tidak ditemukan"},{status:404});return n.Z.json({success:!0,data:t})}catch(e){return console.error("Get event boost error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function m(e,{params:a}){try{let t=await (0,d.getServerSession)(u.Lz);if(!t?.user||"ADMIN"!==t.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{status:s}=await e.json();if(!s)return n.Z.json({success:!1,message:"Status wajib diisi"},{status:400});if(!Object.values(c.BoostStatus).includes(s))return n.Z.json({success:!1,message:"Status tidak valid"},{status:400});let r=await l.prisma.eventBoost.findUnique({where:{id:a.id},include:{event:{select:{id:!0,title:!0}},organizer:{select:{id:!0,name:!0,email:!0}},package:{select:{name:!0}}}});if(!r)return n.Z.json({success:!1,message:"Boost tidak ditemukan"},{status:404});let i={status:s},o=await l.prisma.$transaction(async e=>{let t=await e.eventBoost.update({where:{id:a.id},data:i,include:{event:!0,package:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0}}}});return("CANCELLED"===s||"EXPIRED"===s)&&await e.event.update({where:{id:r.event.id},data:{isBoosted:!1,boostEndDate:null}}),t});return await l.prisma.notification.create({data:{userId:r.organizer.id,title:"Update Status Boost",message:`Status boost untuk event "${r.event.title}" telah diupdate menjadi ${s}`,type:"BOOST_UPDATE",data:{boostId:o.id,eventId:r.event.id,status:s,packageName:r.package.name}}}),n.Z.json({success:!0,data:o,message:"Status boost berhasil diupdate"})}catch(e){return console.error("Update event boost error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let g=new r.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/booster/boosts/[id]/route",pathname:"/api/admin/booster/boosts/[id]",filename:"route",bundlePath:"app/api/admin/booster/boosts/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\booster\\boosts\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:v,staticGenerationAsyncStorage:b,serverHooks:w,headerHooks:h,staticGenerationBailout:x}=g,f="/api/admin/booster/boosts/[id]/route";function j(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:b})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>d});var s=t(65822),r=t(86485),i=t(98432),o=t.n(i),n=t(3214);t(53524);let d={adapter:(0,s.N)(n.prisma),providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await n.prisma.user.findUnique({where:{email:e.email}});if(!a||!await o().compare(e.password,a.password))throw Error("Email atau password salah");return await n.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:s})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&s&&(e={...e,...s}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await n.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,t)=>{t.d(a,{prisma:()=>r});var s=t(53524);let r=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var a=require("../../../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[1638,6206,9155],()=>t(76507));module.exports=s})();