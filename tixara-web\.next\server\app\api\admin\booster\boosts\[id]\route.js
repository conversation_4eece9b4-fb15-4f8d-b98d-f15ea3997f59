"use strict";(()=>{var e={};e.id=1119,e.ids=[1119],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},76507:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>O,patchFetch:()=>h,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>v,staticGenerationAsyncStorage:()=>b,staticGenerationBailout:()=>g});var s={};r.r(s),r.d(s,{GET:()=>c,PATCH:()=>l});var a=r(95419),o=r(69108),i=r(99678),n=r(78070),u=r(81355),d=r(53524);async function c(e,{params:t}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).eventBoost.findUnique({where:{id:t.id},include:{event:{select:{id:!0,title:!0,slug:!0,startDate:!0,endDate:!0,location:!0,image:!0,soldTickets:!0,totalTickets:!0}},package:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0,badgeType:!0}}}});if(!r)return n.Z.json({success:!1,message:"Boost tidak ditemukan"},{status:404});return n.Z.json({success:!0,data:r})}catch(e){return console.error("Get event boost error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function l(e,{params:t}){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||"ADMIN"!==r.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{status:s}=await e.json();if(!s)return n.Z.json({success:!1,message:"Status wajib diisi"},{status:400});if(!Object.values(d.BoostStatus).includes(s))return n.Z.json({success:!1,message:"Status tidak valid"},{status:400});let a=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).eventBoost.findUnique({where:{id:t.id},include:{event:{select:{id:!0,title:!0}},organizer:{select:{id:!0,name:!0,email:!0}},package:{select:{name:!0}}}});if(!a)return n.Z.json({success:!1,message:"Boost tidak ditemukan"},{status:404});let o={status:s},i=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{let r=await e.eventBoost.update({where:{id:t.id},data:o,include:{event:!0,package:!0,organizer:{select:{id:!0,name:!0,email:!0,verified:!0}}}});return("CANCELLED"===s||"EXPIRED"===s)&&await e.event.update({where:{id:a.event.id},data:{isBoosted:!1,boostEndDate:null}}),r});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:a.organizer.id,title:"Update Status Boost",message:`Status boost untuk event "${a.event.title}" telah diupdate menjadi ${s}`,type:"BOOST_UPDATE",data:{boostId:i.id,eventId:a.event.id,status:s,packageName:a.package.name}}}),n.Z.json({success:!0,data:i,message:"Status boost berhasil diupdate"})}catch(e){return console.error("Update event boost error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/booster/boosts/[id]/route",pathname:"/api/admin/booster/boosts/[id]",filename:"route",bundlePath:"app/api/admin/booster/boosts/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\booster\\boosts\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:b,serverHooks:v,headerHooks:f,staticGenerationBailout:g}=p,O="/api/admin/booster/boosts/[id]/route";function h(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:b})}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,1355],()=>r(76507));module.exports=s})();