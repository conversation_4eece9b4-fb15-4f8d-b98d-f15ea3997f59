"use strict";(()=>{var e={};e.id=2414,e.ids=[2414],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},51432:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>x,originalPathname:()=>w,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>c,serverHooks:()=>b,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var t={};a.r(t),a.d(t,{GET:()=>p});var s=a(95419),i=a(69108),o=a(99678),n=a(78070),d=a(81355),u=a(3205),l=a(3214);async function p(e){try{let r=await (0,d.getServerSession)(u.Lz);if(!r?.user||"ADMIN"!==r.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),t=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"20"),i=a.get("status"),o=a.get("packageId"),p=a.get("organizerId"),c=(t-1)*s,m={};i&&(m.status=i),o&&(m.packageId=o),p&&(m.organizerId=p);let[g,b]=await Promise.all([l.prisma.eventBoost.findMany({where:m,include:{event:{select:{id:!0,title:!0,slug:!0,startDate:!0,image:!0}},package:{select:{id:!0,name:!0,priority:!0,features:!0}},organizer:{select:{id:!0,name:!0,email:!0,verified:!0}}},orderBy:{createdAt:"desc"},skip:c,take:s}),l.prisma.eventBoost.count({where:m})]);return n.Z.json({success:!0,data:g,pagination:{page:t,limit:s,total:b,totalPages:Math.ceil(b/s)}})}catch(e){return console.error("Get event boosts error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/booster/boosts/route",pathname:"/api/admin/booster/boosts",filename:"route",bundlePath:"app/api/admin/booster/boosts/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\booster\\boosts\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:b,headerHooks:x,staticGenerationBailout:h}=c,w="/api/admin/booster/boosts/route";function v(){return(0,o.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:g})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>d});var t=a(65822),s=a(86485),i=a(98432),o=a.n(i),n=a(3214);a(53524);let d={adapter:(0,t.N)(n.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await n.prisma.user.findUnique({where:{email:e.email}});if(!r||!await o().compare(e.password,r.password))throw Error("Email atau password salah");return await n.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:t})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&t&&(e={...e,...t}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await n.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>s});var t=a(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[1638,6206,9155],()=>a(51432));module.exports=t})();