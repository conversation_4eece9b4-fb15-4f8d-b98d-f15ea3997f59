"use strict";(()=>{var e={};e.id=1368,e.ids=[1368],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},83379:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>O,patchFetch:()=>k,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>b});var s={};r.r(s),r.d(s,{DELETE:()=>l,GET:()=>c,PUT:()=>d});var a=r(95419),o=r(69108),i=r(99678),n=r(78070),u=r(81355);async function c(e,{params:t}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findUnique({where:{id:t.id},include:{_count:{select:{boosts:!0}},boosts:{include:{event:{select:{id:!0,title:!0,slug:!0,startDate:!0}},organizer:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:10}}});if(!r)return n.Z.json({success:!1,message:"Paket tidak ditemukan"},{status:404});return n.Z.json({success:!0,data:r})}catch(e){return console.error("Get booster package error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function d(e,{params:t}){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||"ADMIN"!==r.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{name:s,description:a,duration:o,price:i,features:c,priority:d,isActive:l}=await e.json();if(!s||!a||void 0===o||void 0===i||void 0===d)return n.Z.json({success:!1,message:"Semua field wajib diisi"},{status:400});if(i<0)return n.Z.json({success:!1,message:"Harga tidak boleh negatif"},{status:400});if(o<1)return n.Z.json({success:!1,message:"Durasi minimal 1 hari"},{status:400});if(d<1||d>5)return n.Z.json({success:!1,message:"Prioritas harus antara 1-5"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findUnique({where:{id:t.id}}))return n.Z.json({success:!1,message:"Paket tidak ditemukan"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findFirst({where:{name:s,id:{not:t.id}}}))return n.Z.json({success:!1,message:"Nama paket sudah digunakan"},{status:400});let p=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.update({where:{id:t.id},data:{name:s,description:a,duration:o,price:i,features:c||[],priority:d,isActive:void 0===l||l},include:{_count:{select:{boosts:!0}}}});return n.Z.json({success:!0,data:p,message:"Paket Booster berhasil diupdate"})}catch(e){return console.error("Update booster package error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function l(e,{params:t}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findUnique({where:{id:t.id},include:{_count:{select:{boosts:!0}}}}))return n.Z.json({success:!1,message:"Paket tidak ditemukan"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).eventBoost.count({where:{packageId:t.id,status:"ACTIVE"}})>0)return n.Z.json({success:!1,message:"Tidak dapat menghapus paket yang memiliki boost aktif"},{status:400});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.delete({where:{id:t.id}}),n.Z.json({success:!0,message:"Paket Booster berhasil dihapus"})}catch(e){return console.error("Delete booster package error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/booster/packages/[id]/route",pathname:"/api/admin/booster/packages/[id]",filename:"route",bundlePath:"app/api/admin/booster/packages/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\booster\\packages\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:g,headerHooks:h,staticGenerationBailout:b}=p,O="/api/admin/booster/packages/[id]/route";function k(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:f})}}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,1355],()=>r(83379));module.exports=s})();