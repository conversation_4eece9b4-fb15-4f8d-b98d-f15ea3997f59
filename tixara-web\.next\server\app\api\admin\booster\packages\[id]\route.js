"use strict";(()=>{var e={};e.id=1368,e.ids=[1368],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},83379:(e,s,a)=>{a.r(s),a.d(s,{headerHooks:()=>b,originalPathname:()=>j,patchFetch:()=>v,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>w,staticGenerationAsyncStorage:()=>k,staticGenerationBailout:()=>f});var r={};a.r(r),a.d(r,{DELETE:()=>m,GET:()=>l,PUT:()=>p});var t=a(95419),i=a(69108),o=a(99678),n=a(78070),u=a(81355),d=a(3205),c=a(3214);async function l(e,{params:s}){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let a=await c.prisma.boosterPackage.findUnique({where:{id:s.id},include:{_count:{select:{boosts:!0}},boosts:{include:{event:{select:{id:!0,title:!0,slug:!0,startDate:!0}},organizer:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},take:10}}});if(!a)return n.Z.json({success:!1,message:"Paket tidak ditemukan"},{status:404});return n.Z.json({success:!0,data:a})}catch(e){return console.error("Get booster package error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function p(e,{params:s}){try{let a=await (0,u.getServerSession)(d.Lz);if(!a?.user||"ADMIN"!==a.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{name:r,description:t,duration:i,price:o,features:l,priority:p,isActive:m}=await e.json();if(!r||!t||void 0===i||void 0===o||void 0===p)return n.Z.json({success:!1,message:"Semua field wajib diisi"},{status:400});if(o<0)return n.Z.json({success:!1,message:"Harga tidak boleh negatif"},{status:400});if(i<1)return n.Z.json({success:!1,message:"Durasi minimal 1 hari"},{status:400});if(p<1||p>5)return n.Z.json({success:!1,message:"Prioritas harus antara 1-5"},{status:400});if(!await c.prisma.boosterPackage.findUnique({where:{id:s.id}}))return n.Z.json({success:!1,message:"Paket tidak ditemukan"},{status:404});if(await c.prisma.boosterPackage.findFirst({where:{name:r,id:{not:s.id}}}))return n.Z.json({success:!1,message:"Nama paket sudah digunakan"},{status:400});let g=await c.prisma.boosterPackage.update({where:{id:s.id},data:{name:r,description:t,duration:i,price:o,features:l||[],priority:p,isActive:void 0===m||m},include:{_count:{select:{boosts:!0}}}});return n.Z.json({success:!0,data:g,message:"Paket Booster berhasil diupdate"})}catch(e){return console.error("Update booster package error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function m(e,{params:s}){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});if(!await c.prisma.boosterPackage.findUnique({where:{id:s.id},include:{_count:{select:{boosts:!0}}}}))return n.Z.json({success:!1,message:"Paket tidak ditemukan"},{status:404});if(await c.prisma.eventBoost.count({where:{packageId:s.id,status:"ACTIVE"}})>0)return n.Z.json({success:!1,message:"Tidak dapat menghapus paket yang memiliki boost aktif"},{status:400});return await c.prisma.boosterPackage.delete({where:{id:s.id}}),n.Z.json({success:!0,message:"Paket Booster berhasil dihapus"})}catch(e){return console.error("Delete booster package error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let g=new t.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/booster/packages/[id]/route",pathname:"/api/admin/booster/packages/[id]",filename:"route",bundlePath:"app/api/admin/booster/packages/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\booster\\packages\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:k,serverHooks:w,headerHooks:b,staticGenerationBailout:f}=g,j="/api/admin/booster/packages/[id]/route";function v(){return(0,o.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:k})}},3205:(e,s,a)=>{a.d(s,{Lz:()=>u});var r=a(65822),t=a(86485),i=a(98432),o=a.n(i),n=a(3214);a(53524);let u={adapter:(0,r.N)(n.prisma),providers:[(0,t.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let s=await n.prisma.user.findUnique({where:{email:e.email}});if(!s||!await o().compare(e.password,s.password))throw Error("Email atau password salah");return await n.prisma.user.update({where:{id:s.id},data:{lastLoginAt:new Date}}),{id:s.id,email:s.email,name:s.name,role:s.role,isVerified:s.isVerified,badge:s.badge,avatar:s.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:s,trigger:a,session:r})=>(s&&(e.role=s.role,e.isVerified=s.isVerified,e.badge=s.badge,e.avatar=s.avatar),"update"===a&&r&&(e={...e,...r}),e),session:async({session:e,token:s})=>(s&&(e.user.id=s.sub,e.user.role=s.role,e.user.isVerified=s.isVerified,e.user.badge=s.badge,e.user.avatar=s.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:s}){s&&await n.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,s,a)=>{a.d(s,{prisma:()=>t});var r=a(53524);let t=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var s=require("../../../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),r=s.X(0,[1638,6206,9155],()=>a(83379));module.exports=r})();