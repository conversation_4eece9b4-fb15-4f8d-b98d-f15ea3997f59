"use strict";(()=>{var e={};e.id=3864,e.ids=[3864],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},68974:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>w,originalPathname:()=>x,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>g,serverHooks:()=>b,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>f});var s={};r.r(s),r.d(s,{GET:()=>l,POST:()=>p});var t=r(95419),i=r(69108),o=r(99678),n=r(78070),u=r(81355),c=r(3205),d=r(3214);async function l(e){try{let a=await (0,u.getServerSession)(c.Lz);if(!a?.user||"ADMIN"!==a.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),t=parseInt(r.get("limit")||"20"),i=r.get("isActive"),o=(s-1)*t,l={};null!==i&&(l.isActive="true"===i);let[p,g]=await Promise.all([d.prisma.boosterPackage.findMany({where:l,include:{_count:{select:{boosts:!0}}},orderBy:[{priority:"desc"},{createdAt:"desc"}],skip:o,take:t}),d.prisma.boosterPackage.count({where:l})]);return n.Z.json({success:!0,data:p,pagination:{page:s,limit:t,total:g,totalPages:Math.ceil(g/t)}})}catch(e){return console.error("Get booster packages error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function p(e){try{let a=await (0,u.getServerSession)(c.Lz);if(!a?.user||"ADMIN"!==a.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{name:r,description:s,duration:t,price:i,features:o,priority:l,isActive:p}=await e.json();if(!r||!s||!t||!i||!l)return n.Z.json({success:!1,message:"Semua field wajib diisi"},{status:400});if(i<0)return n.Z.json({success:!1,message:"Harga tidak boleh negatif"},{status:400});if(t<1)return n.Z.json({success:!1,message:"Durasi minimal 1 hari"},{status:400});if(l<1||l>5)return n.Z.json({success:!1,message:"Prioritas harus antara 1-5"},{status:400});if(await d.prisma.boosterPackage.findFirst({where:{name:r}}))return n.Z.json({success:!1,message:"Nama paket sudah digunakan"},{status:400});let g=await d.prisma.boosterPackage.create({data:{name:r,description:s,duration:t,price:i,features:o||[],priority:l,isActive:void 0===p||p},include:{_count:{select:{boosts:!0}}}});return n.Z.json({success:!0,data:g,message:"Paket Booster berhasil dibuat"})}catch(e){return console.error("Create booster package error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let g=new t.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/booster/packages/route",pathname:"/api/admin/booster/packages",filename:"route",bundlePath:"app/api/admin/booster/packages/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\booster\\packages\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:h,serverHooks:b,headerHooks:w,staticGenerationBailout:f}=g,x="/api/admin/booster/packages/route";function v(){return(0,o.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:h})}},3205:(e,a,r)=>{r.d(a,{Lz:()=>u});var s=r(65822),t=r(86485),i=r(98432),o=r.n(i),n=r(3214);r(53524);let u={adapter:(0,s.N)(n.prisma),providers:[(0,t.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await n.prisma.user.findUnique({where:{email:e.email}});if(!a||!await o().compare(e.password,a.password))throw Error("Email atau password salah");return await n.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:r,session:s})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===r&&s&&(e={...e,...s}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await n.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,r)=>{r.d(a,{prisma:()=>t});var s=r(53524);let t=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var a=require("../../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),s=a.X(0,[1638,6206,9155],()=>r(68974));module.exports=s})();