"use strict";(()=>{var e={};e.id=3864,e.ids=[3864],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},68974:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>f,originalPathname:()=>h,patchFetch:()=>O,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>b});var s={};t.r(s),t.d(s,{GET:()=>c,POST:()=>d});var a=t(95419),o=t(69108),n=t(99678),i=t(78070),u=t(81355);async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||"ADMIN"!==r.user.role)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"20"),o=t.get("isActive"),n=(s-1)*a,c={};null!==o&&(c.isActive="true"===o);let[d,l]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findMany({where:c,include:{_count:{select:{boosts:!0}}},orderBy:[{priority:"desc"},{createdAt:"desc"}],skip:n,take:a}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.count({where:c})]);return i.Z.json({success:!0,data:d,pagination:{page:s,limit:a,total:l,totalPages:Math.ceil(l/a)}})}catch(e){return console.error("Get booster packages error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function d(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||"ADMIN"!==r.user.role)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{name:t,description:s,duration:a,price:o,features:n,priority:c,isActive:d}=await e.json();if(!t||!s||!a||!o||!c)return i.Z.json({success:!1,message:"Semua field wajib diisi"},{status:400});if(o<0)return i.Z.json({success:!1,message:"Harga tidak boleh negatif"},{status:400});if(a<1)return i.Z.json({success:!1,message:"Durasi minimal 1 hari"},{status:400});if(c<1||c>5)return i.Z.json({success:!1,message:"Prioritas harus antara 1-5"},{status:400});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findFirst({where:{name:t}}))return i.Z.json({success:!1,message:"Nama paket sudah digunakan"},{status:400});let l=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.create({data:{name:t,description:s,duration:a,price:o,features:n||[],priority:c,isActive:void 0===d||d},include:{_count:{select:{boosts:!0}}}});return i.Z.json({success:!0,data:l,message:"Paket Booster berhasil dibuat"})}catch(e){return console.error("Create booster package error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/booster/packages/route",pathname:"/api/admin/booster/packages",filename:"route",bundlePath:"app/api/admin/booster/packages/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\booster\\packages\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:g,headerHooks:f,staticGenerationBailout:b}=l,h="/api/admin/booster/packages/route";function O(){return(0,n.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:m})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6206,1355],()=>t(68974));module.exports=s})();