"use strict";(()=>{var e={};e.id=5024,e.ids=[5024],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},76100:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>b,originalPathname:()=>O,patchFetch:()=>v,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>f});var a={};r.r(a),r.d(a,{GET:()=>u});var i=r(95419),o=r(69108),n=r(99678),s=r(78070),d=r(81355),c=r(53524);async function u(e){try{let e=await (0,d.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==c.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let[t,r,a,i]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,name:!0,email:!0,role:!0,createdAt:!0}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,title:!0,createdAt:!0,organizer:{select:{name:!0}}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.findMany({take:5,orderBy:{createdAt:"desc"},where:{status:"ACTIVE"},select:{id:!0,createdAt:!0,event:{select:{title:!0}},user:{select:{name:!0}}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,badge:!0,createdAt:!0,user:{select:{name:!0}}}})]),o=[];t.forEach(e=>{o.push({id:`user-${e.id}`,type:"user_registration",description:`${e.name} mendaftar sebagai ${e.role.toLowerCase()}`,timestamp:e.createdAt,user:e.name})}),r.forEach(e=>{o.push({id:`event-${e.id}`,type:"event_created",description:`Event "${e.title}" dibuat oleh ${e.organizer.name}`,timestamp:e.createdAt,user:e.organizer.name})}),a.forEach(e=>{o.push({id:`ticket-${e.id}`,type:"ticket_sold",description:`Tiket untuk "${e.event.title}" dibeli oleh ${e.user.name}`,timestamp:e.createdAt,user:e.user.name})}),i.forEach(e=>{o.push({id:`subscription-${e.id}`,type:"subscription_created",description:`${e.user.name} berlangganan badge ${e.badge}`,timestamp:e.createdAt,user:e.user.name})});let n=o.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime()).slice(0,20).map(e=>({...e,timestamp:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.timestamp)}));return s.Z.json(n)}catch(e){return console.error("Error fetching dashboard activity:",e),s.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/dashboard/activity/route",pathname:"/api/admin/dashboard/activity",filename:"route",bundlePath:"app/api/admin/dashboard/activity/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\dashboard\\activity\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:h,headerHooks:b,staticGenerationBailout:f}=l,O="/api/admin/dashboard/activity/route";function v(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:m})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355],()=>r(76100));module.exports=a})();