(()=>{var m={};m.id=5024,m.ids=[5024],m.modules={53524:m=>{"use strict";m.exports=require("@prisma/client")},98432:m=>{"use strict";m.exports=require("bcryptjs")},72934:m=>{"use strict";m.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:m=>{"use strict";m.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:m=>{"use strict";m.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:m=>{"use strict";m.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:m=>{"use strict";m.exports=require("assert")},14300:m=>{"use strict";m.exports=require("buffer")},6113:m=>{"use strict";m.exports=require("crypto")},82361:m=>{"use strict";m.exports=require("events")},13685:m=>{"use strict";m.exports=require("http")},95687:m=>{"use strict";m.exports=require("https")},63477:m=>{"use strict";m.exports=require("querystring")},57310:m=>{"use strict";m.exports=require("url")},73837:m=>{"use strict";m.exports=require("util")},59796:m=>{"use strict";m.exports=require("zlib")},76100:(m,n,e)=>{"use strict";e.r(n),e.d(n,{headerHooks:()=>h,originalPathname:()=>I,patchFetch:()=>y,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>D,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>p});var t={};e.r(t),e.d(t,{GET:()=>u});var x=e(95419),b=e(69108),r=e(99678),a=e(78070),i=e(81355),o=e(3205),s=e(3214),f=e(53524),d=e(95752);async function u(m){try{let m=await (0,i.getServerSession)(o.Lz);if(!m?.user||m.user.role!==f.UserRole.ADMIN)return a.Z.json({error:"Unauthorized"},{status:401});let[n,e,t,x]=await Promise.all([s.prisma.user.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,name:!0,email:!0,role:!0,createdAt:!0}}),s.prisma.event.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,title:!0,createdAt:!0,organizer:{select:{name:!0}}}}),s.prisma.ticket.findMany({take:5,orderBy:{createdAt:"desc"},where:{status:"ACTIVE"},select:{id:!0,createdAt:!0,event:{select:{title:!0}},user:{select:{name:!0}}}}),s.prisma.badgeSubscription.findMany({take:5,orderBy:{createdAt:"desc"},select:{id:!0,badge:!0,createdAt:!0,user:{select:{name:!0}}}})]),b=[];n.forEach(m=>{b.push({id:`user-${m.id}`,type:"user_registration",description:`${m.name} mendaftar sebagai ${m.role.toLowerCase()}`,timestamp:m.createdAt,user:m.name})}),e.forEach(m=>{b.push({id:`event-${m.id}`,type:"event_created",description:`Event "${m.title}" dibuat oleh ${m.organizer.name}`,timestamp:m.createdAt,user:m.organizer.name})}),t.forEach(m=>{b.push({id:`ticket-${m.id}`,type:"ticket_sold",description:`Tiket untuk "${m.event.title}" dibeli oleh ${m.user.name}`,timestamp:m.createdAt,user:m.user.name})}),x.forEach(m=>{b.push({id:`subscription-${m.id}`,type:"subscription_created",description:`${m.user.name} berlangganan badge ${m.badge}`,timestamp:m.createdAt,user:m.user.name})});let r=b.sort((m,n)=>new Date(n.timestamp).getTime()-new Date(m.timestamp).getTime()).slice(0,20).map(m=>({...m,timestamp:(0,d.formatRelativeTime)(m.timestamp)}));return a.Z.json(r)}catch(m){return console.error("Error fetching dashboard activity:",m),a.Z.json({error:"Internal server error"},{status:500})}}let c=new x.AppRouteRouteModule({definition:{kind:b.x.APP_ROUTE,page:"/api/admin/dashboard/activity/route",pathname:"/api/admin/dashboard/activity",filename:"route",bundlePath:"app/api/admin/dashboard/activity/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\dashboard\\activity\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:l,staticGenerationAsyncStorage:g,serverHooks:D,headerHooks:h,staticGenerationBailout:p}=c,I="/api/admin/dashboard/activity/route";function y(){return(0,r.patchFetch)({serverHooks:D,staticGenerationAsyncStorage:g})}},3205:(m,n,e)=>{"use strict";e.d(n,{Lz:()=>i});var t=e(65822),x=e(86485),b=e(98432),r=e.n(b),a=e(3214);e(53524);let i={adapter:(0,t.N)(a.prisma),providers:[(0,x.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(m){if(!m?.email||!m?.password)throw Error("Email dan password harus diisi");let n=await a.prisma.user.findUnique({where:{email:m.email}});if(!n||!await r().compare(m.password,n.password))throw Error("Email atau password salah");return await a.prisma.user.update({where:{id:n.id},data:{lastLoginAt:new Date}}),{id:n.id,email:n.email,name:n.name,role:n.role,isVerified:n.isVerified,badge:n.badge,avatar:n.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:m,user:n,trigger:e,session:t})=>(n&&(m.role=n.role,m.isVerified=n.isVerified,m.badge=n.badge,m.avatar=n.avatar),"update"===e&&t&&(m={...m,...t}),m),session:async({session:m,token:n})=>(n&&(m.user.id=n.sub,m.user.role=n.role,m.user.isVerified=n.isVerified,m.user.badge=n.badge,m.user.avatar=n.avatar),m)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:m,isNewUser:n}){n&&await a.prisma.notification.create({data:{userId:m.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(m,n,e)=>{"use strict";e.d(n,{prisma:()=>x});var t=e(53524);let x=globalThis.prisma??new t.PrismaClient({log:["error"]})},95752:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatCurrency` is defined multiple times\n    ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:15:1]\n \x1b[2m15\x1b[0m │   }).format(new Date(date))\n \x1b[2m16\x1b[0m │ }\n \x1b[2m17\x1b[0m │ \n \x1b[2m18\x1b[0m │ export function formatCurrency(amount: number) {\n    \xb7 \x1b[38;2;246;87;248m                ───────┬──────\x1b[0m\n    \xb7                        \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatCurrency` here\x1b[0m\x1b[0m\n \x1b[2m19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m20\x1b[0m │     style: 'currency',\n \x1b[2m21\x1b[0m │     currency: 'IDR',\n \x1b[2m22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m23\x1b[0m │   }).format(amount)\n \x1b[2m24\x1b[0m │ }\n \x1b[2m25\x1b[0m │ \n \x1b[2m26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m27\x1b[0m │   const now = new Date()\n \x1b[2m28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m30\x1b[0m │ \n \x1b[2m31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m37\x1b[0m │     return formatDate(date)\n \x1b[2m38\x1b[0m │   }\n \x1b[2m39\x1b[0m │ \n \x1b[2m40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m44\x1b[0m │ \n \x1b[2m45\x1b[0m │   return formatDate(date)\n \x1b[2m46\x1b[0m │ }\n \x1b[2m47\x1b[0m │ \n \x1b[2m48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m49\x1b[0m │ export function formatCurrency(amount: number): string {\n    \xb7 \x1b[38;2;30;201;212m                ───────┬──────\x1b[0m\n    \xb7                        \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatCurrency` redefined here\x1b[0m\x1b[0m\n \x1b[2m50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m51\x1b[0m │     style: 'currency',\n \x1b[2m52\x1b[0m │     currency: 'IDR',\n    ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatDate` is defined multiple times\n    ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:5:1]\n \x1b[2m 5\x1b[0m │   return twMerge(clsx(inputs))\n \x1b[2m 6\x1b[0m │ }\n \x1b[2m 7\x1b[0m │ \n \x1b[2m 8\x1b[0m │ export function formatDate(date: string | Date) {\n    \xb7 \x1b[38;2;246;87;248m                ─────┬────\x1b[0m\n    \xb7                      \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatDate` here\x1b[0m\x1b[0m\n \x1b[2m 9\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m10\x1b[0m │     year: 'numeric',\n \x1b[2m11\x1b[0m │     month: 'long',\n \x1b[2m12\x1b[0m │     day: 'numeric',\n \x1b[2m13\x1b[0m │     hour: '2-digit',\n \x1b[2m14\x1b[0m │     minute: '2-digit',\n \x1b[2m15\x1b[0m │   }).format(new Date(date))\n \x1b[2m16\x1b[0m │ }\n \x1b[2m17\x1b[0m │ \n \x1b[2m18\x1b[0m │ export function formatCurrency(amount: number) {\n \x1b[2m19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m20\x1b[0m │     style: 'currency',\n \x1b[2m21\x1b[0m │     currency: 'IDR',\n \x1b[2m22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m23\x1b[0m │   }).format(amount)\n \x1b[2m24\x1b[0m │ }\n \x1b[2m25\x1b[0m │ \n \x1b[2m26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m27\x1b[0m │   const now = new Date()\n \x1b[2m28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m30\x1b[0m │ \n \x1b[2m31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m37\x1b[0m │     return formatDate(date)\n \x1b[2m38\x1b[0m │   }\n \x1b[2m39\x1b[0m │ \n \x1b[2m40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m44\x1b[0m │ \n \x1b[2m45\x1b[0m │   return formatDate(date)\n \x1b[2m46\x1b[0m │ }\n \x1b[2m47\x1b[0m │ \n \x1b[2m48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m51\x1b[0m │     style: 'currency',\n \x1b[2m52\x1b[0m │     currency: 'IDR',\n \x1b[2m53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m55\x1b[0m │   }).format(amount)\n \x1b[2m56\x1b[0m │ }\n \x1b[2m57\x1b[0m │ \n \x1b[2m58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m59\x1b[0m │ export function formatDate(date: Date): string {\n    \xb7 \x1b[38;2;30;201;212m                ─────┬────\x1b[0m\n    \xb7                      \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatDate` redefined here\x1b[0m\x1b[0m\n \x1b[2m60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m61\x1b[0m │     weekday: 'long',\n \x1b[2m62\x1b[0m │     year: 'numeric',\n    ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatRelativeTime` is defined multiple times\n    ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:23:1]\n \x1b[2m23\x1b[0m │   }).format(amount)\n \x1b[2m24\x1b[0m │ }\n \x1b[2m25\x1b[0m │ \n \x1b[2m26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n    \xb7 \x1b[38;2;246;87;248m                ─────────┬────────\x1b[0m\n    \xb7                          \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatRelativeTime` here\x1b[0m\x1b[0m\n \x1b[2m27\x1b[0m │   const now = new Date()\n \x1b[2m28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m30\x1b[0m │ \n \x1b[2m31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m37\x1b[0m │     return formatDate(date)\n \x1b[2m38\x1b[0m │   }\n \x1b[2m39\x1b[0m │ \n \x1b[2m40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m44\x1b[0m │ \n \x1b[2m45\x1b[0m │   return formatDate(date)\n \x1b[2m46\x1b[0m │ }\n \x1b[2m47\x1b[0m │ \n \x1b[2m48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m51\x1b[0m │     style: 'currency',\n \x1b[2m52\x1b[0m │     currency: 'IDR',\n \x1b[2m53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m55\x1b[0m │   }).format(amount)\n \x1b[2m56\x1b[0m │ }\n \x1b[2m57\x1b[0m │ \n \x1b[2m58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m61\x1b[0m │     weekday: 'long',\n \x1b[2m62\x1b[0m │     year: 'numeric',\n \x1b[2m63\x1b[0m │     month: 'long',\n \x1b[2m64\x1b[0m │     day: 'numeric',\n \x1b[2m65\x1b[0m │   }).format(date)\n \x1b[2m66\x1b[0m │ }\n \x1b[2m67\x1b[0m │ \n \x1b[2m68\x1b[0m │ // Format date and time\n \x1b[2m69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m71\x1b[0m │     weekday: 'long',\n \x1b[2m72\x1b[0m │     year: 'numeric',\n \x1b[2m73\x1b[0m │     month: 'long',\n \x1b[2m74\x1b[0m │     day: 'numeric',\n \x1b[2m75\x1b[0m │     hour: '2-digit',\n \x1b[2m76\x1b[0m │     minute: '2-digit',\n \x1b[2m77\x1b[0m │   }).format(date)\n \x1b[2m78\x1b[0m │ }\n \x1b[2m79\x1b[0m │ \n \x1b[2m80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n    \xb7 \x1b[38;2;30;201;212m                ─────────┬────────\x1b[0m\n    \xb7                          \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatRelativeTime` redefined here\x1b[0m\x1b[0m\n \x1b[2m82\x1b[0m │   const now = new Date()\n \x1b[2m83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n    ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatCurrency` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:15:1]\n \x1b[2m 15\x1b[0m │   }).format(new Date(date))\n \x1b[2m 16\x1b[0m │ }\n \x1b[2m 17\x1b[0m │ \n \x1b[2m 18\x1b[0m │ export function formatCurrency(amount: number) {\n     \xb7 \x1b[38;2;246;87;248m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatCurrency` here\x1b[0m\x1b[0m\n \x1b[2m 19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 20\x1b[0m │     style: 'currency',\n \x1b[2m 21\x1b[0m │     currency: 'IDR',\n \x1b[2m 22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 23\x1b[0m │   }).format(amount)\n \x1b[2m 24\x1b[0m │ }\n \x1b[2m 25\x1b[0m │ \n \x1b[2m 26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m 27\x1b[0m │   const now = new Date()\n \x1b[2m 28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m 29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m 30\x1b[0m │ \n \x1b[2m 31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m 32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m 33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m 34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m 35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m 36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m 37\x1b[0m │     return formatDate(date)\n \x1b[2m 38\x1b[0m │   }\n \x1b[2m 39\x1b[0m │ \n \x1b[2m 40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m 41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m 42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m 43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m 44\x1b[0m │ \n \x1b[2m 45\x1b[0m │   return formatDate(date)\n \x1b[2m 46\x1b[0m │ }\n \x1b[2m 47\x1b[0m │ \n \x1b[2m 48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m 49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m 50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 51\x1b[0m │     style: 'currency',\n \x1b[2m 52\x1b[0m │     currency: 'IDR',\n \x1b[2m 53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m 55\x1b[0m │   }).format(amount)\n \x1b[2m 56\x1b[0m │ }\n \x1b[2m 57\x1b[0m │ \n \x1b[2m 58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m 59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m 60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 61\x1b[0m │     weekday: 'long',\n \x1b[2m 62\x1b[0m │     year: 'numeric',\n \x1b[2m 63\x1b[0m │     month: 'long',\n \x1b[2m 64\x1b[0m │     day: 'numeric',\n \x1b[2m 65\x1b[0m │   }).format(date)\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n     \xb7 \x1b[38;2;30;201;212m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatCurrency` redefined here\x1b[0m\x1b[0m\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatDate` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:5:1]\n \x1b[2m  5\x1b[0m │   return twMerge(clsx(inputs))\n \x1b[2m  6\x1b[0m │ }\n \x1b[2m  7\x1b[0m │ \n \x1b[2m  8\x1b[0m │ export function formatDate(date: string | Date) {\n     \xb7 \x1b[38;2;246;87;248m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatDate` here\x1b[0m\x1b[0m\n \x1b[2m  9\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 10\x1b[0m │     year: 'numeric',\n \x1b[2m 11\x1b[0m │     month: 'long',\n \x1b[2m 12\x1b[0m │     day: 'numeric',\n \x1b[2m 13\x1b[0m │     hour: '2-digit',\n \x1b[2m 14\x1b[0m │     minute: '2-digit',\n \x1b[2m 15\x1b[0m │   }).format(new Date(date))\n \x1b[2m 16\x1b[0m │ }\n \x1b[2m 17\x1b[0m │ \n \x1b[2m 18\x1b[0m │ export function formatCurrency(amount: number) {\n \x1b[2m 19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 20\x1b[0m │     style: 'currency',\n \x1b[2m 21\x1b[0m │     currency: 'IDR',\n \x1b[2m 22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 23\x1b[0m │   }).format(amount)\n \x1b[2m 24\x1b[0m │ }\n \x1b[2m 25\x1b[0m │ \n \x1b[2m 26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m 27\x1b[0m │   const now = new Date()\n \x1b[2m 28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m 29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m 30\x1b[0m │ \n \x1b[2m 31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m 32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m 33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m 34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m 35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m 36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m 37\x1b[0m │     return formatDate(date)\n \x1b[2m 38\x1b[0m │   }\n \x1b[2m 39\x1b[0m │ \n \x1b[2m 40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m 41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m 42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m 43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m 44\x1b[0m │ \n \x1b[2m 45\x1b[0m │   return formatDate(date)\n \x1b[2m 46\x1b[0m │ }\n \x1b[2m 47\x1b[0m │ \n \x1b[2m 48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m 49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m 50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 51\x1b[0m │     style: 'currency',\n \x1b[2m 52\x1b[0m │     currency: 'IDR',\n \x1b[2m 53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m 55\x1b[0m │   }).format(amount)\n \x1b[2m 56\x1b[0m │ }\n \x1b[2m 57\x1b[0m │ \n \x1b[2m 58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m 59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m 60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 61\x1b[0m │     weekday: 'long',\n \x1b[2m 62\x1b[0m │     year: 'numeric',\n \x1b[2m 63\x1b[0m │     month: 'long',\n \x1b[2m 64\x1b[0m │     day: 'numeric',\n \x1b[2m 65\x1b[0m │   }).format(date)\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n     \xb7 \x1b[38;2;30;201;212m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatDate` redefined here\x1b[0m\x1b[0m\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatDateTime` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:66:1]\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n     \xb7 \x1b[38;2;246;87;248m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatDateTime` here\x1b[0m\x1b[0m\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n     \xb7 \x1b[38;2;30;201;212m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatDateTime` redefined here\x1b[0m\x1b[0m\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatRelativeTime` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:23:1]\n \x1b[2m 23\x1b[0m │   }).format(amount)\n \x1b[2m 24\x1b[0m │ }\n \x1b[2m 25\x1b[0m │ \n \x1b[2m 26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n     \xb7 \x1b[38;2;246;87;248m                ─────────┬────────\x1b[0m\n     \xb7                          \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatRelativeTime` here\x1b[0m\x1b[0m\n \x1b[2m 27\x1b[0m │   const now = new Date()\n \x1b[2m 28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m 29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m 30\x1b[0m │ \n \x1b[2m 31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m 32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m 33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m 34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m 35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m 36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m 37\x1b[0m │     return formatDate(date)\n \x1b[2m 38\x1b[0m │   }\n \x1b[2m 39\x1b[0m │ \n \x1b[2m 40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m 41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m 42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m 43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m 44\x1b[0m │ \n \x1b[2m 45\x1b[0m │   return formatDate(date)\n \x1b[2m 46\x1b[0m │ }\n \x1b[2m 47\x1b[0m │ \n \x1b[2m 48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m 49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m 50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 51\x1b[0m │     style: 'currency',\n \x1b[2m 52\x1b[0m │     currency: 'IDR',\n \x1b[2m 53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m 55\x1b[0m │   }).format(amount)\n \x1b[2m 56\x1b[0m │ }\n \x1b[2m 57\x1b[0m │ \n \x1b[2m 58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m 59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m 60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 61\x1b[0m │     weekday: 'long',\n \x1b[2m 62\x1b[0m │     year: 'numeric',\n \x1b[2m 63\x1b[0m │     month: 'long',\n \x1b[2m 64\x1b[0m │     day: 'numeric',\n \x1b[2m 65\x1b[0m │   }).format(date)\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n     \xb7 \x1b[38;2;30;201;212m                ─────────┬────────\x1b[0m\n     \xb7                          \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatRelativeTime` redefined here\x1b[0m\x1b[0m\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `generateId` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:111:1]\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n     \xb7 \x1b[38;2;246;87;248m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `generateId` here\x1b[0m\x1b[0m\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n \x1b[2m175\x1b[0m │ \n \x1b[2m176\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m177\x1b[0m │     return 'Baru saja'\n \x1b[2m178\x1b[0m │   } else if (diffInSeconds < 3600) {\n \x1b[2m179\x1b[0m │     const minutes = Math.floor(diffInSeconds / 60)\n \x1b[2m180\x1b[0m │     return `${minutes} menit yang lalu`\n \x1b[2m181\x1b[0m │   } else if (diffInSeconds < 86400) {\n \x1b[2m182\x1b[0m │     const hours = Math.floor(diffInSeconds / 3600)\n \x1b[2m183\x1b[0m │     return `${hours} jam yang lalu`\n \x1b[2m184\x1b[0m │   } else if (diffInSeconds < 604800) {\n \x1b[2m185\x1b[0m │     const days = Math.floor(diffInSeconds / 86400)\n \x1b[2m186\x1b[0m │     return `${days} hari yang lalu`\n \x1b[2m187\x1b[0m │   } else {\n \x1b[2m188\x1b[0m │     return formatDate(dateObj)\n \x1b[2m189\x1b[0m │   }\n \x1b[2m190\x1b[0m │ }\n \x1b[2m191\x1b[0m │ \n \x1b[2m192\x1b[0m │ export function slugify(text: string): string {\n \x1b[2m193\x1b[0m │   return text\n \x1b[2m194\x1b[0m │     .toLowerCase()\n \x1b[2m195\x1b[0m │     .replace(/[^\\w\\s-]/g, '')\n \x1b[2m196\x1b[0m │     .replace(/[\\s_-]+/g, '-')\n \x1b[2m197\x1b[0m │     .replace(/^-+|-+$/g, '')\n \x1b[2m198\x1b[0m │ }\n \x1b[2m199\x1b[0m │ \n \x1b[2m200\x1b[0m │ export function generateId(): string {\n     \xb7 \x1b[38;2;30;201;212m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`generateId` redefined here\x1b[0m\x1b[0m\n \x1b[2m201\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m202\x1b[0m │ }\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `truncateText` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:105:1]\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n     \xb7 \x1b[38;2;246;87;248m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `truncateText` here\x1b[0m\x1b[0m\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n \x1b[2m175\x1b[0m │ \n \x1b[2m176\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m177\x1b[0m │     return 'Baru saja'\n \x1b[2m178\x1b[0m │   } else if (diffInSeconds < 3600) {\n \x1b[2m179\x1b[0m │     const minutes = Math.floor(diffInSeconds / 60)\n \x1b[2m180\x1b[0m │     return `${minutes} menit yang lalu`\n \x1b[2m181\x1b[0m │   } else if (diffInSeconds < 86400) {\n \x1b[2m182\x1b[0m │     const hours = Math.floor(diffInSeconds / 3600)\n \x1b[2m183\x1b[0m │     return `${hours} jam yang lalu`\n \x1b[2m184\x1b[0m │   } else if (diffInSeconds < 604800) {\n \x1b[2m185\x1b[0m │     const days = Math.floor(diffInSeconds / 86400)\n \x1b[2m186\x1b[0m │     return `${days} hari yang lalu`\n \x1b[2m187\x1b[0m │   } else {\n \x1b[2m188\x1b[0m │     return formatDate(dateObj)\n \x1b[2m189\x1b[0m │   }\n \x1b[2m190\x1b[0m │ }\n \x1b[2m191\x1b[0m │ \n \x1b[2m192\x1b[0m │ export function slugify(text: string): string {\n \x1b[2m193\x1b[0m │   return text\n \x1b[2m194\x1b[0m │     .toLowerCase()\n \x1b[2m195\x1b[0m │     .replace(/[^\\w\\s-]/g, '')\n \x1b[2m196\x1b[0m │     .replace(/[\\s_-]+/g, '-')\n \x1b[2m197\x1b[0m │     .replace(/^-+|-+$/g, '')\n \x1b[2m198\x1b[0m │ }\n \x1b[2m199\x1b[0m │ \n \x1b[2m200\x1b[0m │ export function generateId(): string {\n \x1b[2m201\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m202\x1b[0m │ }\n \x1b[2m203\x1b[0m │ \n \x1b[2m204\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n     \xb7 \x1b[38;2;30;201;212m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`truncateText` redefined here\x1b[0m\x1b[0m\n \x1b[2m205\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m206\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m207\x1b[0m │ }\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `isValidEmail` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:116:1]\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n     \xb7 \x1b[38;2;246;87;248m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `isValidEmail` here\x1b[0m\x1b[0m\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n \x1b[2m175\x1b[0m │ \n \x1b[2m176\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m177\x1b[0m │     return 'Baru saja'\n \x1b[2m178\x1b[0m │   } else if (diffInSeconds < 3600) {\n \x1b[2m179\x1b[0m │     const minutes = Math.floor(diffInSeconds / 60)\n \x1b[2m180\x1b[0m │     return `${minutes} menit yang lalu`\n \x1b[2m181\x1b[0m │   } else if (diffInSeconds < 86400) {\n \x1b[2m182\x1b[0m │     const hours = Math.floor(diffInSeconds / 3600)\n \x1b[2m183\x1b[0m │     return `${hours} jam yang lalu`\n \x1b[2m184\x1b[0m │   } else if (diffInSeconds < 604800) {\n \x1b[2m185\x1b[0m │     const days = Math.floor(diffInSeconds / 86400)\n \x1b[2m186\x1b[0m │     return `${days} hari yang lalu`\n \x1b[2m187\x1b[0m │   } else {\n \x1b[2m188\x1b[0m │     return formatDate(dateObj)\n \x1b[2m189\x1b[0m │   }\n \x1b[2m190\x1b[0m │ }\n \x1b[2m191\x1b[0m │ \n \x1b[2m192\x1b[0m │ export function slugify(text: string): string {\n \x1b[2m193\x1b[0m │   return text\n \x1b[2m194\x1b[0m │     .toLowerCase()\n \x1b[2m195\x1b[0m │     .replace(/[^\\w\\s-]/g, '')\n \x1b[2m196\x1b[0m │     .replace(/[\\s_-]+/g, '-')\n \x1b[2m197\x1b[0m │     .replace(/^-+|-+$/g, '')\n \x1b[2m198\x1b[0m │ }\n \x1b[2m199\x1b[0m │ \n \x1b[2m200\x1b[0m │ export function generateId(): string {\n \x1b[2m201\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m202\x1b[0m │ }\n \x1b[2m203\x1b[0m │ \n \x1b[2m204\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m205\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m206\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m207\x1b[0m │ }\n \x1b[2m208\x1b[0m │ \n \x1b[2m209\x1b[0m │ export function isValidEmail(email: string): boolean {\n     \xb7 \x1b[38;2;30;201;212m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`isValidEmail` redefined here\x1b[0m\x1b[0m\n \x1b[2m210\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m211\x1b[0m │   return emailRegex.test(email)\n \x1b[2m212\x1b[0m │ }\n     ╰────\n")}};var n=require("../../../../../webpack-runtime.js");n.C(m);var e=m=>n(n.s=m),t=n.X(0,[1638,6206,9155],()=>e(76100));module.exports=t})();