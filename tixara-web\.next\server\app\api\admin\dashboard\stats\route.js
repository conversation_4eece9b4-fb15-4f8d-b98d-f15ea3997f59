"use strict";(()=>{var e={};e.id=9358,e.ids=[9358],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},18757:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>v,patchFetch:()=>A,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>b});var a={};r.r(a),r.d(a,{GET:()=>c});var s=r(95419),i=r(69108),n=r(99678),o=r(78070),u=r(81355),d=r(3205),p=r(3214),l=r(53524);async function c(e){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user||e.user.role!==l.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let t=new Date,r=new Date(t.getFullYear(),t.getMonth(),1),a=new Date(t.getFullYear(),t.getMonth()-1,1),s=new Date(t.getFullYear(),t.getMonth(),0),[i,n,c,m,g,h,w,x,b,v,A]=await Promise.all([p.prisma.user.count(),p.prisma.event.count(),p.prisma.ticket.count({where:{status:"ACTIVE"}}),p.prisma.transaction.aggregate({where:{status:"SUCCESS"},_sum:{amount:!0}}),p.prisma.badgeSubscription.count({where:{endDate:{gt:t},status:"ACTIVE"}}),p.prisma.user.count({where:{role:l.UserRole.ORGANIZER,isVerified:!1}}),p.prisma.transaction.count({where:{createdAt:{gte:new Date(t.getTime()-864e5)}}}),p.prisma.user.count({where:{createdAt:{gte:a,lte:s}}}),p.prisma.event.count({where:{createdAt:{gte:a,lte:s}}}),p.prisma.ticket.count({where:{createdAt:{gte:a,lte:s},status:"ACTIVE"}}),p.prisma.transaction.aggregate({where:{createdAt:{gte:a,lte:s},status:"SUCCESS"},_sum:{amount:!0}})]),f=(e,t)=>0===t?0:Math.round((e-t)/t*100),q=await p.prisma.user.count({where:{createdAt:{gte:r}}}),E=await p.prisma.event.count({where:{createdAt:{gte:r}}}),y=await p.prisma.ticket.count({where:{createdAt:{gte:r},status:"ACTIVE"}}),S=await p.prisma.transaction.aggregate({where:{createdAt:{gte:r},status:"SUCCESS"},_sum:{amount:!0}}),j="healthy",C=await p.prisma.transaction.count({where:{status:"FAILED",createdAt:{gte:new Date(t.getTime()-36e5)}}});C>10&&(j="warning"),C>50&&(j="error");let T={totalUsers:i,totalEvents:n,totalTicketsSold:c,totalRevenue:m._sum.amount||0,activeSubscriptions:g,pendingVerifications:h,recentTransactions:w,systemStatus:j,growth:{users:f(q,x),events:f(E,b),tickets:f(y,v),revenue:f(S._sum.amount||0,A._sum.amount||0)}};return o.Z.json(T)}catch(e){return console.error("Error fetching dashboard stats:",e),o.Z.json({error:"Internal server error"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/dashboard/stats/route",pathname:"/api/admin/dashboard/stats",filename:"route",bundlePath:"app/api/admin/dashboard/stats/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:w,headerHooks:x,staticGenerationBailout:b}=m,v="/api/admin/dashboard/stats/route";function A(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:h})}},3205:(e,t,r)=>{r.d(t,{Lz:()=>u});var a=r(65822),s=r(86485),i=r(98432),n=r.n(i),o=r(3214);r(53524);let u={adapter:(0,a.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:r,session:a})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===r&&a&&(e={...e,...a}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,r)=>{r.d(t,{prisma:()=>s});var a=r(53524);let s=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,9155],()=>r(18757));module.exports=a})();