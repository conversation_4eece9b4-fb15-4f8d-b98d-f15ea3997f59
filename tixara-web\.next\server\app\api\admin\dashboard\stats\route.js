"use strict";(()=>{var e={};e.id=9358,e.ids=[9358],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},18757:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>E,patchFetch:()=>b,requestAsyncStorage:()=>O,routeModule:()=>l,serverHooks:()=>p,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>w});var o={};r.r(o),r.d(o,{GET:()=>d});var n=r(95419),a=r(69108),i=r(99678),s=r(78070),u=r(81355),c=r(53524);async function d(e){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==c.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let t=new Date,r=new Date(t.getFullYear(),t.getMonth(),1),o=new Date(t.getFullYear(),t.getMonth()-1,1),n=new Date(t.getFullYear(),t.getMonth(),0),[a,i,d,l,O,m,p,h,w,E,b]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.count(),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.count(),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{status:"ACTIVE"}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).transaction.aggregate({where:{status:"SUCCESS"},_sum:{amount:!0}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.count({where:{endDate:{gt:t},status:"ACTIVE"}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.count({where:{role:c.UserRole.ORGANIZER,isVerified:!1}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).transaction.count({where:{createdAt:{gte:new Date(t.getTime()-864e5)}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.count({where:{createdAt:{gte:o,lte:n}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.count({where:{createdAt:{gte:o,lte:n}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{createdAt:{gte:o,lte:n},status:"ACTIVE"}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).transaction.aggregate({where:{createdAt:{gte:o,lte:n},status:"SUCCESS"},_sum:{amount:!0}})]),f=(e,t)=>0===t?0:Math.round((e-t)/t*100),D=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.count({where:{createdAt:{gte:r}}}),U=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.count({where:{createdAt:{gte:r}}}),_=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{createdAt:{gte:r},status:"ACTIVE"}}),g=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).transaction.aggregate({where:{createdAt:{gte:r},status:"SUCCESS"},_sum:{amount:!0}}),N="healthy",v=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).transaction.count({where:{status:"FAILED",createdAt:{gte:new Date(t.getTime()-36e5)}}});v>10&&(N="warning"),v>50&&(N="error");let C={totalUsers:a,totalEvents:i,totalTicketsSold:d,totalRevenue:l._sum.amount||0,activeSubscriptions:O,pendingVerifications:m,recentTransactions:p,systemStatus:N,growth:{users:f(D,h),events:f(U,w),tickets:f(_,E),revenue:f(g._sum.amount||0,b._sum.amount||0)}};return s.Z.json(C)}catch(e){return console.error("Error fetching dashboard stats:",e),s.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/admin/dashboard/stats/route",pathname:"/api/admin/dashboard/stats",filename:"route",bundlePath:"app/api/admin/dashboard/stats/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:O,staticGenerationAsyncStorage:m,serverHooks:p,headerHooks:h,staticGenerationBailout:w}=l,E="/api/admin/dashboard/stats/route";function b(){return(0,i.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:m})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[1638,6206,1355],()=>r(18757));module.exports=o})();