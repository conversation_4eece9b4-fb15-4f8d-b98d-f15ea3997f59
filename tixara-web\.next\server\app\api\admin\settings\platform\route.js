"use strict";(()=>{var e={};e.id=1390,e.ids=[1390],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},60333:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>N,originalPathname:()=>y,patchFetch:()=>S,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>h,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>R});var t={};r.r(t),r.d(t,{GET:()=>d,POST:()=>c});var i=r(95419),s=r(69108),n=r(99678),o=r(78070),l=r(81355),u=r(3205),p=r(3214),m=r(53524);async function d(e){try{let e=await (0,l.getServerSession)(u.Lz);if(!e?.user||e.user.role!==m.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let a=await p.prisma.platformSetting.findMany({select:{key:!0,value:!0,type:!0}}),r={};return a.forEach(e=>{let a=e.value;switch(e.type){case"BOOLEAN":a="true"===a;break;case"NUMBER":a=parseFloat(a);break;case"INTEGER":a=parseInt(a);break;case"JSON":try{a=JSON.parse(a)}catch{a=null}}r[e.key]=a}),o.Z.json(r)}catch(e){return console.error("Error fetching platform settings:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function c(e){try{let a=await (0,l.getServerSession)(u.Lz);if(!a?.user||a.user.role!==m.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let r=await e.json(),t={platformName:"STRING",platformDescription:"STRING",platformUrl:"STRING",supportEmail:"STRING",maintenanceMode:"BOOLEAN",maintenanceMessage:"STRING",defaultCommissionRate:"NUMBER",defaultTaxRate:"NUMBER",withdrawalFee:"INTEGER",minimumWithdrawal:"INTEGER",allowUserRegistration:"BOOLEAN",requireEmailVerification:"BOOLEAN",allowOrganizerSelfVerification:"BOOLEAN",enableNotifications:"BOOLEAN",primaryColor:"STRING",secondaryColor:"STRING",logoUrl:"STRING",faviconUrl:"STRING",metaTitle:"STRING",metaDescription:"STRING",metaKeywords:"STRING",facebookUrl:"STRING",twitterUrl:"STRING",instagramUrl:"STRING",linkedinUrl:"STRING"};return await p.prisma.$transaction(async e=>{for(let[a,i]of Object.entries(r))if(t[a]){let r=String(i);"JSON"===t[a]&&(r=JSON.stringify(i)),await e.platformSetting.upsert({where:{key:a},update:{value:r,updatedAt:new Date},create:{key:a,value:r,type:t[a],description:{platformName:"Nama platform",platformDescription:"Deskripsi platform",platformUrl:"URL utama platform",supportEmail:"Email support customer service",maintenanceMode:"Mode maintenance platform",maintenanceMessage:"Pesan yang ditampilkan saat maintenance",defaultCommissionRate:"Rate komisi default untuk organizer (%)",defaultTaxRate:"Rate pajak default (%)",withdrawalFee:"Biaya withdrawal dalam rupiah",minimumWithdrawal:"Minimum amount untuk withdrawal",allowUserRegistration:"Izinkan registrasi user baru",requireEmailVerification:"Wajibkan verifikasi email",allowOrganizerSelfVerification:"Izinkan organizer verifikasi sendiri",enableNotifications:"Aktifkan sistem notifikasi",primaryColor:"Warna primary theme",secondaryColor:"Warna secondary theme",logoUrl:"URL logo platform",faviconUrl:"URL favicon platform",metaTitle:"Meta title untuk SEO",metaDescription:"Meta description untuk SEO",metaKeywords:"Meta keywords untuk SEO",facebookUrl:"URL halaman Facebook",twitterUrl:"URL halaman Twitter",instagramUrl:"URL halaman Instagram",linkedinUrl:"URL halaman LinkedIn"}[a]||a}})}}),o.Z.json({message:"Platform settings updated successfully"})}catch(e){return console.error("Error updating platform settings:",e),o.Z.json({error:"Internal server error"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/settings/platform/route",pathname:"/api/admin/settings/platform",filename:"route",bundlePath:"app/api/admin/settings/platform/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\settings\\platform\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:g,staticGenerationAsyncStorage:w,serverHooks:h,headerHooks:N,staticGenerationBailout:R}=f,y="/api/admin/settings/platform/route";function S(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:w})}},3205:(e,a,r)=>{r.d(a,{Lz:()=>l});var t=r(65822),i=r(86485),s=r(98432),n=r.n(s),o=r(3214);r(53524);let l={adapter:(0,t.N)(o.prisma),providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:r,session:t})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===r&&t&&(e={...e,...t}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,r)=>{r.d(a,{prisma:()=>i});var t=r(53524);let i=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var a=require("../../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[1638,6206,9155],()=>r(60333));module.exports=t})();