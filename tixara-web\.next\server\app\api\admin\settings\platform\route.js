"use strict";(()=>{var e={};e.id=1390,e.ids=[1390],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},60333:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>O,originalPathname:()=>U,patchFetch:()=>h,requestAsyncStorage:()=>d,routeModule:()=>c,serverHooks:()=>N,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>g});var a={};t.r(a),t.d(a,{GET:()=>m,POST:()=>p});var i=t(95419),o=t(69108),n=t(99678),s=t(78070),l=t(81355),u=t(53524);async function m(e){try{let e=await (0,l.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==u.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).platformSetting.findMany({select:{key:!0,value:!0,type:!0}}),t={};return r.forEach(e=>{let r=e.value;switch(e.type){case"BOOLEAN":r="true"===r;break;case"NUMBER":r=parseFloat(r);break;case"INTEGER":r=parseInt(r);break;case"JSON":try{r=JSON.parse(r)}catch{r=null}}t[e.key]=r}),s.Z.json(t)}catch(e){return console.error("Error fetching platform settings:",e),s.Z.json({error:"Internal server error"},{status:500})}}async function p(e){try{let r=await (0,l.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||r.user.role!==u.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let t=await e.json(),a={platformName:"STRING",platformDescription:"STRING",platformUrl:"STRING",supportEmail:"STRING",maintenanceMode:"BOOLEAN",maintenanceMessage:"STRING",defaultCommissionRate:"NUMBER",defaultTaxRate:"NUMBER",withdrawalFee:"INTEGER",minimumWithdrawal:"INTEGER",allowUserRegistration:"BOOLEAN",requireEmailVerification:"BOOLEAN",allowOrganizerSelfVerification:"BOOLEAN",enableNotifications:"BOOLEAN",primaryColor:"STRING",secondaryColor:"STRING",logoUrl:"STRING",faviconUrl:"STRING",metaTitle:"STRING",metaDescription:"STRING",metaKeywords:"STRING",facebookUrl:"STRING",twitterUrl:"STRING",instagramUrl:"STRING",linkedinUrl:"STRING"};return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{for(let[r,i]of Object.entries(t))if(a[r]){let t=String(i);"JSON"===a[r]&&(t=JSON.stringify(i)),await e.platformSetting.upsert({where:{key:r},update:{value:t,updatedAt:new Date},create:{key:r,value:t,type:a[r],description:{platformName:"Nama platform",platformDescription:"Deskripsi platform",platformUrl:"URL utama platform",supportEmail:"Email support customer service",maintenanceMode:"Mode maintenance platform",maintenanceMessage:"Pesan yang ditampilkan saat maintenance",defaultCommissionRate:"Rate komisi default untuk organizer (%)",defaultTaxRate:"Rate pajak default (%)",withdrawalFee:"Biaya withdrawal dalam rupiah",minimumWithdrawal:"Minimum amount untuk withdrawal",allowUserRegistration:"Izinkan registrasi user baru",requireEmailVerification:"Wajibkan verifikasi email",allowOrganizerSelfVerification:"Izinkan organizer verifikasi sendiri",enableNotifications:"Aktifkan sistem notifikasi",primaryColor:"Warna primary theme",secondaryColor:"Warna secondary theme",logoUrl:"URL logo platform",faviconUrl:"URL favicon platform",metaTitle:"Meta title untuk SEO",metaDescription:"Meta description untuk SEO",metaKeywords:"Meta keywords untuk SEO",facebookUrl:"URL halaman Facebook",twitterUrl:"URL halaman Twitter",instagramUrl:"URL halaman Instagram",linkedinUrl:"URL halaman LinkedIn"}[r]||r}})}}),s.Z.json({message:"Platform settings updated successfully"})}catch(e){return console.error("Error updating platform settings:",e),s.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let c=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/admin/settings/platform/route",pathname:"/api/admin/settings/platform",filename:"route",bundlePath:"app/api/admin/settings/platform/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\settings\\platform\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:f,serverHooks:N,headerHooks:O,staticGenerationBailout:g}=c,U="/api/admin/settings/platform/route";function h(){return(0,n.patchFetch)({serverHooks:N,staticGenerationAsyncStorage:f})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,1355],()=>t(60333));module.exports=a})();