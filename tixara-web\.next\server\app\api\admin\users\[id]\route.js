"use strict";(()=>{var e={};e.id=1317,e.ids=[1317],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},12519:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>w,originalPathname:()=>v,patchFetch:()=>D,requestAsyncStorage:()=>h,routeModule:()=>f,serverHooks:()=>m,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>U});var n={};t.r(n),t.d(n,{DELETE:()=>p,GET:()=>d,PATCH:()=>l});var o=t(95419),s=t(69108),a=t(99678),i=t(78070),u=t(81355),c=t(53524);async function d(e,{params:r}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==c.UserRole.ADMIN)return i.Z.json({error:"Unauthorized"},{status:401});let{id:t}=r,n=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{id:t},include:{events:{select:{id:!0,title:!0,status:!0,createdAt:!0,_count:{select:{tickets:!0}}}},tickets:{select:{id:!0,status:!0,createdAt:!0,event:{select:{title:!0}}}},badgeSubscription:{select:{badge:!0,status:!0,startDate:!0,endDate:!0}},transactions:{select:{id:!0,amount:!0,type:!0,status:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10},_count:{select:{events:!0,tickets:!0,transactions:!0,notifications:!0}}}});if(!n)return i.Z.json({error:"User not found"},{status:404});return i.Z.json(n)}catch(e){return console.error("Error fetching user:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function l(e,{params:r}){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user||t.user.role!==c.UserRole.ADMIN)return i.Z.json({error:"Unauthorized"},{status:401});let{id:n}=r,o=await e.json();if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{id:n}}))return i.Z.json({error:"User not found"},{status:404});let s=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.update({where:{id:n},data:{...o,updatedAt:new Date},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,avatar:!0,uangtixBalance:!0,updatedAt:!0}});return i.Z.json(s)}catch(e){return console.error("Error updating user:",e),i.Z.json({error:"Internal server error"},{status:500})}}async function p(e,{params:r}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||e.user.role!==c.UserRole.ADMIN)return i.Z.json({error:"Unauthorized"},{status:401});let{id:t}=r,n=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{id:t},include:{_count:{select:{events:!0,tickets:!0,transactions:!0}}}});if(!n)return i.Z.json({error:"User not found"},{status:404});if(n.role===c.UserRole.ADMIN)return i.Z.json({error:"Cannot delete admin users"},{status:400});if(n._count.events>0||n._count.tickets>0)return i.Z.json({error:"Cannot delete user with active events or tickets"},{status:400});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{await e.notification.deleteMany({where:{userId:t}}),await e.badgeSubscription.deleteMany({where:{userId:t}}),await e.transaction.deleteMany({where:{userId:t}}),await e.user.delete({where:{id:t}})}),i.Z.json({message:"User deleted successfully"})}catch(e){return console.error("Error deleting user:",e),i.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let f=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/users/[id]/route",pathname:"/api/admin/users/[id]",filename:"route",bundlePath:"app/api/admin/users/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\users\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:h,staticGenerationAsyncStorage:O,serverHooks:m,headerHooks:w,staticGenerationBailout:U}=f,v="/api/admin/users/[id]/route";function D(){return(0,a.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:O})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[1638,6206,1355],()=>t(12519));module.exports=n})();