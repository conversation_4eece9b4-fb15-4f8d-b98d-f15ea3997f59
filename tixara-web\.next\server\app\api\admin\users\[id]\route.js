"use strict";(()=>{var e={};e.id=1317,e.ids=[1317],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},12519:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>x,originalPathname:()=>b,patchFetch:()=>j,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>y});var a={};t.r(a),t.d(a,{DELETE:()=>w,GET:()=>p,PATCH:()=>m});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),u=t(81355),d=t(3205),l=t(3214),c=t(53524);async function p(e,{params:r}){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user||e.user.role!==c.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{id:t}=r,a=await l.prisma.user.findUnique({where:{id:t},include:{events:{select:{id:!0,title:!0,status:!0,createdAt:!0,_count:{select:{tickets:!0}}}},tickets:{select:{id:!0,status:!0,createdAt:!0,event:{select:{title:!0}}}},badgeSubscription:{select:{badge:!0,status:!0,startDate:!0,endDate:!0}},transactions:{select:{id:!0,amount:!0,type:!0,status:!0,createdAt:!0},orderBy:{createdAt:"desc"},take:10},_count:{select:{events:!0,tickets:!0,transactions:!0,notifications:!0}}}});if(!a)return o.Z.json({error:"User not found"},{status:404});return o.Z.json(a)}catch(e){return console.error("Error fetching user:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let t=await (0,u.getServerSession)(d.Lz);if(!t?.user||t.user.role!==c.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{id:a}=r,s=await e.json();if(!await l.prisma.user.findUnique({where:{id:a}}))return o.Z.json({error:"User not found"},{status:404});let i=await l.prisma.user.update({where:{id:a},data:{...s,updatedAt:new Date},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,avatar:!0,uangtixBalance:!0,updatedAt:!0}});return o.Z.json(i)}catch(e){return console.error("Error updating user:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function w(e,{params:r}){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user||e.user.role!==c.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{id:t}=r,a=await l.prisma.user.findUnique({where:{id:t},include:{_count:{select:{events:!0,tickets:!0,transactions:!0}}}});if(!a)return o.Z.json({error:"User not found"},{status:404});if(a.role===c.UserRole.ADMIN)return o.Z.json({error:"Cannot delete admin users"},{status:400});if(a._count.events>0||a._count.tickets>0)return o.Z.json({error:"Cannot delete user with active events or tickets"},{status:400});return await l.prisma.$transaction(async e=>{await e.notification.deleteMany({where:{userId:t}}),await e.badgeSubscription.deleteMany({where:{userId:t}}),await e.transaction.deleteMany({where:{userId:t}}),await e.user.delete({where:{id:t}})}),o.Z.json({message:"User deleted successfully"})}catch(e){return console.error("Error deleting user:",e),o.Z.json({error:"Internal server error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/users/[id]/route",pathname:"/api/admin/users/[id]",filename:"route",bundlePath:"app/api/admin/users/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\users\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:f,serverHooks:v,headerHooks:x,staticGenerationBailout:y}=g,b="/api/admin/users/[id]/route";function j(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:f})}},3205:(e,r,t)=>{t.d(r,{Lz:()=>u});var a=t(65822),s=t(86485),i=t(98432),n=t.n(i),o=t(3214);t(53524);let u={adapter:(0,a.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:t,session:a})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===t&&a&&(e={...e,...a}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,t)=>{t.d(r,{prisma:()=>s});var a=t(53524);let s=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,9155],()=>t(12519));module.exports=a})();