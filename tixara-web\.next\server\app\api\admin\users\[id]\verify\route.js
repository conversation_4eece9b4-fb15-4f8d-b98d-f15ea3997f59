"use strict";(()=>{var e={};e.id=7461,e.ids=[7461],e.modules={53524:e=>{e.exports=require("@prisma/client")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},13918:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>v,originalPathname:()=>x,patchFetch:()=>O,requestAsyncStorage:()=>c,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>h});var i={};t.r(i),t.d(i,{PATCH:()=>l});var a=t(95419),n=t(69108),o=t(99678),s=t(78070),u=t(81355),d=t(53524);async function l(e,{params:r}){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user||t.user.role!==d.UserRole.ADMIN)return s.Z.json({error:"Unauthorized"},{status:401});let{id:i}=r,{verified:a}=await e.json();if("boolean"!=typeof a)return s.Z.json({error:"Invalid verified value"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{id:i}}))return s.Z.json({error:"User not found"},{status:404});let n=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.update({where:{id:i},data:{isVerified:a},select:{id:!0,name:!0,email:!0,role:!0,isVerified:!0}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:i,title:a?"Akun Terverifikasi":"Verifikasi Dibatalkan",message:a?"Selamat! Akun Anda telah berhasil diverifikasi oleh admin.":"Verifikasi akun Anda telah dibatalkan. Silakan hubungi admin untuk informasi lebih lanjut.",type:"SYSTEM_ANNOUNCEMENT"}}),s.Z.json({message:`User ${a?"verified":"unverified"} successfully`,user:n})}catch(e){return console.error("Error updating user verification:",e),s.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/admin/users/[id]/verify/route",pathname:"/api/admin/users/[id]/verify",filename:"route",bundlePath:"app/api/admin/users/[id]/verify/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\users\\[id]\\verify\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:c,staticGenerationAsyncStorage:f,serverHooks:m,headerHooks:v,staticGenerationBailout:h}=p,x="/api/admin/users/[id]/verify/route";function O(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:f})}}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[1638,6206,1355],()=>t(13918));module.exports=i})();