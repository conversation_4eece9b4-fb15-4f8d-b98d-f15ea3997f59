"use strict";(()=>{var e={};e.id=7461,e.ids=[7461],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},13918:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>v,originalPathname:()=>x,patchFetch:()=>b,requestAsyncStorage:()=>f,routeModule:()=>c,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>w});var i={};a.r(i),a.d(i,{PATCH:()=>m});var t=a(95419),s=a(69108),n=a(99678),o=a(78070),u=a(81355),d=a(3205),l=a(3214),p=a(53524);async function m(e,{params:r}){try{let a=await (0,u.getServerSession)(d.Lz);if(!a?.user||a.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{id:i}=r,{verified:t}=await e.json();if("boolean"!=typeof t)return o.Z.json({error:"Invalid verified value"},{status:400});if(!await l.prisma.user.findUnique({where:{id:i}}))return o.Z.json({error:"User not found"},{status:404});let s=await l.prisma.user.update({where:{id:i},data:{isVerified:t},select:{id:!0,name:!0,email:!0,role:!0,isVerified:!0}});return await l.prisma.notification.create({data:{userId:i,title:t?"Akun Terverifikasi":"Verifikasi Dibatalkan",message:t?"Selamat! Akun Anda telah berhasil diverifikasi oleh admin.":"Verifikasi akun Anda telah dibatalkan. Silakan hubungi admin untuk informasi lebih lanjut.",type:"SYSTEM_ANNOUNCEMENT"}}),o.Z.json({message:`User ${t?"verified":"unverified"} successfully`,user:s})}catch(e){return console.error("Error updating user verification:",e),o.Z.json({error:"Internal server error"},{status:500})}}let c=new t.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/admin/users/[id]/verify/route",pathname:"/api/admin/users/[id]/verify",filename:"route",bundlePath:"app/api/admin/users/[id]/verify/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\users\\[id]\\verify\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:f,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:v,staticGenerationBailout:w}=c,x="/api/admin/users/[id]/verify/route";function b(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>u});var i=a(65822),t=a(86485),s=a(98432),n=a.n(s),o=a(3214);a(53524);let u={adapter:(0,i.N)(o.prisma),providers:[(0,t.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:i})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&i&&(e={...e,...i}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>t});var i=a(53524);let t=globalThis.prisma??new i.PrismaClient({log:["error"]})}};var r=require("../../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),i=r.X(0,[1638,6206,9155],()=>a(13918));module.exports=i})();