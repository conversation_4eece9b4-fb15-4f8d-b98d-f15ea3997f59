"use strict";(()=>{var e={};e.id=2628,e.ids=[2628],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17907:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>x,originalPathname:()=>b,patchFetch:()=>q,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>w,staticGenerationBailout:()=>v});var t={};a.r(t),a.d(t,{GET:()=>c,POST:()=>m});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),l=a(81355),u=a(3205),d=a(3214),p=a(53524);async function c(e){try{let r=await (0,l.getServerSession)(u.Lz);if(!r?.user||r.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),t=parseInt(a.get("page")||"1"),s=parseInt(a.get("limit")||"20"),i=a.get("search")||"",n=a.get("role")||"all",c=a.get("badge")||"all",m=a.get("verified")||"all",g={};i&&(g.OR=[{name:{contains:i,mode:"insensitive"}},{email:{contains:i,mode:"insensitive"}}]),"all"!==n&&(g.role=n),"all"!==c&&(g.badge=c),"all"!==m&&(g.isVerified="verified"===m);let[h,w]=await Promise.all([d.prisma.user.findMany({where:g,skip:(t-1)*s,take:s,orderBy:{createdAt:"desc"},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,avatar:!0,uangtixBalance:!0,createdAt:!0,lastLoginAt:!0,_count:{select:{events:!0,tickets:!0}}}}),d.prisma.user.count({where:g})]);return o.Z.json({users:h,totalPages:Math.ceil(w/s),currentPage:t,totalCount:w})}catch(e){return console.error("Error fetching users:",e),o.Z.json({error:"Internal server error"},{status:500})}}async function m(e){try{let r=await (0,l.getServerSession)(u.Lz);if(!r?.user||r.user.role!==p.UserRole.ADMIN)return o.Z.json({error:"Unauthorized"},{status:401});let{name:t,email:s,password:i,role:n,badge:c}=await e.json();if(!t||!s||!i||!n)return o.Z.json({error:"Missing required fields"},{status:400});if(await d.prisma.user.findUnique({where:{email:s}}))return o.Z.json({error:"Email already exists"},{status:400});let m=a(98432),g=await m.hash(i,12),h=await d.prisma.user.create({data:{name:t,email:s,password:g,role:n,badge:c||"BRONZE",isVerified:n===p.UserRole.ADMIN||n===p.UserRole.STAFF},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,createdAt:!0}});return o.Z.json(h,{status:201})}catch(e){return console.error("Error creating user:",e),o.Z.json({error:"Internal server error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:h,staticGenerationAsyncStorage:w,serverHooks:f,headerHooks:x,staticGenerationBailout:v}=g,b="/api/admin/users/route";function q(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:w})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>l});var t=a(65822),s=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let l={adapter:(0,t.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:t})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&t&&(e={...e,...t}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>s});var t=a(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[1638,6206,9155],()=>a(17907));module.exports=t})();