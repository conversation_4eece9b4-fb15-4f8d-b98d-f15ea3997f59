"use strict";(()=>{var e={};e.id=2628,e.ids=[2628],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17907:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>g,originalPathname:()=>x,patchFetch:()=>v,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>O,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>h});var a={};t.r(a),t.d(a,{GET:()=>d,POST:()=>c});var o=t(95419),i=t(69108),s=t(99678),n=t(78070),u=t(81355),l=t(53524);async function d(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||r.user.role!==l.UserRole.ADMIN)return n.Z.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),a=parseInt(t.get("page")||"1"),o=parseInt(t.get("limit")||"20"),i=t.get("search")||"",s=t.get("role")||"all",d=t.get("badge")||"all",c=t.get("verified")||"all",p={};i&&(p.OR=[{name:{contains:i,mode:"insensitive"}},{email:{contains:i,mode:"insensitive"}}]),"all"!==s&&(p.role=s),"all"!==d&&(p.badge=d),"all"!==c&&(p.isVerified="verified"===c);let[m,f]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findMany({where:p,skip:(a-1)*o,take:o,orderBy:{createdAt:"desc"},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,avatar:!0,uangtixBalance:!0,createdAt:!0,lastLoginAt:!0,_count:{select:{events:!0,tickets:!0}}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.count({where:p})]);return n.Z.json({users:m,totalPages:Math.ceil(f/o),currentPage:a,totalCount:f})}catch(e){return console.error("Error fetching users:",e),n.Z.json({error:"Internal server error"},{status:500})}}async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||r.user.role!==l.UserRole.ADMIN)return n.Z.json({error:"Unauthorized"},{status:401});let{name:a,email:o,password:i,role:s,badge:d}=await e.json();if(!a||!o||!i||!s)return n.Z.json({error:"Missing required fields"},{status:400});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{email:o}}))return n.Z.json({error:"Email already exists"},{status:400});let c=t(98432),p=await c.hash(i,12),m=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.create({data:{name:a,email:o,password:p,role:s,badge:d||"BRONZE",isVerified:s===l.UserRole.ADMIN||s===l.UserRole.STAFF},select:{id:!0,name:!0,email:!0,role:!0,badge:!0,isVerified:!0,createdAt:!0}});return n.Z.json(m,{status:201})}catch(e){return console.error("Error creating user:",e),n.Z.json({error:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new o.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/admin/users/route",pathname:"/api/admin/users",filename:"route",bundlePath:"app/api/admin/users/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\admin\\users\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:f,serverHooks:O,headerHooks:g,staticGenerationBailout:h}=p,x="/api/admin/users/route";function v(){return(0,s.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:f})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,1355],()=>t(17907));module.exports=a})();