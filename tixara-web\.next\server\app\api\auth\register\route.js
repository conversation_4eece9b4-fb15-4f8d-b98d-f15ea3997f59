"use strict";(()=>{var e={};e.id=3002,e.ids=[3002],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},85911:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>P,originalPathname:()=>b,patchFetch:()=>x,requestAsyncStorage:()=>g,routeModule:()=>w,serverHooks:()=>v,staticGenerationAsyncStorage:()=>k,staticGenerationBailout:()=>j});var t={};r.r(t),r.d(t,{POST:()=>f});var i=r(95419),s=r(69108),o=r(99678),n=r(78070),u=r(98432),l=r.n(u),d=r(25252),m=r(52178),p=r(3214),c=r(53524);let h=d.Ry({name:d.Z_().min(2,"Nama minimal 2 karakter"),email:d.Z_().email("Email tidak valid"),password:d.Z_().min(6,"Password minimal 6 karakter"),confirmPassword:d.Z_(),phone:d.Z_().optional(),role:d.jb(c.UserRole).default(c.UserRole.BUYER)}).refine(e=>e.password===e.confirmPassword,{message:"Password tidak sama",path:["confirmPassword"]});async function f(e){try{let a=await e.json(),r=h.parse(a);if(await p.prisma.user.findUnique({where:{email:r.email}}))return n.Z.json({error:"Email sudah terdaftar"},{status:400});let t=await l().hash(r.password,12),i=await p.prisma.user.create({data:{name:r.name,email:r.email,password:t,phone:r.phone,role:r.role},select:{id:!0,name:!0,email:!0,role:!0,isVerified:!0,createdAt:!0}});return await p.prisma.notification.create({data:{userId:i.id,title:"Selamat Datang di TiXara!",message:"Akun Anda berhasil dibuat. Silakan verifikasi email untuk mengaktifkan semua fitur.",type:"SYSTEM_ANNOUNCEMENT"}}),n.Z.json({success:!0,message:"Akun berhasil dibuat",data:i})}catch(e){if(e instanceof m.jm)return n.Z.json({error:"Data tidak valid",details:e.errors},{status:400});return console.error("Registration error:",e),n.Z.json({error:"Terjadi kesalahan server"},{status:500})}}let w=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:g,staticGenerationAsyncStorage:k,serverHooks:v,headerHooks:P,staticGenerationBailout:j}=w,b="/api/auth/register/route";function x(){return(0,o.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:k})}},3214:(e,a,r)=>{r.d(a,{prisma:()=>i});var t=r(53524);let i=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[1638,6206,5252],()=>r(85911));module.exports=t})();