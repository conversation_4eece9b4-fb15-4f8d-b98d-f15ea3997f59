"use strict";(()=>{var e={};e.id=3002,e.ids=[3002],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},85911:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>g,originalPathname:()=>v,patchFetch:()=>E,requestAsyncStorage:()=>w,routeModule:()=>f,serverHooks:()=>O,staticGenerationAsyncStorage:()=>k,staticGenerationBailout:()=>b});var t={};r.r(t),r.d(t,{POST:()=>h});var i=r(95419),o=r(69108),s=r(99678),n=r(78070),u=r(98432),d=r.n(u),l=r(25252),m=r(52178),p=r(53524);!function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let c=l.Ry({name:l.Z_().min(2,"Nama minimal 2 karakter"),email:l.Z_().email("Email tidak valid"),password:l.Z_().min(6,"Password minimal 6 karakter"),confirmPassword:l.Z_(),phone:l.Z_().optional(),role:l.jb(p.UserRole).default(p.UserRole.BUYER)}).refine(e=>e.password===e.confirmPassword,{message:"Password tidak sama",path:["confirmPassword"]});async function h(e){try{let a=await e.json(),r=c.parse(a);if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{email:r.email}}))return n.Z.json({error:"Email sudah terdaftar"},{status:400});let t=await d().hash(r.password,12),i=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.create({data:{name:r.name,email:r.email,password:t,phone:r.phone,role:r.role},select:{id:!0,name:!0,email:!0,role:!0,isVerified:!0,createdAt:!0}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:i.id,title:"Selamat Datang di TiXara!",message:"Akun Anda berhasil dibuat. Silakan verifikasi email untuk mengaktifkan semua fitur.",type:"SYSTEM_ANNOUNCEMENT"}}),n.Z.json({success:!0,message:"Akun berhasil dibuat",data:i})}catch(e){if(e instanceof m.jm)return n.Z.json({error:"Data tidak valid",details:e.errors},{status:400});return console.error("Registration error:",e),n.Z.json({error:"Terjadi kesalahan server"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:w,staticGenerationAsyncStorage:k,serverHooks:O,headerHooks:g,staticGenerationBailout:b}=f,v="/api/auth/register/route";function E(){return(0,s.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:k})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[1638,6206,5252],()=>r(85911));module.exports=t})();