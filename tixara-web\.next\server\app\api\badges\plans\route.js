"use strict";(()=>{var e={};e.id=7064,e.ids=[7064],e.modules={53524:e=>{e.exports=require("@prisma/client")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},51492:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>b,originalPathname:()=>x,patchFetch:()=>f,requestAsyncStorage:()=>d,routeModule:()=>m,serverHooks:()=>g,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>y});var a={};r.r(a),r.d(a,{GET:()=>c});var s=r(95419),i=r(69108),o=r(99678),n=r(78070),l=r(3214),u=r(30746);async function c(e){try{let e=await l.prisma.badgePlan.findMany({where:{isActive:!0},orderBy:[{badge:"asc"}]});if(0===e.length){await l.prisma.badgePlan.createMany({data:u.JB});let e=await l.prisma.badgePlan.findMany({where:{isActive:!0},orderBy:[{badge:"asc"}]});return n.Z.json({success:!0,data:e})}return n.Z.json({success:!0,data:e})}catch(e){return console.error("Get badge plans error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/badges/plans/route",pathname:"/api/badges/plans",filename:"route",bundlePath:"app/api/badges/plans/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\badges\\plans\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:p,serverHooks:g,headerHooks:b,staticGenerationBailout:y}=m,x="/api/badges/plans/route";function f(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:p})}},30746:(e,t,r)=>{r.d(t,{JB:()=>s,JT:()=>i});let a={BRONZE:{name:"Bronze",color:"#CD7F32",bgColor:"bg-amber-100 dark:bg-amber-900/20",textColor:"text-amber-700 dark:text-amber-300",borderColor:"border-amber-300",icon:"\uD83E\uDD49",features:["Akses dasar platform","Buat event unlimited","Template tiket gratis","Support email","Komisi standar"],limits:{maxEvents:null,maxStaff:3,maxTemplates:5,canUseCustomDomain:!1,canUsePremiumTemplates:!1,prioritySupport:!1,analyticsAccess:"basic"}},SILVER:{name:"Silver",color:"#C0C0C0",bgColor:"bg-gray-100 dark:bg-gray-900/20",textColor:"text-gray-700 dark:text-gray-300",borderColor:"border-gray-300",icon:"\uD83E\uDD48",features:["Semua fitur Bronze","Multi-event management","Staff unlimited","Template premium (terbatas)","Analytics dasar","Priority email support"],limits:{maxEvents:null,maxStaff:null,maxTemplates:15,canUseCustomDomain:!1,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"standard"}},GOLD:{name:"Gold",color:"#FFD700",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",textColor:"text-yellow-700 dark:text-yellow-300",borderColor:"border-yellow-300",icon:"\uD83E\uDD47",features:["Semua fitur Silver","Premium templates unlimited","Boost event khusus","Analytics advanced","Custom branding","Priority chat support","Komisi lebih rendah"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"advanced",customBranding:!0,boostDiscount:20}},TITANIUM:{name:"Titanium",color:"#878681",bgColor:"bg-slate-100 dark:bg-slate-900/20",textColor:"text-slate-700 dark:text-slate-300",borderColor:"border-slate-300",icon:"\uD83D\uDC8E",features:["Semua fitur Gold","Semua fitur premium","Priority support 24/7","Dedicated account manager","Custom integrations","White-label options","Komisi terendah","Early access features"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"enterprise",customBranding:!0,boostDiscount:50,dedicatedSupport:!0,whiteLabel:!0}}},s=[{badge:"BRONZE",name:"Bronze Plan",description:"Perfect untuk organizer pemula",features:a.BRONZE.features,monthlyPrice:0,yearlyPrice:0},{badge:"SILVER",name:"Silver Plan",description:"Ideal untuk organizer yang berkembang",features:a.SILVER.features,monthlyPrice:99e3,yearlyPrice:99e4},{badge:"GOLD",name:"Gold Plan",description:"Untuk organizer profesional",features:a.GOLD.features,monthlyPrice:299e3,yearlyPrice:299e4},{badge:"TITANIUM",name:"Titanium Plan",description:"Enterprise solution untuk organizer besar",features:a.TITANIUM.features,monthlyPrice:999e3,yearlyPrice:999e4}];class i{static getBadgeConfig(e){return a[e]}static getBadgeIcon(e){return a[e].icon}static getBadgeName(e){return a[e].name}static getBadgeColor(e){return a[e].color}static getBadgeClasses(e){let t=a[e];return{bg:t.bgColor,text:t.textColor,border:t.borderColor}}static canAccessFeature(e,t){if(!e)return"BRONZE"===t;let r=["BRONZE","SILVER","GOLD","TITANIUM"];return r.indexOf(e)>=r.indexOf(t)}static getFeatureLimits(e){return e?a[e].limits:a.BRONZE.limits}static calculateSubscriptionPrice(e,t){let r=s.find(t=>t.badge===e);return r?"MONTHLY"===t?r.monthlyPrice:r.yearlyPrice:0}static getSubscriptionDiscount(e){return"YEARLY"===e?16.67:0}static isSubscriptionActive(e,t){let r=new Date;return r>=e&&r<=t}static calculateEndDate(e,t){let r=new Date(e);return"MONTHLY"===t?r.setMonth(r.getMonth()+1):r.setFullYear(r.getFullYear()+1),r}static getDaysUntilExpiry(e){let t=new Date;return Math.ceil((e.getTime()-t.getTime())/864e5)}static shouldShowRenewalReminder(e){let t=this.getDaysUntilExpiry(e);return t<=7&&t>0}static getCommissionRate(e){switch(e){case"BRONZE":default:return 5;case"SILVER":return 4;case"GOLD":return 3;case"TITANIUM":return 2}}static getBoostDiscount(e){return this.getFeatureLimits(e).boostDiscount||0}static canUseTemplate(e,t){return!t||this.canAccessFeature(e,t)}static formatBadgeForDisplay(e){if(!e)return"";let t=a[e];return`${t.icon} ${t.name}`}static getBadgeUpgradePath(e){let t=["BRONZE","SILVER","GOLD","TITANIUM"],r=e?t.indexOf(e):-1;return t.slice(r+1)}static getRecommendedBadge(e){return e>=5e7?"TITANIUM":e>=1e7?"GOLD":e>=2e6?"SILVER":"BRONZE"}}},3214:(e,t,r)=>{r.d(t,{prisma:()=>s});var a=r(53524);let s=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206],()=>r(51492));module.exports=a})();