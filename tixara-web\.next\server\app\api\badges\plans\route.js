"use strict";(()=>{var e={};e.id=7064,e.ids=[7064],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51492:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>b,originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>c,routeModule:()=>u,serverHooks:()=>p,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>O});var t={};r.r(t),r.d(t,{GET:()=>d});var n=r(95419),o=r(69108),i=r(99678),s=r(78070);async function d(e){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findMany({where:{isActive:!0},orderBy:[{badge:"asc"}]});if(0===e.length){await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.createMany({data:Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}())});let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findMany({where:{isActive:!0},orderBy:[{badge:"asc"}]});return s.Z.json({success:!0,data:e})}return s.Z.json({success:!0,data:e})}catch(e){return console.error("Get badge plans error:",e),s.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}();let u=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/badges/plans/route",pathname:"/api/badges/plans",filename:"route",bundlePath:"app/api/badges/plans/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\badges\\plans\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:c,staticGenerationAsyncStorage:l,serverHooks:p,headerHooks:b,staticGenerationBailout:O}=u,f="/api/badges/plans/route";function g(){return(0,i.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:l})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[1638,6206],()=>r(51492));module.exports=t})();