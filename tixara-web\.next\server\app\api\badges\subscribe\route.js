"use strict";(()=>{var e={};e.id=195,e.ids=[195,6112],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},45435:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>b,originalPathname:()=>T,patchFetch:()=>x,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>f,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>w});var r={};t.r(r),t.d(r,{POST:()=>p});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),c=t(81355),u=t(3205),l=t(3214),d=t(30746),m=t(76112);async function p(e){try{let a=await (0,c.getServerSession)(u.Lz);if(!a?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{badge:t,type:r,paymentMethod:s="UANGTIX"}=await e.json();if(!t||!r)return o.Z.json({success:!1,message:"Badge dan tipe langganan diperlukan"},{status:400});if("ORGANIZER"!==a.user.role)return o.Z.json({success:!1,message:"Hanya organizer yang dapat berlangganan badge"},{status:403});let i=await l.prisma.badgePlan.findUnique({where:{badge:t}});if(!i||!i.isActive)return o.Z.json({success:!1,message:"Badge plan tidak ditemukan"},{status:404});if(await l.prisma.badgeSubscription.findFirst({where:{userId:a.user.id,isActive:!0,endDate:{gte:new Date}}}))return o.Z.json({success:!1,message:"Anda sudah memiliki langganan badge aktif"},{status:400});let n=d.JT.calculateSubscriptionPrice(t,r);if(0===n&&"BRONZE"===t){let e=new Date,s=new Date;s.setFullYear(s.getFullYear()+10);let n=await l.prisma.badgeSubscription.create({data:{userId:a.user.id,planId:i.id,badge:t,type:r,startDate:e,endDate:s,price:0,isActive:!0}});return await l.prisma.user.update({where:{id:a.user.id},data:{badge:t}}),o.Z.json({success:!0,message:"Berhasil berlangganan Bronze (gratis)",data:n})}if("UANGTIX"===s){if(await m.UangtiXWallet.getBalance(a.user.id)<n)return o.Z.json({success:!1,message:"Saldo UangtiX tidak mencukupi"},{status:400});if(!await m.UangtiXWallet.addBalance(a.user.id,-n,`Langganan ${i.name} (${r})`,`BADGE-${t}-${Date.now()}`))return o.Z.json({success:!1,message:"Gagal memproses pembayaran"},{status:500});let e=new Date,s=d.JT.calculateEndDate(e,r),c=await l.prisma.badgeSubscription.create({data:{userId:a.user.id,planId:i.id,badge:t,type:r,startDate:e,endDate:s,price:n,isActive:!0}});return await l.prisma.user.update({where:{id:a.user.id},data:{badge:t}}),await l.prisma.notification.create({data:{userId:a.user.id,title:"Badge Subscription Active",message:`Selamat! Anda sekarang memiliki badge ${d.JT.getBadgeName(t)}`,type:"SUBSCRIPTION_SUCCESS",data:{badge:t,type:r,endDate:s.toISOString()}}}),o.Z.json({success:!0,message:"Berhasil berlangganan badge",data:{subscription:c,badge:d.JT.getBadgeConfig(t),endDate:s}})}return o.Z.json({success:!1,message:"Payment method tidak didukung saat ini"},{status:400})}catch(e){return console.error("Badge subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/badges/subscribe/route",pathname:"/api/badges/subscribe",filename:"route",bundlePath:"app/api/badges/subscribe/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\badges\\subscribe\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:f,headerHooks:b,staticGenerationBailout:w}=g,T="/api/badges/subscribe/route";function x(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:y})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>c});var r=t(65822),s=t(86485),i=t(98432),n=t.n(i),o=t(3214);t(53524);let c={adapter:(0,r.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:r})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&r&&(e={...e,...r}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},30746:(e,a,t)=>{t.d(a,{JB:()=>s,JT:()=>i});let r={BRONZE:{name:"Bronze",color:"#CD7F32",bgColor:"bg-amber-100 dark:bg-amber-900/20",textColor:"text-amber-700 dark:text-amber-300",borderColor:"border-amber-300",icon:"\uD83E\uDD49",features:["Akses dasar platform","Buat event unlimited","Template tiket gratis","Support email","Komisi standar"],limits:{maxEvents:null,maxStaff:3,maxTemplates:5,canUseCustomDomain:!1,canUsePremiumTemplates:!1,prioritySupport:!1,analyticsAccess:"basic"}},SILVER:{name:"Silver",color:"#C0C0C0",bgColor:"bg-gray-100 dark:bg-gray-900/20",textColor:"text-gray-700 dark:text-gray-300",borderColor:"border-gray-300",icon:"\uD83E\uDD48",features:["Semua fitur Bronze","Multi-event management","Staff unlimited","Template premium (terbatas)","Analytics dasar","Priority email support"],limits:{maxEvents:null,maxStaff:null,maxTemplates:15,canUseCustomDomain:!1,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"standard"}},GOLD:{name:"Gold",color:"#FFD700",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",textColor:"text-yellow-700 dark:text-yellow-300",borderColor:"border-yellow-300",icon:"\uD83E\uDD47",features:["Semua fitur Silver","Premium templates unlimited","Boost event khusus","Analytics advanced","Custom branding","Priority chat support","Komisi lebih rendah"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"advanced",customBranding:!0,boostDiscount:20}},TITANIUM:{name:"Titanium",color:"#878681",bgColor:"bg-slate-100 dark:bg-slate-900/20",textColor:"text-slate-700 dark:text-slate-300",borderColor:"border-slate-300",icon:"\uD83D\uDC8E",features:["Semua fitur Gold","Semua fitur premium","Priority support 24/7","Dedicated account manager","Custom integrations","White-label options","Komisi terendah","Early access features"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"enterprise",customBranding:!0,boostDiscount:50,dedicatedSupport:!0,whiteLabel:!0}}},s=[{badge:"BRONZE",name:"Bronze Plan",description:"Perfect untuk organizer pemula",features:r.BRONZE.features,monthlyPrice:0,yearlyPrice:0},{badge:"SILVER",name:"Silver Plan",description:"Ideal untuk organizer yang berkembang",features:r.SILVER.features,monthlyPrice:99e3,yearlyPrice:99e4},{badge:"GOLD",name:"Gold Plan",description:"Untuk organizer profesional",features:r.GOLD.features,monthlyPrice:299e3,yearlyPrice:299e4},{badge:"TITANIUM",name:"Titanium Plan",description:"Enterprise solution untuk organizer besar",features:r.TITANIUM.features,monthlyPrice:999e3,yearlyPrice:999e4}];class i{static getBadgeConfig(e){return r[e]}static getBadgeIcon(e){return r[e].icon}static getBadgeName(e){return r[e].name}static getBadgeColor(e){return r[e].color}static getBadgeClasses(e){let a=r[e];return{bg:a.bgColor,text:a.textColor,border:a.borderColor}}static canAccessFeature(e,a){if(!e)return"BRONZE"===a;let t=["BRONZE","SILVER","GOLD","TITANIUM"];return t.indexOf(e)>=t.indexOf(a)}static getFeatureLimits(e){return e?r[e].limits:r.BRONZE.limits}static calculateSubscriptionPrice(e,a){let t=s.find(a=>a.badge===e);return t?"MONTHLY"===a?t.monthlyPrice:t.yearlyPrice:0}static getSubscriptionDiscount(e){return"YEARLY"===e?16.67:0}static isSubscriptionActive(e,a){let t=new Date;return t>=e&&t<=a}static calculateEndDate(e,a){let t=new Date(e);return"MONTHLY"===a?t.setMonth(t.getMonth()+1):t.setFullYear(t.getFullYear()+1),t}static getDaysUntilExpiry(e){let a=new Date;return Math.ceil((e.getTime()-a.getTime())/864e5)}static shouldShowRenewalReminder(e){let a=this.getDaysUntilExpiry(e);return a<=7&&a>0}static getCommissionRate(e){switch(e){case"BRONZE":default:return 5;case"SILVER":return 4;case"GOLD":return 3;case"TITANIUM":return 2}}static getBoostDiscount(e){return this.getFeatureLimits(e).boostDiscount||0}static canUseTemplate(e,a){return!a||this.canAccessFeature(e,a)}static formatBadgeForDisplay(e){if(!e)return"";let a=r[e];return`${a.icon} ${a.name}`}static getBadgeUpgradePath(e){let a=["BRONZE","SILVER","GOLD","TITANIUM"],t=e?a.indexOf(e):-1;return a.slice(t+1)}static getRecommendedBadge(e){return e>=5e7?"TITANIUM":e>=1e7?"GOLD":e>=2e6?"SILVER":"BRONZE"}}},76112:(e,a,t)=>{t.d(a,{H4:()=>u,PaymentFactory:()=>d,UangtiXWallet:()=>l,YZ:()=>o,Z4:()=>c});var r=t(6113),s=t.n(r),i=t(83949);let n={TRIPAY:{baseUrl:process.env.TRIPAY_BASE_URL||"https://tripay.co.id/api-sandbox",merchantCode:process.env.TRIPAY_MERCHANT_CODE||"",apiKey:process.env.TRIPAY_API_KEY||"",privateKey:process.env.TRIPAY_PRIVATE_KEY||""},MIDTRANS:{baseUrl:process.env.MIDTRANS_BASE_URL||"https://api.sandbox.midtrans.com/v2",serverKey:process.env.MIDTRANS_SERVER_KEY||"",clientKey:process.env.MIDTRANS_CLIENT_KEY||""},XENDIT:{baseUrl:process.env.XENDIT_BASE_URL||"https://api.xendit.co",secretKey:process.env.XENDIT_SECRET_KEY||""}};class o{generateSignature(e){let a=JSON.stringify(e);return s().createHmac("sha256",this.config.privateKey).update(a).digest("hex")}async getPaymentChannels(){try{let e=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+this.config.apiKey).digest("hex");return(await i.Z.get(`${this.config.baseUrl}/merchant/payment-channel`,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":e}})).data}catch(e){throw console.error("Tripay get channels error:",e),e}}async createPayment(e,a="QRIS"){try{let t=e.expiredTime||60,r=new Date(Date.now()+6e4*t),n={method:a,merchant_ref:e.orderId,amount:e.amount,customer_name:e.customerName,customer_email:e.customerEmail,customer_phone:e.customerPhone||"",order_items:[{sku:"TICKET",name:e.description,price:e.amount,quantity:1}],return_url:e.returnUrl||"http://localhost:3000/payment/success",expired_time:Math.floor(r.getTime()/1e3),signature:""},o=this.config.merchantCode+e.orderId+e.amount;n.signature=s().createHmac("sha256",this.config.privateKey).update(o).digest("hex");let c=await i.Z.post(`${this.config.baseUrl}/transaction/create`,n,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}});if(c.data.success)return{success:!0,paymentId:c.data.data.reference,paymentUrl:c.data.data.checkout_url,qrCode:c.data.data.qr_url,expiredAt:r,data:c.data.data};return{success:!1,paymentId:"",message:c.data.message}}catch(e){return console.error("Tripay create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let a=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+e).digest("hex");return(await i.Z.get(`${this.config.baseUrl}/transaction/detail`,{params:{reference:e},headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":a}})).data}catch(e){throw console.error("Tripay check status error:",e),e}}verifyCallback(e,a){return s().createHmac("sha256",this.config.privateKey).update(JSON.stringify(e)).digest("hex")===a}constructor(){this.config=n.TRIPAY}}class c{async createPayment(e){try{let a={transaction_details:{order_id:e.orderId,gross_amount:e.amount},customer_details:{first_name:e.customerName,email:e.customerEmail,phone:e.customerPhone||""},item_details:[{id:"TICKET",price:e.amount,quantity:1,name:e.description}],credit_card:{secure:!0}},t=Buffer.from(this.config.serverKey+":").toString("base64"),r=await i.Z.post(`${this.config.baseUrl}/charge`,a,{headers:{Authorization:`Basic ${t}`,"Content-Type":"application/json"}});if("201"===r.data.status_code)return{success:!0,paymentId:r.data.transaction_id,paymentUrl:r.data.redirect_url,data:r.data};return{success:!1,paymentId:"",message:r.data.status_message}}catch(e){return console.error("Midtrans create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.status_message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let a=Buffer.from(this.config.serverKey+":").toString("base64");return(await i.Z.get(`${this.config.baseUrl}/${e}/status`,{headers:{Authorization:`Basic ${a}`}})).data}catch(e){throw console.error("Midtrans check status error:",e),e}}verifyCallback(e,a){let t=e.order_id,r=e.status_code,i=e.gross_amount;return s().createHash("sha512").update(t+r+i+this.config.serverKey).digest("hex")===a}constructor(){this.config=n.MIDTRANS}}class u{async createPayment(e){try{let a=e.expiredTime||60,t=new Date(Date.now()+6e4*a),r={external_id:e.orderId,amount:e.amount,description:e.description,payer_email:e.customerEmail,success_redirect_url:e.returnUrl||"http://localhost:3000/payment/success",failure_redirect_url:e.returnUrl||"http://localhost:3000/payment/failed",invoice_duration:60*a},s=Buffer.from(this.config.secretKey+":").toString("base64"),n=await i.Z.post(`${this.config.baseUrl}/v2/invoices`,r,{headers:{Authorization:`Basic ${s}`,"Content-Type":"application/json"}});return{success:!0,paymentId:n.data.id,paymentUrl:n.data.invoice_url,expiredAt:t,data:n.data}}catch(e){return console.error("Xendit create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let a=Buffer.from(this.config.secretKey+":").toString("base64");return(await i.Z.get(`${this.config.baseUrl}/v2/invoices/${e}`,{headers:{Authorization:`Basic ${a}`}})).data}catch(e){throw console.error("Xendit check status error:",e),e}}verifyCallback(e,a){return a===process.env.XENDIT_WEBHOOK_TOKEN}constructor(){this.config=n.XENDIT}}class l{static async getBalance(e){let{prisma:a}=await Promise.resolve().then(t.bind(t,3214)),r=await a.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});return r?.uangtixBalance||0}static async addBalance(e,a,r,s){let{prisma:i}=await Promise.resolve().then(t.bind(t,3214));try{return await i.$transaction(async t=>{let i=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!i)throw Error("User not found");let n=i.uangtixBalance,o=n+a;await t.user.update({where:{id:e},data:{uangtixBalance:o}}),await t.uangtiXTransaction.create({data:{userId:e,type:a>0?"DEPOSIT":"WITHDRAW",amount:Math.abs(a),description:r,reference:s,status:"SUCCESS",balanceBefore:n,balanceAfter:o}})}),!0}catch(e){return console.error("UangtiX add balance error:",e),!1}}static async transfer(e,a,r,s){let{prisma:i}=await Promise.resolve().then(t.bind(t,3214));try{return await i.$transaction(async t=>{let i=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!i||i.uangtixBalance<r)throw Error("Insufficient balance");let n=await t.user.findUnique({where:{id:a},select:{uangtixBalance:!0}});if(!n)throw Error("Receiver not found");let o=i.uangtixBalance,c=o-r;await t.user.update({where:{id:e},data:{uangtixBalance:c}});let u=n.uangtixBalance,l=u+r;await t.user.update({where:{id:a},data:{uangtixBalance:l}}),await t.uangtiXTransaction.createMany({data:[{userId:e,type:"TRANSFER",amount:-r,description:`Transfer ke ${a}: ${s}`,reference:a,status:"SUCCESS",balanceBefore:o,balanceAfter:c},{userId:a,type:"TRANSFER",amount:r,description:`Transfer dari ${e}: ${s}`,reference:e,status:"SUCCESS",balanceBefore:u,balanceAfter:l}]})}),!0}catch(e){return console.error("UangtiX transfer error:",e),!1}}}class d{static createPaymentGateway(e){switch(e.toUpperCase()){case"TRIPAY":return new o;case"MIDTRANS":return new c;case"XENDIT":return new u;default:throw Error(`Unsupported payment gateway: ${e}`)}}}},3214:(e,a,t)=>{t.d(a,{prisma:()=>s});var r=t(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1638,6206,9155,3949],()=>t(45435));module.exports=r})();