"use strict";(()=>{var e={};e.id=195,e.ids=[195],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},45435:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>p,originalPathname:()=>m,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>g,staticGenerationAsyncStorage:()=>b,staticGenerationBailout:()=>O});var a={};r.r(a),r.d(a,{POST:()=>d});var n=r(95419),i=r(69108),s=r(99678),o=r(78070),u=r(81355);async function d(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{badge:r,type:a,paymentMethod:n="UANGTIX"}=await e.json();if(!r||!a)return o.Z.json({success:!1,message:"Badge dan tipe langganan diperlukan"},{status:400});if("ORGANIZER"!==t.user.role)return o.Z.json({success:!1,message:"Hanya organizer yang dapat berlangganan badge"},{status:403});let i=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgePlan.findUnique({where:{badge:r}});if(!i||!i.isActive)return o.Z.json({success:!1,message:"Badge plan tidak ditemukan"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.findFirst({where:{userId:t.user.id,isActive:!0,endDate:{gte:new Date}}}))return o.Z.json({success:!1,message:"Anda sudah memiliki langganan badge aktif"},{status:400});let s=Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).calculateSubscriptionPrice(r,a);if(0===s&&"BRONZE"===r){let e=new Date,n=new Date;n.setFullYear(n.getFullYear()+10);let s=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.create({data:{userId:t.user.id,planId:i.id,badge:r,type:a,startDate:e,endDate:n,price:0,isActive:!0}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.update({where:{id:t.user.id},data:{badge:r}}),o.Z.json({success:!0,message:"Berhasil berlangganan Bronze (gratis)",data:s})}if("UANGTIX"===n){if(await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBalance(t.user.id)<s)return o.Z.json({success:!1,message:"Saldo UangtiX tidak mencukupi"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).addBalance(t.user.id,-s,`Langganan ${i.name} (${a})`,`BADGE-${r}-${Date.now()}`))return o.Z.json({success:!1,message:"Gagal memproses pembayaran"},{status:500});let e=new Date,n=Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).calculateEndDate(e,a),u=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.create({data:{userId:t.user.id,planId:i.id,badge:r,type:a,startDate:e,endDate:n,price:s,isActive:!0}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.update({where:{id:t.user.id},data:{badge:r}}),await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:t.user.id,title:"Badge Subscription Active",message:`Selamat! Anda sekarang memiliki badge ${Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBadgeName(r)}`,type:"SUBSCRIPTION_SUCCESS",data:{badge:r,type:a,endDate:n.toISOString()}}}),o.Z.json({success:!0,message:"Berhasil berlangganan badge",data:{subscription:u,badge:Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBadgeConfig(r),endDate:n}})}return o.Z.json({success:!1,message:"Payment method tidak didukung saat ini"},{status:400})}catch(e){return console.error("Badge subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}();let c=new n.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/badges/subscribe/route",pathname:"/api/badges/subscribe",filename:"route",bundlePath:"app/api/badges/subscribe/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\badges\\subscribe\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:b,serverHooks:g,headerHooks:p,staticGenerationBailout:O}=c,m="/api/badges/subscribe/route";function f(){return(0,s.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:b})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355],()=>r(45435));module.exports=a})();