"use strict";(()=>{var e={};e.id=5315,e.ids=[5315],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},62668:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>D,patchFetch:()=>T,requestAsyncStorage:()=>f,routeModule:()=>b,serverHooks:()=>h,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>w});var r={};a.r(r),a.d(r,{DELETE:()=>g,GET:()=>m,PATCH:()=>p});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),l=a(3205),c=a(3214),d=a(30746);async function m(e){try{let e=await (0,u.getServerSession)(l.Lz);if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let t=await c.prisma.badgeSubscription.findFirst({where:{userId:e.user.id,isActive:!0,endDate:{gte:new Date}},include:{plan:!0},orderBy:{createdAt:"desc"}});if(!t)return o.Z.json({success:!0,data:null,message:"Tidak ada langganan aktif"});let a=d.JT.getBadgeConfig(t.badge),r=d.JT.getDaysUntilExpiry(t.endDate),s=d.JT.shouldShowRenewalReminder(t.endDate);return o.Z.json({success:!0,data:{...t,badgeConfig:a,daysLeft:r,shouldShowReminder:s,isActive:d.JT.isSubscriptionActive(t.startDate,t.endDate)}})}catch(e){return console.error("Get subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function p(e){try{let t=await (0,u.getServerSession)(l.Lz);if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{autoRenew:a}=await e.json(),r=await c.prisma.badgeSubscription.findFirst({where:{userId:t.user.id,isActive:!0,endDate:{gte:new Date}}});if(!r)return o.Z.json({success:!1,message:"Tidak ada langganan aktif"},{status:404});let s=await c.prisma.badgeSubscription.update({where:{id:r.id},data:{autoRenew:a??r.autoRenew},include:{plan:!0}});return o.Z.json({success:!0,message:"Pengaturan langganan berhasil diperbarui",data:s})}catch(e){return console.error("Update subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function g(e){try{let e=await (0,u.getServerSession)(l.Lz);if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let t=await c.prisma.badgeSubscription.findFirst({where:{userId:e.user.id,isActive:!0,endDate:{gte:new Date}}});if(!t)return o.Z.json({success:!1,message:"Tidak ada langganan aktif"},{status:404});return await c.prisma.badgeSubscription.update({where:{id:t.id},data:{isActive:!1,autoRenew:!1,endDate:new Date}}),await c.prisma.user.update({where:{id:e.user.id},data:{badge:"BRONZE"}}),await c.prisma.notification.create({data:{userId:e.user.id,title:"Subscription Cancelled",message:"Langganan badge Anda telah dibatalkan. Badge dikembalikan ke Bronze.",type:"SUBSCRIPTION_CANCELLED"}}),o.Z.json({success:!0,message:"Langganan berhasil dibatalkan"})}catch(e){return console.error("Cancel subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let b=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/badges/subscription/route",pathname:"/api/badges/subscription",filename:"route",bundlePath:"app/api/badges/subscription/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\badges\\subscription\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:y,serverHooks:h,headerHooks:x,staticGenerationBailout:w}=b,D="/api/badges/subscription/route";function T(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:y})}},3205:(e,t,a)=>{a.d(t,{Lz:()=>u});var r=a(65822),s=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let u={adapter:(0,r.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:a,session:r})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===a&&r&&(e={...e,...r}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},30746:(e,t,a)=>{a.d(t,{JB:()=>s,JT:()=>i});let r={BRONZE:{name:"Bronze",color:"#CD7F32",bgColor:"bg-amber-100 dark:bg-amber-900/20",textColor:"text-amber-700 dark:text-amber-300",borderColor:"border-amber-300",icon:"\uD83E\uDD49",features:["Akses dasar platform","Buat event unlimited","Template tiket gratis","Support email","Komisi standar"],limits:{maxEvents:null,maxStaff:3,maxTemplates:5,canUseCustomDomain:!1,canUsePremiumTemplates:!1,prioritySupport:!1,analyticsAccess:"basic"}},SILVER:{name:"Silver",color:"#C0C0C0",bgColor:"bg-gray-100 dark:bg-gray-900/20",textColor:"text-gray-700 dark:text-gray-300",borderColor:"border-gray-300",icon:"\uD83E\uDD48",features:["Semua fitur Bronze","Multi-event management","Staff unlimited","Template premium (terbatas)","Analytics dasar","Priority email support"],limits:{maxEvents:null,maxStaff:null,maxTemplates:15,canUseCustomDomain:!1,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"standard"}},GOLD:{name:"Gold",color:"#FFD700",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",textColor:"text-yellow-700 dark:text-yellow-300",borderColor:"border-yellow-300",icon:"\uD83E\uDD47",features:["Semua fitur Silver","Premium templates unlimited","Boost event khusus","Analytics advanced","Custom branding","Priority chat support","Komisi lebih rendah"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"advanced",customBranding:!0,boostDiscount:20}},TITANIUM:{name:"Titanium",color:"#878681",bgColor:"bg-slate-100 dark:bg-slate-900/20",textColor:"text-slate-700 dark:text-slate-300",borderColor:"border-slate-300",icon:"\uD83D\uDC8E",features:["Semua fitur Gold","Semua fitur premium","Priority support 24/7","Dedicated account manager","Custom integrations","White-label options","Komisi terendah","Early access features"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"enterprise",customBranding:!0,boostDiscount:50,dedicatedSupport:!0,whiteLabel:!0}}},s=[{badge:"BRONZE",name:"Bronze Plan",description:"Perfect untuk organizer pemula",features:r.BRONZE.features,monthlyPrice:0,yearlyPrice:0},{badge:"SILVER",name:"Silver Plan",description:"Ideal untuk organizer yang berkembang",features:r.SILVER.features,monthlyPrice:99e3,yearlyPrice:99e4},{badge:"GOLD",name:"Gold Plan",description:"Untuk organizer profesional",features:r.GOLD.features,monthlyPrice:299e3,yearlyPrice:299e4},{badge:"TITANIUM",name:"Titanium Plan",description:"Enterprise solution untuk organizer besar",features:r.TITANIUM.features,monthlyPrice:999e3,yearlyPrice:999e4}];class i{static getBadgeConfig(e){return r[e]}static getBadgeIcon(e){return r[e].icon}static getBadgeName(e){return r[e].name}static getBadgeColor(e){return r[e].color}static getBadgeClasses(e){let t=r[e];return{bg:t.bgColor,text:t.textColor,border:t.borderColor}}static canAccessFeature(e,t){if(!e)return"BRONZE"===t;let a=["BRONZE","SILVER","GOLD","TITANIUM"];return a.indexOf(e)>=a.indexOf(t)}static getFeatureLimits(e){return e?r[e].limits:r.BRONZE.limits}static calculateSubscriptionPrice(e,t){let a=s.find(t=>t.badge===e);return a?"MONTHLY"===t?a.monthlyPrice:a.yearlyPrice:0}static getSubscriptionDiscount(e){return"YEARLY"===e?16.67:0}static isSubscriptionActive(e,t){let a=new Date;return a>=e&&a<=t}static calculateEndDate(e,t){let a=new Date(e);return"MONTHLY"===t?a.setMonth(a.getMonth()+1):a.setFullYear(a.getFullYear()+1),a}static getDaysUntilExpiry(e){let t=new Date;return Math.ceil((e.getTime()-t.getTime())/864e5)}static shouldShowRenewalReminder(e){let t=this.getDaysUntilExpiry(e);return t<=7&&t>0}static getCommissionRate(e){switch(e){case"BRONZE":default:return 5;case"SILVER":return 4;case"GOLD":return 3;case"TITANIUM":return 2}}static getBoostDiscount(e){return this.getFeatureLimits(e).boostDiscount||0}static canUseTemplate(e,t){return!t||this.canAccessFeature(e,t)}static formatBadgeForDisplay(e){if(!e)return"";let t=r[e];return`${t.icon} ${t.name}`}static getBadgeUpgradePath(e){let t=["BRONZE","SILVER","GOLD","TITANIUM"],a=e?t.indexOf(e):-1;return t.slice(a+1)}static getRecommendedBadge(e){return e>=5e7?"TITANIUM":e>=1e7?"GOLD":e>=2e6?"SILVER":"BRONZE"}}},3214:(e,t,a)=>{a.d(t,{prisma:()=>s});var r=a(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,9155],()=>a(62668));module.exports=r})();