"use strict";(()=>{var e={};e.id=5315,e.ids=[5315],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},62668:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>f,originalPathname:()=>m,patchFetch:()=>D,requestAsyncStorage:()=>p,routeModule:()=>b,serverHooks:()=>g,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>h});var a={};t.r(a),t.d(a,{DELETE:()=>l,GET:()=>d,PATCH:()=>c});var n=t(95419),s=t(69108),i=t(99678),o=t(78070),u=t(81355);async function d(e){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.findFirst({where:{userId:e.user.id,isActive:!0,endDate:{gte:new Date}},include:{plan:!0},orderBy:{createdAt:"desc"}});if(!r)return o.Z.json({success:!0,data:null,message:"Tidak ada langganan aktif"});let t=Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBadgeConfig(r.badge),a=Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getDaysUntilExpiry(r.endDate),n=Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).shouldShowRenewalReminder(r.endDate);return o.Z.json({success:!0,data:{...r,badgeConfig:t,daysLeft:a,shouldShowReminder:n,isActive:Object(function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}()).isSubscriptionActive(r.startDate,r.endDate)}})}catch(e){return console.error("Get subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{autoRenew:t}=await e.json(),a=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.findFirst({where:{userId:r.user.id,isActive:!0,endDate:{gte:new Date}}});if(!a)return o.Z.json({success:!1,message:"Tidak ada langganan aktif"},{status:404});let n=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.update({where:{id:a.id},data:{autoRenew:t??a.autoRenew},include:{plan:!0}});return o.Z.json({success:!0,message:"Pengaturan langganan berhasil diperbarui",data:n})}catch(e){return console.error("Update subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function l(e){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.findFirst({where:{userId:e.user.id,isActive:!0,endDate:{gte:new Date}}});if(!r)return o.Z.json({success:!1,message:"Tidak ada langganan aktif"},{status:404});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).badgeSubscription.update({where:{id:r.id},data:{isActive:!1,autoRenew:!1,endDate:new Date}}),await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.update({where:{id:e.user.id},data:{badge:"BRONZE"}}),await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:e.user.id,title:"Subscription Cancelled",message:"Langganan badge Anda telah dibatalkan. Badge dikembalikan ke Bronze.",type:"SUBSCRIPTION_CANCELLED"}}),o.Z.json({success:!0,message:"Langganan berhasil dibatalkan"})}catch(e){return console.error("Cancel subscription error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/badge-utils'");throw e.code="MODULE_NOT_FOUND",e}();let b=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/badges/subscription/route",pathname:"/api/badges/subscription",filename:"route",bundlePath:"app/api/badges/subscription/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\badges\\subscription\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:O,serverHooks:g,headerHooks:f,staticGenerationBailout:h}=b,m="/api/badges/subscription/route";function D(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:O})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,1355],()=>t(62668));module.exports=a})();