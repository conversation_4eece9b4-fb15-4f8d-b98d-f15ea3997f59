"use strict";(()=>{var e={};e.id=4831,e.ids=[4831],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},29435:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>v,originalPathname:()=>j,patchFetch:()=>q,requestAsyncStorage:()=>y,routeModule:()=>f,serverHooks:()=>k,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>b});var t={};r.r(t),r.d(t,{DELETE:()=>w,GET:()=>m,PUT:()=>h});var s=r(95419),i=r(69108),n=r(99678),o=r(78070),u=r(81355),d=r(3205),c=r(3214),l=r(25252),p=r(52178);let g=l.Ry({name:l.Z_().min(2,"Nama kategori minimal 2 karakter").max(100,"Nama kategori maksimal 100 karakter"),description:l.Z_().optional(),icon:l.Z_().optional(),color:l.Z_().optional(),isActive:l.O7().default(!0)});async function m(e,{params:a}){try{let e=await c.prisma.category.findUnique({where:{id:a.id},include:{_count:{select:{events:!0}}}});if(!e)return o.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:404});return o.Z.json({success:!0,data:e})}catch(e){return console.error("Error fetching category:",e),o.Z.json({success:!1,message:"Gagal mengambil data kategori"},{status:500})}}async function h(e,{params:a}){try{let r=await (0,u.getServerSession)(d.Lz);if(!r||"ADMIN"!==r.user.role)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let t=await e.json(),s=g.parse(t);if(!await c.prisma.category.findUnique({where:{id:a.id}}))return o.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:404});if(await c.prisma.category.findFirst({where:{name:{equals:s.name,mode:"insensitive"},id:{not:a.id}}}))return o.Z.json({success:!1,message:"Nama kategori sudah digunakan"},{status:400});let i=await c.prisma.category.update({where:{id:a.id},data:s});return await c.prisma.notification.create({data:{userId:r.user.id,type:"SYSTEM",title:"Kategori Diperbarui",message:`Kategori "${i.name}" berhasil diperbarui`,isRead:!1}}),o.Z.json({success:!0,data:i,message:"Kategori berhasil diperbarui"})}catch(e){if(e instanceof p.jm)return o.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error updating category:",e),o.Z.json({success:!1,message:"Gagal memperbarui kategori"},{status:500})}}async function w(e,{params:a}){try{let e=await (0,u.getServerSession)(d.Lz);if(!e||"ADMIN"!==e.user.role)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await c.prisma.category.findUnique({where:{id:a.id},include:{_count:{select:{events:!0}}}});if(!r)return o.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:404});if(r._count.events>0)return o.Z.json({success:!1,message:`Kategori tidak dapat dihapus karena masih digunakan oleh ${r._count.events} event`},{status:400});return await c.prisma.category.delete({where:{id:a.id}}),await c.prisma.notification.create({data:{userId:e.user.id,type:"SYSTEM",title:"Kategori Dihapus",message:`Kategori "${r.name}" berhasil dihapus`,isRead:!1}}),o.Z.json({success:!0,message:"Kategori berhasil dihapus"})}catch(e){return console.error("Error deleting category:",e),o.Z.json({success:!1,message:"Gagal menghapus kategori"},{status:500})}}let f=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:y,staticGenerationAsyncStorage:x,serverHooks:k,headerHooks:v,staticGenerationBailout:b}=f,j="/api/categories/[id]/route";function q(){return(0,n.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:x})}},3205:(e,a,r)=>{r.d(a,{Lz:()=>u});var t=r(65822),s=r(86485),i=r(98432),n=r.n(i),o=r(3214);r(53524);let u={adapter:(0,t.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:r,session:t})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===r&&t&&(e={...e,...t}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,r)=>{r.d(a,{prisma:()=>s});var t=r(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var a=require("../../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[1638,6206,9155,5252],()=>r(29435));module.exports=t})();