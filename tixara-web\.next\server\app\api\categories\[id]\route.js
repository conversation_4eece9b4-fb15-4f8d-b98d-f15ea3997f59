"use strict";(()=>{var e={};e.id=4831,e.ids=[4831],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},29435:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>E,originalPathname:()=>_,patchFetch:()=>D,requestAsyncStorage:()=>h,routeModule:()=>f,serverHooks:()=>b,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>w});var a={};r.r(a),r.d(a,{DELETE:()=>g,GET:()=>m,PUT:()=>p});var i=r(95419),s=r(69108),o=r(99678),n=r(78070),u=r(81355),c=r(25252),d=r(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=c.Ry({name:c.Z_().min(2,"Nama kategori minimal 2 karakter").max(100,"Nama kategori maksimal 100 karakter"),description:c.Z_().optional(),icon:c.Z_().optional(),color:c.Z_().optional(),isActive:c.O7().default(!0)});async function m(e,{params:t}){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findUnique({where:{id:t.id},include:{_count:{select:{events:!0}}}});if(!e)return n.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:404});return n.Z.json({success:!0,data:e})}catch(e){return console.error("Error fetching category:",e),n.Z.json({success:!1,message:"Gagal mengambil data kategori"},{status:500})}}async function p(e,{params:t}){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r||"ADMIN"!==r.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let a=await e.json(),i=l.parse(a);if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findUnique({where:{id:t.id}}))return n.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findFirst({where:{name:{equals:i.name,mode:"insensitive"},id:{not:t.id}}}))return n.Z.json({success:!1,message:"Nama kategori sudah digunakan"},{status:400});let s=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.update({where:{id:t.id},data:i});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:r.user.id,type:"SYSTEM",title:"Kategori Diperbarui",message:`Kategori "${s.name}" berhasil diperbarui`,isRead:!1}}),n.Z.json({success:!0,data:s,message:"Kategori berhasil diperbarui"})}catch(e){if(e instanceof d.jm)return n.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error updating category:",e),n.Z.json({success:!1,message:"Gagal memperbarui kategori"},{status:500})}}async function g(e,{params:t}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e||"ADMIN"!==e.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findUnique({where:{id:t.id},include:{_count:{select:{events:!0}}}});if(!r)return n.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:404});if(r._count.events>0)return n.Z.json({success:!1,message:`Kategori tidak dapat dihapus karena masih digunakan oleh ${r._count.events} event`},{status:400});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.delete({where:{id:t.id}}),await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:e.user.id,type:"SYSTEM",title:"Kategori Dihapus",message:`Kategori "${r.name}" berhasil dihapus`,isRead:!1}}),n.Z.json({success:!0,message:"Kategori berhasil dihapus"})}catch(e){return console.error("Error deleting category:",e),n.Z.json({success:!1,message:"Gagal menghapus kategori"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/categories/[id]/route",pathname:"/api/categories/[id]",filename:"route",bundlePath:"app/api/categories/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\categories\\[id]\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:h,staticGenerationAsyncStorage:O,serverHooks:b,headerHooks:E,staticGenerationBailout:w}=f,_="/api/categories/[id]/route";function D(){return(0,o.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:O})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355,5252],()=>r(29435));module.exports=a})();