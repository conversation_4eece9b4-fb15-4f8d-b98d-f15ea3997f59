"use strict";(()=>{var e={};e.id=9961,e.ids=[9961],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},68530:(e,a,r)=>{r.r(a),r.d(a,{headerHooks:()=>y,originalPathname:()=>q,patchFetch:()=>j,requestAsyncStorage:()=>x,routeModule:()=>w,serverHooks:()=>f,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>b});var t={};r.r(t),r.d(t,{GET:()=>g,POST:()=>h});var s=r(95419),i=r(69108),o=r(99678),n=r(78070),u=r(81355),l=r(3205),c=r(3214),d=r(25252),p=r(52178);let m=d.Ry({name:d.Z_().min(2,"Nama kategori minimal 2 karakter").max(100,"Nama kategori maksimal 100 karakter"),description:d.Z_().optional(),icon:d.Z_().optional(),color:d.Z_().optional(),isActive:d.O7().default(!0)});async function g(e){try{let{searchParams:a}=new URL(e.url),r=a.get("active"),t=a.get("search"),s={};null!==r&&(s.isActive="true"===r),t&&(s.OR=[{name:{contains:t,mode:"insensitive"}},{description:{contains:t,mode:"insensitive"}}]);let i=await c.prisma.category.findMany({where:s,include:{_count:{select:{events:!0}}},orderBy:{name:"asc"}});return n.Z.json({success:!0,data:i})}catch(e){return console.error("Error fetching categories:",e),n.Z.json({success:!1,message:"Gagal mengambil data kategori"},{status:500})}}async function h(e){try{let a=await (0,u.getServerSession)(l.Lz);if(!a||"ADMIN"!==a.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await e.json(),t=m.parse(r);if(await c.prisma.category.findFirst({where:{name:{equals:t.name,mode:"insensitive"}}}))return n.Z.json({success:!1,message:"Nama kategori sudah digunakan"},{status:400});let s=await c.prisma.category.create({data:t});return await c.prisma.notification.create({data:{userId:a.user.id,type:"SYSTEM",title:"Kategori Baru Dibuat",message:`Kategori "${s.name}" berhasil dibuat`,isRead:!1}}),n.Z.json({success:!0,data:s,message:"Kategori berhasil dibuat"})}catch(e){if(e instanceof p.jm)return n.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error creating category:",e),n.Z.json({success:!1,message:"Gagal membuat kategori"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\categories\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:x,staticGenerationAsyncStorage:v,serverHooks:f,headerHooks:y,staticGenerationBailout:b}=w,q="/api/categories/route";function j(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:v})}},3205:(e,a,r)=>{r.d(a,{Lz:()=>u});var t=r(65822),s=r(86485),i=r(98432),o=r.n(i),n=r(3214);r(53524);let u={adapter:(0,t.N)(n.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await n.prisma.user.findUnique({where:{email:e.email}});if(!a||!await o().compare(e.password,a.password))throw Error("Email atau password salah");return await n.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:r,session:t})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===r&&t&&(e={...e,...t}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await n.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,r)=>{r.d(a,{prisma:()=>s});var t=r(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var a=require("../../../webpack-runtime.js");a.C(e);var r=e=>a(a.s=e),t=a.X(0,[1638,6206,9155,5252],()=>r(68530));module.exports=t})();