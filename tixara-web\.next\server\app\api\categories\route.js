"use strict";(()=>{var e={};e.id=9961,e.ids=[9961],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},68530:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>b,patchFetch:()=>_,requestAsyncStorage:()=>f,routeModule:()=>g,serverHooks:()=>h,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>v});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>m});var o=r(95419),s=r(69108),i=r(99678),n=r(78070),u=r(81355),c=r(25252),d=r(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=c.Ry({name:c.Z_().min(2,"Nama kategori minimal 2 karakter").max(100,"Nama kategori maksimal 100 karakter"),description:c.Z_().optional(),icon:c.Z_().optional(),color:c.Z_().optional(),isActive:c.O7().default(!0)});async function p(e){try{let{searchParams:t}=new URL(e.url),r=t.get("active"),a=t.get("search"),o={};null!==r&&(o.isActive="true"===r),a&&(o.OR=[{name:{contains:a,mode:"insensitive"}},{description:{contains:a,mode:"insensitive"}}]);let s=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findMany({where:o,include:{_count:{select:{events:!0}}},orderBy:{name:"asc"}});return n.Z.json({success:!0,data:s})}catch(e){return console.error("Error fetching categories:",e),n.Z.json({success:!1,message:"Gagal mengambil data kategori"},{status:500})}}async function m(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t||"ADMIN"!==t.user.role)return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await e.json(),a=l.parse(r);if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findFirst({where:{name:{equals:a.name,mode:"insensitive"}}}))return n.Z.json({success:!1,message:"Nama kategori sudah digunakan"},{status:400});let o=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.create({data:a});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:t.user.id,type:"SYSTEM",title:"Kategori Baru Dibuat",message:`Kategori "${o.name}" berhasil dibuat`,isRead:!1}}),n.Z.json({success:!0,data:o,message:"Kategori berhasil dibuat"})}catch(e){if(e instanceof d.jm)return n.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error creating category:",e),n.Z.json({success:!1,message:"Gagal membuat kategori"},{status:500})}}let g=new o.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/categories/route",pathname:"/api/categories",filename:"route",bundlePath:"app/api/categories/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\categories\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:f,staticGenerationAsyncStorage:O,serverHooks:h,headerHooks:x,staticGenerationBailout:v}=g,b="/api/categories/route";function _(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:O})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355,5252],()=>r(68530));module.exports=a})();