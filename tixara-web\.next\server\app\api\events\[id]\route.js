"use strict";(()=>{var e={};e.id=6007,e.ids=[6007],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},44186:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>E,originalPathname:()=>j,patchFetch:()=>y,requestAsyncStorage:()=>w,routeModule:()=>h,serverHooks:()=>x,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>b});var s={};t.r(s),t.d(s,{DELETE:()=>k,GET:()=>g,PUT:()=>v});var i=t(95419),r=t(69108),n=t(99678),u=t(78070),d=t(81355),o=t(3205),l=t(3214),c=t(25252),m=t(52178);let p=c.Ry({title:c.Z_().min(5,"Judul event minimal 5 karakter").max(200,"Judul event maksimal 200 karakter"),description:c.Z_().min(10,"Deskripsi minimal 10 karakter"),categoryId:c.Z_().min(1,"Kategori wajib dipilih"),location:c.Z_().min(5,"Lokasi minimal 5 karakter"),startDate:c.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),endDate:c.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),price:c.Rx().min(0,"Harga tidak boleh negatif"),maxTickets:c.Rx().min(1,"Maksimal tiket minimal 1"),image:c.Z_().optional(),isActive:c.O7().default(!0),requiresApproval:c.O7().default(!1)});async function g(e,{params:a}){try{let e=await l.prisma.event.findUnique({where:{id:a.id},include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0,_count:{select:{events:!0}}}},staff:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0}}}});if(!e)return u.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});let t=await l.prisma.ticket.count({where:{eventId:a.id,status:"ACTIVE"}});return u.Z.json({success:!0,data:{...e,soldTickets:t,availableTickets:e.maxTickets-t}})}catch(e){return console.error("Error fetching event:",e),u.Z.json({success:!1,message:"Gagal mengambil data event"},{status:500})}}async function v(e,{params:a}){try{let t=await (0,d.getServerSession)(o.Lz);if(!t||!["ORGANIZER","ADMIN"].includes(t.user.role))return u.Z.json({success:!1,message:"Unauthorized"},{status:401});let s=await e.json(),i=p.parse(s),r=await l.prisma.event.findUnique({where:{id:a.id}});if(!r)return u.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if("ORGANIZER"===t.user.role&&r.organizerId!==t.user.id)return u.Z.json({success:!1,message:"Anda tidak memiliki akses untuk mengedit event ini"},{status:403});let n=new Date(i.startDate),c=new Date(i.endDate);if(c<=n)return u.Z.json({success:!1,message:"Tanggal selesai harus setelah tanggal mulai"},{status:400});if(!await l.prisma.category.findUnique({where:{id:i.categoryId}}))return u.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:400});if(i.maxTickets<r.maxTickets){let e=await l.prisma.ticket.count({where:{eventId:a.id,status:"ACTIVE"}});if(i.maxTickets<e)return u.Z.json({success:!1,message:`Tidak dapat mengurangi maksimal tiket karena sudah ada ${e} tiket terjual`},{status:400})}let m=await l.prisma.event.update({where:{id:a.id},data:{...i,startDate:n,endDate:c},include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0}}}});return await l.prisma.notification.create({data:{userId:t.user.id,type:"EVENT",title:"Event Diperbarui",message:`Event "${m.title}" berhasil diperbarui`,isRead:!1,relatedId:m.id}}),u.Z.json({success:!0,data:m,message:"Event berhasil diperbarui"})}catch(e){if(e instanceof m.jm)return u.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error updating event:",e),u.Z.json({success:!1,message:"Gagal memperbarui event"},{status:500})}}async function k(e,{params:a}){try{let e=await (0,d.getServerSession)(o.Lz);if(!e||!["ORGANIZER","ADMIN"].includes(e.user.role))return u.Z.json({success:!1,message:"Unauthorized"},{status:401});let t=await l.prisma.event.findUnique({where:{id:a.id},include:{_count:{select:{tickets:!0}}}});if(!t)return u.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if("ORGANIZER"===e.user.role&&t.organizerId!==e.user.id)return u.Z.json({success:!1,message:"Anda tidak memiliki akses untuk menghapus event ini"},{status:403});let s=await l.prisma.ticket.count({where:{eventId:a.id,status:"ACTIVE"}});if(s>0)return u.Z.json({success:!1,message:`Event tidak dapat dihapus karena sudah ada ${s} tiket terjual`},{status:400});return await l.prisma.event.delete({where:{id:a.id}}),await l.prisma.notification.create({data:{userId:e.user.id,type:"EVENT",title:"Event Dihapus",message:`Event "${t.title}" berhasil dihapus`,isRead:!1}}),u.Z.json({success:!0,message:"Event berhasil dihapus"})}catch(e){return console.error("Error deleting event:",e),u.Z.json({success:!1,message:"Gagal menghapus event"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/api/events/[id]/route",pathname:"/api/events/[id]",filename:"route",bundlePath:"app/api/events/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\events\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:w,staticGenerationAsyncStorage:f,serverHooks:x,headerHooks:E,staticGenerationBailout:b}=h,j="/api/events/[id]/route";function y(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:f})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>d});var s=t(65822),i=t(86485),r=t(98432),n=t.n(r),u=t(3214);t(53524);let d={adapter:(0,s.N)(u.prisma),providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await u.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await u.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:s})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&s&&(e={...e,...s}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await u.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,t)=>{t.d(a,{prisma:()=>i});var s=t(53524);let i=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[1638,6206,9155,5252],()=>t(44186));module.exports=s})();