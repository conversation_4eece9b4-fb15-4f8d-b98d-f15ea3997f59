"use strict";(()=>{var e={};e.id=6007,e.ids=[6007],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},44186:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>E,originalPathname:()=>b,patchFetch:()=>D,requestAsyncStorage:()=>v,routeModule:()=>g,serverHooks:()=>h,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>k});var r={};a.r(r),a.d(r,{DELETE:()=>f,GET:()=>m,PUT:()=>p});var i=a(95419),s=a(69108),n=a(99678),o=a(78070),u=a(81355),d=a(25252),c=a(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=d.Ry({title:d.Z_().min(5,"Judul event minimal 5 karakter").max(200,"Judul event maksimal 200 karakter"),description:d.Z_().min(10,"Deskripsi minimal 10 karakter"),categoryId:d.Z_().min(1,"Kategori wajib dipilih"),location:d.Z_().min(5,"Lokasi minimal 5 karakter"),startDate:d.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),endDate:d.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),price:d.Rx().min(0,"Harga tidak boleh negatif"),maxTickets:d.Rx().min(1,"Maksimal tiket minimal 1"),image:d.Z_().optional(),isActive:d.O7().default(!0),requiresApproval:d.O7().default(!1)});async function m(e,{params:t}){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findUnique({where:{id:t.id},include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0,_count:{select:{events:!0}}}},staff:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0}}}});if(!e)return o.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});let a=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{eventId:t.id,status:"ACTIVE"}});return o.Z.json({success:!0,data:{...e,soldTickets:a,availableTickets:e.maxTickets-a}})}catch(e){return console.error("Error fetching event:",e),o.Z.json({success:!1,message:"Gagal mengambil data event"},{status:500})}}async function p(e,{params:t}){try{let a=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!a||!["ORGANIZER","ADMIN"].includes(a.user.role))return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await e.json(),i=l.parse(r),s=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findUnique({where:{id:t.id}});if(!s)return o.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if("ORGANIZER"===a.user.role&&s.organizerId!==a.user.id)return o.Z.json({success:!1,message:"Anda tidak memiliki akses untuk mengedit event ini"},{status:403});let n=new Date(i.startDate),d=new Date(i.endDate);if(d<=n)return o.Z.json({success:!1,message:"Tanggal selesai harus setelah tanggal mulai"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findUnique({where:{id:i.categoryId}}))return o.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:400});if(i.maxTickets<s.maxTickets){let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{eventId:t.id,status:"ACTIVE"}});if(i.maxTickets<e)return o.Z.json({success:!1,message:`Tidak dapat mengurangi maksimal tiket karena sudah ada ${e} tiket terjual`},{status:400})}let c=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.update({where:{id:t.id},data:{...i,startDate:n,endDate:d},include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:a.user.id,type:"EVENT",title:"Event Diperbarui",message:`Event "${c.title}" berhasil diperbarui`,isRead:!1,relatedId:c.id}}),o.Z.json({success:!0,data:c,message:"Event berhasil diperbarui"})}catch(e){if(e instanceof c.jm)return o.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error updating event:",e),o.Z.json({success:!1,message:"Gagal memperbarui event"},{status:500})}}async function f(e,{params:t}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e||!["ORGANIZER","ADMIN"].includes(e.user.role))return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let a=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findUnique({where:{id:t.id},include:{_count:{select:{tickets:!0}}}});if(!a)return o.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if("ORGANIZER"===e.user.role&&a.organizerId!==e.user.id)return o.Z.json({success:!1,message:"Anda tidak memiliki akses untuk menghapus event ini"},{status:403});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:{eventId:t.id,status:"ACTIVE"}});if(r>0)return o.Z.json({success:!1,message:`Event tidak dapat dihapus karena sudah ada ${r} tiket terjual`},{status:400});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.delete({where:{id:t.id}}),await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:e.user.id,type:"EVENT",title:"Event Dihapus",message:`Event "${a.title}" berhasil dihapus`,isRead:!1}}),o.Z.json({success:!0,message:"Event berhasil dihapus"})}catch(e){return console.error("Error deleting event:",e),o.Z.json({success:!1,message:"Gagal menghapus event"},{status:500})}}let g=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/events/[id]/route",pathname:"/api/events/[id]",filename:"route",bundlePath:"app/api/events/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\events\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:v,staticGenerationAsyncStorage:O,serverHooks:h,headerHooks:E,staticGenerationBailout:k}=g,b="/api/events/[id]/route";function D(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:O})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,1355,5252],()=>a(44186));module.exports=r})();