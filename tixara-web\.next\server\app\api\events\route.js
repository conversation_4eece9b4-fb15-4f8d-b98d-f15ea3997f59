"use strict";(()=>{var e={};e.id=3873,e.ids=[3873],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},95974:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>b,patchFetch:()=>x,requestAsyncStorage:()=>v,routeModule:()=>p,serverHooks:()=>O,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>D});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>g});var i=r(95419),n=r(69108),s=r(99678),o=r(78070),u=r(81355),c=r(25252),d=r(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=c.Ry({title:c.Z_().min(5,"Judul event minimal 5 karakter").max(200,"Judul event maksimal 200 karakter"),description:c.Z_().min(10,"Deskripsi minimal 10 karakter"),categoryId:c.Z_().min(1,"Kategori wajib dipilih"),location:c.Z_().min(5,"Lokasi minimal 5 karakter"),startDate:c.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),endDate:c.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),price:c.Rx().min(0,"Harga tidak boleh negatif"),maxTickets:c.Rx().min(1,"Maksimal tiket minimal 1"),image:c.Z_().optional(),isActive:c.O7().default(!0),requiresApproval:c.O7().default(!1)});async function m(e){try{let{searchParams:t}=new URL(e.url),r=t.get("categoryId"),a=t.get("organizerId"),i=t.get("active"),n=t.get("search"),s=parseInt(t.get("page")||"1"),c=parseInt(t.get("limit")||"10"),d=t.get("sortBy")||"createdAt",l=t.get("sortOrder")||"desc",m={};r&&(m.categoryId=r),a&&(m.organizerId=a),null!==i&&(m.isActive="true"===i),n&&(m.OR=[{title:{contains:n,mode:"insensitive"}},{description:{contains:n,mode:"insensitive"}},{location:{contains:n,mode:"insensitive"}}]);let g=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));g&&"BUYER"!==g.user.role||(m.endDate={gte:new Date});let p=(s-1)*c,[v,f]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findMany({where:m,include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0,_count:{select:{events:!0}}}},_count:{select:{tickets:!0}}},orderBy:{[d]:l},skip:p,take:c}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.count({where:m})]);return o.Z.json({success:!0,data:v,pagination:{page:s,limit:c,total:f,totalPages:Math.ceil(f/c)}})}catch(e){return console.error("Error fetching events:",e),o.Z.json({success:!1,message:"Gagal mengambil data event"},{status:500})}}async function g(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t||!["ORGANIZER","ADMIN"].includes(t.user.role))return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await e.json(),a=l.parse(r),i=new Date(a.startDate),n=new Date(a.endDate),s=new Date;if(i<s)return o.Z.json({success:!1,message:"Tanggal mulai tidak boleh di masa lalu"},{status:400});if(n<=i)return o.Z.json({success:!1,message:"Tanggal selesai harus setelah tanggal mulai"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).category.findUnique({where:{id:a.categoryId}}))return o.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:400});let c=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.create({data:{...a,startDate:i,endDate:n,organizerId:t.user.id},include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:t.user.id,type:"EVENT",title:"Event Berhasil Dibuat",message:`Event "${c.title}" berhasil dibuat dan sedang menunggu persetujuan`,isRead:!1,relatedId:c.id}}),o.Z.json({success:!0,data:c,message:"Event berhasil dibuat"})}catch(e){if(e instanceof d.jm)return o.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error creating event:",e),o.Z.json({success:!1,message:"Gagal membuat event"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/events/route",pathname:"/api/events",filename:"route",bundlePath:"app/api/events/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\events\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:v,staticGenerationAsyncStorage:f,serverHooks:O,headerHooks:h,staticGenerationBailout:D}=p,b="/api/events/route";function x(){return(0,s.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:f})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355,5252],()=>r(95974));module.exports=a})();