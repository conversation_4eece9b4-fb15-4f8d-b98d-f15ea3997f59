"use strict";(()=>{var e={};e.id=3873,e.ids=[3873],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},95974:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>b,originalPathname:()=>k,patchFetch:()=>j,requestAsyncStorage:()=>w,routeModule:()=>h,serverHooks:()=>x,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>y});var r={};t.r(r),t.d(r,{GET:()=>p,POST:()=>v});var i=t(95419),s=t(69108),n=t(99678),o=t(78070),l=t(81355),u=t(3205),d=t(3214),c=t(25252),g=t(52178);let m=c.Ry({title:c.Z_().min(5,"Judul event minimal 5 karakter").max(200,"Judul event maksimal 200 karakter"),description:c.Z_().min(10,"Deskripsi minimal 10 karakter"),categoryId:c.Z_().min(1,"Kategori wajib dipilih"),location:c.Z_().min(5,"Lokasi minimal 5 karakter"),startDate:c.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),endDate:c.Z_().refine(e=>!isNaN(Date.parse(e)),"Format tanggal tidak valid"),price:c.Rx().min(0,"Harga tidak boleh negatif"),maxTickets:c.Rx().min(1,"Maksimal tiket minimal 1"),image:c.Z_().optional(),isActive:c.O7().default(!0),requiresApproval:c.O7().default(!1)});async function p(e){try{let{searchParams:a}=new URL(e.url),t=a.get("categoryId"),r=a.get("organizerId"),i=a.get("active"),s=a.get("search"),n=parseInt(a.get("page")||"1"),c=parseInt(a.get("limit")||"10"),g=a.get("sortBy")||"createdAt",m=a.get("sortOrder")||"desc",p={};t&&(p.categoryId=t),r&&(p.organizerId=r),null!==i&&(p.isActive="true"===i),s&&(p.OR=[{title:{contains:s,mode:"insensitive"}},{description:{contains:s,mode:"insensitive"}},{location:{contains:s,mode:"insensitive"}}]);let v=await (0,l.getServerSession)(u.Lz);v&&"BUYER"!==v.user.role||(p.endDate={gte:new Date});let h=(n-1)*c,[w,f]=await Promise.all([d.prisma.event.findMany({where:p,include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0,_count:{select:{events:!0}}}},_count:{select:{tickets:!0}}},orderBy:{[g]:m},skip:h,take:c}),d.prisma.event.count({where:p})]);return o.Z.json({success:!0,data:w,pagination:{page:n,limit:c,total:f,totalPages:Math.ceil(f/c)}})}catch(e){return console.error("Error fetching events:",e),o.Z.json({success:!1,message:"Gagal mengambil data event"},{status:500})}}async function v(e){try{let a=await (0,l.getServerSession)(u.Lz);if(!a||!["ORGANIZER","ADMIN"].includes(a.user.role))return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let t=await e.json(),r=m.parse(t),i=new Date(r.startDate),s=new Date(r.endDate),n=new Date;if(i<n)return o.Z.json({success:!1,message:"Tanggal mulai tidak boleh di masa lalu"},{status:400});if(s<=i)return o.Z.json({success:!1,message:"Tanggal selesai harus setelah tanggal mulai"},{status:400});if(!await d.prisma.category.findUnique({where:{id:r.categoryId}}))return o.Z.json({success:!1,message:"Kategori tidak ditemukan"},{status:400});let c=await d.prisma.event.create({data:{...r,startDate:i,endDate:s,organizerId:a.user.id},include:{category:!0,organizer:{select:{id:!0,name:!0,email:!0,isVerified:!0,badge:!0}}}});return await d.prisma.notification.create({data:{userId:a.user.id,type:"EVENT",title:"Event Berhasil Dibuat",message:`Event "${c.title}" berhasil dibuat dan sedang menunggu persetujuan`,isRead:!1,relatedId:c.id}}),o.Z.json({success:!0,data:c,message:"Event berhasil dibuat"})}catch(e){if(e instanceof g.jm)return o.Z.json({success:!1,message:"Data tidak valid",errors:e.errors},{status:400});return console.error("Error creating event:",e),o.Z.json({success:!1,message:"Gagal membuat event"},{status:500})}}let h=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/events/route",pathname:"/api/events",filename:"route",bundlePath:"app/api/events/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\events\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:w,staticGenerationAsyncStorage:f,serverHooks:x,headerHooks:b,staticGenerationBailout:y}=h,k="/api/events/route";function j(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:f})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>l});var r=t(65822),i=t(86485),s=t(98432),n=t.n(s),o=t(3214);t(53524);let l={adapter:(0,r.N)(o.prisma),providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:r})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&r&&(e={...e,...r}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,t)=>{t.d(a,{prisma:()=>i});var r=t(53524);let i=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1638,6206,9155,5252],()=>t(95974));module.exports=r})();