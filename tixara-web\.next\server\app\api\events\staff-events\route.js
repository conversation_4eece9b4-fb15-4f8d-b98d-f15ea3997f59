"use strict";(()=>{var e={};e.id=4689,e.ids=[4689],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},97454:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>v,originalPathname:()=>x,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>f,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c});var n=r(95419),a=r(69108),o=r(99678),i=r(78070),u=r(81355);async function c(e){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});if(!["STAFF","ADMIN"].includes(e.user.role))return i.Z.json({success:!1,message:"Hanya staff yang dapat mengakses endpoint ini"},{status:403});let t=[];return t="ADMIN"===e.user.role?await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findMany({where:{isActive:!0,endDate:{gte:new Date}},select:{id:!0,title:!0,startDate:!0,endDate:!0,location:!0,organizer:{select:{id:!0,name:!0}},_count:{select:{tickets:!0}}},orderBy:{startDate:"asc"}}):(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).eventStaff.findMany({where:{staffId:e.user.id},include:{event:{where:{isActive:!0,endDate:{gte:new Date}},select:{id:!0,title:!0,startDate:!0,endDate:!0,location:!0,organizer:{select:{id:!0,name:!0}},_count:{select:{tickets:!0}}}}}})).filter(e=>e.event).map(e=>e.event),i.Z.json({success:!0,data:t})}catch(e){return console.error("Error fetching staff events:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let d=new n.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/events/staff-events/route",pathname:"/api/events/staff-events",filename:"route",bundlePath:"app/api/events/staff-events/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\events\\staff-events\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:f,headerHooks:v,staticGenerationBailout:m}=d,x="/api/events/staff-events/route";function h(){return(0,o.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:p})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,1355],()=>r(97454));module.exports=s})();