"use strict";(()=>{var e={};e.id=4689,e.ids=[4689],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},97454:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>v,originalPathname:()=>h,patchFetch:()=>x,requestAsyncStorage:()=>m,routeModule:()=>c,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>w});var r={};t.r(r),t.d(r,{GET:()=>p});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),u=t(81355),d=t(3205),l=t(3214);async function p(e){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if(!["STAFF","ADMIN"].includes(e.user.role))return o.Z.json({success:!1,message:"Hanya staff yang dapat mengakses endpoint ini"},{status:403});let a=[];return a="ADMIN"===e.user.role?await l.prisma.event.findMany({where:{isActive:!0,endDate:{gte:new Date}},select:{id:!0,title:!0,startDate:!0,endDate:!0,location:!0,organizer:{select:{id:!0,name:!0}},_count:{select:{tickets:!0}}},orderBy:{startDate:"asc"}}):(await l.prisma.eventStaff.findMany({where:{staffId:e.user.id},include:{event:{where:{isActive:!0,endDate:{gte:new Date}},select:{id:!0,title:!0,startDate:!0,endDate:!0,location:!0,organizer:{select:{id:!0,name:!0}},_count:{select:{tickets:!0}}}}}})).filter(e=>e.event).map(e=>e.event),o.Z.json({success:!0,data:a})}catch(e){return console.error("Error fetching staff events:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/events/staff-events/route",pathname:"/api/events/staff-events",filename:"route",bundlePath:"app/api/events/staff-events/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\events\\staff-events\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:f,headerHooks:v,staticGenerationBailout:w}=c,h="/api/events/staff-events/route";function x(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>u});var r=t(65822),s=t(86485),i=t(98432),n=t.n(i),o=t(3214);t(53524);let u={adapter:(0,r.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:r})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&r&&(e={...e,...r}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,t)=>{t.d(a,{prisma:()=>s});var r=t(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1638,6206,9155],()=>t(97454));module.exports=r})();