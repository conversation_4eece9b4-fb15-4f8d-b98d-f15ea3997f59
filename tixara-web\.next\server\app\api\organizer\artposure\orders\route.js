"use strict";(()=>{var e={};e.id=4797,e.ids=[4797],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},20170:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>f,originalPathname:()=>h,patchFetch:()=>v,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>O,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>g});var a={};t.r(a),t.d(a,{GET:()=>c,POST:()=>d});var s=t(95419),o=t(69108),n=t(99678),i=t(78070),u=t(81355);async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||!["ORGANIZER","ADMIN"].includes(r.user.role))return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),a=t.get("status"),s=parseInt(t.get("page")||"1"),o=parseInt(t.get("limit")||"20"),n=(s-1)*o,c={organizerId:r.user.id};a&&(c.status=a);let[d,l]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).artposureOrder.findMany({where:c,include:{service:{select:{name:!0,category:!0,price:!0}}},orderBy:{createdAt:"desc"},skip:n,take:o}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).artposureOrder.count({where:c})]);return i.Z.json({success:!0,data:d,pagination:{page:s,limit:o,total:l,totalPages:Math.ceil(l/o)}})}catch(e){return console.error("Get artposure orders error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function d(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||!["ORGANIZER","ADMIN"].includes(r.user.role))return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{serviceId:t,eventId:a,eventTitle:s,requirements:o}=await e.json();if(!t||!a&&!s||!o)return i.Z.json({success:!1,message:"Semua field wajib diisi"},{status:400});let n=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).artposureService.findUnique({where:{id:t}});if(!n)return i.Z.json({success:!1,message:"Layanan tidak ditemukan"},{status:404});if(!n.isActive)return i.Z.json({success:!1,message:"Layanan tidak tersedia"},{status:400});let c=s;if(a){let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findUnique({where:{id:a},select:{title:!0}});e&&(c=e.title)}let d=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).artposureOrder.create({data:{serviceId:t,organizerId:r.user.id,eventId:a||null,eventTitle:c,requirements:o,price:n.price,status:"PENDING"},include:{service:{select:{name:!0,category:!0,price:!0}},organizer:{select:{name:!0,email:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:r.user.id,title:"Order Artposure Baru",message:`Order baru untuk layanan ${n.name} dari ${r.user.name}`,type:"ARTPOSURE_ORDER",data:{orderId:d.id,serviceName:n.name,organizerName:r.user.name,eventTitle:c}}}),i.Z.json({success:!0,data:d,message:"Order Artposure berhasil dibuat"})}catch(e){return console.error("Create artposure order error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=new s.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/organizer/artposure/orders/route",pathname:"/api/organizer/artposure/orders",filename:"route",bundlePath:"app/api/organizer/artposure/orders/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\artposure\\orders\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:O,headerHooks:f,staticGenerationBailout:g}=l,h="/api/organizer/artposure/orders/route";function v(){return(0,n.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:m})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,1355],()=>t(20170));module.exports=a})();