"use strict";(()=>{var e={};e.id=4797,e.ids=[4797],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},20170:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>h,originalPathname:()=>f,patchFetch:()=>b,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>x});var t={};a.r(t),a.d(t,{GET:()=>c,POST:()=>p});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),d=a(3205),l=a(3214);async function c(e){try{let r=await (0,u.getServerSession)(d.Lz);if(!r?.user||!["ORGANIZER","ADMIN"].includes(r.user.role))return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),t=a.get("status"),s=parseInt(a.get("page")||"1"),i=parseInt(a.get("limit")||"20"),n=(s-1)*i,c={organizerId:r.user.id};t&&(c.status=t);let[p,m]=await Promise.all([l.prisma.artposureOrder.findMany({where:c,include:{service:{select:{name:!0,category:!0,price:!0}}},orderBy:{createdAt:"desc"},skip:n,take:i}),l.prisma.artposureOrder.count({where:c})]);return o.Z.json({success:!0,data:p,pagination:{page:s,limit:i,total:m,totalPages:Math.ceil(m/i)}})}catch(e){return console.error("Get artposure orders error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function p(e){try{let r=await (0,u.getServerSession)(d.Lz);if(!r?.user||!["ORGANIZER","ADMIN"].includes(r.user.role))return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{serviceId:t,eventId:s,eventTitle:i,requirements:n,paymentMethod:c="UANGTIX"}=await e.json();if(!t||!s&&!i||!n)return o.Z.json({success:!1,message:"Semua field wajib diisi"},{status:400});let p=await l.prisma.artposureService.findUnique({where:{id:t}});if(!p)return o.Z.json({success:!1,message:"Layanan tidak ditemukan"},{status:404});if(!p.isActive)return o.Z.json({success:!1,message:"Layanan tidak tersedia"},{status:400});let m=i;if(s){let e=await l.prisma.event.findUnique({where:{id:s},select:{title:!0}});e&&(m=e.title)}if("UANGTIX"===c){let e=await l.prisma.user.findUnique({where:{id:r.user.id},select:{uangtixBalance:!0}});if(!e||e.uangtixBalance<p.price)return o.Z.json({success:!1,message:"Saldo UangtiX tidak mencukupi"},{status:400});let a=await l.prisma.$transaction(async a=>{let i=await a.artposureOrder.create({data:{serviceId:t,organizerId:r.user.id,eventId:s||null,eventTitle:m,requirements:n,price:p.price,status:"PAID",paidAt:new Date},include:{service:{select:{name:!0,category:!0,price:!0}},organizer:{select:{name:!0,email:!0}}}}),o=e.uangtixBalance-p.price;return await a.user.update({where:{id:r.user.id},data:{uangtixBalance:o}}),await a.uangtiXTransaction.create({data:{userId:r.user.id,type:"PAYMENT",amount:p.price,description:`Pembayaran Artposure - ${p.name}`,reference:i.id,status:"SUCCESS",balanceBefore:e.uangtixBalance,balanceAfter:o}}),await a.notification.create({data:{userId:r.user.id,title:"Order Artposure Berhasil",message:`Order untuk layanan ${p.name} berhasil dibayar`,type:"ARTPOSURE_ORDER",data:{orderId:i.id,serviceName:p.name,eventTitle:m}}}),i});return o.Z.json({success:!0,data:a,message:"Order Artposure berhasil dibuat dan dibayar"})}{let{PaymentFactory:e}=await Promise.all([a.e(3949),a.e(6112)]).then(a.bind(a,76112)),i=`ARTPOSURE-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let a=e.createPaymentGateway(c),u={amount:p.price,description:`Artposure - ${p.name}`,customerName:r.user.name||"",customerEmail:r.user.email||"",customerPhone:r.user.phone||"",orderId:i,returnUrl:"http://localhost:3000/organizer/artposure/orders",expiredTime:60},d=await a.createPayment(u);if(!d.success)return o.Z.json({success:!1,message:d.message||"Gagal membuat pembayaran"},{status:400});let g=await l.prisma.$transaction(async e=>{let a=await e.artposureOrder.create({data:{serviceId:t,organizerId:r.user.id,eventId:s||null,eventTitle:m,requirements:n,price:p.price,status:"PENDING"},include:{service:{select:{name:!0,category:!0,price:!0}},organizer:{select:{name:!0,email:!0}}}});return await e.payment.create({data:{userId:r.user.id,amount:p.price,gateway:c,status:"PENDING",externalId:i,paymentUrl:d.paymentUrl,expiredAt:d.expiredAt,metadata:d.data,description:`Artposure - ${p.name}`,reference:a.id,type:"ARTPOSURE"}}),{order:a,paymentUrl:d.paymentUrl}});return o.Z.json({success:!0,data:g.order,paymentUrl:g.paymentUrl,message:"Order Artposure berhasil dibuat. Silakan lanjutkan pembayaran."})}catch(e){return console.error("Payment creation error:",e),o.Z.json({success:!1,message:"Gagal membuat pembayaran"},{status:500})}}}catch(e){return console.error("Create artposure order error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/organizer/artposure/orders/route",pathname:"/api/organizer/artposure/orders",filename:"route",bundlePath:"app/api/organizer/artposure/orders/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\artposure\\orders\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:g,staticGenerationAsyncStorage:y,serverHooks:w,headerHooks:h,staticGenerationBailout:x}=m,f="/api/organizer/artposure/orders/route";function b(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:y})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>u});var t=a(65822),s=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let u={adapter:(0,t.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:t})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&t&&(e={...e,...t}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>s});var t=a(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[1638,6206,9155],()=>a(20170));module.exports=t})();