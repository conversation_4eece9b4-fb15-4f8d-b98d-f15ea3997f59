"use strict";(()=>{var e={};e.id=4202,e.ids=[4202],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},67696:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>x,originalPathname:()=>w,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>c,serverHooks:()=>b,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>h});var t={};a.r(t),a.d(t,{GET:()=>p});var s=a(95419),i=a(69108),o=a(99678),n=a(78070),u=a(81355),l=a(3205),d=a(3214);async function p(e){try{let r=await (0,u.getServerSession)(l.Lz);if(!r?.user||!["ORGANIZER","ADMIN"].includes(r.user.role))return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:a}=new URL(e.url),t=a.get("status"),s=parseInt(a.get("page")||"1"),i=parseInt(a.get("limit")||"20"),o=(s-1)*i,p={organizerId:r.user.id};t&&(p.status=t);let[c,g]=await Promise.all([d.prisma.eventBoost.findMany({where:p,include:{event:{select:{id:!0,title:!0,slug:!0,startDate:!0}},package:{select:{name:!0,priority:!0,features:!0}}},orderBy:{createdAt:"desc"},skip:o,take:i}),d.prisma.eventBoost.count({where:p})]);return n.Z.json({success:!0,data:c,pagination:{page:s,limit:i,total:g,totalPages:Math.ceil(g/i)}})}catch(e){return console.error("Get event boosts error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/organizer/boost/boosts/route",pathname:"/api/organizer/boost/boosts",filename:"route",bundlePath:"app/api/organizer/boost/boosts/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\boost\\boosts\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:b,headerHooks:x,staticGenerationBailout:h}=c,w="/api/organizer/boost/boosts/route";function v(){return(0,o.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:m})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>u});var t=a(65822),s=a(86485),i=a(98432),o=a.n(i),n=a(3214);a(53524);let u={adapter:(0,t.N)(n.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await n.prisma.user.findUnique({where:{email:e.email}});if(!r||!await o().compare(e.password,r.password))throw Error("Email atau password salah");return await n.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:t})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&t&&(e={...e,...t}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await n.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>s});var t=a(53524);let s=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[1638,6206,9155],()=>a(67696));module.exports=t})();