"use strict";(()=>{var e={};e.id=2037,e.ids=[2037],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17704:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>g,originalPathname:()=>m,patchFetch:()=>b,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>x,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>h});var o={};t.r(o),t.d(o,{GET:()=>c});var s=t(95419),a=t(69108),n=t(99678),i=t(78070),u=t(81355);async function c(e){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user||!["ORGANIZER","ADMIN"].includes(e.user.role))return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findMany({where:{isActive:!0},orderBy:{priority:"desc"}});return i.Z.json({success:!0,data:r})}catch(e){return console.error("Get booster packages error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new s.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/organizer/boost/packages/route",pathname:"/api/organizer/boost/packages",filename:"route",bundlePath:"app/api/organizer/boost/packages/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\boost\\packages\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:x,headerHooks:g,staticGenerationBailout:h}=p,m="/api/organizer/boost/packages/route";function b(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:l})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1638,6206,1355],()=>t(17704));module.exports=o})();