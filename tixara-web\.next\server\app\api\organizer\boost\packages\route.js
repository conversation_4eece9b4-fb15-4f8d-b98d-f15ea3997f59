"use strict";(()=>{var e={};e.id=2037,e.ids=[2037],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17704:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>h,originalPathname:()=>b,patchFetch:()=>v,requestAsyncStorage:()=>g,routeModule:()=>c,serverHooks:()=>x,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>w});var s={};a.r(s),a.d(s,{GET:()=>l});var t=a(95419),i=a(69108),o=a(99678),n=a(78070),u=a(81355),p=a(3205),d=a(3214);async function l(e){try{let e=await (0,u.getServerSession)(p.Lz);if(!e?.user||!["ORGANIZER","ADMIN"].includes(e.user.role))return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await d.prisma.boosterPackage.findMany({where:{isActive:!0},orderBy:{priority:"desc"}});return n.Z.json({success:!0,data:r})}catch(e){return console.error("Get booster packages error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let c=new t.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/organizer/boost/packages/route",pathname:"/api/organizer/boost/packages",filename:"route",bundlePath:"app/api/organizer/boost/packages/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\boost\\packages\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:x,headerHooks:h,staticGenerationBailout:w}=c,b="/api/organizer/boost/packages/route";function v(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:m})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>u});var s=a(65822),t=a(86485),i=a(98432),o=a.n(i),n=a(3214);a(53524);let u={adapter:(0,s.N)(n.prisma),providers:[(0,t.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await n.prisma.user.findUnique({where:{email:e.email}});if(!r||!await o().compare(e.password,r.password))throw Error("Email atau password salah");return await n.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:s})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&s&&(e={...e,...s}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await n.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>t});var s=a(53524);let t=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var r=require("../../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),s=r.X(0,[1638,6206,9155],()=>a(17704));module.exports=s})();