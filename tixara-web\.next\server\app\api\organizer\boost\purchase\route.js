"use strict";(()=>{var e={};e.id=7023,e.ids=[7023],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},5450:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>g,originalPathname:()=>b,patchFetch:()=>f,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>h});var s={};r.r(s),r.d(s,{POST:()=>c});var a=r(95419),o=r(69108),i=r(99678),n=r(78070),u=r(81355);async function c(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user||!["ORGANIZER","ADMIN"].includes(t.user.role))return n.Z.json({success:!1,message:"Unauthorized"},{status:401});let{packageId:r,eventId:s}=await e.json();if(!r||!s)return n.Z.json({success:!1,message:"Package ID dan Event ID wajib diisi"},{status:400});let a=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).boosterPackage.findUnique({where:{id:r}});if(!a)return n.Z.json({success:!1,message:"Paket boost tidak ditemukan"},{status:404});if(!a.isActive)return n.Z.json({success:!1,message:"Paket boost tidak tersedia"},{status:400});let o=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findUnique({where:{id:s},include:{organizer:{select:{id:!0,name:!0}}}});if(!o)return n.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if(o.organizerId!==t.user.id&&"ADMIN"!==t.user.role)return n.Z.json({success:!1,message:"Anda tidak memiliki akses ke event ini"},{status:403});if(o.isBoosted)return n.Z.json({success:!1,message:"Event sudah dalam status boost"},{status:400});if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).eventBoost.findFirst({where:{eventId:s,status:"ACTIVE"}}))return n.Z.json({success:!1,message:"Event sudah memiliki boost aktif"},{status:400});if(new Date(o.startDate)<=new Date)return n.Z.json({success:!1,message:"Tidak dapat boost event yang sudah dimulai"},{status:400});let i=new Date,c=new Date;c.setDate(c.getDate()+a.duration);let d=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{let n=await e.eventBoost.create({data:{packageId:r,eventId:s,organizerId:t.user.id,startDate:i,endDate:c,price:a.price,status:"ACTIVE"},include:{event:{select:{title:!0,slug:!0}},package:{select:{name:!0,priority:!0}}}});return await e.event.update({where:{id:s},data:{isBoosted:!0,boostEndDate:c}}),await e.notification.create({data:{userId:t.user.id,title:"Boost Event Berhasil",message:`Event "${o.title}" berhasil di-boost dengan paket ${a.name}`,type:"BOOST_ACTIVATED",data:{boostId:n.id,eventId:s,packageName:a.name,endDate:c.toISOString()}}}),n});return n.Z.json({success:!0,data:d,message:"Event boost berhasil dibeli"})}catch(e){return console.error("Purchase boost error:",e),n.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let d=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/organizer/boost/purchase/route",pathname:"/api/organizer/boost/purchase",filename:"route",bundlePath:"app/api/organizer/boost/purchase/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\boost\\purchase\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:m,headerHooks:g,staticGenerationBailout:h}=d,b="/api/organizer/boost/purchase/route";function f(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,1355],()=>r(5450));module.exports=s})();