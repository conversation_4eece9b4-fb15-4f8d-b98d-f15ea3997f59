"use strict";(()=>{var e={};e.id=7023,e.ids=[7023],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},5450:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>w,originalPathname:()=>x,patchFetch:()=>f,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>b,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>h});var s={};t.r(s),t.d(s,{POST:()=>l});var r=t(95419),i=t(69108),n=t(99678),o=t(78070),u=t(81355),d=t(3205),c=t(3214);async function l(e){try{let a=await (0,u.getServerSession)(d.Lz);if(!a?.user||!["ORGANIZER","ADMIN"].includes(a.user.role))return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{packageId:s,eventId:r,paymentMethod:i="UANGTIX"}=await e.json();if(!s||!r)return o.Z.json({success:!1,message:"Package ID dan Event ID wajib diisi"},{status:400});let n=await c.prisma.boosterPackage.findUnique({where:{id:s}});if(!n)return o.Z.json({success:!1,message:"Paket boost tidak ditemukan"},{status:404});if(!n.isActive)return o.Z.json({success:!1,message:"Paket boost tidak tersedia"},{status:400});let l=await c.prisma.event.findUnique({where:{id:r},include:{organizer:{select:{id:!0,name:!0}}}});if(!l)return o.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if(l.organizerId!==a.user.id&&"ADMIN"!==a.user.role)return o.Z.json({success:!1,message:"Anda tidak memiliki akses ke event ini"},{status:403});if(l.isBoosted)return o.Z.json({success:!1,message:"Event sudah dalam status boost"},{status:400});if(await c.prisma.eventBoost.findFirst({where:{eventId:r,status:"ACTIVE"}}))return o.Z.json({success:!1,message:"Event sudah memiliki boost aktif"},{status:400});if(new Date(l.startDate)<=new Date)return o.Z.json({success:!1,message:"Tidak dapat boost event yang sudah dimulai"},{status:400});let p=new Date,m=new Date;if(m.setDate(m.getDate()+n.duration),"UANGTIX"===i){let e=await c.prisma.user.findUnique({where:{id:a.user.id},select:{uangtixBalance:!0}});if(!e||e.uangtixBalance<n.price)return o.Z.json({success:!1,message:"Saldo UangtiX tidak mencukupi"},{status:400});let t=await c.prisma.$transaction(async t=>{let i=await t.eventBoost.create({data:{packageId:s,eventId:r,organizerId:a.user.id,startDate:p,endDate:m,price:n.price,status:"ACTIVE"},include:{event:{select:{title:!0,slug:!0}},package:{select:{name:!0,priority:!0}}}});await t.event.update({where:{id:r},data:{isBoosted:!0,boostEndDate:m}});let o=e.uangtixBalance-n.price;return await t.user.update({where:{id:a.user.id},data:{uangtixBalance:o}}),await t.uangtiXTransaction.create({data:{userId:a.user.id,type:"PAYMENT",amount:n.price,description:`Boost Event - ${n.name}`,reference:i.id,status:"SUCCESS",balanceBefore:e.uangtixBalance,balanceAfter:o}}),await t.notification.create({data:{userId:a.user.id,title:"Boost Event Berhasil",message:`Event "${l.title}" berhasil di-boost dengan paket ${n.name}`,type:"BOOST_ACTIVATED",data:{boostId:i.id,eventId:r,packageName:n.name,endDate:m.toISOString()}}}),i});return o.Z.json({success:!0,data:t,message:"Event boost berhasil dibeli dan diaktifkan"})}{let{PaymentFactory:e}=await Promise.all([t.e(3949),t.e(6112)]).then(t.bind(t,76112)),u=`BOOST-${Date.now()}-${Math.random().toString(36).substr(2,9)}`;try{let t=e.createPaymentGateway(i),d={amount:n.price,description:`Event Boost - ${n.name}`,customerName:a.user.name||"",customerEmail:a.user.email||"",customerPhone:a.user.phone||"",orderId:u,returnUrl:"http://localhost:3000/organizer/boost",expiredTime:60},l=await t.createPayment(d);if(!l.success)return o.Z.json({success:!1,message:l.message||"Gagal membuat pembayaran"},{status:400});let g=await c.prisma.$transaction(async e=>{let t=await e.eventBoost.create({data:{packageId:s,eventId:r,organizerId:a.user.id,startDate:p,endDate:m,price:n.price,status:"PENDING"},include:{event:{select:{title:!0,slug:!0}},package:{select:{name:!0,priority:!0}}}});return await e.payment.create({data:{userId:a.user.id,amount:n.price,gateway:i,status:"PENDING",externalId:u,paymentUrl:l.paymentUrl,expiredAt:l.expiredAt,metadata:l.data,description:`Event Boost - ${n.name}`,reference:t.id,type:"BOOST"}}),{boost:t,paymentUrl:l.paymentUrl}});return o.Z.json({success:!0,data:g.boost,paymentUrl:g.paymentUrl,message:"Order boost berhasil dibuat. Silakan lanjutkan pembayaran untuk mengaktifkan boost."})}catch(e){return console.error("Payment creation error:",e),o.Z.json({success:!1,message:"Gagal membuat pembayaran"},{status:500})}}}catch(e){return console.error("Purchase boost error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let p=new r.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/organizer/boost/purchase/route",pathname:"/api/organizer/boost/purchase",filename:"route",bundlePath:"app/api/organizer/boost/purchase/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\boost\\purchase\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:b,headerHooks:w,staticGenerationBailout:h}=p,x="/api/organizer/boost/purchase/route";function f(){return(0,n.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:g})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>u});var s=t(65822),r=t(86485),i=t(98432),n=t.n(i),o=t(3214);t(53524);let u={adapter:(0,s.N)(o.prisma),providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:s})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&s&&(e={...e,...s}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,t)=>{t.d(a,{prisma:()=>r});var s=t(53524);let r=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var a=require("../../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[1638,6206,9155],()=>t(5450));module.exports=s})();