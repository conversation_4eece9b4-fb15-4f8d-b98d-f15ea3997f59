"use strict";(()=>{var e={};e.id=4072,e.ids=[4072],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},58510:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>v,originalPathname:()=>m,patchFetch:()=>h,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>x});var o={};t.r(o),t.d(o,{GET:()=>c});var n=t(95419),s=t(69108),a=t(99678),i=t(78070),u=t(81355);async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user||!["ORGANIZER","ADMIN"].includes(r.user.role))return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),o=t.get("active"),n=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"50"),a=(n-1)*s,c={};"ORGANIZER"===r.user.role&&(c.organizerId=r.user.id),null!==o&&(c.isActive="true"===o);let[l,p]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findMany({where:c,select:{id:!0,title:!0,slug:!0,startDate:!0,endDate:!0,location:!0,isActive:!0,isBoosted:!0},orderBy:{startDate:"asc"},skip:a,take:s}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.count({where:c})]);return i.Z.json({success:!0,data:l,pagination:{page:n,limit:s,total:p,totalPages:Math.ceil(p/s)}})}catch(e){return console.error("Get organizer events error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/organizer/events/route",pathname:"/api/organizer/events",filename:"route",bundlePath:"app/api/organizer/events/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\organizer\\events\\route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:g,headerHooks:v,staticGenerationBailout:x}=l,m="/api/organizer/events/route";function h(){return(0,a.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:d})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[1638,6206,1355],()=>t(58510));module.exports=o})();