"use strict";(()=>{var t={};t.id=9755,t.ids=[9755],t.modules={72934:t=>{t.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:t=>{t.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:t=>{t.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:t=>{t.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:t=>{t.exports=require("assert")},14300:t=>{t.exports=require("buffer")},6113:t=>{t.exports=require("crypto")},82361:t=>{t.exports=require("events")},13685:t=>{t.exports=require("http")},95687:t=>{t.exports=require("https")},63477:t=>{t.exports=require("querystring")},57310:t=>{t.exports=require("url")},73837:t=>{t.exports=require("util")},59796:t=>{t.exports=require("zlib")},43934:(t,e,a)=>{a.r(e),a.d(e,{headerHooks:()=>w,originalPathname:()=>y,patchFetch:()=>g,requestAsyncStorage:()=>p,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>D});var r={};a.r(r),a.d(r,{GET:()=>c});var s=a(95419),n=a(69108),o=a(99678),i=a(78070),u=a(81355);async function c(t,{params:e}){try{let t=await (0,u.getServerSession)(Object(function(){var t=Error("Cannot find module '@/lib/auth'");throw t.code="MODULE_NOT_FOUND",t}()));if(!t?.user)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{orderId:a}=e,r=await Object(function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}()).payment.findFirst({where:{externalId:a,userId:t.user.id},include:{uangtiXTransaction:!0}});if(!r)return i.Z.json({success:!1,message:"Payment not found"},{status:404});if("PAID"===r.status)return i.Z.json({success:!0,data:{status:r.status,paidAt:r.paidAt}});try{let t;let e=Object(function(){var t=Error("Cannot find module '@/lib/payment-utils'");throw t.code="MODULE_NOT_FOUND",t}()).createPaymentGateway(r.gateway);switch(r.gateway){case"TRIPAY":case"MIDTRANS":t=await e.checkPaymentStatus(r.externalId);break;case"XENDIT":let a=r.metadata,s=a?.id||r.externalId;t=await e.checkPaymentStatus(s);break;default:throw Error(`Unsupported gateway: ${r.gateway}`)}let n=r.status,o=r.paidAt;return"TRIPAY"===r.gateway?t.data?.status==="PAID"?(n="PAID",o=new Date):t.data?.status==="EXPIRED"?n="EXPIRED":t.data?.status==="FAILED"&&(n="FAILED"):"MIDTRANS"===r.gateway?"settlement"===t.transaction_status||"capture"===t.transaction_status?(n="PAID",o=new Date):"expire"===t.transaction_status?n="EXPIRED":("cancel"===t.transaction_status||"deny"===t.transaction_status)&&(n="FAILED"):"XENDIT"===r.gateway&&("SETTLED"===t.status?(n="PAID",o=new Date):"EXPIRED"===t.status&&(n="EXPIRED")),n!==r.status&&await Object(function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}()).$transaction(async t=>{if(await t.payment.update({where:{id:r.id},data:{status:n,paidAt:"PAID"===n?o:null}}),"PAID"===n&&r.uangtiXTransaction){let e=await t.user.findUnique({where:{id:r.userId},select:{uangtixBalance:!0}});if(e){let a=e.uangtixBalance+r.amount;await t.user.update({where:{id:r.userId},data:{uangtixBalance:a}}),await t.uangtiXTransaction.update({where:{id:r.uangtiXTransaction.id},data:{status:"SUCCESS",balanceBefore:e.uangtixBalance,balanceAfter:a}}),await t.notification.create({data:{userId:r.userId,title:"Top Up Berhasil",message:`Top Up UangtiX sebesar ${r.amount.toLocaleString("id-ID")} berhasil. Saldo Anda sekarang ${a.toLocaleString("id-ID")}`,type:"PAYMENT_SUCCESS",isRead:!1}})}}}),i.Z.json({success:!0,data:{status:n,paidAt:o,gatewayData:t}})}catch(t){return console.error("Gateway status check error:",t),i.Z.json({success:!0,data:{status:r.status,paidAt:r.paidAt,error:"Failed to check gateway status"}})}}catch(t){return console.error("Check payment status error:",t),i.Z.json({success:!1,message:"Internal server error"},{status:500})}}(function(){var t=Error("Cannot find module '@/lib/auth'");throw t.code="MODULE_NOT_FOUND",t})(),function(){var t=Error("Cannot find module '@/lib/prisma'");throw t.code="MODULE_NOT_FOUND",t}(),function(){var t=Error("Cannot find module '@/lib/payment-utils'");throw t.code="MODULE_NOT_FOUND",t}();let d=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/payments/[orderId]/status/route",pathname:"/api/payments/[orderId]/status",filename:"route",bundlePath:"app/api/payments/[orderId]/status/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\payments\\[orderId]\\status\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:l,serverHooks:m,headerHooks:w,staticGenerationBailout:D}=d,y="/api/payments/[orderId]/status/route";function g(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:l})}}};var e=require("../../../../../webpack-runtime.js");e.C(t);var a=t=>e(e.s=t),r=e.X(0,[1638,6206,1355],()=>a(43934));module.exports=r})();