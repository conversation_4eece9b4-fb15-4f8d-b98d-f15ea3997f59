"use strict";(()=>{var e={};e.id=9792,e.ids=[9792],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},85931:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>f,originalPathname:()=>h,patchFetch:()=>U,requestAsyncStorage:()=>m,routeModule:()=>l,serverHooks:()=>O,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>y});var a={};r.r(a),r.d(a,{POST:()=>d});var n=r(95419),s=r(69108),i=r(99678),o=r(78070),u=r(81355),c=r(63721);async function d(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{eventId:r,quantity:a=1,gateway:n="UANGTIX",paymentMethod:s="QRIS"}=await e.json();if(!r)return o.Z.json({success:!1,message:"Event ID diperlukan"},{status:400});let i=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findUnique({where:{id:r},include:{organizer:{select:{id:!0,name:!0,email:!0}},category:{select:{name:!0}}}});if(!i)return o.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if(!i.isActive)return o.Z.json({success:!1,message:"Event tidak aktif"},{status:400});if(i.totalTickets-i.soldTickets<a)return o.Z.json({success:!1,message:"Tiket tidak tersedia"},{status:400});if(new Date>i.endDate)return o.Z.json({success:!1,message:"Event sudah berakhir"},{status:400});let d=i.price,l=d*a,m=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).systemSettings.findFirst(),p=m?.adminCommissionRate||5,O=Math.round(p/100*l),f=l+O,y=`TIX-${(0,c.x0)(10)}`;if("UANGTIX"===n){if(await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBalance(t.user.id)<f)return o.Z.json({success:!1,message:"Saldo UangtiX tidak mencukupi"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).addBalance(t.user.id,-f,`Pembelian tiket ${i.title}`,y))return o.Z.json({success:!1,message:"Gagal memproses pembayaran UangtiX"},{status:500});let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).payment.create({data:{userId:t.user.id,gateway:"UANGTIX",externalId:y,amount:l,adminFee:O,totalAmount:f,description:`Tiket ${i.title}`,status:"PAID",paidAt:new Date}}),n=[];for(let s=0;s<a;s++){let{generateTicketCode:s,generateQRCode:i}=await Promise.resolve().then(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}),o=s(),u=`TIXARA:${y}:${r}:${Date.now()}`,c=await i(u),l=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.create({data:{eventId:r,buyerId:t.user.id,paymentId:e.id,qrCode:c,ticketCode:o,price:d,adminFee:O/a,totalPaid:f/a,paymentMethod:"UANGTIX"}});n.push(l)}return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.update({where:{id:r},data:{soldTickets:{increment:a}}}),await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).addBalance(i.organizer.id,l-O,`Komisi penjualan tiket ${i.title}`,y),o.Z.json({success:!0,message:"Pembayaran berhasil",data:{paymentId:e.id,orderId:y,tickets:n,totalAmount:f,paymentMethod:"UANGTIX"}})}try{let e=Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).createPaymentGateway(n),u={amount:f,description:`Tiket ${i.title}`,customerName:t.user.name,customerEmail:t.user.email,customerPhone:t.user.phone||"",orderId:y,returnUrl:`http://localhost:3000/payment/success?orderId=${y}`,expiredTime:60},c=await e.createPayment(u,s);if(!c.success)return o.Z.json({success:!1,message:c.message},{status:400});let m=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).payment.create({data:{userId:t.user.id,gateway:n,externalId:c.paymentId,amount:l,adminFee:O,totalAmount:f,description:`Tiket ${i.title}`,status:"PENDING",paymentUrl:c.paymentUrl,expiredAt:c.expiredAt,metadata:c.data}}),p=[];for(let e=0;e<a;e++){let{generateTicketCode:e,generateQRCode:s}=await Promise.resolve().then(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}),i=e(),o=`TIXARA:${y}:${r}:${Date.now()}`,u=await s(o),c=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.create({data:{eventId:r,buyerId:t.user.id,paymentId:m.id,qrCode:u,ticketCode:i,price:d,adminFee:O/a,totalPaid:f/a,paymentMethod:n}});p.push(c)}return o.Z.json({success:!0,message:"Payment created successfully",data:{paymentId:m.id,orderId:y,paymentUrl:c.paymentUrl,qrCode:c.qrCode,expiredAt:c.expiredAt,tickets:p,totalAmount:f,gateway:n}})}catch(e){return console.error("Payment creation error:",e),o.Z.json({success:!1,message:e.message||"Gagal membuat pembayaran"},{status:500})}}catch(e){return console.error("Payment API error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/payments/create/route",pathname:"/api/payments/create",filename:"route",bundlePath:"app/api/payments/create/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\payments\\create\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:p,serverHooks:O,headerHooks:f,staticGenerationBailout:y}=l,h="/api/payments/create/route";function U(){return(0,i.patchFetch)({serverHooks:O,staticGenerationAsyncStorage:p})}},63721:(e,t,r)=>{let a,n;r.d(t,{x0:()=>i});let s=require("node:crypto");function i(e=21){var t;t=e|=0,!a||a.length<t?(a=Buffer.allocUnsafe(128*t),s.webcrypto.getRandomValues(a),n=0):n+t>a.length&&(s.webcrypto.getRandomValues(a),n=0),n+=t;let r="";for(let t=n-e;t<n;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&a[t]];return r}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355],()=>r(85931));module.exports=a})();