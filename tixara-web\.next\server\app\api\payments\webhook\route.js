"use strict";(()=>{var e={};e.id=1004,e.ids=[1004,6112],e.modules={53524:e=>{e.exports=require("@prisma/client")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},71671:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>y,originalPathname:()=>f,patchFetch:()=>w,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>g});var r={};a.r(r),a.d(r,{POST:()=>u});var s=a(95419),n=a(69108),i=a(99678),o=a(78070),c=a(3214),d=a(76112);async function u(e){try{let t=await e.json(),a=e.headers.get("x-signature")||e.headers.get("x-callback-signature")||"",r=e.headers.get("x-gateway")||"TRIPAY";console.log("Webhook received:",{gateway:r,body:t});let s=null,n=!1;switch(r.toUpperCase()){case"TRIPAY":n=new d.YZ().verifyCallback(t,a),s=t;break;case"MIDTRANS":n=new d.Z4().verifyCallback(t,a),s=t;break;case"XENDIT":let i=new d.H4,u=e.headers.get("x-callback-token")||"";n=i.verifyCallback(t,u),s=t;break;default:return o.Z.json({success:!1,message:"Unsupported gateway"},{status:400})}if(!n)return console.error("Invalid webhook signature"),o.Z.json({success:!1,message:"Invalid signature"},{status:401});let l="",p="",m="";switch(r.toUpperCase()){case"TRIPAY":l=s.merchant_ref,m=s.reference,p=s.status;break;case"MIDTRANS":l=s.order_id,m=s.transaction_id,p=s.transaction_status;break;case"XENDIT":l=s.external_id,m=s.id,p=s.status}let h=await c.prisma.payment.findFirst({where:{OR:[{externalId:m},{description:{contains:l}}]},include:{user:{select:{id:!0,name:!0,email:!0}},tickets:{include:{event:{include:{organizer:{select:{id:!0,name:!0}}}}}}}});if(!h)return console.error("Payment not found:",{orderId:l,externalId:m}),o.Z.json({success:!1,message:"Payment not found"},{status:404});let y=function(e,t){switch(t.toUpperCase()){case"TRIPAY":case"XENDIT":return"PAID"===e;case"MIDTRANS":return["capture","settlement"].includes(e);default:return!1}}(p,r),g=g(p,r);return y&&"PAID"!==h.status?(await c.prisma.$transaction(async e=>{if(await e.payment.update({where:{id:h.id},data:{status:"PAID",paidAt:new Date,metadata:s}}),"TICKET"===h.type&&h.tickets.length>0){let t=h.tickets[0].eventId;await e.event.update({where:{id:t},data:{soldTickets:{increment:h.tickets.length}}});let a=h.tickets[0].event,r=h.amount;await d.UangtiXWallet.addBalance(a.organizer.id,r,`Komisi penjualan tiket ${a.title}`,l)}else if("ARTPOSURE"===h.type)await e.artposureOrder.update({where:{id:h.reference},data:{status:"PAID",paidAt:new Date}}),await e.notification.create({data:{userId:h.userId,title:"Pembayaran Artposure Berhasil",message:"Pembayaran untuk layanan Artposure berhasil. Order Anda akan segera diproses.",type:"ARTPOSURE_PAYMENT",data:{orderId:h.reference,amount:h.amount}}});else if("BOOST"===h.type){let t=await e.eventBoost.update({where:{id:h.reference},data:{status:"ACTIVE"},include:{event:!0,package:!0}});await e.event.update({where:{id:t.eventId},data:{isBoosted:!0,boostEndDate:t.endDate}}),await e.notification.create({data:{userId:h.userId,title:"Boost Event Diaktifkan",message:`Event "${t.event.title}" berhasil di-boost dengan paket ${t.package.name}`,type:"BOOST_ACTIVATED",data:{boostId:t.id,eventId:t.eventId,packageName:t.package.name,endDate:t.endDate.toISOString()}}})}else"UANGTIX_DEPOSIT"===h.type&&await d.UangtiXWallet.addBalance(h.userId,h.amount,`Top up UangtiX via ${h.gateway}`,l);await e.notification.create({data:{userId:h.userId,title:"Pembayaran Berhasil",message:`Pembayaran untuk ${h.description} telah berhasil diproses`,type:"PAYMENT_SUCCESS",data:{paymentId:h.id,orderId:l,amount:h.totalAmount}}})}),console.log("Payment processed successfully:",{orderId:l,paymentId:h.id})):g&&"PENDING"===h.status&&(await c.prisma.$transaction(async e=>{await e.payment.update({where:{id:h.id},data:{status:"FAILED",metadata:s}}),"TICKET"===h.type?await e.ticket.deleteMany({where:{paymentId:h.id}}):"ARTPOSURE"===h.type?await e.artposureOrder.update({where:{id:h.reference},data:{status:"FAILED"}}):"BOOST"===h.type&&await e.eventBoost.delete({where:{id:h.reference}}),await e.notification.create({data:{userId:h.userId,title:"Pembayaran Gagal",message:`Pembayaran untuk ${h.description} gagal diproses`,type:"PAYMENT_FAILED",data:{paymentId:h.id,orderId:l,reason:p}}})}),console.log("Payment failed:",{orderId:l,paymentId:h.id,status:p})),o.Z.json({success:!0,message:"Webhook processed"})}catch(e){return console.error("Webhook processing error:",e),o.Z.json({success:!1,message:"Webhook processing failed"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/payments/webhook/route",pathname:"/api/payments/webhook",filename:"route",bundlePath:"app/api/payments/webhook/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\payments\\webhook\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:h,headerHooks:y,staticGenerationBailout:g}=l,f="/api/payments/webhook/route";function w(){return(0,i.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:m})}},76112:(e,t,a)=>{a.d(t,{H4:()=>d,PaymentFactory:()=>l,UangtiXWallet:()=>u,YZ:()=>o,Z4:()=>c});var r=a(6113),s=a.n(r),n=a(83949);let i={TRIPAY:{baseUrl:process.env.TRIPAY_BASE_URL||"https://tripay.co.id/api-sandbox",merchantCode:process.env.TRIPAY_MERCHANT_CODE||"",apiKey:process.env.TRIPAY_API_KEY||"",privateKey:process.env.TRIPAY_PRIVATE_KEY||""},MIDTRANS:{baseUrl:process.env.MIDTRANS_BASE_URL||"https://api.sandbox.midtrans.com/v2",serverKey:process.env.MIDTRANS_SERVER_KEY||"",clientKey:process.env.MIDTRANS_CLIENT_KEY||""},XENDIT:{baseUrl:process.env.XENDIT_BASE_URL||"https://api.xendit.co",secretKey:process.env.XENDIT_SECRET_KEY||""}};class o{generateSignature(e){let t=JSON.stringify(e);return s().createHmac("sha256",this.config.privateKey).update(t).digest("hex")}async getPaymentChannels(){try{let e=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+this.config.apiKey).digest("hex");return(await n.Z.get(`${this.config.baseUrl}/merchant/payment-channel`,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":e}})).data}catch(e){throw console.error("Tripay get channels error:",e),e}}async createPayment(e,t="QRIS"){try{let a=e.expiredTime||60,r=new Date(Date.now()+6e4*a),i={method:t,merchant_ref:e.orderId,amount:e.amount,customer_name:e.customerName,customer_email:e.customerEmail,customer_phone:e.customerPhone||"",order_items:[{sku:"TICKET",name:e.description,price:e.amount,quantity:1}],return_url:e.returnUrl||"http://localhost:3000/payment/success",expired_time:Math.floor(r.getTime()/1e3),signature:""},o=this.config.merchantCode+e.orderId+e.amount;i.signature=s().createHmac("sha256",this.config.privateKey).update(o).digest("hex");let c=await n.Z.post(`${this.config.baseUrl}/transaction/create`,i,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}});if(c.data.success)return{success:!0,paymentId:c.data.data.reference,paymentUrl:c.data.data.checkout_url,qrCode:c.data.data.qr_url,expiredAt:r,data:c.data.data};return{success:!1,paymentId:"",message:c.data.message}}catch(e){return console.error("Tripay create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let t=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+e).digest("hex");return(await n.Z.get(`${this.config.baseUrl}/transaction/detail`,{params:{reference:e},headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":t}})).data}catch(e){throw console.error("Tripay check status error:",e),e}}verifyCallback(e,t){return s().createHmac("sha256",this.config.privateKey).update(JSON.stringify(e)).digest("hex")===t}constructor(){this.config=i.TRIPAY}}class c{async createPayment(e){try{let t={transaction_details:{order_id:e.orderId,gross_amount:e.amount},customer_details:{first_name:e.customerName,email:e.customerEmail,phone:e.customerPhone||""},item_details:[{id:"TICKET",price:e.amount,quantity:1,name:e.description}],credit_card:{secure:!0}},a=Buffer.from(this.config.serverKey+":").toString("base64"),r=await n.Z.post(`${this.config.baseUrl}/charge`,t,{headers:{Authorization:`Basic ${a}`,"Content-Type":"application/json"}});if("201"===r.data.status_code)return{success:!0,paymentId:r.data.transaction_id,paymentUrl:r.data.redirect_url,data:r.data};return{success:!1,paymentId:"",message:r.data.status_message}}catch(e){return console.error("Midtrans create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.status_message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let t=Buffer.from(this.config.serverKey+":").toString("base64");return(await n.Z.get(`${this.config.baseUrl}/${e}/status`,{headers:{Authorization:`Basic ${t}`}})).data}catch(e){throw console.error("Midtrans check status error:",e),e}}verifyCallback(e,t){let a=e.order_id,r=e.status_code,n=e.gross_amount;return s().createHash("sha512").update(a+r+n+this.config.serverKey).digest("hex")===t}constructor(){this.config=i.MIDTRANS}}class d{async createPayment(e){try{let t=e.expiredTime||60,a=new Date(Date.now()+6e4*t),r={external_id:e.orderId,amount:e.amount,description:e.description,payer_email:e.customerEmail,success_redirect_url:e.returnUrl||"http://localhost:3000/payment/success",failure_redirect_url:e.returnUrl||"http://localhost:3000/payment/failed",invoice_duration:60*t},s=Buffer.from(this.config.secretKey+":").toString("base64"),i=await n.Z.post(`${this.config.baseUrl}/v2/invoices`,r,{headers:{Authorization:`Basic ${s}`,"Content-Type":"application/json"}});return{success:!0,paymentId:i.data.id,paymentUrl:i.data.invoice_url,expiredAt:a,data:i.data}}catch(e){return console.error("Xendit create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let t=Buffer.from(this.config.secretKey+":").toString("base64");return(await n.Z.get(`${this.config.baseUrl}/v2/invoices/${e}`,{headers:{Authorization:`Basic ${t}`}})).data}catch(e){throw console.error("Xendit check status error:",e),e}}verifyCallback(e,t){return t===process.env.XENDIT_WEBHOOK_TOKEN}constructor(){this.config=i.XENDIT}}class u{static async getBalance(e){let{prisma:t}=await Promise.resolve().then(a.bind(a,3214)),r=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});return r?.uangtixBalance||0}static async addBalance(e,t,r,s){let{prisma:n}=await Promise.resolve().then(a.bind(a,3214));try{return await n.$transaction(async a=>{let n=await a.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!n)throw Error("User not found");let i=n.uangtixBalance,o=i+t;await a.user.update({where:{id:e},data:{uangtixBalance:o}}),await a.uangtiXTransaction.create({data:{userId:e,type:t>0?"DEPOSIT":"WITHDRAW",amount:Math.abs(t),description:r,reference:s,status:"SUCCESS",balanceBefore:i,balanceAfter:o}})}),!0}catch(e){return console.error("UangtiX add balance error:",e),!1}}static async transfer(e,t,r,s){let{prisma:n}=await Promise.resolve().then(a.bind(a,3214));try{return await n.$transaction(async a=>{let n=await a.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!n||n.uangtixBalance<r)throw Error("Insufficient balance");let i=await a.user.findUnique({where:{id:t},select:{uangtixBalance:!0}});if(!i)throw Error("Receiver not found");let o=n.uangtixBalance,c=o-r;await a.user.update({where:{id:e},data:{uangtixBalance:c}});let d=i.uangtixBalance,u=d+r;await a.user.update({where:{id:t},data:{uangtixBalance:u}}),await a.uangtiXTransaction.createMany({data:[{userId:e,type:"TRANSFER",amount:-r,description:`Transfer ke ${t}: ${s}`,reference:t,status:"SUCCESS",balanceBefore:o,balanceAfter:c},{userId:t,type:"TRANSFER",amount:r,description:`Transfer dari ${e}: ${s}`,reference:e,status:"SUCCESS",balanceBefore:d,balanceAfter:u}]})}),!0}catch(e){return console.error("UangtiX transfer error:",e),!1}}}class l{static createPaymentGateway(e){switch(e.toUpperCase()){case"TRIPAY":return new o;case"MIDTRANS":return new c;case"XENDIT":return new d;default:throw Error(`Unsupported payment gateway: ${e}`)}}}},3214:(e,t,a)=>{a.d(t,{prisma:()=>s});var r=a(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,3949],()=>a(71671));module.exports=r})();