"use strict";(()=>{var e={};e.id=1004,e.ids=[1004],e.modules={30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},71671:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>m,originalPathname:()=>b,patchFetch:()=>f,requestAsyncStorage:()=>u,routeModule:()=>d,serverHooks:()=>p,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>h});var n={};a.r(n),a.d(n,{POST:()=>c});var r=a(95419),s=a(69108),o=a(99678),i=a(78070);async function c(e){try{let t=await e.json(),a=e.headers.get("x-signature")||e.headers.get("x-callback-signature")||"",n=e.headers.get("x-gateway")||"TRIPAY";console.log("Webhook received:",{gateway:n,body:t});let r=null,s=!1;switch(n.toUpperCase()){case"TRIPAY":s=Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}())().verifyCallback(t,a),r=t;break;case"MIDTRANS":s=Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}())().verifyCallback(t,a),r=t;break;case"XENDIT":let o=Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}())(),c=e.headers.get("x-callback-token")||"";s=o.verifyCallback(t,c),r=t;break;default:return i.Z.json({success:!1,message:"Unsupported gateway"},{status:400})}if(!s)return console.error("Invalid webhook signature"),i.Z.json({success:!1,message:"Invalid signature"},{status:401});let d="",u="",l="";switch(n.toUpperCase()){case"TRIPAY":d=r.merchant_ref,l=r.reference,u=r.status;break;case"MIDTRANS":d=r.order_id,l=r.transaction_id,u=r.transaction_status;break;case"XENDIT":d=r.external_id,l=r.id,u=r.status}let p=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).payment.findFirst({where:{OR:[{externalId:l},{description:{contains:d}}]},include:{user:{select:{id:!0,name:!0,email:!0}},tickets:{include:{event:{include:{organizer:{select:{id:!0,name:!0}}}}}}}});if(!p)return console.error("Payment not found:",{orderId:d,externalId:l}),i.Z.json({success:!1,message:"Payment not found"},{status:404});let m=function(e,t){switch(t.toUpperCase()){case"TRIPAY":case"XENDIT":return"PAID"===e;case"MIDTRANS":return["capture","settlement"].includes(e);default:return!1}}(u,n),h=h(u,n);return m&&"PAID"!==p.status?(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{if(await e.payment.update({where:{id:p.id},data:{status:"PAID",paidAt:new Date,metadata:r}}),p.tickets.length>0){let t=p.tickets[0].eventId;await e.event.update({where:{id:t},data:{soldTickets:{increment:p.tickets.length}}});let a=p.tickets[0].event,n=p.amount;await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).addBalance(a.organizer.id,n,`Komisi penjualan tiket ${a.title}`,d)}await e.notification.create({data:{userId:p.userId,title:"Pembayaran Berhasil",message:`Pembayaran untuk ${p.description} telah berhasil diproses`,type:"PAYMENT_SUCCESS",data:{paymentId:p.id,orderId:d,amount:p.totalAmount}}})}),console.log("Payment processed successfully:",{orderId:d,paymentId:p.id})):h&&"PENDING"===p.status&&(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{await e.payment.update({where:{id:p.id},data:{status:"FAILED",metadata:r}}),await e.ticket.deleteMany({where:{paymentId:p.id}}),await e.notification.create({data:{userId:p.userId,title:"Pembayaran Gagal",message:`Pembayaran untuk ${p.description} gagal diproses`,type:"PAYMENT_FAILED",data:{paymentId:p.id,orderId:d,reason:u}}})}),console.log("Payment failed:",{orderId:d,paymentId:p.id,status:u})),i.Z.json({success:!0,message:"Webhook processed"})}catch(e){return console.error("Webhook processing error:",e),i.Z.json({success:!1,message:"Webhook processing failed"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}();let d=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/payments/webhook/route",pathname:"/api/payments/webhook",filename:"route",bundlePath:"app/api/payments/webhook/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\payments\\webhook\\route.ts",nextConfigOutput:"",userland:n}),{requestAsyncStorage:u,staticGenerationAsyncStorage:l,serverHooks:p,headerHooks:m,staticGenerationBailout:h}=d,b="/api/payments/webhook/route";function f(){return(0,o.patchFetch)({serverHooks:p,staticGenerationAsyncStorage:l})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[1638,6206],()=>a(71671));module.exports=n})();