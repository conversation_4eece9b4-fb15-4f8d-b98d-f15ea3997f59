"use strict";(()=>{var e={};e.id=2175,e.ids=[2175],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},12645:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>y,patchFetch:()=>b,requestAsyncStorage:()=>f,routeModule:()=>k,serverHooks:()=>j,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>T});var s={};a.r(s),a.d(s,{DELETE:()=>w,GET:()=>g,PUT:()=>h});var r=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),d=a(3205),l=a(3214),p=a(25252),c=a(52178);let m=p.Ry({name:p.Z_().min(1,"Nama template wajib diisi").optional(),description:p.Z_().optional(),templateCode:p.Z_().min(1,"Kode template wajib diisi").optional(),preview:p.Z_().url().optional(),category:p.Z_().optional(),isPremium:p.O7().optional(),requiredBadge:p.Km(["BRONZE","SILVER","GOLD","TITANIUM"]).optional(),price:p.Rx().min(0).optional(),isActive:p.O7().optional()});async function g(e,{params:t}){try{let e=await l.prisma.ticketTemplate.findUnique({where:{id:t.id},include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}}});if(!e)return o.Z.json({success:!1,message:"Template tidak ditemukan"},{status:404});return o.Z.json({success:!0,data:e})}catch(e){return console.error("Error fetching ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function h(e,{params:t}){try{let a=await (0,u.getServerSession)(d.Lz);if(!a?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if("ADMIN"!==a.user.role)return o.Z.json({success:!1,message:"Hanya admin yang dapat mengubah template"},{status:403});let s=await e.json(),r=m.parse(s),i=await l.prisma.ticketTemplate.findUnique({where:{id:t.id}});if(!i)return o.Z.json({success:!1,message:"Template tidak ditemukan"},{status:404});if(r.name&&r.name!==i.name&&await l.prisma.ticketTemplate.findFirst({where:{name:r.name,id:{not:t.id}}}))return o.Z.json({success:!1,message:"Nama template sudah digunakan"},{status:400});if(r.templateCode&&!function(e){try{if(!e.includes("<temptix>")||!e.includes("</temptix>"))return!1;return["{{eventName}}","{{buyerName}}","{{qr}}"].every(t=>e.includes(t))}catch(e){return!1}}(r.templateCode))return o.Z.json({success:!1,message:"Format .temptix tidak valid"},{status:400});let n=await l.prisma.ticketTemplate.update({where:{id:t.id},data:r,include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}}});return await l.prisma.notification.create({data:{userId:a.user.id,title:"Template Tiket Diperbarui",message:`Template "${n.name}" berhasil diperbarui`,type:"SYSTEM",data:{templateId:n.id}}}),o.Z.json({success:!0,data:n,message:"Template berhasil diperbarui"})}catch(e){if(e instanceof c.jm)return o.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error updating ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function w(e,{params:t}){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if("ADMIN"!==e.user.role)return o.Z.json({success:!1,message:"Hanya admin yang dapat menghapus template"},{status:403});let a=await l.prisma.ticketTemplate.findUnique({where:{id:t.id},include:{_count:{select:{tickets:!0,events:!0}}}});if(!a)return o.Z.json({success:!1,message:"Template tidak ditemukan"},{status:404});if(a._count.tickets>0||a._count.events>0)return o.Z.json({success:!1,message:`Template tidak dapat dihapus karena sedang digunakan oleh ${a._count.events} event dan ${a._count.tickets} tiket`},{status:400});return await l.prisma.ticketTemplate.delete({where:{id:t.id}}),await l.prisma.notification.create({data:{userId:e.user.id,title:"Template Tiket Dihapus",message:`Template "${a.name}" berhasil dihapus`,type:"SYSTEM",data:{templateId:a.id}}}),o.Z.json({success:!0,message:"Template berhasil dihapus"})}catch(e){return console.error("Error deleting ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let k=new r.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/ticket-templates/[id]/route",pathname:"/api/ticket-templates/[id]",filename:"route",bundlePath:"app/api/ticket-templates/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\ticket-templates\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:v,serverHooks:j,headerHooks:x,staticGenerationBailout:T}=k,y="/api/ticket-templates/[id]/route";function b(){return(0,n.patchFetch)({serverHooks:j,staticGenerationAsyncStorage:v})}},3205:(e,t,a)=>{a.d(t,{Lz:()=>u});var s=a(65822),r=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let u={adapter:(0,s.N)(o.prisma),providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:a,session:s})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===a&&s&&(e={...e,...s}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,a)=>{a.d(t,{prisma:()=>r});var s=a(53524);let r=globalThis.prisma??new s.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,6206,9155,5252],()=>a(12645));module.exports=s})();