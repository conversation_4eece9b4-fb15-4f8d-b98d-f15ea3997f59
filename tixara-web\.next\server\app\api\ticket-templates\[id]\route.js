"use strict";(()=>{var e={};e.id=2175,e.ids=[2175],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},12645:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>k,originalPathname:()=>b,patchFetch:()=>v,requestAsyncStorage:()=>O,routeModule:()=>h,serverHooks:()=>T,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>j});var r={};a.r(r),a.d(r,{DELETE:()=>f,GET:()=>m,PUT:()=>p});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),c=a(25252),d=a(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=c.Ry({name:c.Z_().min(1,"Nama template wajib diisi").optional(),description:c.Z_().optional(),templateCode:c.Z_().min(1,"Kode template wajib diisi").optional(),preview:c.Z_().url().optional(),category:c.Z_().optional(),isPremium:c.O7().optional(),requiredBadge:c.Km(["BRONZE","SILVER","GOLD","TITANIUM"]).optional(),price:c.Rx().min(0).optional(),isActive:c.O7().optional()});async function m(e,{params:t}){try{let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.findUnique({where:{id:t.id},include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}}});if(!e)return o.Z.json({success:!1,message:"Template tidak ditemukan"},{status:404});return o.Z.json({success:!0,data:e})}catch(e){return console.error("Error fetching ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function p(e,{params:t}){try{let a=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!a?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if("ADMIN"!==a.user.role)return o.Z.json({success:!1,message:"Hanya admin yang dapat mengubah template"},{status:403});let r=await e.json(),s=l.parse(r),i=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.findUnique({where:{id:t.id}});if(!i)return o.Z.json({success:!1,message:"Template tidak ditemukan"},{status:404});if(s.name&&s.name!==i.name&&await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.findFirst({where:{name:s.name,id:{not:t.id}}}))return o.Z.json({success:!1,message:"Nama template sudah digunakan"},{status:400});if(s.templateCode&&!function(e){try{if(!e.includes("<temptix>")||!e.includes("</temptix>"))return!1;return["{{eventName}}","{{buyerName}}","{{qr}}"].every(t=>e.includes(t))}catch(e){return!1}}(s.templateCode))return o.Z.json({success:!1,message:"Format .temptix tidak valid"},{status:400});let n=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.update({where:{id:t.id},data:s,include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:a.user.id,title:"Template Tiket Diperbarui",message:`Template "${n.name}" berhasil diperbarui`,type:"SYSTEM",data:{templateId:n.id}}}),o.Z.json({success:!0,data:n,message:"Template berhasil diperbarui"})}catch(e){if(e instanceof d.jm)return o.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error updating ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function f(e,{params:t}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if("ADMIN"!==e.user.role)return o.Z.json({success:!1,message:"Hanya admin yang dapat menghapus template"},{status:403});let a=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.findUnique({where:{id:t.id},include:{_count:{select:{tickets:!0,events:!0}}}});if(!a)return o.Z.json({success:!1,message:"Template tidak ditemukan"},{status:404});if(a._count.tickets>0||a._count.events>0)return o.Z.json({success:!1,message:`Template tidak dapat dihapus karena sedang digunakan oleh ${a._count.events} event dan ${a._count.tickets} tiket`},{status:400});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.delete({where:{id:t.id}}),await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:e.user.id,title:"Template Tiket Dihapus",message:`Template "${a.name}" berhasil dihapus`,type:"SYSTEM",data:{templateId:a.id}}}),o.Z.json({success:!0,message:"Template berhasil dihapus"})}catch(e){return console.error("Error deleting ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/ticket-templates/[id]/route",pathname:"/api/ticket-templates/[id]",filename:"route",bundlePath:"app/api/ticket-templates/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\ticket-templates\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:O,staticGenerationAsyncStorage:g,serverHooks:T,headerHooks:k,staticGenerationBailout:j}=h,b="/api/ticket-templates/[id]/route";function v(){return(0,n.patchFetch)({serverHooks:T,staticGenerationAsyncStorage:g})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,1355,5252],()=>a(12645));module.exports=r})();