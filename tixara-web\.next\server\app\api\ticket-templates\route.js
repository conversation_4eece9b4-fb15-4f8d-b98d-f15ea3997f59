"use strict";(()=>{var e={};e.id=9179,e.ids=[9179],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},55327:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>v,originalPathname:()=>b,patchFetch:()=>j,requestAsyncStorage:()=>g,routeModule:()=>f,serverHooks:()=>h,staticGenerationAsyncStorage:()=>O,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>p});var i=r(95419),s=r(69108),n=r(99678),o=r(78070),u=r(81355),c=r(25252),l=r(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let d=c.Ry({name:c.Z_().min(1,"Nama template wajib diisi"),description:c.Z_().optional(),templateCode:c.Z_().min(1,"Kode template wajib diisi"),preview:c.Z_().url().optional(),category:c.Z_().default("general"),isPremium:c.O7().default(!1),requiredBadge:c.Km(["BRONZE","SILVER","GOLD","TITANIUM"]).optional(),price:c.Rx().min(0).default(0),isActive:c.O7().default(!0)});async function m(e){try{let{searchParams:t}=new URL(e.url),r=t.get("search")||"",a=t.get("category")||"",i=t.get("isPremium"),s=t.get("isActive"),n=parseInt(t.get("page")||"1"),u=parseInt(t.get("limit")||"20"),c=(n-1)*u,l={};r&&(l.OR=[{name:{contains:r,mode:"insensitive"}},{description:{contains:r,mode:"insensitive"}}]),a&&"all"!==a&&(l.category=a),null!==i&&(l.isPremium="true"===i),null!==s&&(l.isActive="true"===s);let[d,m]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.findMany({where:l,include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}},orderBy:{createdAt:"desc"},skip:c,take:u}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.count({where:l})]);return o.Z.json({success:!0,data:d,pagination:{page:n,limit:u,total:m,totalPages:Math.ceil(m/u)}})}catch(e){return console.error("Error fetching ticket templates:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function p(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if("ADMIN"!==t.user.role)return o.Z.json({success:!1,message:"Hanya admin yang dapat membuat template"},{status:403});let r=await e.json(),a=d.parse(r);if(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.findFirst({where:{name:a.name}}))return o.Z.json({success:!1,message:"Nama template sudah digunakan"},{status:400});if(!function(e){try{if(!e.includes("<temptix>")||!e.includes("</temptix>"))return!1;return["{{eventName}}","{{buyerName}}","{{qr}}"].every(t=>e.includes(t))}catch(e){return!1}}(a.templateCode))return o.Z.json({success:!1,message:"Format .temptix tidak valid"},{status:400});let i=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.create({data:{...a,createdBy:t.user.id},include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:t.user.id,title:"Template Tiket Dibuat",message:`Template "${i.name}" berhasil dibuat`,type:"SYSTEM",data:{templateId:i.id}}}),o.Z.json({success:!0,data:i,message:"Template berhasil dibuat"})}catch(e){if(e instanceof l.jm)return o.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error creating ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let f=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/ticket-templates/route",pathname:"/api/ticket-templates",filename:"route",bundlePath:"app/api/ticket-templates/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\ticket-templates\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:g,staticGenerationAsyncStorage:O,serverHooks:h,headerHooks:v,staticGenerationBailout:x}=f,b="/api/ticket-templates/route";function j(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:O})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355,5252],()=>r(55327));module.exports=a})();