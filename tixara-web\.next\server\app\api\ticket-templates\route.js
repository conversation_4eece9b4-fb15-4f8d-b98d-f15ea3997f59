"use strict";(()=>{var e={};e.id=9179,e.ids=[9179],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},55327:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>y,originalPathname:()=>k,patchFetch:()=>j,requestAsyncStorage:()=>f,routeModule:()=>w,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>b});var r={};a.r(r),a.d(r,{GET:()=>g,POST:()=>h});var s=a(95419),i=a(69108),n=a(99678),o=a(78070),u=a(81355),l=a(3205),c=a(3214),d=a(25252),p=a(52178);let m=d.Ry({name:d.Z_().min(1,"Nama template wajib diisi"),description:d.Z_().optional(),templateCode:d.Z_().min(1,"Kode template wajib diisi"),preview:d.Z_().url().optional(),category:d.Z_().default("general"),isPremium:d.O7().default(!1),requiredBadge:d.Km(["BRONZE","SILVER","GOLD","TITANIUM"]).optional(),price:d.Rx().min(0).default(0),isActive:d.O7().default(!0)});async function g(e){try{let{searchParams:t}=new URL(e.url),a=t.get("search")||"",r=t.get("category")||"",s=t.get("isPremium"),i=t.get("isActive"),n=parseInt(t.get("page")||"1"),u=parseInt(t.get("limit")||"20"),l=(n-1)*u,d={};a&&(d.OR=[{name:{contains:a,mode:"insensitive"}},{description:{contains:a,mode:"insensitive"}}]),r&&"all"!==r&&(d.category=r),null!==s&&(d.isPremium="true"===s),null!==i&&(d.isActive="true"===i);let[p,m]=await Promise.all([c.prisma.ticketTemplate.findMany({where:d,include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}},orderBy:{createdAt:"desc"},skip:l,take:u}),c.prisma.ticketTemplate.count({where:d})]);return o.Z.json({success:!0,data:p,pagination:{page:n,limit:u,total:m,totalPages:Math.ceil(m/u)}})}catch(e){return console.error("Error fetching ticket templates:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function h(e){try{let t=await (0,u.getServerSession)(l.Lz);if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if("ADMIN"!==t.user.role)return o.Z.json({success:!1,message:"Hanya admin yang dapat membuat template"},{status:403});let a=await e.json(),r=m.parse(a);if(await c.prisma.ticketTemplate.findFirst({where:{name:r.name}}))return o.Z.json({success:!1,message:"Nama template sudah digunakan"},{status:400});if(!function(e){try{if(!e.includes("<temptix>")||!e.includes("</temptix>"))return!1;return["{{eventName}}","{{buyerName}}","{{qr}}"].every(t=>e.includes(t))}catch(e){return!1}}(r.templateCode))return o.Z.json({success:!1,message:"Format .temptix tidak valid"},{status:400});let s=await c.prisma.ticketTemplate.create({data:{...r,createdBy:t.user.id},include:{creator:{select:{id:!0,name:!0,email:!0}},_count:{select:{tickets:!0,events:!0}}}});return await c.prisma.notification.create({data:{userId:t.user.id,title:"Template Tiket Dibuat",message:`Template "${s.name}" berhasil dibuat`,type:"SYSTEM",data:{templateId:s.id}}}),o.Z.json({success:!0,data:s,message:"Template berhasil dibuat"})}catch(e){if(e instanceof p.jm)return o.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error creating ticket template:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/ticket-templates/route",pathname:"/api/ticket-templates",filename:"route",bundlePath:"app/api/ticket-templates/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\ticket-templates\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:f,staticGenerationAsyncStorage:x,serverHooks:v,headerHooks:y,staticGenerationBailout:b}=w,k="/api/ticket-templates/route";function j(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}},3205:(e,t,a)=>{a.d(t,{Lz:()=>u});var r=a(65822),s=a(86485),i=a(98432),n=a.n(i),o=a(3214);a(53524);let u={adapter:(0,r.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:a,session:r})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===a&&r&&(e={...e,...r}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,a)=>{a.d(t,{prisma:()=>s});var r=a(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,6206,9155,5252],()=>a(55327));module.exports=r})();