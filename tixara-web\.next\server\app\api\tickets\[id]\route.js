"use strict";(()=>{var e={};e.id=800,e.ids=[800],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},10852:(e,r,a)=>{a.r(r),a.d(r,{headerHooks:()=>x,originalPathname:()=>v,patchFetch:()=>k,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>w,staticGenerationAsyncStorage:()=>h,staticGenerationBailout:()=>f});var t={};a.r(t),a.d(t,{GET:()=>p});var i=a(95419),s=a(69108),n=a(99678),o=a(78070),u=a(81355),d=a(3205),l=a(3214);async function p(e,{params:r}){try{let e=await (0,u.getServerSession)(d.Lz);if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let a=await l.prisma.ticket.findUnique({where:{id:r.id},include:{event:{include:{organizer:{select:{id:!0,name:!0,isVerified:!0}},category:{select:{id:!0,name:!0,color:!0}}}},template:{select:{id:!0,name:!0,preview:!0}},validator:{select:{id:!0,name:!0,email:!0}},buyer:{select:{id:!0,name:!0,email:!0}}}});if(!a)return o.Z.json({success:!1,message:"Tiket tidak ditemukan"},{status:404});if(!(a.buyerId===e.user.id||"ADMIN"===e.user.role||"STAFF"===e.user.role&&await c(e.user.id,a.eventId)||a.event.organizerId===e.user.id))return o.Z.json({success:!1,message:"Anda tidak memiliki akses untuk melihat tiket ini"},{status:403});return o.Z.json({success:!0,data:a})}catch(e){return console.error("Error fetching ticket:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function c(e,r){return!!await l.prisma.eventStaff.findFirst({where:{eventId:r,staffId:e}})}let m=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/tickets/[id]/route",pathname:"/api/tickets/[id]",filename:"route",bundlePath:"app/api/tickets/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{requestAsyncStorage:g,staticGenerationAsyncStorage:h,serverHooks:w,headerHooks:x,staticGenerationBailout:f}=m,v="/api/tickets/[id]/route";function k(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:h})}},3205:(e,r,a)=>{a.d(r,{Lz:()=>u});var t=a(65822),i=a(86485),s=a(98432),n=a.n(s),o=a(3214);a(53524);let u={adapter:(0,t.N)(o.prisma),providers:[(0,i.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let r=await o.prisma.user.findUnique({where:{email:e.email}});if(!r||!await n().compare(e.password,r.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:r.id},data:{lastLoginAt:new Date}}),{id:r.id,email:r.email,name:r.name,role:r.role,isVerified:r.isVerified,badge:r.badge,avatar:r.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:r,trigger:a,session:t})=>(r&&(e.role=r.role,e.isVerified=r.isVerified,e.badge=r.badge,e.avatar=r.avatar),"update"===a&&t&&(e={...e,...t}),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role,e.user.isVerified=r.isVerified,e.user.badge=r.badge,e.user.avatar=r.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:r}){r&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,r,a)=>{a.d(r,{prisma:()=>i});var t=a(53524);let i=globalThis.prisma??new t.PrismaClient({log:["error"]})}};var r=require("../../../../webpack-runtime.js");r.C(e);var a=e=>r(r.s=e),t=r.X(0,[1638,6206,9155],()=>a(10852));module.exports=t})();