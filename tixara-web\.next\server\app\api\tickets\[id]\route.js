"use strict";(()=>{var e={};e.id=800,e.ids=[800],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},10852:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>v,patchFetch:()=>g,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>f,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>h});var i={};r.r(i),r.d(i,{GET:()=>c});var s=r(95419),n=r(69108),a=r(99678),o=r(78070),u=r(81355);async function c(e,{params:t}){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.findUnique({where:{id:t.id},include:{event:{include:{organizer:{select:{id:!0,name:!0,isVerified:!0}},category:{select:{id:!0,name:!0,color:!0}}}},template:{select:{id:!0,name:!0,preview:!0}},validator:{select:{id:!0,name:!0,email:!0}},buyer:{select:{id:!0,name:!0,email:!0}}}});if(!r)return o.Z.json({success:!1,message:"Tiket tidak ditemukan"},{status:404});if(!(r.buyerId===e.user.id||"ADMIN"===e.user.role||"STAFF"===e.user.role&&await d(e.user.id,r.eventId)||r.event.organizerId===e.user.id))return o.Z.json({success:!1,message:"Anda tidak memiliki akses untuk melihat tiket ini"},{status:403});return o.Z.json({success:!0,data:r})}catch(e){return console.error("Error fetching ticket:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}async function d(e,t){return!!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).eventStaff.findFirst({where:{eventId:t,staffId:e}})}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let l=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/tickets/[id]/route",pathname:"/api/tickets/[id]",filename:"route",bundlePath:"app/api/tickets/[id]/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\[id]\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:p,staticGenerationAsyncStorage:m,serverHooks:f,headerHooks:x,staticGenerationBailout:h}=l,v="/api/tickets/[id]/route";function g(){return(0,a.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[1638,6206,1355],()=>r(10852));module.exports=i})();