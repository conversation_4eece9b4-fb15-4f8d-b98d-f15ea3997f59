"use strict";(()=>{var e={};e.id=4397,e.ids=[4397],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},28702:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>v,originalPathname:()=>g,patchFetch:()=>h,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>x});var s={};r.r(s),r.d(s,{GET:()=>c});var i=r(95419),a=r(69108),n=r(99678),o=r(78070),u=r(81355);async function c(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=r.get("status"),i=r.get("eventId"),a=parseInt(r.get("page")||"1"),n=parseInt(r.get("limit")||"20"),c=(a-1)*n,d={buyerId:t.user.id};"active"===s?d.isUsed=!1:"used"===s&&(d.isUsed=!0),i&&(d.eventId=i);let[l,p]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.findMany({where:d,include:{event:{include:{organizer:{select:{id:!0,name:!0,isVerified:!0}},category:{select:{id:!0,name:!0,color:!0}}}},template:{select:{id:!0,name:!0,preview:!0}},validator:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:c,take:n}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.count({where:d})]),m=l.reduce((e,t)=>{let r=t.event.id;return e[r]||(e[r]={event:t.event,tickets:[]}),e[r].tickets.push(t),e},{});return o.Z.json({success:!0,data:{tickets:l,ticketsByEvent:Object.values(m),summary:{total:p,active:l.filter(e=>!e.isUsed).length,used:l.filter(e=>e.isUsed).length}},pagination:{page:a,limit:n,total:p,totalPages:Math.ceil(p/n)}})}catch(e){return console.error("Error fetching user tickets:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let d=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/tickets/my-tickets/route",pathname:"/api/tickets/my-tickets",filename:"route",bundlePath:"app/api/tickets/my-tickets/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\my-tickets\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:m,headerHooks:v,staticGenerationBailout:x}=d,g="/api/tickets/my-tickets/route";function h(){return(0,n.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,1355],()=>r(28702));module.exports=s})();