"use strict";(()=>{var e={};e.id=4397,e.ids=[4397],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},28702:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>w,patchFetch:()=>y,requestAsyncStorage:()=>m,routeModule:()=>p,serverHooks:()=>v,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{GET:()=>c});var s=r(95419),i=r(69108),n=r(99678),o=r(78070),u=r(81355),d=r(3205),l=r(3214);async function c(e){try{let t=await (0,u.getServerSession)(d.Lz);if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=r.get("status"),s=r.get("eventId"),i=parseInt(r.get("page")||"1"),n=parseInt(r.get("limit")||"20"),c=(i-1)*n,p={buyerId:t.user.id};"active"===a?p.isUsed=!1:"used"===a&&(p.isUsed=!0),s&&(p.eventId=s);let[m,g]=await Promise.all([l.prisma.ticket.findMany({where:p,include:{event:{include:{organizer:{select:{id:!0,name:!0,isVerified:!0}},category:{select:{id:!0,name:!0,color:!0}}}},template:{select:{id:!0,name:!0,preview:!0}},validator:{select:{id:!0,name:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:c,take:n}),l.prisma.ticket.count({where:p})]),v=m.reduce((e,t)=>{let r=t.event.id;return e[r]||(e[r]={event:t.event,tickets:[]}),e[r].tickets.push(t),e},{});return o.Z.json({success:!0,data:{tickets:m,ticketsByEvent:Object.values(v),summary:{total:g,active:m.filter(e=>!e.isUsed).length,used:m.filter(e=>e.isUsed).length}},pagination:{page:i,limit:n,total:g,totalPages:Math.ceil(g/n)}})}catch(e){return console.error("Error fetching user tickets:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let p=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/tickets/my-tickets/route",pathname:"/api/tickets/my-tickets",filename:"route",bundlePath:"app/api/tickets/my-tickets/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\my-tickets\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:m,staticGenerationAsyncStorage:g,serverHooks:v,headerHooks:h,staticGenerationBailout:x}=p,w="/api/tickets/my-tickets/route";function y(){return(0,n.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:g})}},3205:(e,t,r)=>{r.d(t,{Lz:()=>u});var a=r(65822),s=r(86485),i=r(98432),n=r.n(i),o=r(3214);r(53524);let u={adapter:(0,a.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:r,session:a})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===r&&a&&(e={...e,...a}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,r)=>{r.d(t,{prisma:()=>s});var a=r(53524);let s=globalThis.prisma??new a.PrismaClient({log:["error"]})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,9155],()=>r(28702));module.exports=a})();