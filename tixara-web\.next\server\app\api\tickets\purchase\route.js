"use strict";(()=>{var e={};e.id=3946,e.ids=[3946],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},9357:(e,t,i)=>{i.r(t),i.d(t,{headerHooks:()=>k,originalPathname:()=>b,patchFetch:()=>U,requestAsyncStorage:()=>O,routeModule:()=>p,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>D});var a={};i.r(a),i.d(a,{POST:()=>m});var r=i(95419),n=i(69108),o=i(99678),s=i(78070),u=i(81355),d=i(25252),c=i(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=d.Ry({eventId:d.Z_().min(1,"Event ID wajib diisi"),quantity:d.Rx().min(1,"Jumlah tiket minimal 1").max(10,"Maksimal 10 tiket per transaksi"),paymentMethod:d.Km(["UANGTIX","TRIPAY","MIDTRANS","XENDIT","MANUAL"]),templateId:d.Z_().optional()});async function m(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return s.Z.json({success:!1,message:"Unauthorized"},{status:401});let i=await e.json(),a=l.parse(i),r=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).event.findUnique({where:{id:a.eventId},include:{organizer:!0,category:!0,template:!0,_count:{select:{tickets:!0}}}});if(!r)return s.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if(!r.isActive)return s.Z.json({success:!1,message:"Event tidak aktif"},{status:400});if(new Date(r.endDate)<new Date)return s.Z.json({success:!1,message:"Event sudah berakhir"},{status:400});let n=r.maxTickets-r._count.tickets;if(n<a.quantity)return s.Z.json({success:!1,message:`Tiket tidak mencukupi. Tersisa ${n} tiket`},{status:400});let o=r.price,d=Math.round(.05*o),c=o+d,m=c*a.quantity;if("UANGTIX"===a.paymentMethod){let e=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{id:t.user.id}});if(!e||e.uangtixBalance<m)return s.Z.json({success:!1,message:"Saldo UangtiX tidak mencukupi",required:m,current:e?.uangtixBalance||0},{status:400})}let p=null;a.templateId?p=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticketTemplate.findUnique({where:{id:a.templateId}}):r.templateId&&(p=r.template);let O=p?.templateCode||Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}()),f=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{let i=[];for(let n=0;n<a.quantity;n++){let n=Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}())(),s=Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}())("",a.eventId),u=await e.ticket.create({data:{eventId:a.eventId,buyerId:t.user.id,templateId:p?.id,qrCode:s,ticketCode:n,price:o,adminFee:d,totalPaid:c,paymentMethod:a.paymentMethod}}),l=Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}())(u.id,a.eventId),m=await Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}())(l),f=function(e,t,i,a,r){let n={eventName:t.title,category:t.category.name,buyerName:i.name||i.email,qr:a,isVerified:t.organizer.isVerified,adminFee:e.adminFee,ticketCode:e.ticketCode,eventDate:new Date(t.startDate).toLocaleDateString("id-ID"),eventLocation:t.location,organizerName:t.organizer.name,price:e.price},o=Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}())(r,n);return Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}())(o)}(u,r,t.user,m,O),h=await e.ticket.update({where:{id:u.id},data:{qrCode:l}});i.push({...h,html:f,qrImage:m})}if("UANGTIX"===a.paymentMethod){await e.user.update({where:{id:t.user.id},data:{uangtixBalance:{decrement:m}}});let n=o*a.quantity;await e.user.update({where:{id:r.organizerId},data:{uangtixBalance:{increment:n}}}),await e.uangtiXTransaction.create({data:{userId:t.user.id,type:"PAYMENT",amount:-m,description:`Pembelian ${a.quantity} tiket untuk ${r.title}`,reference:i[0].id,status:"SUCCESS"}}),await e.uangtiXTransaction.create({data:{userId:r.organizerId,type:"COMMISSION",amount:n,description:`Penjualan tiket dari ${r.title}`,reference:i[0].id,status:"SUCCESS"}})}return i});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:t.user.id,title:"Tiket Berhasil Dibeli",message:`${a.quantity} tiket untuk "${r.title}" berhasil dibeli`,type:"TICKET",data:{eventId:r.id,ticketIds:f.map(e=>e.id)}}}),await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:r.organizerId,title:"Tiket Terjual",message:`${a.quantity} tiket untuk "${r.title}" telah terjual`,type:"SALE",data:{eventId:r.id,buyerId:t.user.id,quantity:a.quantity}}}),s.Z.json({success:!0,data:{tickets:f.map(e=>({id:e.id,ticketCode:e.ticketCode,qrCode:e.qrCode,price:e.price,adminFee:e.adminFee,totalPaid:e.totalPaid})),event:{id:r.id,title:r.title,startDate:r.startDate,location:r.location},payment:{method:a.paymentMethod,totalAmount:m,quantity:a.quantity}},message:"Tiket berhasil dibeli"})}catch(e){if(e instanceof c.jm)return s.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error purchasing tickets:",e),s.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let p=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/tickets/purchase/route",pathname:"/api/tickets/purchase",filename:"route",bundlePath:"app/api/tickets/purchase/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\purchase\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:O,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:k,staticGenerationBailout:D}=p,b="/api/tickets/purchase/route";function U(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[1638,6206,1355,5252],()=>i(9357));module.exports=a})();