"use strict";(()=>{var e={};e.id=3946,e.ids=[3946,58],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},9357:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>w,patchFetch:()=>q,requestAsyncStorage:()=>h,routeModule:()=>y,serverHooks:()=>k,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>b});var i={};a.r(i),a.d(i,{POST:()=>f});var r=a(95419),n=a(69108),s=a(99678),o=a(78070),d=a(81355),c=a(3205),u=a(3214),l=a(25252),p=a(52178),m=a(58);let g=l.Ry({eventId:l.Z_().min(1,"Event ID wajib diisi"),quantity:l.Rx().min(1,"Jumlah tiket minimal 1").max(10,"Maksimal 10 tiket per transaksi"),paymentMethod:l.Km(["UANGTIX","TRIPAY","MIDTRANS","XENDIT","MANUAL"]),templateId:l.Z_().optional()});async function f(e){try{let t=await (0,d.getServerSession)(c.Lz);if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let a=await e.json(),i=g.parse(a),r=await u.prisma.event.findUnique({where:{id:i.eventId},include:{organizer:!0,category:!0,template:!0,_count:{select:{tickets:!0}}}});if(!r)return o.Z.json({success:!1,message:"Event tidak ditemukan"},{status:404});if(!r.isActive)return o.Z.json({success:!1,message:"Event tidak aktif"},{status:400});if(new Date(r.endDate)<new Date)return o.Z.json({success:!1,message:"Event sudah berakhir"},{status:400});let n=r.maxTickets-r._count.tickets;if(n<i.quantity)return o.Z.json({success:!1,message:`Tiket tidak mencukupi. Tersisa ${n} tiket`},{status:400});let s=r.price,l=Math.round(.05*s),p=s+l,f=p*i.quantity;if("UANGTIX"===i.paymentMethod){let e=await u.prisma.user.findUnique({where:{id:t.user.id}});if(!e||e.uangtixBalance<f)return o.Z.json({success:!1,message:"Saldo UangtiX tidak mencukupi",required:f,current:e?.uangtixBalance||0},{status:400})}let y=null;i.templateId?y=await u.prisma.ticketTemplate.findUnique({where:{id:i.templateId}}):r.templateId&&(y=r.template);let h=y?.templateCode||m.ZY,v=await u.prisma.$transaction(async e=>{let a=[];for(let n=0;n<i.quantity;n++){let n=(0,m.generateTicketCode)(),o=(0,m.F8)("",i.eventId),d=await e.ticket.create({data:{eventId:i.eventId,buyerId:t.user.id,templateId:y?.id,qrCode:o,ticketCode:n,price:s,adminFee:l,totalPaid:p,paymentMethod:i.paymentMethod}}),c=(0,m.F8)(d.id,i.eventId),u=await (0,m.generateQRCode)(c),g=function(e,t,a,i,r){let n={eventName:t.title,category:t.category.name,buyerName:a.name||a.email,qr:i,isVerified:t.organizer.isVerified,adminFee:e.adminFee,ticketCode:e.ticketCode,eventDate:new Date(t.startDate).toLocaleDateString("id-ID"),eventLocation:t.location,organizerName:t.organizer.name,price:e.price},s=(0,m.kT)(r,n);return(0,m.a)(s)}(d,r,t.user,u,h),f=await e.ticket.update({where:{id:d.id},data:{qrCode:c}});a.push({...f,html:g,qrImage:u})}if("UANGTIX"===i.paymentMethod){await e.user.update({where:{id:t.user.id},data:{uangtixBalance:{decrement:f}}});let n=s*i.quantity;await e.user.update({where:{id:r.organizerId},data:{uangtixBalance:{increment:n}}}),await e.uangtiXTransaction.create({data:{userId:t.user.id,type:"PAYMENT",amount:-f,description:`Pembelian ${i.quantity} tiket untuk ${r.title}`,reference:a[0].id,status:"SUCCESS"}}),await e.uangtiXTransaction.create({data:{userId:r.organizerId,type:"COMMISSION",amount:n,description:`Penjualan tiket dari ${r.title}`,reference:a[0].id,status:"SUCCESS"}})}return a});return await u.prisma.notification.create({data:{userId:t.user.id,title:"Tiket Berhasil Dibeli",message:`${i.quantity} tiket untuk "${r.title}" berhasil dibeli`,type:"TICKET",data:{eventId:r.id,ticketIds:v.map(e=>e.id)}}}),await u.prisma.notification.create({data:{userId:r.organizerId,title:"Tiket Terjual",message:`${i.quantity} tiket untuk "${r.title}" telah terjual`,type:"SALE",data:{eventId:r.id,buyerId:t.user.id,quantity:i.quantity}}}),o.Z.json({success:!0,data:{tickets:v.map(e=>({id:e.id,ticketCode:e.ticketCode,qrCode:e.qrCode,price:e.price,adminFee:e.adminFee,totalPaid:e.totalPaid})),event:{id:r.id,title:r.title,startDate:r.startDate,location:r.location},payment:{method:i.paymentMethod,totalAmount:f,quantity:i.quantity}},message:"Tiket berhasil dibeli"})}catch(e){if(e instanceof p.jm)return o.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error purchasing tickets:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let y=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/tickets/purchase/route",pathname:"/api/tickets/purchase",filename:"route",bundlePath:"app/api/tickets/purchase/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\purchase\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:h,staticGenerationAsyncStorage:v,serverHooks:k,headerHooks:x,staticGenerationBailout:b}=y,w="/api/tickets/purchase/route";function q(){return(0,s.patchFetch)({serverHooks:k,staticGenerationAsyncStorage:v})}},3205:(e,t,a)=>{a.d(t,{Lz:()=>d});var i=a(65822),r=a(86485),n=a(98432),s=a.n(n),o=a(3214);a(53524);let d={adapter:(0,i.N)(o.prisma),providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await o.prisma.user.findUnique({where:{email:e.email}});if(!t||!await s().compare(e.password,t.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:a,session:i})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===a&&i&&(e={...e,...i}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,a)=>{a.d(t,{prisma:()=>r});var i=a(53524);let r=globalThis.prisma??new i.PrismaClient({log:["error"]})},58:(e,t,a)=>{a.d(t,{F8:()=>l,ZY:()=>c,a:()=>d,generateQRCode:()=>s,generateTicketCode:()=>n,kT:()=>o,qE:()=>u});var i=a(77670),r=a(63721);function n(){let e=(0,r.x0)(8).toUpperCase();return`TIX-${e}`}async function s(e){try{return await i.toDataURL(e,{errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256})}catch(e){throw console.error("Error generating QR code:",e),Error("Failed to generate QR code")}}function o(e,t){let a=e;return Object.entries({"{{eventName}}":t.eventName,"{{category}}":t.category,"{{buyerName}}":t.buyerName,"{{qr}}":t.qr,"{{isVerified}}":t.isVerified?"✓ Verified":"","{{adminFee}}":`Rp ${t.adminFee.toLocaleString("id-ID")}`,"{{ticketCode}}":t.ticketCode,"{{eventDate}}":t.eventDate,"{{eventLocation}}":t.eventLocation,"{{organizerName}}":t.organizerName,"{{price}}":0===t.price?"Gratis":`Rp ${t.price.toLocaleString("id-ID")}`}).forEach(([e,t])=>{a=a.replace(RegExp(e,"g"),t.toString())}),a}function d(e){let t=e;return Object.entries({"<temptix>":'<div class="ticket-container">',"</temptix>":"</div>","<ticket-header>":'<div class="ticket-header">',"</ticket-header>":"</div>","<ticket-body>":'<div class="ticket-body">',"</ticket-body>":"</div>","<event>":'<div class="event-name">',"</event>":"</div>","<category>":'<div class="event-category">',"</category>":"</div>","<buyer>":'<div class="buyer-name">',"</buyer>":"</div>","<qr-code":'<img class="qr-code"',"/>":" />","<verified>":'<div class="verified-badge">',"</verified>":"</div>","<admin-fee>":'<div class="admin-fee">',"</admin-fee>":"</div>"}).forEach(([e,a])=>{t=t.replace(RegExp(e,"g"),a)}),`
    <style>
      .ticket-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 20px;
        border: 2px dashed #0066cc;
        border-radius: 10px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        font-family: 'Arial', sans-serif;
        color: #1e40af;
      }
      .ticket-header {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #0066cc;
      }
      .ticket-body {
        text-align: center;
      }
      .event-name {
        font-size: 20px;
        font-weight: bold;
        margin: 10px 0;
        color: #1e40af;
      }
      .event-category {
        font-size: 14px;
        color: #64748b;
        margin: 5px 0;
      }
      .buyer-name {
        font-size: 16px;
        margin: 15px 0;
        padding: 10px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 5px;
      }
      .qr-code {
        width: 150px;
        height: 150px;
        margin: 15px 0;
        border: 2px solid #0066cc;
        border-radius: 5px;
      }
      .verified-badge {
        color: #059669;
        font-weight: bold;
        margin: 10px 0;
      }
      .admin-fee {
        font-size: 12px;
        color: #64748b;
        margin-top: 15px;
      }
    </style>
  `+t}let c=`<temptix>
  <ticket-header>🎫 TiXara</ticket-header>
  <ticket-body>
    <event>{{eventName}}</event>
    <category>{{category}}</category>
    <buyer>{{buyerName}}</buyer>
    <qr-code src="{{qr}}" />
    <verified>{{isVerified}}</verified>
    <admin-fee>Biaya Admin: {{adminFee}}</admin-fee>
  </ticket-body>
</temptix>`;function u(e){try{let t=e.split(":");if(4!==t.length||"TIXARA"!==t[0])return{isValid:!1,error:"Format QR code tidak valid"};let[,a,i,r]=t;if(!a||!i||!r)return{isValid:!1,error:"Data QR code tidak lengkap"};return{isValid:!0,ticketId:a,eventId:i}}catch(e){return{isValid:!1,error:"QR code tidak dapat dibaca"}}}function l(e,t){let a=Date.now();return`TIXARA:${e}:${t}:${a}`}},63721:(e,t,a)=>{let i,r;a.d(t,{x0:()=>s});let n=require("node:crypto");function s(e=21){var t;t=e|=0,!i||i.length<t?(i=Buffer.allocUnsafe(128*t),n.webcrypto.getRandomValues(i),r=0):r+t>i.length&&(n.webcrypto.getRandomValues(i),r=0),r+=t;let a="";for(let t=r-e;t<r;t++)a+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&i[t]];return a}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[1638,6206,9155,5252,7670],()=>a(9357));module.exports=i})();