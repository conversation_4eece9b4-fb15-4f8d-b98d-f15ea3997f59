"use strict";(()=>{var e={};e.id=145,e.ids=[145,58],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17756:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>x,originalPathname:()=>w,patchFetch:()=>D,requestAsyncStorage:()=>y,routeModule:()=>f,serverHooks:()=>b,staticGenerationAsyncStorage:()=>k,staticGenerationBailout:()=>h});var i={};a.r(i),a.d(i,{POST:()=>v});var r=a(95419),s=a(69108),n=a(99678),d=a(78070),o=a(81355),c=a(3205),u=a(3214),l=a(25252),p=a(52178),m=a(58);let g=l.Ry({qrData:l.Z_().min(1,"QR data wajib diisi"),eventId:l.Z_().min(1,"Event ID wajib diisi")});async function v(e){try{let t=await (0,o.getServerSession)(c.Lz);if(!t?.user)return d.Z.json({success:!1,message:"Unauthorized"},{status:401});if(!["STAFF","ADMIN"].includes(t.user.role))return d.Z.json({success:!1,message:"Hanya staff yang dapat memvalidasi tiket"},{status:403});let a=await e.json(),i=g.parse(a),r=(0,m.qE)(i.qrData);if(!r.isValid)return d.Z.json({success:!1,message:r.error||"QR code tidak valid",status:"INVALID_QR"},{status:400});let s=await u.prisma.ticket.findUnique({where:{qrCode:i.qrData},include:{event:{include:{organizer:!0,category:!0}},buyer:{select:{id:!0,name:!0,email:!0}},validator:{select:{id:!0,name:!0,email:!0}}}});if(!s)return d.Z.json({success:!1,message:"Tiket tidak ditemukan",status:"NOT_FOUND"},{status:404});if(s.eventId!==i.eventId)return d.Z.json({success:!1,message:"Tiket tidak valid untuk event ini",status:"WRONG_EVENT"},{status:400});if("STAFF"===t.user.role&&!await u.prisma.eventStaff.findFirst({where:{eventId:i.eventId,staffId:t.user.id}}))return d.Z.json({success:!1,message:"Anda tidak memiliki akses untuk memvalidasi tiket event ini",status:"NO_PERMISSION"},{status:403});if(s.isUsed)return d.Z.json({success:!1,message:"Tiket sudah pernah digunakan",status:"ALREADY_USED",data:{ticket:{id:s.id,ticketCode:s.ticketCode,usedAt:s.usedAt,validatedBy:s.validator},event:{id:s.event.id,title:s.event.title},buyer:s.buyer}},{status:400});let n=new Date,l=new Date(s.event.startDate),p=new Date(s.event.endDate);if(n<l)return d.Z.json({success:!0,warning:"Event belum dimulai",status:"EARLY_VALIDATION",data:{ticket:{id:s.id,ticketCode:s.ticketCode,price:s.price,createdAt:s.createdAt},event:{id:s.event.id,title:s.event.title,startDate:s.event.startDate,endDate:s.event.endDate,location:s.event.location,organizer:s.event.organizer.name},buyer:s.buyer}});if(n>p)return d.Z.json({success:!1,message:"Event sudah berakhir",status:"EVENT_ENDED"},{status:400});let v=await u.prisma.ticket.update({where:{id:s.id},data:{isUsed:!0,usedAt:new Date,validatedBy:t.user.id},include:{event:{include:{organizer:!0,category:!0}},buyer:{select:{id:!0,name:!0,email:!0}},validator:{select:{id:!0,name:!0,email:!0}}}});return await u.prisma.notification.create({data:{userId:s.buyerId,title:"Tiket Divalidasi",message:`Tiket Anda untuk "${s.event.title}" telah divalidasi`,type:"TICKET",data:{ticketId:s.id,eventId:s.eventId,validatedBy:t.user.id}}}),d.Z.json({success:!0,message:"Tiket berhasil divalidasi",status:"VALIDATED",data:{ticket:{id:v.id,ticketCode:v.ticketCode,price:v.price,usedAt:v.usedAt,createdAt:v.createdAt},event:{id:v.event.id,title:v.event.title,startDate:v.event.startDate,endDate:v.event.endDate,location:v.event.location,organizer:v.event.organizer.name,category:v.event.category.name},buyer:v.buyer,validator:v.validator}})}catch(e){if(e instanceof p.jm)return d.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error validating ticket:",e),d.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let f=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/tickets/validate/route",pathname:"/api/tickets/validate",filename:"route",bundlePath:"app/api/tickets/validate/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\validate\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:y,staticGenerationAsyncStorage:k,serverHooks:b,headerHooks:x,staticGenerationBailout:h}=f,w="/api/tickets/validate/route";function D(){return(0,n.patchFetch)({serverHooks:b,staticGenerationAsyncStorage:k})}},3205:(e,t,a)=>{a.d(t,{Lz:()=>o});var i=a(65822),r=a(86485),s=a(98432),n=a.n(s),d=a(3214);a(53524);let o={adapter:(0,i.N)(d.prisma),providers:[(0,r.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let t=await d.prisma.user.findUnique({where:{email:e.email}});if(!t||!await n().compare(e.password,t.password))throw Error("Email atau password salah");return await d.prisma.user.update({where:{id:t.id},data:{lastLoginAt:new Date}}),{id:t.id,email:t.email,name:t.name,role:t.role,isVerified:t.isVerified,badge:t.badge,avatar:t.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:t,trigger:a,session:i})=>(t&&(e.role=t.role,e.isVerified=t.isVerified,e.badge=t.badge,e.avatar=t.avatar),"update"===a&&i&&(e={...e,...i}),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role,e.user.isVerified=t.isVerified,e.user.badge=t.badge,e.user.avatar=t.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:t}){t&&await d.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,t,a)=>{a.d(t,{prisma:()=>r});var i=a(53524);let r=globalThis.prisma??new i.PrismaClient({log:["error"]})},58:(e,t,a)=>{a.d(t,{F8:()=>l,ZY:()=>c,a:()=>o,generateQRCode:()=>n,generateTicketCode:()=>s,kT:()=>d,qE:()=>u});var i=a(77670),r=a(63721);function s(){let e=(0,r.x0)(8).toUpperCase();return`TIX-${e}`}async function n(e){try{return await i.toDataURL(e,{errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256})}catch(e){throw console.error("Error generating QR code:",e),Error("Failed to generate QR code")}}function d(e,t){let a=e;return Object.entries({"{{eventName}}":t.eventName,"{{category}}":t.category,"{{buyerName}}":t.buyerName,"{{qr}}":t.qr,"{{isVerified}}":t.isVerified?"✓ Verified":"","{{adminFee}}":`Rp ${t.adminFee.toLocaleString("id-ID")}`,"{{ticketCode}}":t.ticketCode,"{{eventDate}}":t.eventDate,"{{eventLocation}}":t.eventLocation,"{{organizerName}}":t.organizerName,"{{price}}":0===t.price?"Gratis":`Rp ${t.price.toLocaleString("id-ID")}`}).forEach(([e,t])=>{a=a.replace(RegExp(e,"g"),t.toString())}),a}function o(e){let t=e;return Object.entries({"<temptix>":'<div class="ticket-container">',"</temptix>":"</div>","<ticket-header>":'<div class="ticket-header">',"</ticket-header>":"</div>","<ticket-body>":'<div class="ticket-body">',"</ticket-body>":"</div>","<event>":'<div class="event-name">',"</event>":"</div>","<category>":'<div class="event-category">',"</category>":"</div>","<buyer>":'<div class="buyer-name">',"</buyer>":"</div>","<qr-code":'<img class="qr-code"',"/>":" />","<verified>":'<div class="verified-badge">',"</verified>":"</div>","<admin-fee>":'<div class="admin-fee">',"</admin-fee>":"</div>"}).forEach(([e,a])=>{t=t.replace(RegExp(e,"g"),a)}),`
    <style>
      .ticket-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 20px;
        border: 2px dashed #0066cc;
        border-radius: 10px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        font-family: 'Arial', sans-serif;
        color: #1e40af;
      }
      .ticket-header {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #0066cc;
      }
      .ticket-body {
        text-align: center;
      }
      .event-name {
        font-size: 20px;
        font-weight: bold;
        margin: 10px 0;
        color: #1e40af;
      }
      .event-category {
        font-size: 14px;
        color: #64748b;
        margin: 5px 0;
      }
      .buyer-name {
        font-size: 16px;
        margin: 15px 0;
        padding: 10px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 5px;
      }
      .qr-code {
        width: 150px;
        height: 150px;
        margin: 15px 0;
        border: 2px solid #0066cc;
        border-radius: 5px;
      }
      .verified-badge {
        color: #059669;
        font-weight: bold;
        margin: 10px 0;
      }
      .admin-fee {
        font-size: 12px;
        color: #64748b;
        margin-top: 15px;
      }
    </style>
  `+t}let c=`<temptix>
  <ticket-header>🎫 TiXara</ticket-header>
  <ticket-body>
    <event>{{eventName}}</event>
    <category>{{category}}</category>
    <buyer>{{buyerName}}</buyer>
    <qr-code src="{{qr}}" />
    <verified>{{isVerified}}</verified>
    <admin-fee>Biaya Admin: {{adminFee}}</admin-fee>
  </ticket-body>
</temptix>`;function u(e){try{let t=e.split(":");if(4!==t.length||"TIXARA"!==t[0])return{isValid:!1,error:"Format QR code tidak valid"};let[,a,i,r]=t;if(!a||!i||!r)return{isValid:!1,error:"Data QR code tidak lengkap"};return{isValid:!0,ticketId:a,eventId:i}}catch(e){return{isValid:!1,error:"QR code tidak dapat dibaca"}}}function l(e,t){let a=Date.now();return`TIXARA:${e}:${t}:${a}`}},63721:(e,t,a)=>{let i,r;a.d(t,{x0:()=>n});let s=require("node:crypto");function n(e=21){var t;t=e|=0,!i||i.length<t?(i=Buffer.allocUnsafe(128*t),s.webcrypto.getRandomValues(i),r=0):r+t>i.length&&(s.webcrypto.getRandomValues(i),r=0),r+=t;let a="";for(let t=r-e;t<r;t++)a+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&i[t]];return a}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[1638,6206,9155,5252,7670],()=>a(17756));module.exports=i})();