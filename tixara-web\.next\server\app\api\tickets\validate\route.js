"use strict";(()=>{var e={};e.id=145,e.ids=[145],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17756:(e,t,a)=>{a.r(t),a.d(t,{headerHooks:()=>k,originalPathname:()=>g,patchFetch:()=>E,requestAsyncStorage:()=>p,routeModule:()=>m,serverHooks:()=>f,staticGenerationAsyncStorage:()=>D,staticGenerationBailout:()=>O});var i={};a.r(i),a.d(i,{POST:()=>v});var r=a(95419),s=a(69108),n=a(99678),o=a(78070),d=a(81355),u=a(25252),c=a(52178);(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=u.Ry({qrData:u.Z_().min(1,"QR data wajib diisi"),eventId:u.Z_().min(1,"Event ID wajib diisi")});async function v(e){try{let t=await (0,d.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});if(!["STAFF","ADMIN"].includes(t.user.role))return o.Z.json({success:!1,message:"Hanya staff yang dapat memvalidasi tiket"},{status:403});let a=await e.json(),i=l.parse(a),r=Object(function(){var e=Error("Cannot find module '@/lib/ticket-utils'");throw e.code="MODULE_NOT_FOUND",e}())(i.qrData);if(!r.isValid)return o.Z.json({success:!1,message:r.error||"QR code tidak valid",status:"INVALID_QR"},{status:400});let s=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.findUnique({where:{qrCode:i.qrData},include:{event:{include:{organizer:!0,category:!0}},buyer:{select:{id:!0,name:!0,email:!0}},validator:{select:{id:!0,name:!0,email:!0}}}});if(!s)return o.Z.json({success:!1,message:"Tiket tidak ditemukan",status:"NOT_FOUND"},{status:404});if(s.eventId!==i.eventId)return o.Z.json({success:!1,message:"Tiket tidak valid untuk event ini",status:"WRONG_EVENT"},{status:400});if("STAFF"===t.user.role&&!await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).eventStaff.findFirst({where:{eventId:i.eventId,staffId:t.user.id}}))return o.Z.json({success:!1,message:"Anda tidak memiliki akses untuk memvalidasi tiket event ini",status:"NO_PERMISSION"},{status:403});if(s.isUsed)return o.Z.json({success:!1,message:"Tiket sudah pernah digunakan",status:"ALREADY_USED",data:{ticket:{id:s.id,ticketCode:s.ticketCode,usedAt:s.usedAt,validatedBy:s.validator},event:{id:s.event.id,title:s.event.title},buyer:s.buyer}},{status:400});let n=new Date,u=new Date(s.event.startDate),c=new Date(s.event.endDate);if(n<u)return o.Z.json({success:!0,warning:"Event belum dimulai",status:"EARLY_VALIDATION",data:{ticket:{id:s.id,ticketCode:s.ticketCode,price:s.price,createdAt:s.createdAt},event:{id:s.event.id,title:s.event.title,startDate:s.event.startDate,endDate:s.event.endDate,location:s.event.location,organizer:s.event.organizer.name},buyer:s.buyer}});if(n>c)return o.Z.json({success:!1,message:"Event sudah berakhir",status:"EVENT_ENDED"},{status:400});let v=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).ticket.update({where:{id:s.id},data:{isUsed:!0,usedAt:new Date,validatedBy:t.user.id},include:{event:{include:{organizer:!0,category:!0}},buyer:{select:{id:!0,name:!0,email:!0}},validator:{select:{id:!0,name:!0,email:!0}}}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.create({data:{userId:s.buyerId,title:"Tiket Divalidasi",message:`Tiket Anda untuk "${s.event.title}" telah divalidasi`,type:"TICKET",data:{ticketId:s.id,eventId:s.eventId,validatedBy:t.user.id}}}),o.Z.json({success:!0,message:"Tiket berhasil divalidasi",status:"VALIDATED",data:{ticket:{id:v.id,ticketCode:v.ticketCode,price:v.price,usedAt:v.usedAt,createdAt:v.createdAt},event:{id:v.event.id,title:v.event.title,startDate:v.event.startDate,endDate:v.event.endDate,location:v.event.location,organizer:v.event.organizer.name,category:v.event.category.name},buyer:v.buyer,validator:v.validator}})}catch(e){if(e instanceof c.jm)return o.Z.json({success:!1,message:e.errors[0].message},{status:400});return console.error("Error validating ticket:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let m=new r.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/tickets/validate/route",pathname:"/api/tickets/validate",filename:"route",bundlePath:"app/api/tickets/validate/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\tickets\\validate\\route.ts",nextConfigOutput:"",userland:i}),{requestAsyncStorage:p,staticGenerationAsyncStorage:D,serverHooks:f,headerHooks:k,staticGenerationBailout:O}=m,g="/api/tickets/validate/route";function E(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:D})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),i=t.X(0,[1638,6206,1355,5252],()=>a(17756));module.exports=i})();