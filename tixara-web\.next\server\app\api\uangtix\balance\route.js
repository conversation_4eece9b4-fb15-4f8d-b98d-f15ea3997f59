"use strict";(()=>{var e={};e.id=5660,e.ids=[5660],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},57846:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>m,originalPathname:()=>h,patchFetch:()=>f,requestAsyncStorage:()=>p,routeModule:()=>l,serverHooks:()=>x,staticGenerationAsyncStorage:()=>d,staticGenerationBailout:()=>g});var a={};t.r(a),t.d(a,{GET:()=>c});var n=t(95419),o=t(69108),s=t(99678),i=t(78070),u=t(81355);async function c(e){try{let e=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!e?.user)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let r=await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBalance(e.user.id);return i.Z.json({success:!0,data:{balance:r,formattedBalance:new Intl.NumberFormat("id-ID",{style:"currency",currency:"IDR",minimumFractionDigits:0}).format(r)}})}catch(e){return console.error("Get balance error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}();let l=new n.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/uangtix/balance/route",pathname:"/api/uangtix/balance",filename:"route",bundlePath:"app/api/uangtix/balance/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\uangtix\\balance\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:p,staticGenerationAsyncStorage:d,serverHooks:x,headerHooks:m,staticGenerationBailout:g}=l,h="/api/uangtix/balance/route";function f(){return(0,s.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:d})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,1355],()=>t(57846));module.exports=a})();