"use strict";(()=>{var e={};e.id=4563,e.ids=[4563],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},21012:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>g,originalPathname:()=>h,patchFetch:()=>y,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>x,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>f});var s={};r.r(s),r.d(s,{POST:()=>p});var a=r(95419),o=r(69108),n=r(99678),i=r(78070),u=r(81355),c=r(63721);async function p(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{amount:r,gateway:s="TRIPAY",paymentMethod:a="QRIS"}=await e.json();if(!r||r<1e4)return i.Z.json({success:!1,message:"Minimum deposit Rp 10.000"},{status:400});if(r>1e7)return i.Z.json({success:!1,message:"Maksimum deposit Rp 10.000.000"},{status:400});let o=`DEP-${(0,c.x0)(10)}`;try{let e=Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).createPaymentGateway(s),n={amount:r,description:`Deposit UangtiX - ${t.user.name}`,customerName:t.user.name,customerEmail:t.user.email,customerPhone:t.user.phone||"",orderId:o,returnUrl:`http://localhost:3000/uangtix/deposit/success?orderId=${o}`,expiredTime:60},u=await e.createPayment(n,a);if(!u.success)return i.Z.json({success:!1,message:u.message},{status:400});let c=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).payment.create({data:{userId:t.user.id,gateway:s,externalId:u.paymentId,amount:r,adminFee:0,totalAmount:r,description:"Deposit UangtiX",status:"PENDING",paymentUrl:u.paymentUrl,expiredAt:u.expiredAt,metadata:u.data}});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).uangtiXTransaction.create({data:{userId:t.user.id,type:"DEPOSIT",amount:r,description:`Deposit via ${s}`,reference:o,status:"PENDING",paymentId:c.id,balanceBefore:0,balanceAfter:0}}),i.Z.json({success:!0,message:"Deposit request created successfully",data:{paymentId:c.id,orderId:o,paymentUrl:u.paymentUrl,qrCode:u.qrCode,expiredAt:u.expiredAt,amount:r,gateway:s}})}catch(e){return console.error("Deposit creation error:",e),i.Z.json({success:!1,message:e.message||"Gagal membuat deposit"},{status:500})}}catch(e){return console.error("Deposit API error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}();let d=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/uangtix/deposit/route",pathname:"/api/uangtix/deposit",filename:"route",bundlePath:"app/api/uangtix/deposit/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\uangtix\\deposit\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:l,staticGenerationAsyncStorage:m,serverHooks:x,headerHooks:g,staticGenerationBailout:f}=d,h="/api/uangtix/deposit/route";function y(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:m})}},63721:(e,t,r)=>{let s,a;r.d(t,{x0:()=>n});let o=require("node:crypto");function n(e=21){var t;t=e|=0,!s||s.length<t?(s=Buffer.allocUnsafe(128*t),o.webcrypto.getRandomValues(s),a=0):a+t>s.length&&(o.webcrypto.getRandomValues(s),a=0),a+=t;let r="";for(let t=a-e;t<a;t++)r+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&s[t]];return r}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[1638,6206,1355],()=>r(21012));module.exports=s})();