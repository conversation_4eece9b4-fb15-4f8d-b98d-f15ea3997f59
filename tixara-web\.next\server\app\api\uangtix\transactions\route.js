"use strict";(()=>{var e={};e.id=8235,e.ids=[8235],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17496:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>w,originalPathname:()=>h,patchFetch:()=>f,requestAsyncStorage:()=>g,routeModule:()=>c,serverHooks:()=>x,staticGenerationAsyncStorage:()=>m,staticGenerationBailout:()=>y});var r={};t.r(r),t.d(r,{GET:()=>l});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),u=t(81355),p=t(3205),d=t(3214);async function l(e){try{let a=await (0,u.getServerSession)(p.Lz);if(!a?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),s=parseInt(t.get("limit")||"20"),i=t.get("type"),n=t.get("status"),l=t.get("search"),c="true"===t.get("export"),g=(r-1)*s,m={userId:a.user.id};if(i&&"ALL"!==i&&(m.type=i),n&&"ALL"!==n&&(m.status=n),l&&(m.description={contains:l,mode:"insensitive"}),c){let e=(await d.prisma.uangtiXTransaction.findMany({where:m,orderBy:{createdAt:"desc"},include:{payment:{select:{id:!0,gateway:!0,status:!0}}}})).map(e=>{let a=new Date(e.createdAt).toLocaleDateString("id-ID"),t="DEPOSIT"===e.type||e.amount>0?e.amount:-e.amount,r=e.payment?.gateway||"";return`"${a}","${e.type}","${e.description}","${t}","${e.status}","${e.balanceBefore}","${e.balanceAfter}","${r}"`}).join("\n");return new o.Z("Date,Type,Description,Amount,Status,Balance Before,Balance After,Gateway\n"+e,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="uangtix-transactions-${new Date().toISOString().split("T")[0]}.csv"`}})}let[x,w]=await Promise.all([d.prisma.uangtiXTransaction.findMany({where:m,orderBy:{createdAt:"desc"},skip:g,take:s,include:{payment:{select:{id:!0,gateway:!0,status:!0}}}}),d.prisma.uangtiXTransaction.count({where:m})]);return o.Z.json({success:!0,data:x,pagination:{page:r,limit:s,total:w,totalPages:Math.ceil(w/s)}})}catch(e){return console.error("Get transactions error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/uangtix/transactions/route",pathname:"/api/uangtix/transactions",filename:"route",bundlePath:"app/api/uangtix/transactions/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\uangtix\\transactions\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:m,serverHooks:x,headerHooks:w,staticGenerationBailout:y}=c,h="/api/uangtix/transactions/route";function f(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:m})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>u});var r=t(65822),s=t(86485),i=t(98432),n=t.n(i),o=t(3214);t(53524);let u={adapter:(0,r.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:r})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&r&&(e={...e,...r}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},3214:(e,a,t)=>{t.d(a,{prisma:()=>s});var r=t(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1638,6206,9155],()=>t(17496));module.exports=r})();