"use strict";(()=>{var e={};e.id=8235,e.ids=[8235],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},17496:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>f,patchFetch:()=>h,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>g,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>x});var a={};r.r(a),r.d(a,{GET:()=>c});var n=r(95419),s=r(69108),o=r(99678),i=r(78070),u=r(81355);async function c(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),a=parseInt(r.get("page")||"1"),n=parseInt(r.get("limit")||"20"),s=r.get("type"),o=r.get("status"),c=r.get("search"),p="true"===r.get("export"),d=(a-1)*n,l={userId:t.user.id};if(s&&"ALL"!==s&&(l.type=s),o&&"ALL"!==o&&(l.status=o),c&&(l.description={contains:c,mode:"insensitive"}),p){let e=(await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).uangtiXTransaction.findMany({where:l,orderBy:{createdAt:"desc"},include:{payment:{select:{id:!0,gateway:!0,status:!0}}}})).map(e=>{let t=new Date(e.createdAt).toLocaleDateString("id-ID"),r="DEPOSIT"===e.type||e.amount>0?e.amount:-e.amount,a=e.payment?.gateway||"";return`"${t}","${e.type}","${e.description}","${r}","${e.status}","${e.balanceBefore}","${e.balanceAfter}","${a}"`}).join("\n");return new i.Z("Date,Type,Description,Amount,Status,Balance Before,Balance After,Gateway\n"+e,{headers:{"Content-Type":"text/csv","Content-Disposition":`attachment; filename="uangtix-transactions-${new Date().toISOString().split("T")[0]}.csv"`}})}let[g,m]=await Promise.all([Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).uangtiXTransaction.findMany({where:l,orderBy:{createdAt:"desc"},skip:d,take:n,include:{payment:{select:{id:!0,gateway:!0,status:!0}}}}),Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).uangtiXTransaction.count({where:l})]);return i.Z.json({success:!0,data:g,pagination:{page:a,limit:n,total:m,totalPages:Math.ceil(m/n)}})}catch(e){return console.error("Get transactions error:",e),i.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/uangtix/transactions/route",pathname:"/api/uangtix/transactions",filename:"route",bundlePath:"app/api/uangtix/transactions/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\uangtix\\transactions\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:g,headerHooks:m,staticGenerationBailout:x}=p,f="/api/uangtix/transactions/route";function h(){return(0,o.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:l})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355],()=>r(17496));module.exports=a})();