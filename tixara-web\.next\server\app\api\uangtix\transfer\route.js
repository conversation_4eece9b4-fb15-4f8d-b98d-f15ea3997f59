"use strict";(()=>{var e={};e.id=4422,e.ids=[4422],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},50591:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>f,originalPathname:()=>O,patchFetch:()=>x,requestAsyncStorage:()=>l,routeModule:()=>d,serverHooks:()=>m,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>g});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(95419),n=t(69108),i=t(99678),o=t(78070),u=t(81355);async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{recipientEmail:t,amount:a,description:s="Transfer UangtiX"}=await e.json();if(!t||!a)return o.Z.json({success:!1,message:"Email penerima dan jumlah diperlukan"},{status:400});if(a<1e3)return o.Z.json({success:!1,message:"Minimum transfer Rp 1.000"},{status:400});if(t===r.user.email)return o.Z.json({success:!1,message:"Tidak dapat transfer ke diri sendiri"},{status:400});let n=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findUnique({where:{email:t},select:{id:!0,name:!0,email:!0}});if(!n)return o.Z.json({success:!1,message:"Pengguna penerima tidak ditemukan"},{status:404});if(await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBalance(r.user.id)<a)return o.Z.json({success:!1,message:"Saldo tidak mencukupi"},{status:400});if(!await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).transfer(r.user.id,n.id,a,s))return o.Z.json({success:!1,message:"Gagal memproses transfer"},{status:500});return await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).notification.createMany({data:[{userId:r.user.id,title:"Transfer Berhasil",message:`Transfer Rp ${a.toLocaleString("id-ID")} ke ${n.name} berhasil`,type:"PAYMENT_SUCCESS",data:{type:"TRANSFER_OUT",amount:a,recipient:n.name}},{userId:n.id,title:"Menerima Transfer",message:`Anda menerima transfer Rp ${a.toLocaleString("id-ID")} dari ${r.user.name}`,type:"PAYMENT_SUCCESS",data:{type:"TRANSFER_IN",amount:a,sender:r.user.name}}]}),o.Z.json({success:!0,message:"Transfer berhasil",data:{amount:a,recipient:{name:n.name,email:n.email},description:s}})}catch(e){return console.error("Transfer error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}();let d=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/uangtix/transfer/route",pathname:"/api/uangtix/transfer",filename:"route",bundlePath:"app/api/uangtix/transfer/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\uangtix\\transfer\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:m,headerHooks:f,staticGenerationBailout:g}=d,O="/api/uangtix/transfer/route";function x(){return(0,i.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:p})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,6206,1355],()=>t(50591));module.exports=a})();