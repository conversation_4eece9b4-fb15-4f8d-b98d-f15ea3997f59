"use strict";(()=>{var e={};e.id=4422,e.ids=[4422,6112],e.modules={53524:e=>{e.exports=require("@prisma/client")},98432:e=>{e.exports=require("bcryptjs")},72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},57147:e=>{e.exports=require("fs")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},22037:e=>{e.exports=require("os")},71017:e=>{e.exports=require("path")},63477:e=>{e.exports=require("querystring")},12781:e=>{e.exports=require("stream")},76224:e=>{e.exports=require("tty")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},50591:(e,a,t)=>{t.r(a),t.d(a,{headerHooks:()=>y,originalPathname:()=>x,patchFetch:()=>T,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>f,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>w});var r={};t.r(r),t.d(r,{POST:()=>p});var s=t(95419),i=t(69108),n=t(99678),o=t(78070),c=t(81355),u=t(3205),d=t(3214),l=t(76112);async function p(e){try{let a=await (0,c.getServerSession)(u.Lz);if(!a?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{recipientEmail:t,amount:r,description:s="Transfer UangtiX"}=await e.json();if(!t||!r)return o.Z.json({success:!1,message:"Email penerima dan jumlah diperlukan"},{status:400});if(r<1e3)return o.Z.json({success:!1,message:"Minimum transfer Rp 1.000"},{status:400});if(t===a.user.email)return o.Z.json({success:!1,message:"Tidak dapat transfer ke diri sendiri"},{status:400});let i=await d.prisma.user.findUnique({where:{email:t},select:{id:!0,name:!0,email:!0}});if(!i)return o.Z.json({success:!1,message:"Pengguna penerima tidak ditemukan"},{status:404});if(await l.UangtiXWallet.getBalance(a.user.id)<r)return o.Z.json({success:!1,message:"Saldo tidak mencukupi"},{status:400});if(!await l.UangtiXWallet.transfer(a.user.id,i.id,r,s))return o.Z.json({success:!1,message:"Gagal memproses transfer"},{status:500});return await d.prisma.notification.createMany({data:[{userId:a.user.id,title:"Transfer Berhasil",message:`Transfer Rp ${r.toLocaleString("id-ID")} ke ${i.name} berhasil`,type:"PAYMENT_SUCCESS",data:{type:"TRANSFER_OUT",amount:r,recipient:i.name}},{userId:i.id,title:"Menerima Transfer",message:`Anda menerima transfer Rp ${r.toLocaleString("id-ID")} dari ${a.user.name}`,type:"PAYMENT_SUCCESS",data:{type:"TRANSFER_IN",amount:r,sender:a.user.name}}]}),o.Z.json({success:!0,message:"Transfer berhasil",data:{amount:r,recipient:{name:i.name,email:i.email},description:s}})}catch(e){return console.error("Transfer error:",e),o.Z.json({success:!1,message:"Terjadi kesalahan server"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/uangtix/transfer/route",pathname:"/api/uangtix/transfer",filename:"route",bundlePath:"app/api/uangtix/transfer/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\uangtix\\transfer\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:g,serverHooks:f,headerHooks:y,staticGenerationBailout:w}=m,x="/api/uangtix/transfer/route";function T(){return(0,n.patchFetch)({serverHooks:f,staticGenerationAsyncStorage:g})}},3205:(e,a,t)=>{t.d(a,{Lz:()=>c});var r=t(65822),s=t(86485),i=t(98432),n=t.n(i),o=t(3214);t(53524);let c={adapter:(0,r.N)(o.prisma),providers:[(0,s.Z)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.email||!e?.password)throw Error("Email dan password harus diisi");let a=await o.prisma.user.findUnique({where:{email:e.email}});if(!a||!await n().compare(e.password,a.password))throw Error("Email atau password salah");return await o.prisma.user.update({where:{id:a.id},data:{lastLoginAt:new Date}}),{id:a.id,email:a.email,name:a.name,role:a.role,isVerified:a.isVerified,badge:a.badge,avatar:a.avatar}}})],session:{strategy:"jwt",maxAge:2592e3},jwt:{maxAge:2592e3},callbacks:{jwt:async({token:e,user:a,trigger:t,session:r})=>(a&&(e.role=a.role,e.isVerified=a.isVerified,e.badge=a.badge,e.avatar=a.avatar),"update"===t&&r&&(e={...e,...r}),e),session:async({session:e,token:a})=>(a&&(e.user.id=a.sub,e.user.role=a.role,e.user.isVerified=a.isVerified,e.user.badge=a.badge,e.user.avatar=a.avatar),e)},pages:{signIn:"/auth/login",signUp:"/auth/register",error:"/auth/error"},events:{async signIn({user:e,isNewUser:a}){a&&await o.prisma.notification.create({data:{userId:e.id,title:"Selamat Datang di TiXara!",message:"Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.",type:"SYSTEM_ANNOUNCEMENT"}})}},debug:!1}},76112:(e,a,t)=>{t.d(a,{H4:()=>u,PaymentFactory:()=>l,UangtiXWallet:()=>d,YZ:()=>o,Z4:()=>c});var r=t(6113),s=t.n(r),i=t(83949);let n={TRIPAY:{baseUrl:process.env.TRIPAY_BASE_URL||"https://tripay.co.id/api-sandbox",merchantCode:process.env.TRIPAY_MERCHANT_CODE||"",apiKey:process.env.TRIPAY_API_KEY||"",privateKey:process.env.TRIPAY_PRIVATE_KEY||""},MIDTRANS:{baseUrl:process.env.MIDTRANS_BASE_URL||"https://api.sandbox.midtrans.com/v2",serverKey:process.env.MIDTRANS_SERVER_KEY||"",clientKey:process.env.MIDTRANS_CLIENT_KEY||""},XENDIT:{baseUrl:process.env.XENDIT_BASE_URL||"https://api.xendit.co",secretKey:process.env.XENDIT_SECRET_KEY||""}};class o{generateSignature(e){let a=JSON.stringify(e);return s().createHmac("sha256",this.config.privateKey).update(a).digest("hex")}async getPaymentChannels(){try{let e=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+this.config.apiKey).digest("hex");return(await i.Z.get(`${this.config.baseUrl}/merchant/payment-channel`,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":e}})).data}catch(e){throw console.error("Tripay get channels error:",e),e}}async createPayment(e,a="QRIS"){try{let t=e.expiredTime||60,r=new Date(Date.now()+6e4*t),n={method:a,merchant_ref:e.orderId,amount:e.amount,customer_name:e.customerName,customer_email:e.customerEmail,customer_phone:e.customerPhone||"",order_items:[{sku:"TICKET",name:e.description,price:e.amount,quantity:1}],return_url:e.returnUrl||"http://localhost:3000/payment/success",expired_time:Math.floor(r.getTime()/1e3),signature:""},o=this.config.merchantCode+e.orderId+e.amount;n.signature=s().createHmac("sha256",this.config.privateKey).update(o).digest("hex");let c=await i.Z.post(`${this.config.baseUrl}/transaction/create`,n,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}});if(c.data.success)return{success:!0,paymentId:c.data.data.reference,paymentUrl:c.data.data.checkout_url,qrCode:c.data.data.qr_url,expiredAt:r,data:c.data.data};return{success:!1,paymentId:"",message:c.data.message}}catch(e){return console.error("Tripay create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let a=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+e).digest("hex");return(await i.Z.get(`${this.config.baseUrl}/transaction/detail`,{params:{reference:e},headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":a}})).data}catch(e){throw console.error("Tripay check status error:",e),e}}verifyCallback(e,a){return s().createHmac("sha256",this.config.privateKey).update(JSON.stringify(e)).digest("hex")===a}constructor(){this.config=n.TRIPAY}}class c{async createPayment(e){try{let a={transaction_details:{order_id:e.orderId,gross_amount:e.amount},customer_details:{first_name:e.customerName,email:e.customerEmail,phone:e.customerPhone||""},item_details:[{id:"TICKET",price:e.amount,quantity:1,name:e.description}],credit_card:{secure:!0}},t=Buffer.from(this.config.serverKey+":").toString("base64"),r=await i.Z.post(`${this.config.baseUrl}/charge`,a,{headers:{Authorization:`Basic ${t}`,"Content-Type":"application/json"}});if("201"===r.data.status_code)return{success:!0,paymentId:r.data.transaction_id,paymentUrl:r.data.redirect_url,data:r.data};return{success:!1,paymentId:"",message:r.data.status_message}}catch(e){return console.error("Midtrans create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.status_message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let a=Buffer.from(this.config.serverKey+":").toString("base64");return(await i.Z.get(`${this.config.baseUrl}/${e}/status`,{headers:{Authorization:`Basic ${a}`}})).data}catch(e){throw console.error("Midtrans check status error:",e),e}}verifyCallback(e,a){let t=e.order_id,r=e.status_code,i=e.gross_amount;return s().createHash("sha512").update(t+r+i+this.config.serverKey).digest("hex")===a}constructor(){this.config=n.MIDTRANS}}class u{async createPayment(e){try{let a=e.expiredTime||60,t=new Date(Date.now()+6e4*a),r={external_id:e.orderId,amount:e.amount,description:e.description,payer_email:e.customerEmail,success_redirect_url:e.returnUrl||"http://localhost:3000/payment/success",failure_redirect_url:e.returnUrl||"http://localhost:3000/payment/failed",invoice_duration:60*a},s=Buffer.from(this.config.secretKey+":").toString("base64"),n=await i.Z.post(`${this.config.baseUrl}/v2/invoices`,r,{headers:{Authorization:`Basic ${s}`,"Content-Type":"application/json"}});return{success:!0,paymentId:n.data.id,paymentUrl:n.data.invoice_url,expiredAt:t,data:n.data}}catch(e){return console.error("Xendit create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let a=Buffer.from(this.config.secretKey+":").toString("base64");return(await i.Z.get(`${this.config.baseUrl}/v2/invoices/${e}`,{headers:{Authorization:`Basic ${a}`}})).data}catch(e){throw console.error("Xendit check status error:",e),e}}verifyCallback(e,a){return a===process.env.XENDIT_WEBHOOK_TOKEN}constructor(){this.config=n.XENDIT}}class d{static async getBalance(e){let{prisma:a}=await Promise.resolve().then(t.bind(t,3214)),r=await a.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});return r?.uangtixBalance||0}static async addBalance(e,a,r,s){let{prisma:i}=await Promise.resolve().then(t.bind(t,3214));try{return await i.$transaction(async t=>{let i=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!i)throw Error("User not found");let n=i.uangtixBalance,o=n+a;await t.user.update({where:{id:e},data:{uangtixBalance:o}}),await t.uangtiXTransaction.create({data:{userId:e,type:a>0?"DEPOSIT":"WITHDRAW",amount:Math.abs(a),description:r,reference:s,status:"SUCCESS",balanceBefore:n,balanceAfter:o}})}),!0}catch(e){return console.error("UangtiX add balance error:",e),!1}}static async transfer(e,a,r,s){let{prisma:i}=await Promise.resolve().then(t.bind(t,3214));try{return await i.$transaction(async t=>{let i=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!i||i.uangtixBalance<r)throw Error("Insufficient balance");let n=await t.user.findUnique({where:{id:a},select:{uangtixBalance:!0}});if(!n)throw Error("Receiver not found");let o=i.uangtixBalance,c=o-r;await t.user.update({where:{id:e},data:{uangtixBalance:c}});let u=n.uangtixBalance,d=u+r;await t.user.update({where:{id:a},data:{uangtixBalance:d}}),await t.uangtiXTransaction.createMany({data:[{userId:e,type:"TRANSFER",amount:-r,description:`Transfer ke ${a}: ${s}`,reference:a,status:"SUCCESS",balanceBefore:o,balanceAfter:c},{userId:a,type:"TRANSFER",amount:r,description:`Transfer dari ${e}: ${s}`,reference:e,status:"SUCCESS",balanceBefore:u,balanceAfter:d}]})}),!0}catch(e){return console.error("UangtiX transfer error:",e),!1}}}class l{static createPaymentGateway(e){switch(e.toUpperCase()){case"TRIPAY":return new o;case"MIDTRANS":return new c;case"XENDIT":return new u;default:throw Error(`Unsupported payment gateway: ${e}`)}}}},3214:(e,a,t)=>{t.d(a,{prisma:()=>s});var r=t(53524);let s=globalThis.prisma??new r.PrismaClient({log:["error"]})}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1638,6206,9155,3949],()=>t(50591));module.exports=r})();