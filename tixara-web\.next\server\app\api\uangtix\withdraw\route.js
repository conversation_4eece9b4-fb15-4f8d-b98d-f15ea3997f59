"use strict";(()=>{var e={};e.id=8093,e.ids=[8093],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},92889:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>h,patchFetch:()=>g,requestAsyncStorage:()=>l,routeModule:()=>c,serverHooks:()=>w,staticGenerationAsyncStorage:()=>p,staticGenerationBailout:()=>f});var a={};r.r(a),r.d(a,{POST:()=>d});var s=r(95419),i=r(69108),n=r(99678),o=r(78070),u=r(81355);async function d(e){try{let t=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!t?.user)return o.Z.json({success:!1,message:"Unauthorized"},{status:401});let{amount:r,bankCode:a,accountNumber:s,accountName:i}=await e.json();if(!r||!a||!s||!i)return o.Z.json({success:!1,message:"Missing required fields"},{status:400});if(r<5e4)return o.Z.json({success:!1,message:"Minimum withdraw amount is Rp 50.000"},{status:400});if(s.length<8)return o.Z.json({success:!1,message:"Account number must be at least 8 digits"},{status:400});let n=r<=1e5?2500:5e3,d=r+n;if(await Object(function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}()).getBalance(t.user.id)<d)return o.Z.json({success:!1,message:`Insufficient balance. Required: Rp ${d.toLocaleString("id-ID")} (including admin fee Rp ${n.toLocaleString("id-ID")})`},{status:400});let c=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).$transaction(async e=>{let o=await e.user.findUnique({where:{id:t.user.id},select:{uangtixBalance:!0}});if(!o)throw Error("User not found");let u=o.uangtixBalance,c=u-d;await e.user.update({where:{id:t.user.id},data:{uangtixBalance:c}});let l=await e.uangtiXTransaction.create({data:{userId:t.user.id,type:"WITHDRAW",amount:r,description:`Withdraw to ${a} ${s} - ${i}`,reference:`${a}-${s}`,status:"PENDING",balanceBefore:u,balanceAfter:c}});return await e.uangtiXTransaction.create({data:{userId:t.user.id,type:"PAYMENT",amount:n,description:`Admin fee for withdraw ${l.id}`,reference:l.id,status:"SUCCESS",balanceBefore:c,balanceAfter:c}}),await e.notification.create({data:{userId:t.user.id,title:"Withdraw Request Submitted",message:`Your withdraw request of Rp ${r.toLocaleString("id-ID")} has been submitted and will be processed within 1-3 business days.`,type:"TRANSACTION",isRead:!1}}),l});return o.Z.json({success:!0,message:"Withdraw request submitted successfully",data:{transactionId:c.id,amount:r,fee:n,totalDeduction:d,bankCode:a,accountNumber:s,accountName:i,status:"PENDING"}})}catch(e){return console.error("Withdraw error:",e),o.Z.json({success:!1,message:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/payment-utils'");throw e.code="MODULE_NOT_FOUND",e}();let c=new s.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/uangtix/withdraw/route",pathname:"/api/uangtix/withdraw",filename:"route",bundlePath:"app/api/uangtix/withdraw/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\uangtix\\withdraw\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:l,staticGenerationAsyncStorage:p,serverHooks:w,headerHooks:m,staticGenerationBailout:f}=c,h="/api/uangtix/withdraw/route";function g(){return(0,n.patchFetch)({serverHooks:w,staticGenerationAsyncStorage:p})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,6206,1355],()=>r(92889));module.exports=a})();