"use strict";(()=>{var e={};e.id=45,e.ids=[45],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},39491:e=>{e.exports=require("assert")},14300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},82361:e=>{e.exports=require("events")},13685:e=>{e.exports=require("http")},95687:e=>{e.exports=require("https")},63477:e=>{e.exports=require("querystring")},57310:e=>{e.exports=require("url")},73837:e=>{e.exports=require("util")},59796:e=>{e.exports=require("zlib")},38910:(e,r,t)=>{t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>f,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>m,staticGenerationAsyncStorage:()=>l,staticGenerationBailout:()=>x});var s={};t.r(s),t.d(s,{GET:()=>c});var a=t(95419),n=t(69108),o=t(99678),i=t(78070),u=t(81355);async function c(e){try{let r=await (0,u.getServerSession)(Object(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e}()));if(!r?.user)return i.Z.json({success:!1,message:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=t.get("email"),a=t.get("name"),n=parseInt(t.get("limit")||"10");if(!s&&!a)return i.Z.json({success:!1,message:"Email or name parameter required"},{status:400});let o={AND:[{id:{not:r.user.id}},{isVerified:!0}]};s&&o.AND.push({email:{contains:s,mode:"insensitive"}}),a&&o.AND.push({name:{contains:a,mode:"insensitive"}});let c=await Object(function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}()).user.findMany({where:o,select:{id:!0,name:!0,email:!0,avatar:!0,role:!0,badge:!0},take:n,orderBy:{name:"asc"}});return i.Z.json({success:!0,data:c})}catch(e){return console.error("User search error:",e),i.Z.json({success:!1,message:"Internal server error"},{status:500})}}(function(){var e=Error("Cannot find module '@/lib/auth'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/prisma'");throw e.code="MODULE_NOT_FOUND",e}();let p=new a.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/users/search/route",pathname:"/api/users/search",filename:"route",bundlePath:"app/api/users/search/route"},resolvedPagePath:"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\api\\users\\search\\route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:m,headerHooks:h,staticGenerationBailout:x}=p,f="/api/users/search/route";function g(){return(0,o.patchFetch)({serverHooks:m,staticGenerationAsyncStorage:l})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[1638,6206,1355],()=>t(38910));module.exports=s})();