(()=>{var e={};e.id=8454,e.ids=[8454],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},46525:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>l});var a=t(50482),o=t(69108),n=t(62563),s=t.n(n),i=t(68300),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l=["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,37372)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\auth\\register\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\auth\\register\\page.tsx"],m="/auth/register/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},24933:(e,r,t)=>{Promise.resolve().then(t.bind(t,21909))},16509:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},23978:()=>{},21909:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var a=t(95344),o=t(3729),n=t(8428),s=t(56506);(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}();var i=t(1222),d=t(53148),l=t(42739);function c(){let[e,r]=(0,o.useState)({name:"",email:"",password:"",confirmPassword:"",phone:"",role:"BUYER"}),[t,c]=(0,o.useState)(!1),[m,u]=(0,o.useState)(!1),[p,h]=(0,o.useState)(!1),[f,O]=(0,o.useState)({}),x=(0,n.useRouter)(),{toast:N}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),v=()=>{let r={};return e.name.trim()?e.name.trim().length<2&&(r.name="Nama minimal 2 karakter"):r.name="Nama lengkap wajib diisi",e.email.trim()?Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.email)||(r.email="Format email tidak valid"):r.email="Email wajib diisi",e.password?e.password.length<8&&(r.password="Password minimal 8 karakter"):r.password="Password wajib diisi",e.confirmPassword?e.password!==e.confirmPassword&&(r.confirmPassword="Password tidak cocok"):r.confirmPassword="Konfirmasi password wajib diisi",e.phone.trim()?/^(\+62|62|0)[0-9]{9,13}$/.test(e.phone.replace(/\s/g,""))||(r.phone="Format nomor telepon tidak valid"):r.phone="Nomor telepon wajib diisi",O(r),0===Object.keys(r).length},w=async r=>{if(r.preventDefault(),v()){h(!0);try{let r=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name.trim(),email:e.email.toLowerCase().trim(),password:e.password,phone:e.phone.replace(/\s/g,""),role:e.role})}),t=await r.json();r.ok?(N({title:"Registrasi Berhasil!",description:"Akun Anda telah dibuat. Silakan login untuk melanjutkan.",variant:"success"}),x.push("/auth/login?message=registration-success")):N({title:"Registrasi Gagal",description:t.message||"Terjadi kesalahan saat membuat akun.",variant:"destructive"})}catch(e){N({title:"Error",description:"Terjadi kesalahan. Silakan coba lagi.",variant:"destructive"})}finally{h(!1)}}},j=(e,t)=>{r(r=>({...r,[e]:t})),f[e]&&O(r=>({...r,[e]:""}))};return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 p-4",children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full max-w-md",children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-2xl font-bold text-primary-600",children:"Daftar ke TiXara"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Buat akun untuk mulai jual beli tiket event"})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"name",children:"Nama Lengkap"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"name",type:"text",placeholder:"Masukkan nama lengkap",value:e.name,onChange:e=>j("name",e.target.value),disabled:p,className:f.name?"border-red-500":""}),f.name&&a.jsx("p",{className:"text-sm text-red-500",children:f.name})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"email",children:"Email"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"email",type:"email",placeholder:"<EMAIL>",value:e.email,onChange:e=>j("email",e.target.value),disabled:p,className:f.email?"border-red-500":""}),f.email&&a.jsx("p",{className:"text-sm text-red-500",children:f.email})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"phone",children:"Nomor Telepon"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"phone",type:"tel",placeholder:"08123456789",value:e.phone,onChange:e=>j("phone",e.target.value),disabled:p,className:f.phone?"border-red-500":""}),f.phone&&a.jsx("p",{className:"text-sm text-red-500",children:f.phone})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"role",children:"Daftar Sebagai"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.role,onValueChange:e=>j("role",e),disabled:p,children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Pilih peran"})}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"BUYER",children:"Pembeli Tiket"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"ORGANIZER",children:"Organizer Event"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"password",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"password",type:t?"text":"password",placeholder:"Minimal 8 karakter",value:e.password,onChange:e=>j("password",e.target.value),disabled:p,className:f.password?"border-red-500":""}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>c(!t),disabled:p,children:t?a.jsx(i.Z,{className:"h-4 w-4"}):a.jsx(d.Z,{className:"h-4 w-4"})})]}),f.password&&a.jsx("p",{className:"text-sm text-red-500",children:f.password})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"confirmPassword",children:"Konfirmasi Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"confirmPassword",type:m?"text":"password",placeholder:"Ulangi password",value:e.confirmPassword,onChange:e=>j("confirmPassword",e.target.value),disabled:p,className:f.confirmPassword?"border-red-500":""}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>u(!m),disabled:p,children:m?a.jsx(i.Z,{className:"h-4 w-4"}):a.jsx(d.Z,{className:"h-4 w-4"})})]}),f.confirmPassword&&a.jsx("p",{className:"text-sm text-red-500",children:f.confirmPassword})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"submit",className:"w-full",disabled:p,variant:"primary",children:[p&&a.jsx(l.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Daftar Sekarang"]})]}),a.jsx("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Sudah punya akun?"," ",a.jsx(s.default,{href:"/auth/login",className:"text-primary-600 hover:text-primary-700 font-medium",children:"Masuk di sini"})]})}),(0,a.jsxs)("div",{className:"mt-4 text-xs text-center text-muted-foreground",children:["Dengan mendaftar, Anda menyetujui"," ",a.jsx(s.default,{href:"/terms",className:"text-primary-600 hover:underline",children:"Syarat & Ketentuan"})," ","dan"," ",a.jsx(s.default,{href:"/privacy",className:"text-primary-600 hover:underline",children:"Kebijakan Privasi"})," ","kami."]})]})]})})}(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},69224:(e,r,t)=>{"use strict";t.d(r,{Z:()=>s});var a=t(3729),o={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),s=(e,r)=>{let t=(0,a.forwardRef)(({color:t="currentColor",size:s=24,strokeWidth:i=2,absoluteStrokeWidth:d,className:l="",children:c,...m},u)=>(0,a.createElement)("svg",{ref:u,...o,width:s,height:s,stroke:t,strokeWidth:d?24*Number(i)/Number(s):i,className:["lucide",`lucide-${n(e)}`,l].join(" "),...m},[...r.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(c)?c:[c]]));return t.displayName=`${e}`,t}},1222:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},42739:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8428:(e,r,t)=>{"use strict";var a=t(14767);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},37372:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>n,__esModule:()=>o,default:()=>s});let a=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\auth\register\page.tsx`),{__esModule:o,$$typeof:n}=a,s=a.default},82917:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>l,metadata:()=>d});var a=t(25036),o=t(450),n=t.n(o),s=t(14824),i=t.n(s);t(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let d={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function l({children:e}){return a.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:a.jsx("body",{className:`${n().variable} ${i().variable} font-sans antialiased`,children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),a.jsx("main",{className:"flex-1",children:e}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},67272:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[1638,3293,6506],()=>t(46525));module.exports=a})();