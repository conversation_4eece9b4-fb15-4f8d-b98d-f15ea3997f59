(()=>{var e={};e.id=8454,e.ids=[8454],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},46525:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var r=s(50482),t=s(69108),i=s(62563),l=s.n(i),n=s(68300),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(a,d);let o=["",{children:["auth",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,37372)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\auth\\register\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\auth\\register\\page.tsx"],m="/auth/register/page",p={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/auth/register/page",pathname:"/auth/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},24933:(e,a,s)=>{Promise.resolve().then(s.bind(s,21909))},21909:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>g});var r=s(95344),t=s(3729),i=s(8428),l=s(56506),n=s(16212),d=s(61351),o=s(92549),c=s(54572),m=s(17470),p=s(1222),u=s(53148),x=s(42739),h=s(30692),f=s(91626);function g(){let[e,a]=(0,t.useState)({name:"",email:"",password:"",confirmPassword:"",phone:"",role:"BUYER"}),[s,g]=(0,t.useState)(!1),[j,w]=(0,t.useState)(!1),[y,b]=(0,t.useState)(!1),[N,v]=(0,t.useState)({}),k=(0,i.useRouter)(),{toast:P}=(0,h.pm)(),_=()=>{let a={};return e.name.trim()?e.name.trim().length<2&&(a.name="Nama minimal 2 karakter"):a.name="Nama lengkap wajib diisi",e.email.trim()?(0,f.isValidEmail)(e.email)||(a.email="Format email tidak valid"):a.email="Email wajib diisi",e.password?e.password.length<8&&(a.password="Password minimal 8 karakter"):a.password="Password wajib diisi",e.confirmPassword?e.password!==e.confirmPassword&&(a.confirmPassword="Password tidak cocok"):a.confirmPassword="Konfirmasi password wajib diisi",e.phone.trim()?/^(\+62|62|0)[0-9]{9,13}$/.test(e.phone.replace(/\s/g,""))||(a.phone="Format nomor telepon tidak valid"):a.phone="Nomor telepon wajib diisi",v(a),0===Object.keys(a).length},R=async a=>{if(a.preventDefault(),_()){b(!0);try{let a=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name.trim(),email:e.email.toLowerCase().trim(),password:e.password,phone:e.phone.replace(/\s/g,""),role:e.role})}),s=await a.json();a.ok?(P({title:"Registrasi Berhasil!",description:"Akun Anda telah dibuat. Silakan login untuk melanjutkan.",variant:"success"}),k.push("/auth/login?message=registration-success")):P({title:"Registrasi Gagal",description:s.message||"Terjadi kesalahan saat membuat akun.",variant:"destructive"})}catch(e){P({title:"Error",description:"Terjadi kesalahan. Silakan coba lagi.",variant:"destructive"})}finally{b(!1)}}},Z=(e,s)=>{a(a=>({...a,[e]:s})),N[e]&&v(a=>({...a,[e]:""}))};return r.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-primary-50 to-secondary-50 p-4",children:(0,r.jsxs)(d.Zb,{className:"w-full max-w-md",children:[(0,r.jsxs)(d.Ol,{className:"text-center",children:[r.jsx(d.ll,{className:"text-2xl font-bold text-primary-600",children:"Daftar ke TiXara"}),r.jsx(d.SZ,{children:"Buat akun untuk mulai jual beli tiket event"})]}),(0,r.jsxs)(d.aY,{children:[(0,r.jsxs)("form",{onSubmit:R,className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c._,{htmlFor:"name",children:"Nama Lengkap"}),r.jsx(o.I,{id:"name",type:"text",placeholder:"Masukkan nama lengkap",value:e.name,onChange:e=>Z("name",e.target.value),disabled:y,className:N.name?"border-red-500":""}),N.name&&r.jsx("p",{className:"text-sm text-red-500",children:N.name})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c._,{htmlFor:"email",children:"Email"}),r.jsx(o.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:e.email,onChange:e=>Z("email",e.target.value),disabled:y,className:N.email?"border-red-500":""}),N.email&&r.jsx("p",{className:"text-sm text-red-500",children:N.email})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c._,{htmlFor:"phone",children:"Nomor Telepon"}),r.jsx(o.I,{id:"phone",type:"tel",placeholder:"08123456789",value:e.phone,onChange:e=>Z("phone",e.target.value),disabled:y,className:N.phone?"border-red-500":""}),N.phone&&r.jsx("p",{className:"text-sm text-red-500",children:N.phone})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c._,{htmlFor:"role",children:"Daftar Sebagai"}),(0,r.jsxs)(m.Ph,{value:e.role,onValueChange:e=>Z("role",e),disabled:y,children:[r.jsx(m.i4,{children:r.jsx(m.ki,{placeholder:"Pilih peran"})}),(0,r.jsxs)(m.Bw,{children:[r.jsx(m.Ql,{value:"BUYER",children:"Pembeli Tiket"}),r.jsx(m.Ql,{value:"ORGANIZER",children:"Organizer Event"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c._,{htmlFor:"password",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(o.I,{id:"password",type:s?"text":"password",placeholder:"Minimal 8 karakter",value:e.password,onChange:e=>Z("password",e.target.value),disabled:y,className:N.password?"border-red-500":""}),r.jsx(n.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>g(!s),disabled:y,children:s?r.jsx(p.Z,{className:"h-4 w-4"}):r.jsx(u.Z,{className:"h-4 w-4"})})]}),N.password&&r.jsx("p",{className:"text-sm text-red-500",children:N.password})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(c._,{htmlFor:"confirmPassword",children:"Konfirmasi Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(o.I,{id:"confirmPassword",type:j?"text":"password",placeholder:"Ulangi password",value:e.confirmPassword,onChange:e=>Z("confirmPassword",e.target.value),disabled:y,className:N.confirmPassword?"border-red-500":""}),r.jsx(n.z,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>w(!j),disabled:y,children:j?r.jsx(p.Z,{className:"h-4 w-4"}):r.jsx(u.Z,{className:"h-4 w-4"})})]}),N.confirmPassword&&r.jsx("p",{className:"text-sm text-red-500",children:N.confirmPassword})]}),(0,r.jsxs)(n.z,{type:"submit",className:"w-full",disabled:y,variant:"primary",children:[y&&r.jsx(x.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Daftar Sekarang"]})]}),r.jsx("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Sudah punya akun?"," ",r.jsx(l.default,{href:"/auth/login",className:"text-primary-600 hover:text-primary-700 font-medium",children:"Masuk di sini"})]})}),(0,r.jsxs)("div",{className:"mt-4 text-xs text-center text-muted-foreground",children:["Dengan mendaftar, Anda menyetujui"," ",r.jsx(l.default,{href:"/terms",className:"text-primary-600 hover:underline",children:"Syarat & Ketentuan"})," ","dan"," ",r.jsx(l.default,{href:"/privacy",className:"text-primary-600 hover:underline",children:"Kebijakan Privasi"})," ","kami."]})]})]})})}},61351:(e,a,s)=>{"use strict";s.d(a,{Ol:()=>n,SZ:()=>o,Zb:()=>l,aY:()=>c,ll:()=>d});var r=s(95344),t=s(3729),i=s(91626);let l=t.forwardRef(({className:e,elevated:a=!1,padding:s="md",...t},l)=>r.jsx("div",{ref:l,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",a&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===s,"p-3":"sm"===s,"p-6":"md"===s,"p-8":"lg"===s},e),...t}));l.displayName="Card";let n=t.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));n.displayName="CardHeader";let d=t.forwardRef(({className:e,...a},s)=>r.jsx("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));d.displayName="CardTitle";let o=t.forwardRef(({className:e,...a},s)=>r.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...a}));c.displayName="CardContent",t.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},92549:(e,a,s)=>{"use strict";s.d(a,{I:()=>l});var r=s(95344),t=s(3729),i=s(91626);let l=t.forwardRef(({className:e,type:a,...s},t)=>r.jsx("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));l.displayName="Input"},54572:(e,a,s)=>{"use strict";s.d(a,{_:()=>c});var r=s(95344),t=s(3729),i=s(62409),l=t.forwardRef((e,a)=>(0,r.jsx)(i.WV.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));l.displayName="Label";var n=s(92193),d=s(91626);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef(({className:e,...a},s)=>r.jsx(l,{ref:s,className:(0,d.cn)(o(),e),...a}));c.displayName=l.displayName},17470:(e,a,s)=>{"use strict";s.d(a,{Bw:()=>h,Ph:()=>c,Ql:()=>f,i4:()=>p,ki:()=>m});var r=s(95344),t=s(3729),i=s(32116),l=s(25390),n=s(12704),d=s(62312),o=s(91626);let c=i.fC;i.ZA;let m=i.B4,p=t.forwardRef(({className:e,children:a,...s},t)=>(0,r.jsxs)(i.xz,{ref:t,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,r.jsx(i.JO,{asChild:!0,children:r.jsx(l.Z,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=i.xz.displayName;let u=t.forwardRef(({className:e,...a},s)=>r.jsx(i.u_,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(n.Z,{className:"h-4 w-4"})}));u.displayName=i.u_.displayName;let x=t.forwardRef(({className:e,...a},s)=>r.jsx(i.$G,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(l.Z,{className:"h-4 w-4"})}));x.displayName=i.$G.displayName;let h=t.forwardRef(({className:e,children:a,position:s="popper",...t},l)=>r.jsx(i.h_,{children:(0,r.jsxs)(i.VY,{ref:l,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...t,children:[r.jsx(u,{}),r.jsx(i.l_,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),r.jsx(x,{})]})}));h.displayName=i.VY.displayName,t.forwardRef(({className:e,...a},s)=>r.jsx(i.__,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=i.__.displayName;let f=t.forwardRef(({className:e,children:a,...s},t)=>(0,r.jsxs)(i.ck,{ref:t,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(d.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:a})]}));f.displayName=i.ck.displayName,t.forwardRef(({className:e,...a},s)=>r.jsx(i.Z0,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=i.Z0.displayName},1222:(e,a,s)=>{"use strict";s.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,a,s)=>{"use strict";s.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},37372:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>i,__esModule:()=>t,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\auth\register\page.tsx`),{__esModule:t,$$typeof:i}=r,l=r.default}};var a=require("../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),r=a.X(0,[1638,3088,4739,9205],()=>s(46525));module.exports=r})();