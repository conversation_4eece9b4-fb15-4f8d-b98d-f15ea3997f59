(()=>{var e={};e.id=3202,e.ids=[3202],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},6738:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=a(50482),s=a(69108),i=a(62563),n=a.n(i),l=a(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let d=["",{children:["badges",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,57149)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\badges\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\badges\\page.tsx"],u="/badges/page",m={require:a,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/badges/page",pathname:"/badges",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63579:(e,t,a)=>{Promise.resolve().then(a.bind(a,5989))},5989:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>R});var r=a(95344),s=a(3729),i=a(47674),n=a(8428),l=a(16212),o=a(61351),d=a(69436),c=a(25757),u=a(71809),m=a(54572),x=a(42739),p=a(1750),g=a(23485);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let f=(0,a(69224).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]);var h=a(14513),b=a(85674),y=a(30692);let v={BRONZE:{name:"Bronze",color:"#CD7F32",bgColor:"bg-amber-100 dark:bg-amber-900/20",textColor:"text-amber-700 dark:text-amber-300",borderColor:"border-amber-300",icon:"\uD83E\uDD49",features:["Akses dasar platform","Buat event unlimited","Template tiket gratis","Support email","Komisi standar"],limits:{maxEvents:null,maxStaff:3,maxTemplates:5,canUseCustomDomain:!1,canUsePremiumTemplates:!1,prioritySupport:!1,analyticsAccess:"basic"}},SILVER:{name:"Silver",color:"#C0C0C0",bgColor:"bg-gray-100 dark:bg-gray-900/20",textColor:"text-gray-700 dark:text-gray-300",borderColor:"border-gray-300",icon:"\uD83E\uDD48",features:["Semua fitur Bronze","Multi-event management","Staff unlimited","Template premium (terbatas)","Analytics dasar","Priority email support"],limits:{maxEvents:null,maxStaff:null,maxTemplates:15,canUseCustomDomain:!1,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"standard"}},GOLD:{name:"Gold",color:"#FFD700",bgColor:"bg-yellow-100 dark:bg-yellow-900/20",textColor:"text-yellow-700 dark:text-yellow-300",borderColor:"border-yellow-300",icon:"\uD83E\uDD47",features:["Semua fitur Silver","Premium templates unlimited","Boost event khusus","Analytics advanced","Custom branding","Priority chat support","Komisi lebih rendah"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"advanced",customBranding:!0,boostDiscount:20}},TITANIUM:{name:"Titanium",color:"#878681",bgColor:"bg-slate-100 dark:bg-slate-900/20",textColor:"text-slate-700 dark:text-slate-300",borderColor:"border-slate-300",icon:"\uD83D\uDC8E",features:["Semua fitur Gold","Semua fitur premium","Priority support 24/7","Dedicated account manager","Custom integrations","White-label options","Komisi terendah","Early access features"],limits:{maxEvents:null,maxStaff:null,maxTemplates:null,canUseCustomDomain:!0,canUsePremiumTemplates:!0,prioritySupport:!0,analyticsAccess:"enterprise",customBranding:!0,boostDiscount:50,dedicatedSupport:!0,whiteLabel:!0}}},j=[{badge:"BRONZE",name:"Bronze Plan",description:"Perfect untuk organizer pemula",features:v.BRONZE.features,monthlyPrice:0,yearlyPrice:0},{badge:"SILVER",name:"Silver Plan",description:"Ideal untuk organizer yang berkembang",features:v.SILVER.features,monthlyPrice:99e3,yearlyPrice:99e4},{badge:"GOLD",name:"Gold Plan",description:"Untuk organizer profesional",features:v.GOLD.features,monthlyPrice:299e3,yearlyPrice:299e4},{badge:"TITANIUM",name:"Titanium Plan",description:"Enterprise solution untuk organizer besar",features:v.TITANIUM.features,monthlyPrice:999e3,yearlyPrice:999e4}];class N{static getBadgeConfig(e){return v[e]}static getBadgeIcon(e){return v[e].icon}static getBadgeName(e){return v[e].name}static getBadgeColor(e){return v[e].color}static getBadgeClasses(e){let t=v[e];return{bg:t.bgColor,text:t.textColor,border:t.borderColor}}static canAccessFeature(e,t){if(!e)return"BRONZE"===t;let a=["BRONZE","SILVER","GOLD","TITANIUM"];return a.indexOf(e)>=a.indexOf(t)}static getFeatureLimits(e){return e?v[e].limits:v.BRONZE.limits}static calculateSubscriptionPrice(e,t){let a=j.find(t=>t.badge===e);return a?"MONTHLY"===t?a.monthlyPrice:a.yearlyPrice:0}static getSubscriptionDiscount(e){return"YEARLY"===e?16.67:0}static isSubscriptionActive(e,t){let a=new Date;return a>=e&&a<=t}static calculateEndDate(e,t){let a=new Date(e);return"MONTHLY"===t?a.setMonth(a.getMonth()+1):a.setFullYear(a.getFullYear()+1),a}static getDaysUntilExpiry(e){let t=new Date;return Math.ceil((e.getTime()-t.getTime())/864e5)}static shouldShowRenewalReminder(e){let t=this.getDaysUntilExpiry(e);return t<=7&&t>0}static getCommissionRate(e){switch(e){case"BRONZE":default:return 5;case"SILVER":return 4;case"GOLD":return 3;case"TITANIUM":return 2}}static getBoostDiscount(e){return this.getFeatureLimits(e).boostDiscount||0}static canUseTemplate(e,t){return!t||this.canAccessFeature(e,t)}static formatBadgeForDisplay(e){if(!e)return"";let t=v[e];return`${t.icon} ${t.name}`}static getBadgeUpgradePath(e){let t=["BRONZE","SILVER","GOLD","TITANIUM"],a=e?t.indexOf(e):-1;return t.slice(a+1)}static getRecommendedBadge(e){return e>=5e7?"TITANIUM":e>=1e7?"GOLD":e>=2e6?"SILVER":"BRONZE"}}var w=a(91626);function k({badge:e,size:t="md",showIcon:a=!0,showName:s=!0,className:i}){if(!e)return null;let n=N.getBadgeConfig(e),l=N.getBadgeClasses(e);return(0,r.jsxs)(d.C,{variant:"secondary",className:(0,w.cn)(l.bg,l.text,l.border,{sm:"text-xs px-2 py-1",md:"text-sm px-3 py-1",lg:"text-base px-4 py-2"}[t],"font-medium border",i),children:[a&&r.jsx("span",{className:(0,w.cn)("mr-1",{sm:"text-xs",md:"text-sm",lg:"text-base"}[t]),children:n.icon}),s&&n.name]})}function C({badge:e,isActive:t=!1,isRecommended:a=!1,plan:s,onSelect:i,className:n}){let l=N.getBadgeConfig(e);return N.getBadgeClasses(e),(0,r.jsxs)("div",{className:(0,w.cn)("relative p-6 rounded-lg border-2 transition-all duration-200",t?"border-primary bg-primary/5":"border-gray-200 hover:border-gray-300",i&&"cursor-pointer hover:shadow-md",n),onClick:i,children:[a&&r.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:r.jsx(d.C,{className:"bg-primary text-primary-foreground",children:"Recommended"})}),(0,r.jsxs)("div",{className:"text-center mb-4",children:[r.jsx("div",{className:"text-4xl mb-2",children:l.icon}),r.jsx("h3",{className:"text-xl font-bold text-gray-900 dark:text-white",children:l.name}),s?.description&&r.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:s.description})]}),s&&r.jsx("div",{className:"mb-4",children:r.jsx("div",{className:"text-center",children:0===s.monthlyPrice?r.jsx("div",{className:"text-2xl font-bold text-green-600",children:"GRATIS"}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:["Rp ",s.monthlyPrice.toLocaleString("id-ID"),r.jsx("span",{className:"text-sm font-normal text-gray-600",children:"/bulan"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600 dark:text-gray-400",children:["atau Rp ",s.yearlyPrice.toLocaleString("id-ID"),"/tahun",r.jsx("span",{className:"text-green-600 ml-1",children:"(hemat 2 bulan)"})]})]})})}),(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Fitur:"}),r.jsx("ul",{className:"space-y-1",children:l.features.map((e,t)=>(0,r.jsxs)("li",{className:"flex items-start text-sm text-gray-600 dark:text-gray-400",children:[r.jsx("span",{className:"text-green-500 mr-2 mt-0.5",children:"✓"}),e]},t))})]}),t&&r.jsx("div",{className:"mt-4 text-center",children:r.jsx(d.C,{className:"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",children:"Current Plan"})})]})}function D({currentBadge:e,className:t}){let a=["BRONZE","SILVER","GOLD","TITANIUM"];return r.jsx("div",{className:(0,w.cn)("overflow-x-auto",t),children:(0,r.jsxs)("table",{className:"w-full border-collapse",children:[r.jsx("thead",{children:(0,r.jsxs)("tr",{children:[r.jsx("th",{className:"text-left p-4 border-b",children:"Fitur"}),a.map(t=>{let a=N.getBadgeConfig(t),s=e===t;return r.jsx("th",{className:(0,w.cn)("text-center p-4 border-b",s&&"bg-primary/10"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[r.jsx("span",{className:"text-2xl mb-1",children:a.icon}),r.jsx("span",{className:"font-medium",children:a.name}),s&&r.jsx(d.C,{size:"sm",className:"mt-1",children:"Current"})]})},t)})]})}),r.jsx("tbody",{children:["Buat event unlimited","Template tiket gratis","Staff maksimal","Template premium","Analytics","Priority support","Custom branding","Boost discount","Dedicated support"].map((t,s)=>(0,r.jsxs)("tr",{className:"border-b",children:[r.jsx("td",{className:"p-4 font-medium",children:t}),a.map(a=>{let s=N.getFeatureLimits(a),i=e===a,n="✓";return t.includes("Staff maksimal")?n=s.maxStaff?s.maxStaff.toString():"Unlimited":t.includes("Template premium")?n=s.canUsePremiumTemplates?"✓":"✗":t.includes("Analytics")?n=s.analyticsAccess||"Basic":t.includes("Priority support")?n=s.prioritySupport?"✓":"✗":t.includes("Custom branding")?n=s.customBranding?"✓":"✗":t.includes("Boost discount")?n=s.boostDiscount?`${s.boostDiscount}%`:"0%":t.includes("Dedicated support")&&(n=s.dedicatedSupport?"✓":"✗"),r.jsx("td",{className:(0,w.cn)("p-4 text-center",i&&"bg-primary/5","✓"===n?"text-green-600":"✗"===n?"text-red-600":"text-gray-600"),children:n},a)})]},s))})]})})}function R(){let{data:e,status:t}=(0,i.useSession)(),a=(0,n.useRouter)(),{toast:v}=(0,y.pm)(),[j,N]=(0,s.useState)([]),[R,P]=(0,s.useState)(null),[S,T]=(0,s.useState)(!0),[E,B]=(0,s.useState)(!1),[A,L]=(0,s.useState)(null),[I,O]=(0,s.useState)("MONTHLY");(0,s.useEffect)(()=>{if("loading"!==t){if(!e?.user){a.push("/auth/login");return}if("ORGANIZER"!==e.user.role){a.push("/dashboard");return}}},[e,t,a]);let M=async()=>{try{T(!0);let[e,t]=await Promise.all([fetch("/api/badges/plans"),fetch("/api/badges/subscription")]),a=await e.json(),r=await t.json();a.success&&N(a.data),r.success&&r.data&&P(r.data)}catch(e){v({title:"Error",description:"Gagal mengambil data badge",variant:"destructive"})}finally{T(!1)}};(0,s.useEffect)(()=>{e?.user?.role==="ORGANIZER"&&M()},[e]);let Z=async(e,t)=>{B(!0);try{let a=await fetch("/api/badges/subscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({badge:e.badge,type:t,paymentMethod:"UANGTIX"})}),r=await a.json();r.success?(v({title:"Success",description:r.message}),await M(),L(null)):v({title:"Error",description:r.message,variant:"destructive"})}catch(e){v({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}finally{B(!1)}},U=async()=>{if(R)try{let e=await fetch("/api/badges/subscription",{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({autoRenew:!R.autoRenew})}),t=await e.json();t.success?(P(e=>e?{...e,autoRenew:!e.autoRenew}:null),v({title:"Success",description:"Pengaturan auto-renewal berhasil diperbarui"})):v({title:"Error",description:t.message,variant:"destructive"})}catch(e){v({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}},q=async()=>{if(R&&confirm("Apakah Anda yakin ingin membatalkan langganan? Badge akan dikembalikan ke Bronze."))try{let e=await fetch("/api/badges/subscription",{method:"DELETE"}),t=await e.json();t.success?(v({title:"Success",description:t.message}),await M()):v({title:"Error",description:t.message,variant:"destructive"})}catch(e){v({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}};if("loading"===t||S)return r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(x.Z,{className:"h-8 w-8 animate-spin"})});if(!e?.user||"ORGANIZER"!==e.user.role)return null;let z=R?.badge||e.user.badge;return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[r.jsx("div",{className:"p-3 bg-primary/10 rounded-lg",children:r.jsx(p.Z,{className:"h-8 w-8 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Badge & Langganan"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Tingkatkan kemampuan organizer Anda dengan badge premium"})]})]}),z&&r.jsx(k,{badge:z,size:"lg"})]}),R&&(0,r.jsxs)(o.Zb,{className:"mb-8",children:[r.jsx(o.Ol,{children:(0,r.jsxs)(o.ll,{className:"flex items-center gap-2",children:[r.jsx(g.Z,{className:"h-5 w-5"}),"Status Langganan"]})}),(0,r.jsxs)(o.aY,{children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx(m._,{className:"text-sm text-gray-600",children:"Badge Aktif"}),r.jsx("div",{className:"mt-1",children:r.jsx(k,{badge:R.badge,size:"md"})})]}),(0,r.jsxs)("div",{children:[r.jsx(m._,{className:"text-sm text-gray-600",children:"Berakhir"}),(0,r.jsxs)("p",{className:"font-medium mt-1",children:[(0,w.formatDate)(R.endDate),(0,r.jsxs)("span",{className:"text-sm text-gray-600 ml-2",children:["(",R.daysLeft," hari lagi)"]})]})]}),(0,r.jsxs)("div",{children:[r.jsx(m._,{className:"text-sm text-gray-600",children:"Auto Renewal"}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-1",children:[r.jsx(u.r,{checked:R.autoRenew,onCheckedChange:U}),r.jsx("span",{className:"text-sm",children:R.autoRenew?"Aktif":"Nonaktif"})]})]})]}),R.shouldShowReminder&&r.jsx("div",{className:"mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 text-yellow-800 dark:text-yellow-200",children:[r.jsx(f,{className:"h-4 w-4"}),(0,r.jsxs)("span",{className:"text-sm",children:["Langganan Anda akan berakhir dalam ",R.daysLeft," hari. Perpanjang sekarang untuk menghindari downgrade."]})]})}),(0,r.jsxs)("div",{className:"flex gap-2 mt-4",children:[(0,r.jsxs)(l.z,{variant:"outline",size:"sm",onClick:()=>a.push("/badges/upgrade"),children:[r.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Upgrade Badge"]}),(0,r.jsxs)(l.z,{variant:"destructive",size:"sm",onClick:q,children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Batalkan Langganan"]})]})]})]}),(0,r.jsxs)(c.mQ,{defaultValue:"plans",className:"space-y-6",children:[(0,r.jsxs)(c.dr,{className:"grid w-full grid-cols-2",children:[r.jsx(c.SP,{value:"plans",children:"Paket Badge"}),r.jsx(c.SP,{value:"comparison",children:"Perbandingan Fitur"})]}),(0,r.jsxs)(c.nU,{value:"plans",className:"space-y-6",children:[r.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:j.map(e=>{let t=R?.badge===e.badge,a="GOLD"===e.badge;return r.jsx(C,{badge:e.badge,isActive:t,isRecommended:a,plan:e,onSelect:()=>L(e)},e.id)})}),A&&(0,r.jsxs)(o.Zb,{className:"border-primary",children:[(0,r.jsxs)(o.Ol,{children:[(0,r.jsxs)(o.ll,{className:"flex items-center gap-2",children:[r.jsx(p.Z,{className:"h-5 w-5"}),"Berlangganan ",A.name]}),r.jsx(o.SZ,{children:"Pilih periode langganan yang sesuai dengan kebutuhan Anda"})]}),(0,r.jsxs)(o.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[r.jsx(o.Zb,{className:`cursor-pointer transition-all ${"MONTHLY"===I?"border-primary bg-primary/5":"hover:border-gray-300"}`,onClick:()=>O("MONTHLY"),children:r.jsx(o.aY,{className:"pt-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:"Bulanan"}),r.jsx("p",{className:"text-2xl font-bold",children:(0,w.formatCurrency)(A.monthlyPrice)}),r.jsx("p",{className:"text-sm text-gray-600",children:"per bulan"})]}),r.jsx("div",{className:`w-4 h-4 rounded-full border-2 ${"MONTHLY"===I?"border-primary bg-primary":"border-gray-300"}`})]})})}),r.jsx(o.Zb,{className:`cursor-pointer transition-all ${"YEARLY"===I?"border-primary bg-primary/5":"hover:border-gray-300"}`,onClick:()=>O("YEARLY"),children:r.jsx(o.aY,{className:"pt-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium",children:"Tahunan"}),r.jsx("p",{className:"text-2xl font-bold",children:(0,w.formatCurrency)(A.yearlyPrice)}),r.jsx("p",{className:"text-sm text-gray-600",children:"per tahun"}),r.jsx(d.C,{className:"mt-1 bg-green-100 text-green-800",children:"Hemat 2 bulan"})]}),r.jsx("div",{className:`w-4 h-4 rounded-full border-2 ${"YEARLY"===I?"border-primary bg-primary":"border-gray-300"}`})]})})})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[r.jsx(l.z,{onClick:()=>Z(A,I),disabled:E,className:"flex-1",children:E?(0,r.jsxs)(r.Fragment,{children:[r.jsx(x.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Memproses..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Berlangganan Sekarang"]})}),r.jsx(l.z,{variant:"outline",onClick:()=>L(null),children:"Batal"})]})]})]})]}),r.jsx(c.nU,{value:"comparison",children:(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{children:[r.jsx(o.ll,{children:"Perbandingan Fitur Badge"}),r.jsx(o.SZ,{children:"Lihat perbedaan fitur antara setiap level badge"})]}),r.jsx(o.aY,{children:r.jsx(D,{currentBadge:z})})]})})]})]})}},69436:(e,t,a)=>{"use strict";a.d(t,{C:()=>l});var r=a(95344);a(3729);var s=a(92193),i=a(91626);let n=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...a}){return r.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...a})}},61351:(e,t,a)=>{"use strict";a.d(t,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>c,ll:()=>o});var r=a(95344),s=a(3729),i=a(91626);let n=s.forwardRef(({className:e,elevated:t=!1,padding:a="md",...s},n)=>r.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",t&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===a,"p-3":"sm"===a,"p-6":"md"===a,"p-8":"lg"===a},e),...s}));n.displayName="Card";let l=s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=s.forwardRef(({className:e,...t},a)=>r.jsx("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let d=s.forwardRef(({className:e,...t},a)=>r.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",s.forwardRef(({className:e,...t},a)=>r.jsx("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},54572:(e,t,a)=>{"use strict";a.d(t,{_:()=>c});var r=a(95344),s=a(3729),i=a(62409),n=s.forwardRef((e,t)=>(0,r.jsx)(i.WV.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var l=a(92193),o=a(91626);let d=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef(({className:e,...t},a)=>r.jsx(n,{ref:a,className:(0,o.cn)(d(),e),...t}));c.displayName=n.displayName},71809:(e,t,a)=>{"use strict";a.d(t,{r:()=>w});var r=a(95344),s=a(3729),i=a(85222),n=a(31405),l=a(98462),o=a(33183),d=a(92062),c=a(63085),u=a(62409),m="Switch",[x,p]=(0,l.b)(m),[g,f]=x(m),h=s.forwardRef((e,t)=>{let{__scopeSwitch:a,name:l,checked:d,defaultChecked:c,required:x,disabled:p,value:f="on",onCheckedChange:h,form:b,...y}=e,[N,w]=s.useState(null),k=(0,n.e)(t,e=>w(e)),C=s.useRef(!1),D=!N||b||!!N.closest("form"),[R,P]=(0,o.T)({prop:d,defaultProp:c??!1,onChange:h,caller:m});return(0,r.jsxs)(g,{scope:a,checked:R,disabled:p,children:[(0,r.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":R,"aria-required":x,"data-state":j(R),"data-disabled":p?"":void 0,disabled:p,value:f,...y,ref:k,onClick:(0,i.M)(e.onClick,e=>{P(e=>!e),D&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),D&&(0,r.jsx)(v,{control:N,bubbles:!C.current,name:l,value:f,checked:R,required:x,disabled:p,form:b,style:{transform:"translateX(-100%)"}})]})});h.displayName=m;var b="SwitchThumb",y=s.forwardRef((e,t)=>{let{__scopeSwitch:a,...s}=e,i=f(b,a);return(0,r.jsx)(u.WV.span,{"data-state":j(i.checked),"data-disabled":i.disabled?"":void 0,...s,ref:t})});y.displayName=b;var v=s.forwardRef(({__scopeSwitch:e,control:t,checked:a,bubbles:i=!0,...l},o)=>{let u=s.useRef(null),m=(0,n.e)(u,o),x=(0,d.D)(a),p=(0,c.t)(t);return s.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==a&&t){let r=new Event("click",{bubbles:i});t.call(e,a),e.dispatchEvent(r)}},[x,a,i]),(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:a,...l,tabIndex:-1,ref:m,style:{...l.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function j(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var N=a(91626);let w=s.forwardRef(({className:e,...t},a)=>r.jsx(h,{className:(0,N.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...t,ref:a,children:r.jsx(y,{className:(0,N.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));w.displayName=h.displayName},25757:(e,t,a)=>{"use strict";a.d(t,{mQ:()=>S,nU:()=>B,dr:()=>T,SP:()=>E});var r=a(95344),s=a(3729),i=a(85222),n=a(98462),l=a(34504),o=a(43234),d=a(62409),c=a(3975),u=a(33183),m=a(99048),x="Tabs",[p,g]=(0,n.b)(x,[l.Pc]),f=(0,l.Pc)(),[h,b]=p(x),y=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,onValueChange:i,defaultValue:n,orientation:l="horizontal",dir:o,activationMode:p="automatic",...g}=e,f=(0,c.gm)(o),[b,y]=(0,u.T)({prop:s,onChange:i,defaultProp:n??"",caller:x});return(0,r.jsx)(h,{scope:a,baseId:(0,m.M)(),value:b,onValueChange:y,orientation:l,dir:f,activationMode:p,children:(0,r.jsx)(d.WV.div,{dir:f,"data-orientation":l,...g,ref:t})})});y.displayName=x;var v="TabsList",j=s.forwardRef((e,t)=>{let{__scopeTabs:a,loop:s=!0,...i}=e,n=b(v,a),o=f(a);return(0,r.jsx)(l.fC,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:s,children:(0,r.jsx)(d.WV.div,{role:"tablist","aria-orientation":n.orientation,...i,ref:t})})});j.displayName=v;var N="TabsTrigger",w=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,disabled:n=!1,...o}=e,c=b(N,a),u=f(a),m=D(c.baseId,s),x=R(c.baseId,s),p=s===c.value;return(0,r.jsx)(l.ck,{asChild:!0,...u,focusable:!n,active:p,children:(0,r.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:m,...o,ref:t,onMouseDown:(0,i.M)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(s)}),onKeyDown:(0,i.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(s)}),onFocus:(0,i.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||n||!e||c.onValueChange(s)})})})});w.displayName=N;var k="TabsContent",C=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:i,forceMount:n,children:l,...c}=e,u=b(k,a),m=D(u.baseId,i),x=R(u.baseId,i),p=i===u.value,g=s.useRef(p);return s.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,r.jsx)(o.z,{present:n||p,children:({present:a})=>(0,r.jsx)(d.WV.div,{"data-state":p?"active":"inactive","data-orientation":u.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:x,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:a&&l})})});function D(e,t){return`${e}-trigger-${t}`}function R(e,t){return`${e}-content-${t}`}C.displayName=k;var P=a(91626);let S=y,T=s.forwardRef(({className:e,...t},a)=>r.jsx(j,{ref:a,className:(0,P.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));T.displayName=j.displayName;let E=s.forwardRef(({className:e,...t},a)=>r.jsx(w,{ref:a,className:(0,P.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));E.displayName=w.displayName;let B=s.forwardRef(({className:e,...t},a)=>r.jsx(C,{ref:a,className:(0,P.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));B.displayName=C.displayName},85674:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1750:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Crown",[["path",{d:"m2 4 3 12h14l3-12-6 7-4-7-4 7-6-7zm3 16h14",key:"zkxr6b"}]])},57149:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>s,default:()=>n});let r=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\badges\page.tsx`),{__esModule:s,$$typeof:i}=r,n=r.default},92062:(e,t,a)=>{"use strict";a.d(t,{D:()=>s});var r=a(3729);function s(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,3088,9205],()=>a(6738));module.exports=r})();