(()=>{var e={};e.id=5857,e.ids=[5857],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},39295:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>o,pages:()=>x,routeModule:()=>p,tree:()=>d});var t=a(50482),r=a(69108),i=a(62563),l=a.n(i),n=a(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["events",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,60937)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\events\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],x=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\events\\[id]\\page.tsx"],o="/events/[id]/page",m={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/events/[id]/page",pathname:"/events/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8241:(e,s,a)=>{Promise.resolve().then(a.bind(a,54615))},54615:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>Z});var t=a(95344),r=a(3729),i=a(47674),l=a(8428),n=a(89410),c=a(16212),d=a(61351),x=a(69436),o=a(5062),m=a(42739),p=a(66138),u=a(36341),h=a(55794),g=a(25545),j=a(80508),v=a(89895),f=a(18822),y=a(7060);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let N=(0,a(69224).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var b=a(89151),k=a(30692),w=a(91626);function Z(){let{data:e}=(0,i.useSession)(),s=(0,l.useParams)(),a=(0,l.useRouter)(),{toast:Z}=(0,k.pm)(),[T,D]=(0,r.useState)(null),[q,C]=(0,r.useState)(!0),[_,P]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=async()=>{try{let e=await fetch(`/api/events/${s.id}`),t=await e.json();t.success?D(t.data):(Z({title:"Error",description:t.message||"Event tidak ditemukan",variant:"destructive"}),a.push("/events"))}catch(e){Z({title:"Error",description:"Terjadi kesalahan saat mengambil data event",variant:"destructive"}),a.push("/events")}finally{C(!1)}};s.id&&e()},[s.id,a,Z]);let A=async()=>{if(!e){a.push("/auth/login");return}P(!0);try{Z({title:"Fitur Segera Hadir",description:"Pembelian tiket akan segera tersedia",variant:"default"})}catch(e){Z({title:"Error",description:"Terjadi kesalahan saat membeli tiket",variant:"destructive"})}finally{P(!1)}};if(q)return t.jsx("div",{className:"flex items-center justify-center min-h-screen",children:t.jsx(m.Z,{className:"h-8 w-8 animate-spin"})});if(!T)return(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4 text-center",children:[t.jsx(p.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Event Tidak Ditemukan"}),t.jsx("p",{className:"text-gray-600 mb-4",children:"Event yang Anda cari tidak tersedia"}),t.jsx(c.z,{onClick:()=>a.push("/events"),children:"Kembali ke Daftar Event"})]});let E=new Date(T.endDate)<new Date;T.startDate;let z=T.availableTickets<=0,S=!E&&!z&&T.isActive;return t.jsx("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[T.image&&t.jsx("div",{className:"relative aspect-video rounded-lg overflow-hidden",children:t.jsx(n.default,{src:T.image,alt:T.title,fill:!0,className:"object-cover"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,t.jsxs)(x.C,{variant:"secondary",style:{backgroundColor:T.category.color+"20",color:T.category.color},children:[t.jsx(u.Z,{className:"h-3 w-3 mr-1"}),T.category.name]}),!T.isActive&&t.jsx(x.C,{variant:"secondary",children:"Nonaktif"}),E&&t.jsx(x.C,{variant:"secondary",children:"Berakhir"}),z&&t.jsx(x.C,{variant:"destructive",children:"Sold Out"})]}),t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:T.title}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(h.Z,{className:"h-4 w-4"}),t.jsx("span",{children:(0,w.formatDate)(T.startDate)})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[t.jsx(g.Z,{className:"h-4 w-4"}),t.jsx("span",{children:(0,w.formatRelativeTime)(T.startDate)})]})]})]}),(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:t.jsx(d.ll,{children:"Tentang Event"})}),t.jsx(d.aY,{children:t.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:T.description})})]}),(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:t.jsx(d.ll,{children:"Detail Event"})}),t.jsx(d.aY,{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx(h.Z,{className:"h-5 w-5 text-primary-500"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:"Tanggal Mulai"}),t.jsx("p",{className:"text-sm text-gray-600",children:(0,w.formatDate)(T.startDate)})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx(h.Z,{className:"h-5 w-5 text-primary-500"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:"Tanggal Selesai"}),t.jsx("p",{className:"text-sm text-gray-600",children:(0,w.formatDate)(T.endDate)})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx(j.Z,{className:"h-5 w-5 text-primary-500"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:"Lokasi"}),t.jsx("p",{className:"text-sm text-gray-600",children:T.location})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[t.jsx(v.Z,{className:"h-5 w-5 text-primary-500"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"font-medium",children:"Kapasitas"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[T.soldTickets," / ",T.maxTickets," tiket terjual"]})]})]})]})})]}),(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:t.jsx(d.ll,{children:"Organizer"})}),t.jsx(d.aY,{children:(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[t.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center",children:t.jsx(f.Z,{className:"h-6 w-6 text-primary-600"})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[t.jsx("h3",{className:"font-medium",children:T.organizer.name}),T.organizer.isVerified&&t.jsx(y.Z,{className:"h-4 w-4 text-green-500"}),T.organizer.badge&&t.jsx(x.C,{variant:"secondary",children:T.organizer.badge})]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[T.organizer._count.events," event dibuat"]})]})]})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(d.Zb,{children:[(0,t.jsxs)(d.Ol,{children:[t.jsx(d.ll,{className:"text-2xl",children:0===T.price?"Gratis":(0,w.formatCurrency)(T.price)}),(0,t.jsxs)(d.SZ,{children:[T.availableTickets," tiket tersisa"]})]}),(0,t.jsxs)(d.aY,{className:"space-y-4",children:[S?(0,t.jsxs)(c.z,{className:"w-full",size:"lg",onClick:A,disabled:_,children:[_&&t.jsx(m.Z,{className:"mr-2 h-4 w-4 animate-spin"}),0===T.price?"Daftar Gratis":"Beli Tiket"]}):(0,t.jsxs)("div",{className:"space-y-2",children:[E&&(0,t.jsxs)(o.bZ,{children:[t.jsx(p.Z,{className:"h-4 w-4"}),t.jsx(o.X,{children:"Event ini telah berakhir"})]}),z&&!E&&(0,t.jsxs)(o.bZ,{children:[t.jsx(p.Z,{className:"h-4 w-4"}),t.jsx(o.X,{children:"Tiket sudah habis terjual"})]}),!T.isActive&&(0,t.jsxs)(o.bZ,{children:[t.jsx(p.Z,{className:"h-4 w-4"}),t.jsx(o.X,{children:"Event ini sedang tidak aktif"})]})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(c.z,{variant:"outline",size:"sm",className:"flex-1",children:[t.jsx(N,{className:"h-4 w-4 mr-2"}),"Simpan"]}),(0,t.jsxs)(c.z,{variant:"outline",size:"sm",className:"flex-1",children:[t.jsx(b.Z,{className:"h-4 w-4 mr-2"}),"Bagikan"]})]})]})]}),(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:t.jsx(d.ll,{children:"Statistik Event"})}),(0,t.jsxs)(d.aY,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Tiket Terjual"}),t.jsx("span",{className:"font-medium",children:T.soldTickets})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Tiket Tersisa"}),t.jsx("span",{className:"font-medium",children:T.availableTickets})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-sm text-gray-600",children:"Total Kapasitas"}),t.jsx("span",{className:"font-medium",children:T.maxTickets})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[t.jsx("span",{children:"Progress Penjualan"}),(0,t.jsxs)("span",{children:[(T.soldTickets/T.maxTickets*100).toFixed(1),"%"]})]}),t.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:t.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${T.soldTickets/T.maxTickets*100}%`}})})]})]})]}),(0,t.jsxs)(d.Zb,{children:[t.jsx(d.Ol,{children:t.jsx(d.ll,{children:"Info Cepat"})}),(0,t.jsxs)(d.aY,{className:"space-y-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Kategori"}),t.jsx("span",{className:"font-medium",children:T.category.name})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Dibuat"}),t.jsx("span",{className:"font-medium",children:(0,w.formatRelativeTime)(T.createdAt)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Status"}),t.jsx(x.C,{variant:T.isActive?"success":"secondary",children:T.isActive?"Aktif":"Nonaktif"})]})]})]})]})]})})}},5062:(e,s,a)=>{"use strict";a.d(s,{X:()=>d,bZ:()=>c});var t=a(95344),r=a(3729),i=a(92193),l=a(91626);let n=(0,i.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive",warning:"border-yellow-500/50 text-yellow-700 bg-yellow-50 dark:border-yellow-500 dark:text-yellow-400 dark:bg-yellow-950/20 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400",success:"border-green-500/50 text-green-700 bg-green-50 dark:border-green-500 dark:text-green-400 dark:bg-green-950/20 [&>svg]:text-green-600 dark:[&>svg]:text-green-400"}},defaultVariants:{variant:"default"}}),c=r.forwardRef(({className:e,variant:s,...a},r)=>t.jsx("div",{ref:r,role:"alert",className:(0,l.cn)(n({variant:s}),e),...a}));c.displayName="Alert",r.forwardRef(({className:e,...s},a)=>t.jsx("h5",{ref:a,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...s}));d.displayName="AlertDescription"},69436:(e,s,a)=>{"use strict";a.d(s,{C:()=>n});var t=a(95344);a(3729);var r=a(92193),i=a(91626);let l=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...a}){return t.jsx("div",{className:(0,i.cn)(l({variant:s}),e),...a})}},61351:(e,s,a)=>{"use strict";a.d(s,{Ol:()=>n,SZ:()=>d,Zb:()=>l,aY:()=>x,ll:()=>c});var t=a(95344),r=a(3729),i=a(91626);let l=r.forwardRef(({className:e,elevated:s=!1,padding:a="md",...r},l)=>t.jsx("div",{ref:l,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",s&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===a,"p-3":"sm"===a,"p-6":"md"===a,"p-8":"lg"===a},e),...r}));l.displayName="Card";let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=r.forwardRef(({className:e,...s},a)=>t.jsx("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let x=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...s}));x.displayName="CardContent",r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},7060:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},89151:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},36341:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},89895:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},60937:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let t=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\events\[id]\page.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,3088,3396,9205],()=>a(39295));module.exports=t})();