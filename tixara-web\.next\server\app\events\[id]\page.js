(()=>{var e={};e.id=5857,e.ids=[5857],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},39295:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>l,routeModule:()=>h,tree:()=>d});var r=n(50482),a=n(69108),s=n(62563),o=n.n(s),i=n(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);n.d(t,c);let d=["",{children:["events",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,60937)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\events\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\events\\[id]\\page.tsx"],m="/events/[id]/page",u={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/events/[id]/page",pathname:"/events/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},8241:(e,t,n)=>{Promise.resolve().then(n.bind(n,54615))},16509:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,26840,23)),Promise.resolve().then(n.t.bind(n,38771,23)),Promise.resolve().then(n.t.bind(n,13225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,43982,23))},23978:()=>{},54615:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>j});var r=n(95344),a=n(3729),s=n(47674),o=n(8428),i=n(89410),c=n(42739),d=n(66138),l=n(36341),m=n(55794),u=n(25545),h=n(80508),x=n(89895),O=n(18822),f=n(7060);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,n(69224).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]);var N=n(89151);function j(){let{data:e}=(0,s.useSession)(),t=(0,o.useParams)(),n=(0,o.useRouter)(),{toast:j}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[v,D]=(0,a.useState)(null),[_,E]=(0,a.useState)(!0),[g,U]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=async()=>{try{let e=await fetch(`/api/events/${t.id}`),r=await e.json();r.success?D(r.data):(j({title:"Error",description:r.message||"Event tidak ditemukan",variant:"destructive"}),n.push("/events"))}catch(e){j({title:"Error",description:"Terjadi kesalahan saat mengambil data event",variant:"destructive"}),n.push("/events")}finally{E(!1)}};t.id&&e()},[t.id,n,j]);let b=async()=>{if(!e){n.push("/auth/login");return}U(!0);try{j({title:"Fitur Segera Hadir",description:"Pembelian tiket akan segera tersedia",variant:"default"})}catch(e){j({title:"Error",description:"Terjadi kesalahan saat membeli tiket",variant:"destructive"})}finally{U(!1)}};if(_)return r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(c.Z,{className:"h-8 w-8 animate-spin"})});if(!v)return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 text-center",children:[r.jsx(d.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("h1",{className:"text-2xl font-bold mb-2",children:"Event Tidak Ditemukan"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Event yang Anda cari tidak tersedia"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>n.push("/events"),children:"Kembali ke Daftar Event"})]});let w=new Date(v.endDate)<new Date;v.startDate;let y=v.availableTickets<=0,T=!w&&!y&&v.isActive;return r.jsx("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[v.image&&r.jsx("div",{className:"relative aspect-video rounded-lg overflow-hidden",children:r.jsx(i.default,{src:v.image,alt:v.title,fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",style:{backgroundColor:v.category.color+"20",color:v.category.color},children:[r.jsx(l.Z,{className:"h-3 w-3 mr-1"}),v.category.name]}),!v.isActive&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",children:"Nonaktif"}),w&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",children:"Berakhir"}),y&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"destructive",children:"Sold Out"})]}),r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white mb-4",children:v.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(m.Z,{className:"h-4 w-4"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(v.startDate)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(u.Z,{className:"h-4 w-4"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(v.startDate)})]})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Tentang Event"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx("p",{className:"text-gray-700 dark:text-gray-300 whitespace-pre-wrap",children:v.description})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Detail Event"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(m.Z,{className:"h-5 w-5 text-primary-500"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Tanggal Mulai"}),r.jsx("p",{className:"text-sm text-gray-600",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(v.startDate)})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(m.Z,{className:"h-5 w-5 text-primary-500"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Tanggal Selesai"}),r.jsx("p",{className:"text-sm text-gray-600",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(v.endDate)})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(h.Z,{className:"h-5 w-5 text-primary-500"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Lokasi"}),r.jsx("p",{className:"text-sm text-gray-600",children:v.location})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx(x.Z,{className:"h-5 w-5 text-primary-500"}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"font-medium",children:"Kapasitas"}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[v.soldTickets," / ",v.maxTickets," tiket terjual"]})]})]})]})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Organizer"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[r.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center",children:r.jsx(O.Z,{className:"h-6 w-6 text-primary-600"})}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx("h3",{className:"font-medium",children:v.organizer.name}),v.organizer.isVerified&&r.jsx(f.Z,{className:"h-4 w-4 text-green-500"}),v.organizer.badge&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",children:v.organizer.badge})]}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:[v.organizer._count.events," event dibuat"]})]})]})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-2xl",children:0===v.price?"Gratis":Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(v.price)}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[v.availableTickets," tiket tersisa"]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[T?(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full",size:"lg",onClick:b,disabled:g,children:[g&&r.jsx(c.Z,{className:"mr-2 h-4 w-4 animate-spin"}),0===v.price?"Daftar Gratis":"Beli Tiket"]}):(0,r.jsxs)("div",{className:"space-y-2",children:[w&&(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Event ini telah berakhir"})]}),y&&!w&&(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Tiket sudah habis terjual"})]}),!v.isActive&&(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/alert'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(d.Z,{className:"h-4 w-4"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/alert'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Event ini sedang tidak aktif"})]})]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",className:"flex-1",children:[r.jsx(p,{className:"h-4 w-4 mr-2"}),"Simpan"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",className:"flex-1",children:[r.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Bagikan"]})]})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Statistik Event"})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"Tiket Terjual"}),r.jsx("span",{className:"font-medium",children:v.soldTickets})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"Tiket Tersisa"}),r.jsx("span",{className:"font-medium",children:v.availableTickets})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-sm text-gray-600",children:"Total Kapasitas"}),r.jsx("span",{className:"font-medium",children:v.maxTickets})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"Progress Penjualan"}),(0,r.jsxs)("span",{children:[(v.soldTickets/v.maxTickets*100).toFixed(1),"%"]})]}),r.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:"bg-primary-500 h-2 rounded-full transition-all duration-300",style:{width:`${v.soldTickets/v.maxTickets*100}%`}})})]})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Info Cepat"})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-3 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Kategori"}),r.jsx("span",{className:"font-medium",children:v.category.name})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Dibuat"}),r.jsx("span",{className:"font-medium",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(v.createdAt)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Status"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:v.isActive?"success":"secondary",children:v.isActive?"Aktif":"Nonaktif"})]})]})]})]})]})})}(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/alert'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},7060:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},89151:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},36341:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},18822:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},89895:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},66150:(e,t,n)=>{"use strict";e.exports=n(16372).vendored.contexts.RouterContext},60937:(e,t,n)=>{"use strict";n.r(t),n.d(t,{$$typeof:()=>s,__esModule:()=>a,default:()=>o});let r=(0,n(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\events\[id]\page.tsx`),{__esModule:a,$$typeof:s}=r,o=r.default},82917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d,metadata:()=>c});var r=n(25036),a=n(450),s=n.n(a),o=n(14824),i=n.n(o);n(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${s().variable} ${i().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},67272:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1638,3293,5504,2972],()=>n(39295));module.exports=r})();