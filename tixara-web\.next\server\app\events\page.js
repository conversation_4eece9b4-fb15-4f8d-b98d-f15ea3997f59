(()=>{var e={};e.id=38,e.ids=[38],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},32646:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>d});var n=r(50482),a=r(69108),o=r(62563),i=r.n(o),s=r(68300),c={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);r.d(t,c);let d=["",{children:["events",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85325)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\events\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\events\\page.tsx"],u="/events/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/events/page",pathname:"/events",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},97672:(e,t,r)=>{Promise.resolve().then(r.bind(r,60638))},16509:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},23978:()=>{},60638:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v});var n=r(95344),a=r(3729),o=r(8428),i=r(56506),s=r(89410),c=r(42739),d=r(69224);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let l=(0,d.Z)("Grid3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),u=(0,d.Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]]),m=(0,d.Z)("SlidersHorizontal",[["line",{x1:"21",x2:"14",y1:"4",y2:"4",key:"obuewd"}],["line",{x1:"10",x2:"3",y1:"4",y2:"4",key:"1q6298"}],["line",{x1:"21",x2:"12",y1:"12",y2:"12",key:"1iu8h1"}],["line",{x1:"8",x2:"3",y1:"12",y2:"12",key:"ntss68"}],["line",{x1:"21",x2:"16",y1:"20",y2:"20",key:"14d8ph"}],["line",{x1:"12",x2:"3",y1:"20",y2:"20",key:"m0wm8r"}],["line",{x1:"14",x2:"14",y1:"2",y2:"6",key:"14e1ph"}],["line",{x1:"8",x2:"8",y1:"10",y2:"14",key:"1i6ji0"}],["line",{x1:"16",x2:"16",y1:"18",y2:"22",key:"1lctlv"}]]);var h=r(28765),p=r(66138),x=r(55794),O=r(80508),f=r(89895);function v(){let e=(0,o.useSearchParams)(),t=(0,o.useRouter)(),{toast:r}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[d,v]=(0,a.useState)([]),[N,j]=(0,a.useState)([]),[g,y]=(0,a.useState)(!0),[_,D]=(0,a.useState)("grid"),[E,b]=(0,a.useState)({search:e.get("search")||"",category:e.get("category")||"",priceMin:e.get("priceMin")||"",priceMax:e.get("priceMax")||"",sortBy:e.get("sortBy")||"startDate"}),U=async()=>{try{let e=new URLSearchParams;E.search&&e.append("search",E.search),E.category&&e.append("categoryId",E.category),E.priceMin&&e.append("priceMin",E.priceMin),E.priceMax&&e.append("priceMax",E.priceMax),E.sortBy&&e.append("sortBy",E.sortBy),e.append("active","true"),e.append("limit","50");let t=await fetch(`/api/events?${e}`),n=await t.json();n.success?v(n.data):r({title:"Error",description:n.message||"Gagal mengambil data event",variant:"destructive"})}catch(e){r({title:"Error",description:"Terjadi kesalahan saat mengambil data",variant:"destructive"})}finally{y(!1)}},w=async()=>{try{let e=await fetch("/api/categories?active=true"),t=await e.json();t.success&&j(t.data)}catch(e){console.error("Error fetching categories:",e)}};(0,a.useEffect)(()=>{U(),w()},[E]),(0,a.useEffect)(()=>{let e=new URLSearchParams;Object.entries(E).forEach(([t,r])=>{r&&e.append(t,r)});let r=e.toString()?`?${e.toString()}`:"/events";t.replace(r,{scroll:!1})},[E,t]);let M=(e,t)=>{b(r=>({...r,[e]:t}))},k=()=>{b({search:"",category:"",priceMin:"",priceMax:"",sortBy:"startDate"})},T=e=>e._count.tickets>=e.maxTickets,C=e=>new Date(e.endDate)<new Date;return g?n.jsx("div",{className:"flex items-center justify-center min-h-screen",children:n.jsx(c.Z,{className:"h-8 w-8 animate-spin"})}):(0,n.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Jelajahi Event"}),n.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Temukan event menarik di sekitar Anda"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"grid"===_?"default":"outline",size:"sm",onClick:()=>D("grid"),children:n.jsx(l,{className:"h-4 w-4"})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"list"===_?"default":"outline",size:"sm",onClick:()=>D("list"),children:n.jsx(u,{className:"h-4 w-4"})})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mb-8",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center gap-2",children:[n.jsx(m,{className:"h-5 w-5"}),"Filter & Pencarian"]})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4",children:[n.jsx("div",{className:"lg:col-span-2",children:(0,n.jsxs)("div",{className:"relative",children:[n.jsx(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Cari event...",value:E.search,onChange:e=>M("search",e.target.value),className:"pl-10"})]})}),n.jsx("div",{children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:E.category,onValueChange:e=>M("category",e),children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Semua Kategori"})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"",children:"Semua Kategori"}),N.map(e=>n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.id,children:e.name},e.id))]})]})}),(0,n.jsxs)("div",{className:"flex gap-2",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"number",placeholder:"Harga min",value:E.priceMin,onChange:e=>M("priceMin",e.target.value)}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"number",placeholder:"Harga max",value:E.priceMax,onChange:e=>M("priceMax",e.target.value)})]}),n.jsx("div",{children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:E.sortBy,onValueChange:e=>M("sortBy",e),children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{})}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"startDate",children:"Tanggal Terdekat"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"price",children:"Harga Terendah"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"title",children:"Nama A-Z"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"createdAt",children:"Terbaru"})]})]})})]}),(E.search||E.category||E.priceMin||E.priceMax)&&(0,n.jsxs)("div",{className:"flex items-center gap-2 mt-4 pt-4 border-t",children:[n.jsx("span",{className:"text-sm text-gray-600",children:"Filter aktif:"}),E.search&&(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",children:["Pencarian: ",E.search]}),E.category&&(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",children:["Kategori: ",N.find(e=>e.id===E.category)?.name]}),(E.priceMin||E.priceMax)&&(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",children:["Harga: ",E.priceMin||"0"," - ",E.priceMax||"∞"]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"sm",onClick:k,className:"text-red-600 hover:text-red-700",children:"Hapus Semua"})]})]})]}),n.jsx("div",{className:"mb-4",children:(0,n.jsxs)("p",{className:"text-gray-600",children:["Menampilkan ",d.length," event"]})}),0===d.length?(0,n.jsxs)("div",{className:"text-center py-12",children:[n.jsx(p.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),n.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Tidak ada event ditemukan"}),n.jsx("p",{className:"text-gray-600 mb-4",children:"Coba ubah filter pencarian atau jelajahi kategori lain"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:k,children:"Hapus Filter"})]}):n.jsx("div",{className:"grid"===_?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6":"space-y-4",children:d.map(e=>(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:`overflow-hidden hover:shadow-lg transition-shadow ${"list"===_?"flex":""}`,children:[e.image&&(0,n.jsxs)("div",{className:`relative ${"list"===_?"w-48 h-32":"aspect-video"}`,children:[n.jsx(s.default,{src:e.image,alt:e.title,fill:!0,className:"object-cover"}),(T(e)||C(e))&&n.jsx("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"destructive",children:C(e)?"Berakhir":"Sold Out"})})]}),(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",style:{backgroundColor:e.category.color+"20",color:e.category.color},children:e.category.name}),e.organizer.isVerified&&n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:"Verified"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"line-clamp-2",children:n.jsx(i.default,{href:`/events/${e.id}`,className:"hover:text-primary-600 transition-colors",children:e.title})}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"line-clamp-2",children:e.description})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-0",children:[(0,n.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(x.Z,{className:"h-4 w-4"}),n.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.startDate)})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(O.Z,{className:"h-4 w-4"}),n.jsx("span",{className:"line-clamp-1",children:e.location})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[n.jsx(f.Z,{className:"h-4 w-4"}),(0,n.jsxs)("span",{children:[e._count.tickets," / ",e.maxTickets," tiket terjual"]})]})]}),(0,n.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[n.jsx("div",{className:"text-lg font-bold text-primary-600",children:0===e.price?"Gratis":Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.price)}),n.jsx(i.default,{href:`/events/${e.id}`,children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{size:"sm",disabled:T(e)||C(e),children:C(e)?"Berakhir":T(e)?"Sold Out":"Lihat Detail"})})]})]})]})]},e.id))})]})}(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},69224:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var n=r(3729),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,t)=>{let r=(0,n.forwardRef)(({color:r="currentColor",size:i=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:d="",children:l,...u},m)=>(0,n.createElement)("svg",{ref:m,...a,width:i,height:i,stroke:r,strokeWidth:c?24*Number(s)/Number(i):s,className:["lucide",`lucide-${o(e)}`,d].join(" "),...u},[...t.map(([e,t])=>(0,n.createElement)(e,t)),...Array.isArray(l)?l:[l]]));return r.displayName=`${e}`,r}},42739:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},28765:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},89895:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},8428:(e,t,r)=>{"use strict";var n=r(14767);r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}}),r.o(n,"useSearchParams")&&r.d(t,{useSearchParams:function(){return n.useSearchParams}})},85325:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>i});let n=(0,r(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\events\page.tsx`),{__esModule:a,$$typeof:o}=n,i=n.default},82917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var n=r(25036),a=r(450),o=r.n(a),i=r(14824),s=r.n(i);r(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return n.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:n.jsx("body",{className:`${o().variable} ${s().variable} font-sans antialiased`,children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),n.jsx("main",{className:"flex-1",children:e}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[1638,3293,6506,2972],()=>r(32646));module.exports=n})();