(()=>{var e={};e.id=5239,e.ids=[5239],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},95069:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>l,routeModule:()=>h,tree:()=>c});var r=n(50482),a=n(69108),i=n(62563),o=n.n(i),s=n(68300),d={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);n.d(t,d);let c=["",{children:["my-tickets",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,53028)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\[id]\\page.tsx"],m="/my-tickets/[id]/page",u={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/my-tickets/[id]/page",pathname:"/my-tickets/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6894:(e,t,n)=>{Promise.resolve().then(n.bind(n,77948))},16509:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,26840,23)),Promise.resolve().then(n.t.bind(n,38771,23)),Promise.resolve().then(n.t.bind(n,13225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,43982,23))},23978:()=>{},77948:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>j});var r=n(95344),a=n(3729),i=n(47674),o=n(8428),s=n(89410);(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}();var d=n(7060),c=n(66138),l=n(53686),m=n(25545),u=n(42739),h=n(76196),x=n(63024),v=n(55794),f=n(80508),O=n(18822),p=n(96885),N=n(89151);function j(){let{data:e,status:t}=(0,i.useSession)(),n=(0,o.useParams)(),j=(0,o.useRouter)(),{toast:g}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[y,D]=(0,a.useState)(null),[w,k]=(0,a.useState)(!0),[_,E]=(0,a.useState)("");(0,a.useEffect)(()=>{if("loading"!==t&&!e?.user){j.push("/auth/login");return}},[e,t,j]),(0,a.useEffect)(()=>{let t=async()=>{try{let e=await fetch(`/api/tickets/${n.id}`),t=await e.json();t.success?(D(t.data),t.data.qrCode&&E(`data:image/svg+xml;base64,${btoa(`
              <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                <rect width="200" height="200" fill="white"/>
                <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12">
                  QR Code: ${t.data.ticketCode}
                </text>
              </svg>
            `)}`)):(g({title:"Error",description:t.message||"Tiket tidak ditemukan",variant:"destructive"}),j.push("/my-tickets"))}catch(e){g({title:"Error",description:"Terjadi kesalahan saat mengambil data tiket",variant:"destructive"}),j.push("/my-tickets")}finally{k(!1)}};n.id&&e?.user&&t()},[n.id,e,j,g]);let b=e=>new Date(e)<new Date,U=async()=>{if(navigator.share)try{await navigator.share({title:`Tiket ${y?.event.title}`,text:`Saya akan menghadiri ${y?.event.title}!`,url:window.location.href})}catch(e){}else navigator.clipboard.writeText(window.location.href),g({title:"Link disalin",description:"Link tiket berhasil disalin ke clipboard"})};if("loading"===t||w)return r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(u.Z,{className:"h-8 w-8 animate-spin"})});if(!e?.user||!y)return null;let T=y?y.isUsed?{label:"Sudah Digunakan",variant:"success",icon:d.Z}:b(y.event.endDate)?{label:"Event Berakhir",variant:"destructive",icon:c.Z}:new Date(y.event.startDate)<=new Date?{label:"Dapat Digunakan",variant:"default",icon:l.Z}:{label:"Menunggu Event",variant:"secondary",icon:m.Z}:null,M=T?.icon||h.Z;return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>j.back(),className:"flex items-center gap-2",children:[r.jsx(x.Z,{className:"h-4 w-4"}),"Kembali"]}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Detail Tiket"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:y.ticketCode})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[y.event.image&&r.jsx("div",{className:"relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0",children:r.jsx(s.default,{src:y.event.image,alt:y.event.title,fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",style:{backgroundColor:y.event.category.color+"20",color:y.event.category.color},children:y.event.category.name}),y.event.organizer.isVerified&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:"Verified"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl mb-2",children:y.event.title}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:y.event.description})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(v.Z,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:"Tanggal & Waktu"}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y.event.startDate)," - ",Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y.event.endDate)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(f.Z,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:"Lokasi"}),r.jsx("div",{className:"text-sm text-gray-600",children:y.event.location})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(O.Z,{className:"h-5 w-5 text-gray-400"}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:"Organizer"}),r.jsx("div",{className:"text-sm text-gray-600",children:y.event.organizer.name})]})]})]})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Informasi Tiket"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Kode Tiket"}),r.jsx("div",{className:"font-mono font-medium",children:y.ticketCode})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Status"}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:T?.variant,children:[r.jsx(M,{className:"h-3 w-3 mr-1"}),T?.label]})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Harga Tiket"}),r.jsx("div",{className:"font-medium",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y.price)})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Biaya Admin"}),r.jsx("div",{className:"font-medium",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y.adminFee)})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Total Dibayar"}),r.jsx("div",{className:"font-medium text-primary",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y.totalPaid)})]}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Tanggal Pembelian"}),r.jsx("div",{className:"font-medium",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y.createdAt)})]}),y.isUsed&&y.usedAt&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Tanggal Digunakan"}),r.jsx("div",{className:"font-medium",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(y.usedAt)})]}),y.validator&&(0,r.jsxs)("div",{children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Divalidasi Oleh"}),r.jsx("div",{className:"font-medium",children:y.validator.name})]})]})]})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center gap-2",children:[r.jsx(l.Z,{className:"h-5 w-5"}),"QR Code"]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Tunjukkan QR code ini kepada staff untuk validasi"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center",children:[_&&r.jsx("div",{className:"mb-4",children:r.jsx(s.default,{src:_,alt:"QR Code",width:200,height:200,className:"mx-auto border rounded-lg"})}),r.jsx("div",{className:"text-xs text-gray-500 font-mono break-all mb-4",children:y.qrCode}),!y.isUsed&&!b(y.event.endDate)&&r.jsx("div",{className:"text-sm text-green-600 font-medium",children:"✓ Siap untuk divalidasi"})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Aksi"})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-3",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full",variant:"outline",children:[r.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full",variant:"outline",onClick:U,children:[r.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Bagikan"]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full",variant:"outline",onClick:()=>j.push(`/events/${y.event.id}`),children:"Lihat Event"})]})]})]})]})]})}(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},63024:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7060:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},96885:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},53686:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},89151:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},76196:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]])},18822:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},66150:(e,t,n)=>{"use strict";e.exports=n(16372).vendored.contexts.RouterContext},82917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>c,metadata:()=>d});var r=n(25036),a=n(450),i=n.n(a),o=n(14824),s=n.n(o);n(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let d={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function c({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${i().variable} ${s().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},53028:(e,t,n)=>{"use strict";n.r(t),n.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>o});let r=(0,n(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\my-tickets\[id]\page.tsx`),{__esModule:a,$$typeof:i}=r,o=r.default},67272:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1638,3293,5504,2972],()=>n(95069));module.exports=r})();