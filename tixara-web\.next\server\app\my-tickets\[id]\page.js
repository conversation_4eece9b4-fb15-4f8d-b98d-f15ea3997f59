(()=>{var e={};e.id=5239,e.ids=[5239],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},95069:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c});var a=s(50482),r=s(69108),i=s(62563),n=s.n(i),l=s(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c=["",{children:["my-tickets",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,53028)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\[id]\\page.tsx"],x="/my-tickets/[id]/page",m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/my-tickets/[id]/page",pathname:"/my-tickets/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6894:(e,t,s)=>{Promise.resolve().then(s.bind(s,77948))},77948:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>Z});var a=s(95344),r=s(3729),i=s(47674),n=s(8428),l=s(89410),d=s(16212),c=s(61351),o=s(69436),x=s(7060),m=s(66138),h=s(53686),u=s(25545),v=s(42739),g=s(76196),p=s(63024),f=s(55794),y=s(80508),j=s(18822),k=s(96885),N=s(89151),w=s(30692),b=s(91626);function Z(){let{data:e,status:t}=(0,i.useSession)(),s=(0,n.useParams)(),Z=(0,n.useRouter)(),{toast:C}=(0,w.pm)(),[D,q]=(0,r.useState)(null),[P,_]=(0,r.useState)(!0),[M,T]=(0,r.useState)("");(0,r.useEffect)(()=>{if("loading"!==t&&!e?.user){Z.push("/auth/login");return}},[e,t,Z]),(0,r.useEffect)(()=>{let t=async()=>{try{let e=await fetch(`/api/tickets/${s.id}`),t=await e.json();t.success?(q(t.data),t.data.qrCode&&T(`data:image/svg+xml;base64,${btoa(`
              <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                <rect width="200" height="200" fill="white"/>
                <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12">
                  QR Code: ${t.data.ticketCode}
                </text>
              </svg>
            `)}`)):(C({title:"Error",description:t.message||"Tiket tidak ditemukan",variant:"destructive"}),Z.push("/my-tickets"))}catch(e){C({title:"Error",description:"Terjadi kesalahan saat mengambil data tiket",variant:"destructive"}),Z.push("/my-tickets")}finally{_(!1)}};s.id&&e?.user&&t()},[s.id,e,Z,C]);let z=e=>new Date(e)<new Date,S=async()=>{if(navigator.share)try{await navigator.share({title:`Tiket ${D?.event.title}`,text:`Saya akan menghadiri ${D?.event.title}!`,url:window.location.href})}catch(e){}else navigator.clipboard.writeText(window.location.href),C({title:"Link disalin",description:"Link tiket berhasil disalin ke clipboard"})};if("loading"===t||P)return a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx(v.Z,{className:"h-8 w-8 animate-spin"})});if(!e?.user||!D)return null;let R=D?D.isUsed?{label:"Sudah Digunakan",variant:"success",icon:x.Z}:z(D.event.endDate)?{label:"Event Berakhir",variant:"destructive",icon:m.Z}:new Date(D.event.startDate)<=new Date?{label:"Dapat Digunakan",variant:"default",icon:h.Z}:{label:"Menunggu Event",variant:"secondary",icon:u.Z}:null,A=R?.icon||g.Z;return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,a.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>Z.back(),className:"flex items-center gap-2",children:[a.jsx(p.Z,{className:"h-4 w-4"}),"Kembali"]}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Detail Tiket"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:D.ticketCode})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:(0,a.jsxs)("div",{className:"flex items-start gap-4",children:[D.event.image&&a.jsx("div",{className:"relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0",children:a.jsx(l.default,{src:D.event.image,alt:D.event.title,fill:!0,className:"object-cover"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[a.jsx(o.C,{variant:"secondary",style:{backgroundColor:D.event.category.color+"20",color:D.event.category.color},children:D.event.category.name}),D.event.organizer.isVerified&&a.jsx(o.C,{variant:"outline",children:"Verified"})]}),a.jsx(c.ll,{className:"text-xl mb-2",children:D.event.title}),a.jsx(c.SZ,{children:D.event.description})]})]})}),a.jsx(c.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(f.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:"Tanggal & Waktu"}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,b.formatDate)(D.event.startDate)," - ",(0,b.formatDate)(D.event.endDate)]})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(y.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:"Lokasi"}),a.jsx("div",{className:"text-sm text-gray-600",children:D.event.location})]})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[a.jsx(j.Z,{className:"h-5 w-5 text-gray-400"}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"font-medium",children:"Organizer"}),a.jsx("div",{className:"text-sm text-gray-600",children:D.event.organizer.name})]})]})]})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Informasi Tiket"})}),a.jsx(c.aY,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Kode Tiket"}),a.jsx("div",{className:"font-mono font-medium",children:D.ticketCode})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Status"}),(0,a.jsxs)(o.C,{variant:R?.variant,children:[a.jsx(A,{className:"h-3 w-3 mr-1"}),R?.label]})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Harga Tiket"}),a.jsx("div",{className:"font-medium",children:(0,b.formatCurrency)(D.price)})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Biaya Admin"}),a.jsx("div",{className:"font-medium",children:(0,b.formatCurrency)(D.adminFee)})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Total Dibayar"}),a.jsx("div",{className:"font-medium text-primary",children:(0,b.formatCurrency)(D.totalPaid)})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Tanggal Pembelian"}),a.jsx("div",{className:"font-medium",children:(0,b.formatDate)(D.createdAt)})]}),D.isUsed&&D.usedAt&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Tanggal Digunakan"}),a.jsx("div",{className:"font-medium",children:(0,b.formatDate)(D.usedAt)})]}),D.validator&&(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-sm text-gray-600",children:"Divalidasi Oleh"}),a.jsx("div",{className:"font-medium",children:D.validator.name})]})]})]})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(c.Zb,{children:[(0,a.jsxs)(c.Ol,{children:[(0,a.jsxs)(c.ll,{className:"flex items-center gap-2",children:[a.jsx(h.Z,{className:"h-5 w-5"}),"QR Code"]}),a.jsx(c.SZ,{children:"Tunjukkan QR code ini kepada staff untuk validasi"})]}),(0,a.jsxs)(c.aY,{className:"text-center",children:[M&&a.jsx("div",{className:"mb-4",children:a.jsx(l.default,{src:M,alt:"QR Code",width:200,height:200,className:"mx-auto border rounded-lg"})}),a.jsx("div",{className:"text-xs text-gray-500 font-mono break-all mb-4",children:D.qrCode}),!D.isUsed&&!z(D.event.endDate)&&a.jsx("div",{className:"text-sm text-green-600 font-medium",children:"✓ Siap untuk divalidasi"})]})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:a.jsx(c.ll,{children:"Aksi"})}),(0,a.jsxs)(c.aY,{className:"space-y-3",children:[(0,a.jsxs)(d.z,{className:"w-full",variant:"outline",children:[a.jsx(k.Z,{className:"h-4 w-4 mr-2"}),"Download PDF"]}),(0,a.jsxs)(d.z,{className:"w-full",variant:"outline",onClick:S,children:[a.jsx(N.Z,{className:"h-4 w-4 mr-2"}),"Bagikan"]}),a.jsx(d.z,{className:"w-full",variant:"outline",onClick:()=>Z.push(`/events/${D.event.id}`),children:"Lihat Event"})]})]})]})]})]})}},69436:(e,t,s)=>{"use strict";s.d(t,{C:()=>l});var a=s(95344);s(3729);var r=s(92193),i=s(91626);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...s}){return a.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...s})}},61351:(e,t,s)=>{"use strict";s.d(t,{Ol:()=>l,SZ:()=>c,Zb:()=>n,aY:()=>o,ll:()=>d});var a=s(95344),r=s(3729),i=s(91626);let n=r.forwardRef(({className:e,elevated:t=!1,padding:s="md",...r},n)=>a.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",t&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===s,"p-3":"sm"===s,"p-6":"md"===s,"p-8":"lg"===s},e),...r}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},s)=>a.jsx("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...t},s)=>a.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...t}));o.displayName="CardContent",r.forwardRef(({className:e,...t},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},63024:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7060:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},96885:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},53686:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},89151:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]])},53028:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let a=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\my-tickets\[id]\page.tsx`),{__esModule:r,$$typeof:i}=a,n=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[1638,3088,3396,9205],()=>s(95069));module.exports=a})();