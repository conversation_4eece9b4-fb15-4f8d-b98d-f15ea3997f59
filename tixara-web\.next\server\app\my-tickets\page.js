(()=>{var e={};e.id=7237,e.ids=[7237],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},99494:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>l,routeModule:()=>h,tree:()=>d});var r=n(50482),a=n(69108),o=n(62563),i=n.n(o),s=n(68300),c={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);n.d(t,c);let d=["",{children:["my-tickets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,46480)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\page.tsx"],m="/my-tickets/page",u={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/my-tickets/page",pathname:"/my-tickets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},94569:(e,t,n)=>{Promise.resolve().then(n.bind(n,99894))},16509:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,26840,23)),Promise.resolve().then(n.t.bind(n,38771,23)),Promise.resolve().then(n.t.bind(n,13225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,43982,23))},23978:()=>{},99894:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>f});var r=n(95344),a=n(3729),o=n(47674),i=n(8428),s=n(89410);(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}();var c=n(7060),d=n(66138),l=n(53686),m=n(25545),u=n(42739),h=n(76196),x=n(55794),p=n(80508),v=n(18822),O=n(53148);function f(){let{data:e,status:t}=(0,o.useSession)(),n=(0,i.useRouter)(),{toast:f}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[N,j]=(0,a.useState)([]),[g,D]=(0,a.useState)([]),[b,_]=(0,a.useState)(!0),[E,y]=(0,a.useState)("all"),[U,k]=(0,a.useState)({total:0,active:0,used:0});(0,a.useEffect)(()=>{if("loading"!==t&&!e?.user){n.push("/auth/login");return}},[e,t,n]);let w=async(e="all")=>{try{_(!0);let t=new URLSearchParams;"all"!==e&&t.append("status",e);let n=await fetch(`/api/tickets/my-tickets?${t}`),r=await n.json();r.success?(j(r.data.tickets),D(r.data.ticketsByEvent),k(r.data.summary)):f({title:"Error",description:r.message||"Gagal mengambil data tiket",variant:"destructive"})}catch(e){f({title:"Error",description:"Terjadi kesalahan saat mengambil data",variant:"destructive"})}finally{_(!1)}};(0,a.useEffect)(()=>{e?.user&&w(E)},[e,E]);let T=e=>new Date(e)<=new Date,M=e=>new Date(e)<new Date,C=e=>e.isUsed?{label:"Sudah Digunakan",variant:"success",icon:c.Z}:M(e.event.endDate)?{label:"Event Berakhir",variant:"destructive",icon:d.Z}:T(e.event.startDate)?{label:"Dapat Digunakan",variant:"default",icon:l.Z}:{label:"Menunggu Event",variant:"secondary",icon:m.Z};return"loading"===t||b?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(u.Z,{className:"h-8 w-8 animate-spin"})}):e?.user?(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[r.jsx("div",{className:"p-3 bg-primary/10 rounded-lg",children:r.jsx(h.Z,{className:"h-8 w-8 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Tiket Saya"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola dan lihat semua tiket yang Anda miliki"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium text-gray-600",children:"Total Tiket"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx("div",{className:"text-2xl font-bold",children:U.total})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium text-gray-600",children:"Tiket Aktif"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx("div",{className:"text-2xl font-bold text-green-600",children:U.active})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pb-3",children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm font-medium text-gray-600",children:"Sudah Digunakan"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx("div",{className:"text-2xl font-bold text-gray-500",children:U.used})})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:E,onValueChange:e=>{y(e)},className:"space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"grid w-full grid-cols-3",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"all",children:"Semua Tiket"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"active",children:"Aktif"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"used",children:"Sudah Digunakan"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/tabs'");throw e.code="MODULE_NOT_FOUND",e}()),{value:E,className:"space-y-6",children:0===g.length?(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx(h.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Belum ada tiket"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Anda belum memiliki tiket. Jelajahi event menarik dan beli tiket sekarang!"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>n.push("/events"),children:"Jelajahi Event"})]}):r.jsx("div",{className:"space-y-8",children:g.map(e=>(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"overflow-hidden",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-start gap-4",children:[e.event.image&&r.jsx("div",{className:"relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0",children:r.jsx(s.default,{src:e.event.image,alt:e.event.title,fill:!0,className:"object-cover"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",style:{backgroundColor:e.event.category.color+"20",color:e.event.category.color},children:e.event.category.name}),e.event.organizer.isVerified&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",children:"Verified"})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-xl mb-2",children:e.event.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(x.Z,{className:"h-4 w-4"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.event.startDate)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(p.Z,{className:"h-4 w-4"}),r.jsx("span",{className:"truncate",children:e.event.location})]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[r.jsx(v.Z,{className:"h-4 w-4"}),r.jsx("span",{children:e.event.organizer.name})]})]})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"text-sm text-gray-600 mb-4",children:[e.tickets.length," tiket untuk event ini"]}),r.jsx("div",{className:"grid gap-4",children:e.tickets.map(e=>{let t=C(e),a=t.icon;return(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:r.jsx(a,{className:"h-5 w-5 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("div",{className:"font-medium",children:e.ticketCode}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.totalPaid)," • Dibeli ",Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.createdAt)]}),e.isUsed&&e.usedAt&&(0,r.jsxs)("div",{className:"text-sm text-green-600",children:["Digunakan ",Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.usedAt)]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:t.variant,children:t.label}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>n.push(`/my-tickets/${e.id}`),children:[r.jsx(O.Z,{className:"h-4 w-4 mr-1"}),"Detail"]})]})]},e.id)})})]})})]},e.event.id))})})]})]}):null}(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},7060:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53148:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},53686:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},76196:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]])},18822:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},66150:(e,t,n)=>{"use strict";e.exports=n(16372).vendored.contexts.RouterContext},82917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d,metadata:()=>c});var r=n(25036),a=n(450),o=n.n(a),i=n(14824),s=n.n(i);n(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${o().variable} ${s().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},46480:(e,t,n)=>{"use strict";n.r(t),n.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>i});let r=(0,n(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\my-tickets\page.tsx`),{__esModule:a,$$typeof:o}=r,i=r.default},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1638,3293,5504,2972],()=>n(99494));module.exports=r})();