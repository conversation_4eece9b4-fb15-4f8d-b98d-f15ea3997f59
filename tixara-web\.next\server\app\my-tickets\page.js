(()=>{var e={};e.id=7237,e.ids=[7237],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},99494:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var s=a(50482),r=a(69108),i=a(62563),n=a.n(i),l=a(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);a.d(t,d);let c=["",{children:["my-tickets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,46480)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\my-tickets\\page.tsx"],m="/my-tickets/page",u={require:a,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/my-tickets/page",pathname:"/my-tickets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},94569:(e,t,a)=>{Promise.resolve().then(a.bind(a,99894))},99894:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>w});var s=a(95344),r=a(3729),i=a(47674),n=a(8428),l=a(89410),d=a(16212),c=a(61351),o=a(69436),m=a(15746),u=a(7060),x=a(66138),p=a(53686),h=a(25545),f=a(42739),v=a(76196),g=a(55794),y=a(80508),j=a(18822),b=a(53148),k=a(30692),N=a(91626);function w(){let{data:e,status:t}=(0,i.useSession)(),a=(0,n.useRouter)(),{toast:w}=(0,k.pm)(),[C,D]=(0,r.useState)([]),[Z,q]=(0,r.useState)([]),[P,M]=(0,r.useState)(!0),[R,V]=(0,r.useState)("all"),[_,S]=(0,r.useState)({total:0,active:0,used:0});(0,r.useEffect)(()=>{if("loading"!==t&&!e?.user){a.push("/auth/login");return}},[e,t,a]);let T=async(e="all")=>{try{M(!0);let t=new URLSearchParams;"all"!==e&&t.append("status",e);let a=await fetch(`/api/tickets/my-tickets?${t}`),s=await a.json();s.success?(D(s.data.tickets),q(s.data.ticketsByEvent),S(s.data.summary)):w({title:"Error",description:s.message||"Gagal mengambil data tiket",variant:"destructive"})}catch(e){w({title:"Error",description:"Terjadi kesalahan saat mengambil data",variant:"destructive"})}finally{M(!1)}};(0,r.useEffect)(()=>{e?.user&&T(R)},[e,R]);let z=e=>new Date(e)<=new Date,E=e=>new Date(e)<new Date,A=e=>e.isUsed?{label:"Sudah Digunakan",variant:"success",icon:u.Z}:E(e.event.endDate)?{label:"Event Berakhir",variant:"destructive",icon:x.Z}:z(e.event.startDate)?{label:"Dapat Digunakan",variant:"default",icon:p.Z}:{label:"Menunggu Event",variant:"secondary",icon:h.Z};return"loading"===t||P?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsx(f.Z,{className:"h-8 w-8 animate-spin"})}):e?.user?(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-6xl",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[s.jsx("div",{className:"p-3 bg-primary/10 rounded-lg",children:s.jsx(v.Z,{className:"h-8 w-8 text-primary"})}),(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Tiket Saya"}),s.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Kelola dan lihat semua tiket yang Anda miliki"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)(c.Zb,{children:[s.jsx(c.Ol,{className:"pb-3",children:s.jsx(c.ll,{className:"text-sm font-medium text-gray-600",children:"Total Tiket"})}),s.jsx(c.aY,{children:s.jsx("div",{className:"text-2xl font-bold",children:_.total})})]}),(0,s.jsxs)(c.Zb,{children:[s.jsx(c.Ol,{className:"pb-3",children:s.jsx(c.ll,{className:"text-sm font-medium text-gray-600",children:"Tiket Aktif"})}),s.jsx(c.aY,{children:s.jsx("div",{className:"text-2xl font-bold text-green-600",children:_.active})})]}),(0,s.jsxs)(c.Zb,{children:[s.jsx(c.Ol,{className:"pb-3",children:s.jsx(c.ll,{className:"text-sm font-medium text-gray-600",children:"Sudah Digunakan"})}),s.jsx(c.aY,{children:s.jsx("div",{className:"text-2xl font-bold text-gray-500",children:_.used})})]})]}),(0,s.jsxs)(m.mQ,{value:R,onValueChange:e=>{V(e)},className:"space-y-6",children:[(0,s.jsxs)(m.dr,{className:"grid w-full grid-cols-3",children:[s.jsx(m.SP,{value:"all",children:"Semua Tiket"}),s.jsx(m.SP,{value:"active",children:"Aktif"}),s.jsx(m.SP,{value:"used",children:"Sudah Digunakan"})]}),s.jsx(m.nU,{value:R,className:"space-y-6",children:0===Z.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx(v.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Belum ada tiket"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Anda belum memiliki tiket. Jelajahi event menarik dan beli tiket sekarang!"}),s.jsx(d.z,{onClick:()=>a.push("/events"),children:"Jelajahi Event"})]}):s.jsx("div",{className:"space-y-8",children:Z.map(e=>(0,s.jsxs)(c.Zb,{className:"overflow-hidden",children:[s.jsx(c.Ol,{children:(0,s.jsxs)("div",{className:"flex items-start gap-4",children:[e.event.image&&s.jsx("div",{className:"relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0",children:s.jsx(l.default,{src:e.event.image,alt:e.event.title,fill:!0,className:"object-cover"})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[s.jsx(o.C,{variant:"secondary",style:{backgroundColor:e.event.category.color+"20",color:e.event.category.color},children:e.event.category.name}),e.event.organizer.isVerified&&s.jsx(o.C,{variant:"outline",children:"Verified"})]}),s.jsx(c.ll,{className:"text-xl mb-2",children:e.event.title}),(0,s.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600",children:[(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx(g.Z,{className:"h-4 w-4"}),s.jsx("span",{children:(0,N.formatRelativeTime)(e.event.startDate)})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx(y.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"truncate",children:e.event.location})]}),(0,s.jsxs)("div",{className:"flex items-center gap-1",children:[s.jsx(j.Z,{className:"h-4 w-4"}),s.jsx("span",{children:e.event.organizer.name})]})]})]})]})}),s.jsx(c.aY,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600 mb-4",children:[e.tickets.length," tiket untuk event ini"]}),s.jsx("div",{className:"grid gap-4",children:e.tickets.map(e=>{let t=A(e),r=t.icon;return(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[s.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:s.jsx(r,{className:"h-5 w-5 text-primary"})}),(0,s.jsxs)("div",{children:[s.jsx("div",{className:"font-medium",children:e.ticketCode}),(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,N.formatCurrency)(e.totalPaid)," • Dibeli ",(0,N.formatRelativeTime)(e.createdAt)]}),e.isUsed&&e.usedAt&&(0,s.jsxs)("div",{className:"text-sm text-green-600",children:["Digunakan ",(0,N.formatRelativeTime)(e.usedAt)]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[s.jsx(o.C,{variant:t.variant,children:t.label}),(0,s.jsxs)(d.z,{variant:"outline",size:"sm",onClick:()=>a.push(`/my-tickets/${e.id}`),children:[s.jsx(b.Z,{className:"h-4 w-4 mr-1"}),"Detail"]})]})]},e.id)})})]})})]},e.event.id))})})]})]}):null}},69436:(e,t,a)=>{"use strict";a.d(t,{C:()=>l});var s=a(95344);a(3729);var r=a(92193),i=a(91626);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...a}){return s.jsx("div",{className:(0,i.cn)(n({variant:t}),e),...a})}},61351:(e,t,a)=>{"use strict";a.d(t,{Ol:()=>l,SZ:()=>c,Zb:()=>n,aY:()=>o,ll:()=>d});var s=a(95344),r=a(3729),i=a(91626);let n=r.forwardRef(({className:e,elevated:t=!1,padding:a="md",...r},n)=>s.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",t&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===a,"p-3":"sm"===a,"p-6":"md"===a,"p-8":"lg"===a},e),...r}));n.displayName="Card";let l=r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...t},a)=>s.jsx("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...t},a)=>s.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...t}));o.displayName="CardContent",r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},15746:(e,t,a)=>{"use strict";a.d(t,{SP:()=>c,dr:()=>d,mQ:()=>l,nU:()=>o});var s=a(95344),r=a(3729),i=a(89128),n=a(91626);let l=i.fC,d=r.forwardRef(({className:e,...t},a)=>s.jsx(i.aV,{ref:a,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...t}));d.displayName=i.aV.displayName;let c=r.forwardRef(({className:e,...t},a)=>s.jsx(i.xz,{ref:a,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...t}));c.displayName=i.xz.displayName;let o=r.forwardRef(({className:e,...t},a)=>s.jsx(i.VY,{ref:a,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...t}));o.displayName=i.VY.displayName},7060:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53148:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},53686:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,a(69224).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},46480:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let s=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\my-tickets\page.tsx`),{__esModule:r,$$typeof:i}=s,n=s.default},89128:(e,t,a)=>{"use strict";a.d(t,{VY:()=>R,aV:()=>P,fC:()=>q,xz:()=>M});var s=a(3729),r=a(85222),i=a(98462),n=a(34504),l=a(43234),d=a(62409),c=a(3975),o=a(33183),m=a(99048),u=a(95344),x="Tabs",[p,h]=(0,i.b)(x,[n.Pc]),f=(0,n.Pc)(),[v,g]=p(x),y=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,onValueChange:r,defaultValue:i,orientation:n="horizontal",dir:l,activationMode:p="automatic",...h}=e,f=(0,c.gm)(l),[g,y]=(0,o.T)({prop:s,onChange:r,defaultProp:i??"",caller:x});return(0,u.jsx)(v,{scope:a,baseId:(0,m.M)(),value:g,onValueChange:y,orientation:n,dir:f,activationMode:p,children:(0,u.jsx)(d.WV.div,{dir:f,"data-orientation":n,...h,ref:t})})});y.displayName=x;var j="TabsList",b=s.forwardRef((e,t)=>{let{__scopeTabs:a,loop:s=!0,...r}=e,i=g(j,a),l=f(a);return(0,u.jsx)(n.fC,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:s,children:(0,u.jsx)(d.WV.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:t})})});b.displayName=j;var k="TabsTrigger",N=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:s,disabled:i=!1,...l}=e,c=g(k,a),o=f(a),m=D(c.baseId,s),x=Z(c.baseId,s),p=s===c.value;return(0,u.jsx)(n.ck,{asChild:!0,...o,focusable:!i,active:p,children:(0,u.jsx)(d.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":x,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...l,ref:t,onMouseDown:(0,r.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(s)}),onKeyDown:(0,r.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(s)}),onFocus:(0,r.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||i||!e||c.onValueChange(s)})})})});N.displayName=k;var w="TabsContent",C=s.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,forceMount:i,children:n,...c}=e,o=g(w,a),m=D(o.baseId,r),x=Z(o.baseId,r),p=r===o.value,h=s.useRef(p);return s.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,u.jsx)(l.z,{present:i||p,children:({present:a})=>(0,u.jsx)(d.WV.div,{"data-state":p?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!a,id:x,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:a&&n})})});function D(e,t){return`${e}-trigger-${t}`}function Z(e,t){return`${e}-content-${t}`}C.displayName=w;var q=y,P=b,M=N,R=C}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[1638,3088,3396,9205],()=>a(99494));module.exports=s})();