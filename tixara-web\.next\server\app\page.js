(()=>{var e={};e.id=1931,e.ids=[1931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},79914:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>l});var r=n(50482),o=n(69108),a=n(62563),i=n.n(a),s=n(68300),d={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>s[e]);n.d(t,d);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,70751)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(n.bind(n,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\page.tsx"],m="/page",u={require:n,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:o.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},16509:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,26840,23)),Promise.resolve().then(n.t.bind(n,38771,23)),Promise.resolve().then(n.t.bind(n,13225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,43982,23))},23978:()=>{},35303:()=>{},82917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>l,metadata:()=>d});var r=n(25036),o=n(450),a=n.n(o),i=n(14824),s=n.n(i);n(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let d={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function l({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${a().variable} ${s().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},70751:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>a,metadata:()=>o});var r=n(25036);(function(){var e=Error("Cannot find module '@/components/sections/hero-section'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/sections/featured-events'");throw e.code="MODULE_NOT_FOUND",e}();let o={title:"TiXara - Platform E-Ticketing Terdepan Indonesia",description:"Jual beli tiket event, konser, workshop, dan seminar dengan mudah dan aman. Bergabunglah dengan ribuan organizer dan pembeli di TiXara."};function a(){return(0,r.jsxs)("div",{className:"min-h-screen",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/sections/hero-section'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/sections/featured-events'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})}},67272:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1638,3293],()=>n(79914));module.exports=r})();