(()=>{var e={};e.id=2558,e.ids=[2558],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},13241:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>u,tree:()=>d});var t=a(50482),r=a(69108),i=a(62563),l=a.n(i),n=a(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d=["",{children:["staff",{children:["validate-ticket",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,29269)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\staff\\validate-ticket\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\staff\\validate-ticket\\page.tsx"],x="/staff/validate-ticket/page",m={require:a,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/staff/validate-ticket/page",pathname:"/staff/validate-ticket",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},26033:(e,s,a)=>{Promise.resolve().then(a.bind(a,89065))},89065:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>D});var t=a(95344),r=a(3729),i=a(47674),l=a(8428),n=a(16212),c=a(61351),d=a(92549),o=a(69436),x=a(7060),m=a(73229),u=a(45961),h=a(53686),p=a(42739),f=a(69224);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,f.Z)("Scan",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}]]),g=(0,f.Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var j=a(76196),y=a(55794),N=a(80508),k=a(18822),b=a(30692),w=a(91626);function D(){let{data:e,status:s}=(0,i.useSession)(),a=(0,l.useRouter)(),{toast:f}=(0,b.pm)(),[D,Z]=(0,r.useState)(""),[E,_]=(0,r.useState)(""),[A,M]=(0,r.useState)(!1),[q,C]=(0,r.useState)(null),[R,S]=(0,r.useState)([]);(0,r.useEffect)(()=>{if("loading"!==s&&(!e?.user||!["STAFF","ADMIN"].includes(e.user.role))){a.push("/dashboard");return}},[e,s,a]),(0,r.useEffect)(()=>{let s=async()=>{if(e?.user)try{let e=await fetch("/api/events/staff-events"),s=await e.json();s.success&&(S(s.data),1===s.data.length&&_(s.data[0].id))}catch(e){console.error("Error fetching staff events:",e)}};e?.user?.role==="STAFF"&&s()},[e]);let T=async()=>{if(!D.trim()){f({title:"Error",description:"Masukkan data QR code",variant:"destructive"});return}if(!E){f({title:"Error",description:"Pilih event terlebih dahulu",variant:"destructive"});return}M(!0),C(null);try{let e=await fetch("/api/tickets/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({qrData:D.trim(),eventId:E})}),s=await e.json();C(s),s.success?(f({title:"Berhasil",description:s.message}),Z("")):f({title:"Validasi Gagal",description:s.message,variant:"destructive"})}catch(e){f({title:"Error",description:"Terjadi kesalahan saat validasi",variant:"destructive"})}finally{M(!1)}};return"loading"===s?t.jsx("div",{className:"flex items-center justify-center min-h-screen",children:t.jsx(p.Z,{className:"h-8 w-8 animate-spin"})}):e?.user&&["STAFF","ADMIN"].includes(e.user.role)?(0,t.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[t.jsx("div",{className:"p-3 bg-primary/10 rounded-lg",children:t.jsx(v,{className:"h-8 w-8 text-primary"})}),(0,t.jsxs)("div",{children:[t.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Validasi Tiket"}),t.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Scan atau masukkan QR code untuk memvalidasi tiket"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(c.Zb,{children:[(0,t.jsxs)(c.Ol,{children:[(0,t.jsxs)(c.ll,{className:"flex items-center gap-2",children:[t.jsx(h.Z,{className:"h-5 w-5"}),"Validasi Tiket"]}),t.jsx(c.SZ,{children:"Masukkan data QR code dan pilih event untuk validasi"})]}),(0,t.jsxs)(c.aY,{className:"space-y-4",children:["STAFF"===e.user.role&&R.length>1&&(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium mb-2",children:"Pilih Event"}),(0,t.jsxs)("select",{value:E,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[t.jsx("option",{value:"",children:"Pilih event..."}),R.map(e=>t.jsx("option",{value:e.id,children:e.title},e.id))]})]}),(0,t.jsxs)("div",{children:[t.jsx("label",{className:"block text-sm font-medium mb-2",children:"QR Code Data"}),t.jsx(d.I,{value:D,onChange:e=>Z(e.target.value),placeholder:"Masukkan atau scan QR code...",className:"font-mono"})]}),(0,t.jsxs)("div",{className:"flex gap-3",children:[(0,t.jsxs)(n.z,{onClick:T,disabled:A||!D.trim()||!E,className:"flex-1",children:[A?t.jsx(p.Z,{className:"h-4 w-4 mr-2 animate-spin"}):t.jsx(v,{className:"h-4 w-4 mr-2"}),"Validasi"]}),t.jsx(n.z,{variant:"outline",onClick:()=>{f({title:"Info",description:"Fitur scan kamera akan segera tersedia"})},children:t.jsx(g,{className:"h-4 w-4"})})]})]})]}),(0,t.jsxs)(c.Zb,{children:[t.jsx(c.Ol,{children:t.jsx(c.ll,{className:"text-sm",children:"Aksi Cepat"})}),t.jsx(c.aY,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[t.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>Z(""),className:"w-full",children:"Clear Input"}),t.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>C(null),className:"w-full",children:"Clear Result"})]})})]})]}),t.jsx("div",{children:q?(0,t.jsxs)(c.Zb,{children:[t.jsx(c.Ol,{children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(e=>{switch(e){case"VALIDATED":return t.jsx(x.Z,{className:"h-8 w-8 text-green-500"});case"ALREADY_USED":case"INVALID_QR":case"NOT_FOUND":case"WRONG_EVENT":case"EVENT_ENDED":case"NO_PERMISSION":return t.jsx(m.Z,{className:"h-8 w-8 text-red-500"});case"EARLY_VALIDATION":return t.jsx(u.Z,{className:"h-8 w-8 text-yellow-500"});default:return t.jsx(h.Z,{className:"h-8 w-8 text-gray-500"})}})(q.status),(0,t.jsxs)("div",{children:[t.jsx(c.ll,{children:"Hasil Validasi"}),t.jsx(c.SZ,{children:t.jsx(o.C,{variant:(e=>{switch(e){case"VALIDATED":return"success";case"EARLY_VALIDATION":return"warning";case"ALREADY_USED":case"INVALID_QR":case"NOT_FOUND":case"WRONG_EVENT":case"EVENT_ENDED":case"NO_PERMISSION":return"destructive";default:return"secondary"}})(q.status),children:q.status})})]})]})}),t.jsx(c.aY,{children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-3 rounded-lg bg-gray-50 dark:bg-gray-800",children:[t.jsx("p",{className:"font-medium",children:q.message}),q.warning&&(0,t.jsxs)("p",{className:"text-yellow-600 text-sm mt-1",children:["⚠️ ",q.warning]})]}),q.data&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[t.jsx(j.Z,{className:"h-4 w-4"}),"Informasi Tiket"]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Kode:"}),t.jsx("span",{className:"font-mono",children:q.data.ticket.ticketCode})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Harga:"}),t.jsx("span",{children:(0,w.formatCurrency)(q.data.ticket.price)})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Dibeli:"}),t.jsx("span",{children:(0,w.formatRelativeTime)(q.data.ticket.createdAt)})]}),q.data.ticket.usedAt&&(0,t.jsxs)("div",{className:"flex justify-between",children:[t.jsx("span",{className:"text-gray-600",children:"Digunakan:"}),t.jsx("span",{children:(0,w.formatRelativeTime)(q.data.ticket.usedAt)})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[t.jsx(y.Z,{className:"h-4 w-4"}),"Informasi Event"]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[t.jsx("div",{children:t.jsx("span",{className:"font-medium",children:q.data.event.title})}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[t.jsx(y.Z,{className:"h-3 w-3"}),t.jsx("span",{children:(0,w.formatDate)(q.data.event.startDate)})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[t.jsx(N.Z,{className:"h-3 w-3"}),t.jsx("span",{children:q.data.event.location})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[t.jsx(k.Z,{className:"h-3 w-3"}),t.jsx("span",{children:q.data.event.organizer})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[t.jsx(k.Z,{className:"h-4 w-4"}),"Informasi Pembeli"]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[t.jsx("div",{children:t.jsx("span",{className:"font-medium",children:q.data.buyer.name})}),t.jsx("div",{className:"text-gray-600",children:q.data.buyer.email})]})]}),q.data.validator&&(0,t.jsxs)("div",{children:[t.jsx("h4",{className:"font-medium mb-2",children:"Divalidasi Oleh"}),(0,t.jsxs)("div",{className:"text-sm",children:[t.jsx("div",{className:"font-medium",children:q.data.validator.name}),t.jsx("div",{className:"text-gray-600",children:q.data.validator.email})]})]})]})]})})]}):t.jsx(c.Zb,{children:(0,t.jsxs)(c.aY,{className:"text-center py-12",children:[t.jsx(h.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),t.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Siap untuk Validasi"}),t.jsx("p",{className:"text-gray-600",children:"Masukkan QR code tiket untuk memulai validasi"})]})})})]})]}):null}},69436:(e,s,a)=>{"use strict";a.d(s,{C:()=>n});var t=a(95344);a(3729);var r=a(92193),i=a(91626);let l=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function n({className:e,variant:s,...a}){return t.jsx("div",{className:(0,i.cn)(l({variant:s}),e),...a})}},61351:(e,s,a)=>{"use strict";a.d(s,{Ol:()=>n,SZ:()=>d,Zb:()=>l,aY:()=>o,ll:()=>c});var t=a(95344),r=a(3729),i=a(91626);let l=r.forwardRef(({className:e,elevated:s=!1,padding:a="md",...r},l)=>t.jsx("div",{ref:l,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",s&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===a,"p-3":"sm"===a,"p-6":"md"===a,"p-8":"lg"===a},e),...r}));l.displayName="Card";let n=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));n.displayName="CardHeader";let c=r.forwardRef(({className:e,...s},a)=>t.jsx("h3",{ref:a,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},a)=>t.jsx("p",{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},a)=>t.jsx("div",{ref:a,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},92549:(e,s,a)=>{"use strict";a.d(s,{I:()=>l});var t=a(95344),r=a(3729),i=a(91626);let l=r.forwardRef(({className:e,type:s,...a},r)=>t.jsx("input",{type:s,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));l.displayName="Input"},45961:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},7060:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},80508:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},53686:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},73229:(e,s,a)=>{"use strict";a.d(s,{Z:()=>t});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let t=(0,a(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},29269:(e,s,a)=>{"use strict";a.r(s),a.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>l});let t=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\staff\validate-ticket\page.tsx`),{__esModule:r,$$typeof:i}=t,l=t.default}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[1638,3088,9205],()=>a(13241));module.exports=t})();