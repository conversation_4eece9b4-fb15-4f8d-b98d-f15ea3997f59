(()=>{var e={};e.id=2558,e.ids=[2558],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13241:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>l,routeModule:()=>h,tree:()=>d});var r=a(50482),n=a(69108),s=a(62563),i=a.n(s),o=a(68300),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);a.d(t,c);let d=["",{children:["staff",{children:["validate-ticket",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,29269)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\staff\\validate-ticket\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\staff\\validate-ticket\\page.tsx"],m="/staff/validate-ticket/page",u={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/staff/validate-ticket/page",pathname:"/staff/validate-ticket",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},26033:(e,t,a)=>{Promise.resolve().then(a.bind(a,89065))},16509:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,2583,23)),Promise.resolve().then(a.t.bind(a,26840,23)),Promise.resolve().then(a.t.bind(a,38771,23)),Promise.resolve().then(a.t.bind(a,13225,23)),Promise.resolve().then(a.t.bind(a,9295,23)),Promise.resolve().then(a.t.bind(a,43982,23))},23978:()=>{},89065:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>O});var r=a(95344),n=a(3729),s=a(47674),i=a(8428),o=a(7060),c=a(73229),d=a(45961),l=a(53686),m=a(42739),u=a(69224);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let h=(0,u.Z)("Scan",[["path",{d:"M3 7V5a2 2 0 0 1 2-2h2",key:"aa7l1z"}],["path",{d:"M17 3h2a2 2 0 0 1 2 2v2",key:"4qcy5o"}],["path",{d:"M21 17v2a2 2 0 0 1-2 2h-2",key:"6vwrx8"}],["path",{d:"M7 21H5a2 2 0 0 1-2-2v-2",key:"ioqczr"}]]),x=(0,u.Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]]);var p=a(76196),f=a(55794),v=a(80508),N=a(18822);function O(){let{data:e,status:t}=(0,s.useSession)(),a=(0,i.useRouter)(),{toast:u}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[O,j]=(0,n.useState)(""),[g,y]=(0,n.useState)(""),[E,D]=(0,n.useState)(!1),[_,k]=(0,n.useState)(null),[w,b]=(0,n.useState)([]);(0,n.useEffect)(()=>{if("loading"!==t&&(!e?.user||!["STAFF","ADMIN"].includes(e.user.role))){a.push("/dashboard");return}},[e,t,a]),(0,n.useEffect)(()=>{let t=async()=>{if(e?.user)try{let e=await fetch("/api/events/staff-events"),t=await e.json();t.success&&(b(t.data),1===t.data.length&&y(t.data[0].id))}catch(e){console.error("Error fetching staff events:",e)}};e?.user?.role==="STAFF"&&t()},[e]);let U=async()=>{if(!O.trim()){u({title:"Error",description:"Masukkan data QR code",variant:"destructive"});return}if(!g){u({title:"Error",description:"Pilih event terlebih dahulu",variant:"destructive"});return}D(!0),k(null);try{let e=await fetch("/api/tickets/validate",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({qrData:O.trim(),eventId:g})}),t=await e.json();k(t),t.success?(u({title:"Berhasil",description:t.message}),j("")):u({title:"Validasi Gagal",description:t.message,variant:"destructive"})}catch(e){u({title:"Error",description:"Terjadi kesalahan saat validasi",variant:"destructive"})}finally{D(!1)}};return"loading"===t?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(m.Z,{className:"h-8 w-8 animate-spin"})}):e?.user&&["STAFF","ADMIN"].includes(e.user.role)?(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[r.jsx("div",{className:"p-3 bg-primary/10 rounded-lg",children:r.jsx(h,{className:"h-8 w-8 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Validasi Tiket"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Scan atau masukkan QR code untuk memvalidasi tiket"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"flex items-center gap-2",children:[r.jsx(l.Z,{className:"h-5 w-5"}),"Validasi Tiket"]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Masukkan data QR code dan pilih event untuk validasi"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:["STAFF"===e.user.role&&w.length>1&&(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium mb-2",children:"Pilih Event"}),(0,r.jsxs)("select",{value:g,onChange:e=>y(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary",children:[r.jsx("option",{value:"",children:"Pilih event..."}),w.map(e=>r.jsx("option",{value:e.id,children:e.title},e.id))]})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium mb-2",children:"QR Code Data"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{value:O,onChange:e=>j(e.target.value),placeholder:"Masukkan atau scan QR code...",className:"font-mono"})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:U,disabled:E||!O.trim()||!g,className:"flex-1",children:[E?r.jsx(m.Z,{className:"h-4 w-4 mr-2 animate-spin"}):r.jsx(h,{className:"h-4 w-4 mr-2"}),"Validasi"]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",onClick:()=>{u({title:"Info",description:"Fitur scan kamera akan segera tersedia"})},children:r.jsx(x,{className:"h-4 w-4"})})]})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-sm",children:"Aksi Cepat"})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"space-y-2",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>j(""),className:"w-full",children:"Clear Input"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>k(null),className:"w-full",children:"Clear Result"})]})})]})]}),r.jsx("div",{children:_?(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(e=>{switch(e){case"VALIDATED":return r.jsx(o.Z,{className:"h-8 w-8 text-green-500"});case"ALREADY_USED":case"INVALID_QR":case"NOT_FOUND":case"WRONG_EVENT":case"EVENT_ENDED":case"NO_PERMISSION":return r.jsx(c.Z,{className:"h-8 w-8 text-red-500"});case"EARLY_VALIDATION":return r.jsx(d.Z,{className:"h-8 w-8 text-yellow-500"});default:return r.jsx(l.Z,{className:"h-8 w-8 text-gray-500"})}})(_.status),(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Hasil Validasi"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:(e=>{switch(e){case"VALIDATED":return"success";case"EARLY_VALIDATION":return"warning";case"ALREADY_USED":case"INVALID_QR":case"NOT_FOUND":case"WRONG_EVENT":case"EVENT_ENDED":case"NO_PERMISSION":return"destructive";default:return"secondary"}})(_.status),children:_.status})})]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-3 rounded-lg bg-gray-50 dark:bg-gray-800",children:[r.jsx("p",{className:"font-medium",children:_.message}),_.warning&&(0,r.jsxs)("p",{className:"text-yellow-600 text-sm mt-1",children:["⚠️ ",_.warning]})]}),_.data&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[r.jsx(p.Z,{className:"h-4 w-4"}),"Informasi Tiket"]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Kode:"}),r.jsx("span",{className:"font-mono",children:_.data.ticket.ticketCode})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Harga:"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(_.data.ticket.price)})]}),(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Dibeli:"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(_.data.ticket.createdAt)})]}),_.data.ticket.usedAt&&(0,r.jsxs)("div",{className:"flex justify-between",children:[r.jsx("span",{className:"text-gray-600",children:"Digunakan:"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(_.data.ticket.usedAt)})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[r.jsx(f.Z,{className:"h-4 w-4"}),"Informasi Event"]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[r.jsx("div",{children:r.jsx("span",{className:"font-medium",children:_.data.event.title})}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[r.jsx(f.Z,{className:"h-3 w-3"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(_.data.event.startDate)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[r.jsx(v.Z,{className:"h-3 w-3"}),r.jsx("span",{children:_.data.event.location})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 text-gray-600",children:[r.jsx(N.Z,{className:"h-3 w-3"}),r.jsx("span",{children:_.data.event.organizer})]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h4",{className:"font-medium mb-2 flex items-center gap-2",children:[r.jsx(N.Z,{className:"h-4 w-4"}),"Informasi Pembeli"]}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[r.jsx("div",{children:r.jsx("span",{className:"font-medium",children:_.data.buyer.name})}),r.jsx("div",{className:"text-gray-600",children:_.data.buyer.email})]})]}),_.data.validator&&(0,r.jsxs)("div",{children:[r.jsx("h4",{className:"font-medium mb-2",children:"Divalidasi Oleh"}),(0,r.jsxs)("div",{className:"text-sm",children:[r.jsx("div",{className:"font-medium",children:_.data.validator.name}),r.jsx("div",{className:"text-gray-600",children:_.data.validator.email})]})]})]})]})})]}):r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"text-center py-12",children:[r.jsx(l.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Siap untuk Validasi"}),r.jsx("p",{className:"text-gray-600",children:"Masukkan QR code tiket untuk memulai validasi"})]})})})]})]}):null}(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},45961:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},55794:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},7060:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},80508:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},53686:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},76196:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Ticket",[["path",{d:"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z",key:"qn84l0"}],["path",{d:"M13 5v2",key:"dyzc3o"}],["path",{d:"M13 17v2",key:"1ont0d"}],["path",{d:"M13 11v2",key:"1wjjxi"}]])},18822:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},73229:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},82917:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>d,metadata:()=>c});var r=a(25036),n=a(450),s=a.n(n),i=a(14824),o=a.n(i);a(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${s().variable} ${o().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},29269:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>s,__esModule:()=>n,default:()=>i});let r=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\staff\validate-ticket\page.tsx`),{__esModule:n,$$typeof:s}=r,i=r.default},67272:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,3293,5504],()=>a(13241));module.exports=r})();