(()=>{var e={};e.id=2984,e.ids=[2984],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},77509:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var r=t(50482),s=t(69108),i=t(62563),n=t.n(i),d=t(68300),o={};for(let e in d)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);t.d(a,o);let l=["",{children:["uangtix",{children:["deposit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,1733)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\deposit\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\deposit\\page.tsx"],p="/uangtix/deposit/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/uangtix/deposit/page",pathname:"/uangtix/deposit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},45142:(e,a,t)=>{Promise.resolve().then(t.bind(t,55699))},55699:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>b});var r=t(95344),s=t(3729),i=t(47674),n=t(8428),d=t(16212),o=t(61351),l=t(92549),c=t(54572),p=t(17470);!function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}();var m=t(53686),u=t(16588),x=t(85674),h=t(63024),f=t(67925),y=t(42739),g=t(30692),j=t(91626);let N=[5e4,1e5,2e5,5e5,1e6],v={TRIPAY:[{id:"QRIS",name:"QRIS",icon:m.Z,description:"Scan QR dengan aplikasi e-wallet"},{id:"GOPAY",name:"GoPay",icon:u.Z,description:"Bayar dengan GoPay"},{id:"OVO",name:"OVO",icon:u.Z,description:"Bayar dengan OVO"},{id:"DANA",name:"DANA",icon:u.Z,description:"Bayar dengan DANA"},{id:"SHOPEEPAY",name:"ShopeePay",icon:u.Z,description:"Bayar dengan ShopeePay"},{id:"BCAVA",name:"BCA Virtual Account",icon:x.Z,description:"Transfer ke VA BCA"},{id:"BNIVA",name:"BNI Virtual Account",icon:x.Z,description:"Transfer ke VA BNI"},{id:"BRIVA",name:"BRI Virtual Account",icon:x.Z,description:"Transfer ke VA BRI"}],MIDTRANS:[{id:"gopay",name:"GoPay",icon:u.Z,description:"Bayar dengan GoPay"},{id:"shopeepay",name:"ShopeePay",icon:u.Z,description:"Bayar dengan ShopeePay"},{id:"bca_va",name:"BCA Virtual Account",icon:x.Z,description:"Transfer ke VA BCA"},{id:"bni_va",name:"BNI Virtual Account",icon:x.Z,description:"Transfer ke VA BNI"},{id:"bri_va",name:"BRI Virtual Account",icon:x.Z,description:"Transfer ke VA BRI"}],XENDIT:[{id:"ID_OVO",name:"OVO",icon:u.Z,description:"Bayar dengan OVO"},{id:"ID_DANA",name:"DANA",icon:u.Z,description:"Bayar dengan DANA"},{id:"ID_LINKAJA",name:"LinkAja",icon:u.Z,description:"Bayar dengan LinkAja"},{id:"BCA",name:"BCA Virtual Account",icon:x.Z,description:"Transfer ke VA BCA"},{id:"BNI",name:"BNI Virtual Account",icon:x.Z,description:"Transfer ke VA BNI"},{id:"BRI",name:"BRI Virtual Account",icon:x.Z,description:"Transfer ke VA BRI"}]};function b(){let{data:e}=(0,i.useSession)(),a=(0,n.useRouter)(),{toast:t}=(0,g.pm)(),[m,u]=(0,s.useState)(""),[x,b]=(0,s.useState)("TRIPAY"),[w,A]=(0,s.useState)(""),[k,Z]=(0,s.useState)(!1),_=e=>{u(e.replace(/[^0-9]/g,""))},I=e=>{u(e.toString())},R=async e=>{if(e.preventDefault(),!m||!w){t({title:"Error",description:"Mohon lengkapi semua field",variant:"destructive"});return}let r=parseInt(m);if(r<1e4){t({title:"Error",description:"Minimum deposit Rp 10.000",variant:"destructive"});return}if(r>1e7){t({title:"Error",description:"Maksimum deposit Rp 10.000.000",variant:"destructive"});return}Z(!0);try{let e=await fetch("/api/uangtix/deposit",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:r,gateway:x,paymentMethod:w})}),s=await e.json();s.success?(t({title:"Success",description:"Deposit berhasil dibuat"}),s.data.paymentUrl&&window.open(s.data.paymentUrl,"_blank"),a.push(`/uangtix/deposit/success?orderId=${s.data.orderId}`)):t({title:"Error",description:s.message||"Gagal membuat deposit",variant:"destructive"})}catch(e){t({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}finally{Z(!1)}},P=v[x]||[];return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[r.jsx(d.z,{variant:"ghost",size:"sm",onClick:()=>a.back(),children:r.jsx(h.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:r.jsx(f.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Top Up UangtiX"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Isi saldo UangtiX Anda"})]})]})]}),(0,r.jsxs)("form",{onSubmit:R,className:"space-y-6",children:[(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{children:[r.jsx(o.ll,{children:"Jumlah Top Up"}),r.jsx(o.SZ,{children:"Minimum Rp 10.000, maksimum Rp 10.000.000"})]}),(0,r.jsxs)(o.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[r.jsx(c._,{htmlFor:"amount",children:"Jumlah (Rp)"}),r.jsx(l.I,{id:"amount",type:"text",placeholder:"0",value:m?parseInt(m).toLocaleString("id-ID"):"",onChange:e=>_(e.target.value),className:"text-lg"})]}),(0,r.jsxs)("div",{children:[r.jsx(c._,{children:"Jumlah Cepat"}),r.jsx("div",{className:"grid grid-cols-3 gap-2 mt-2",children:N.map(e=>r.jsx(d.z,{type:"button",variant:"outline",size:"sm",onClick:()=>I(e),className:m===e.toString()?"border-primary":"",children:(0,j.formatCurrency)(e)},e))})]}),m&&(0,r.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"Jumlah Top Up:"}),r.jsx("span",{className:"font-medium",children:(0,j.formatCurrency)(parseInt(m))})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{children:"Biaya Admin:"}),r.jsx("span",{className:"font-medium",children:"Rp 0"})]}),r.jsx("hr",{className:"my-2"}),(0,r.jsxs)("div",{className:"flex justify-between font-medium",children:[r.jsx("span",{children:"Total Bayar:"}),r.jsx("span",{children:(0,j.formatCurrency)(parseInt(m))})]})]})]})]}),(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{children:[r.jsx(o.ll,{children:"Pilih Payment Gateway"}),r.jsx(o.SZ,{children:"Pilih penyedia layanan pembayaran"})]}),r.jsx(o.aY,{children:(0,r.jsxs)(p.Ph,{value:x,onValueChange:e=>{b(e),A("")},children:[r.jsx(p.i4,{children:r.jsx(p.ki,{placeholder:"Pilih gateway"})}),(0,r.jsxs)(p.Bw,{children:[r.jsx(p.Ql,{value:"TRIPAY",children:"Tripay"}),r.jsx(p.Ql,{value:"MIDTRANS",children:"Midtrans"}),r.jsx(p.Ql,{value:"XENDIT",children:"Xendit"})]})]})})]}),x&&(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{children:[r.jsx(o.ll,{children:"Metode Pembayaran"}),r.jsx(o.SZ,{children:"Pilih metode pembayaran yang Anda inginkan"})]}),r.jsx(o.aY,{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:w,onValueChange:A,children:r.jsx("div",{className:"space-y-3",children:P.map(e=>(0,r.jsxs)("div",{className:"flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/radio-group'");throw e.code="MODULE_NOT_FOUND",e}()),{value:e.id,id:e.id}),(0,r.jsxs)("div",{className:"flex items-center gap-3 flex-1",children:[r.jsx(e.icon,{className:"h-6 w-6 text-gray-600"}),(0,r.jsxs)("div",{children:[r.jsx(c._,{htmlFor:e.id,className:"font-medium cursor-pointer",children:e.name}),r.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.description})]})]})]},e.id))})})})]}),r.jsx(d.z,{type:"submit",className:"w-full",size:"lg",disabled:!m||!w||k,children:k?(0,r.jsxs)(r.Fragment,{children:[r.jsx(y.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Memproses..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(f.Z,{className:"h-4 w-4 mr-2"}),"Top Up ",m?(0,j.formatCurrency)(parseInt(m)):""]})})]})]})}},61351:(e,a,t)=>{"use strict";t.d(a,{Ol:()=>d,SZ:()=>l,Zb:()=>n,aY:()=>c,ll:()=>o});var r=t(95344),s=t(3729),i=t(91626);let n=s.forwardRef(({className:e,elevated:a=!1,padding:t="md",...s},n)=>r.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",a&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===t,"p-3":"sm"===t,"p-6":"md"===t,"p-8":"lg"===t},e),...s}));n.displayName="Card";let d=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));d.displayName="CardHeader";let o=s.forwardRef(({className:e,...a},t)=>r.jsx("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));o.displayName="CardTitle";let l=s.forwardRef(({className:e,...a},t)=>r.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));l.displayName="CardDescription";let c=s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));c.displayName="CardContent",s.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},92549:(e,a,t)=>{"use strict";t.d(a,{I:()=>n});var r=t(95344),s=t(3729),i=t(91626);let n=s.forwardRef(({className:e,type:a,...t},s)=>r.jsx("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));n.displayName="Input"},54572:(e,a,t)=>{"use strict";t.d(a,{_:()=>c});var r=t(95344),s=t(3729),i=t(62409),n=s.forwardRef((e,a)=>(0,r.jsx)(i.WV.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));n.displayName="Label";var d=t(92193),o=t(91626);let l=(0,d.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef(({className:e,...a},t)=>r.jsx(n,{ref:t,className:(0,o.cn)(l(),e),...a}));c.displayName=n.displayName},17470:(e,a,t)=>{"use strict";t.d(a,{Bw:()=>h,Ph:()=>c,Ql:()=>f,i4:()=>m,ki:()=>p});var r=t(95344),s=t(3729),i=t(32116),n=t(25390),d=t(12704),o=t(62312),l=t(91626);let c=i.fC;i.ZA;let p=i.B4,m=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(i.xz,{ref:s,className:(0,l.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...t,children:[a,r.jsx(i.JO,{asChild:!0,children:r.jsx(n.Z,{className:"h-4 w-4 opacity-50"})})]}));m.displayName=i.xz.displayName;let u=s.forwardRef(({className:e,...a},t)=>r.jsx(i.u_,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(d.Z,{className:"h-4 w-4"})}));u.displayName=i.u_.displayName;let x=s.forwardRef(({className:e,...a},t)=>r.jsx(i.$G,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:r.jsx(n.Z,{className:"h-4 w-4"})}));x.displayName=i.$G.displayName;let h=s.forwardRef(({className:e,children:a,position:t="popper",...s},n)=>r.jsx(i.h_,{children:(0,r.jsxs)(i.VY,{ref:n,className:(0,l.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:t,...s,children:[r.jsx(u,{}),r.jsx(i.l_,{className:(0,l.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),r.jsx(x,{})]})}));h.displayName=i.VY.displayName,s.forwardRef(({className:e,...a},t)=>r.jsx(i.__,{ref:t,className:(0,l.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=i.__.displayName;let f=s.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(i.ck,{ref:s,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.wU,{children:r.jsx(o.Z,{className:"h-4 w-4"})})}),r.jsx(i.eT,{children:a})]}));f.displayName=i.ck.displayName,s.forwardRef(({className:e,...a},t)=>r.jsx(i.Z0,{ref:t,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=i.Z0.displayName},63024:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},85674:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},53686:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]])},16588:(e,a,t)=>{"use strict";t.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,t(69224).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},1733:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>i,__esModule:()=>s,default:()=>n});let r=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\deposit\page.tsx`),{__esModule:s,$$typeof:i}=r,n=r.default}};var a=require("../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[1638,3088,4739,9205],()=>t(77509));module.exports=r})();