(()=>{var e={};e.id=4283,e.ids=[4283],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},63499:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var r=a(50482),n=a(69108),s=a(62563),o=a.n(s),i=a(68300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let c=["",{children:["uangtix",{children:["deposit",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,5647)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\deposit\\success\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,69361,23)),"next/dist/client/components/not-found-error"]}],d=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\deposit\\success\\page.tsx"],u="/uangtix/deposit/success/page",m={require:a,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/uangtix/deposit/success/page",pathname:"/uangtix/deposit/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55497:(e,t,a)=>{Promise.resolve().then(a.bind(a,89296))},16509:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,2583,23)),Promise.resolve().then(a.t.bind(a,26840,23)),Promise.resolve().then(a.t.bind(a,38771,23)),Promise.resolve().then(a.t.bind(a,13225,23)),Promise.resolve().then(a.t.bind(a,9295,23)),Promise.resolve().then(a.t.bind(a,43982,23))},23978:()=>{},89296:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>p});var r=a(95344),n=a(3729),s=a(8428),o=a(7060),i=a(25545),l=a(69224);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let c=(0,l.Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]),d=(0,l.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),u=(0,l.Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var m=a(33733),h=a(67925);function p(){let e=(0,s.useRouter)(),t=(0,s.useSearchParams)(),{toast:a}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[l,p]=(0,n.useState)(null),[x,f]=(0,n.useState)(!0),[b,N]=(0,n.useState)(!1),v=t.get("orderId");(0,n.useEffect)(()=>{v&&O()},[v]);let O=async()=>{try{f(!0);let e=await fetch(`/api/payments/${v}`),t=await e.json();t.success?p(t.data):a({title:"Error",description:"Gagal mengambil data pembayaran",variant:"destructive"})}catch(e){a({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}finally{f(!1)}},j=async()=>{if(l){N(!0);try{let e=await fetch(`/api/payments/${l.orderId}/status`),t=await e.json();t.success&&(p(e=>e?{...e,status:t.data.status}:null),"PAID"===t.data.status&&a({title:"Success",description:"Pembayaran berhasil! Saldo UangtiX Anda telah bertambah."}))}catch(e){a({title:"Error",description:"Gagal memeriksa status pembayaran",variant:"destructive"})}finally{N(!1)}}},g=(e,t)=>{navigator.clipboard.writeText(e),a({title:"Copied",description:`${t} berhasil disalin`})};return x?r.jsx("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),r.jsx("p",{children:"Memuat data pembayaran..."})]})}):l?(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[r.jsx("div",{className:"p-4 bg-primary/10 rounded-full w-fit mx-auto mb-4",children:(e=>{switch(e){case"PAID":return r.jsx(o.Z,{className:"h-5 w-5 text-green-500"});case"PENDING":return r.jsx(i.Z,{className:"h-5 w-5 text-yellow-500"});default:return r.jsx(i.Z,{className:"h-5 w-5 text-gray-500"})}})(l.status)}),r.jsx("h1",{className:"text-2xl font-bold mb-2",children:"PAID"===l.status?"Pembayaran Berhasil!":"Menunggu Pembayaran"}),r.jsx("p",{className:"text-gray-600",children:"PAID"===l.status?"Saldo UangtiX Anda telah bertambah":"Silakan selesaikan pembayaran Anda"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mb-6",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Detail Pembayaran"}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:["Order ID: ",l.orderId]})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:(e=>{switch(e){case"PAID":return"success";case"PENDING":return"warning";case"EXPIRED":case"FAILED":return"destructive";default:return"secondary"}})(l.status),children:l.status})]})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Jumlah"}),r.jsx("p",{className:"font-semibold text-lg",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l.amount)})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Gateway"}),r.jsx("p",{className:"font-semibold",children:l.gateway})]}),(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Tanggal"}),r.jsx("p",{className:"font-semibold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l.createdAt)})]}),l.expiredAt&&(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-gray-600",children:"Kedaluwarsa"}),r.jsx("p",{className:"font-semibold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(l.expiredAt)})]})]}),l.virtualAccount&&r.jsx("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:"Virtual Account"}),r.jsx("p",{className:"font-mono font-semibold text-blue-800 dark:text-blue-200",children:l.virtualAccount})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>g(l.virtualAccount,"Virtual Account"),children:r.jsx(d,{className:"h-4 w-4"})})]})}),l.qrCode&&(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Scan QR Code"}),r.jsx("img",{src:l.qrCode,alt:"QR Code",className:"mx-auto border rounded-lg",style:{maxWidth:"200px"}})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:["PENDING"===l.status&&(0,r.jsxs)(r.Fragment,{children:[l.paymentUrl&&(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"w-full",size:"lg",onClick:()=>window.open(l.paymentUrl,"_blank"),children:[r.jsx(u,{className:"h-4 w-4 mr-2"}),"Lanjutkan Pembayaran"]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"w-full",onClick:j,disabled:b,children:b?(0,r.jsxs)(r.Fragment,{children:[r.jsx(m.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Memeriksa..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(m.Z,{className:"h-4 w-4 mr-2"}),"Periksa Status Pembayaran"]})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"PAID"===l.status?"default":"outline",className:"w-full",onClick:()=>e.push("/uangtix"),children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"PAID"===l.status?"Lihat Saldo UangtiX":"Kembali ke UangtiX"]})]}),"PENDING"===l.status&&r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mt-6 border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6",children:[r.jsx("h3",{className:"font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:"Instruksi Pembayaran:"}),(0,r.jsxs)("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[r.jsx("li",{children:"• Selesaikan pembayaran sebelum waktu kedaluwarsa"}),r.jsx("li",{children:"• Gunakan nominal yang tepat sesuai yang tertera"}),r.jsx("li",{children:"• Saldo akan otomatis bertambah setelah pembayaran dikonfirmasi"}),r.jsx("li",{children:"• Jika ada kendala, hubungi customer service"})]})]})})]}):r.jsx("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6 text-center",children:[r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Data tidak ditemukan"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Data pembayaran tidak ditemukan atau sudah kedaluwarsa"}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>e.push("/uangtix"),children:[r.jsx(c,{className:"h-4 w-4 mr-2"}),"Kembali ke UangtiX"]})]})})})}(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},69224:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var r=a(3729),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(e,t)=>{let a=(0,r.forwardRef)(({color:a="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:l,className:c="",children:d,...u},m)=>(0,r.createElement)("svg",{ref:m,...n,width:o,height:o,stroke:a,strokeWidth:l?24*Number(i)/Number(o):i,className:["lucide",`lucide-${s(e)}`,c].join(" "),...u},[...t.map(([e,t])=>(0,r.createElement)(e,t)),...Array.isArray(d)?d:[d]]));return a.displayName=`${e}`,a}},7060:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},33733:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},67925:(e,t,a)=>{"use strict";a.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,a(69224).Z)("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]])},8428:(e,t,a)=>{"use strict";var r=a(14767);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},82917:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c,metadata:()=>l});var r=a(25036),n=a(450),s=a.n(n),o=a(14824),i=a.n(o);a(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let l={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function c({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${s().variable} ${i().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},5647:(e,t,a)=>{"use strict";a.r(t),a.d(t,{$$typeof:()=>s,__esModule:()=>n,default:()=>o});let r=(0,a(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\deposit\success\page.tsx`),{__esModule:n,$$typeof:s}=r,o=r.default},67272:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[1638,3293],()=>a(63499));module.exports=r})();