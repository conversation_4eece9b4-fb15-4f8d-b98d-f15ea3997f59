(()=>{var e={};e.id=4283,e.ids=[4283],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},63499:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c});var s=t(50482),r=t(69108),i=t(62563),n=t.n(i),l=t(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);t.d(a,d);let c=["",{children:["uangtix",{children:["deposit",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5647)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\deposit\\success\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\deposit\\success\\page.tsx"],u="/uangtix/deposit/success/page",x={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/uangtix/deposit/success/page",pathname:"/uangtix/deposit/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55497:(e,a,t)=>{Promise.resolve().then(t.bind(t,63166))},63166:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>f});var s=t(95344),r=t(3729),i=t(8428),n=t(16212),l=t(61351),d=t(69436),c=t(7060),o=t(25545),u=t(72086),x=t(69224);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let m=(0,x.Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),p=(0,x.Z)("ExternalLink",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]);var h=t(33733),y=t(67925),g=t(30692),b=t(91626);function f(){let e=(0,i.useRouter)(),a=(0,i.useSearchParams)(),{toast:t}=(0,g.pm)(),[x,f]=(0,r.useState)(null),[j,v]=(0,r.useState)(!0),[w,N]=(0,r.useState)(!1),k=a.get("orderId");(0,r.useEffect)(()=>{k&&P()},[k]);let P=async()=>{try{v(!0);let e=await fetch(`/api/payments/${k}`),a=await e.json();a.success?f(a.data):t({title:"Error",description:"Gagal mengambil data pembayaran",variant:"destructive"})}catch(e){t({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}finally{v(!1)}},D=async()=>{if(x){N(!0);try{let e=await fetch(`/api/payments/${x.orderId}/status`),a=await e.json();a.success&&(f(e=>e?{...e,status:a.data.status}:null),"PAID"===a.data.status&&t({title:"Success",description:"Pembayaran berhasil! Saldo UangtiX Anda telah bertambah."}))}catch(e){t({title:"Error",description:"Gagal memeriksa status pembayaran",variant:"destructive"})}finally{N(!1)}}},q=(e,a)=>{navigator.clipboard.writeText(e),t({title:"Copied",description:`${a} berhasil disalin`})};return j?s.jsx("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),s.jsx("p",{children:"Memuat data pembayaran..."})]})}):x?(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("div",{className:"p-4 bg-primary/10 rounded-full w-fit mx-auto mb-4",children:(e=>{switch(e){case"PAID":return s.jsx(c.Z,{className:"h-5 w-5 text-green-500"});case"PENDING":return s.jsx(o.Z,{className:"h-5 w-5 text-yellow-500"});default:return s.jsx(o.Z,{className:"h-5 w-5 text-gray-500"})}})(x.status)}),s.jsx("h1",{className:"text-2xl font-bold mb-2",children:"PAID"===x.status?"Pembayaran Berhasil!":"Menunggu Pembayaran"}),s.jsx("p",{className:"text-gray-600",children:"PAID"===x.status?"Saldo UangtiX Anda telah bertambah":"Silakan selesaikan pembayaran Anda"})]}),(0,s.jsxs)(l.Zb,{className:"mb-6",children:[s.jsx(l.Ol,{children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx(l.ll,{children:"Detail Pembayaran"}),(0,s.jsxs)(l.SZ,{children:["Order ID: ",x.orderId]})]}),s.jsx(d.C,{variant:(e=>{switch(e){case"PAID":return"success";case"PENDING":return"warning";case"EXPIRED":case"FAILED":return"destructive";default:return"secondary"}})(x.status),children:x.status})]})}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-gray-600",children:"Jumlah"}),s.jsx("p",{className:"font-semibold text-lg",children:(0,b.formatCurrency)(x.amount)})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-gray-600",children:"Gateway"}),s.jsx("p",{className:"font-semibold",children:x.gateway})]}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-gray-600",children:"Tanggal"}),s.jsx("p",{className:"font-semibold",children:(0,b.formatDate)(x.createdAt)})]}),x.expiredAt&&(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-gray-600",children:"Kedaluwarsa"}),s.jsx("p",{className:"font-semibold",children:(0,b.formatDate)(x.expiredAt)})]})]}),x.virtualAccount&&s.jsx("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-sm text-blue-600 dark:text-blue-400",children:"Virtual Account"}),s.jsx("p",{className:"font-mono font-semibold text-blue-800 dark:text-blue-200",children:x.virtualAccount})]}),s.jsx(n.z,{variant:"outline",size:"sm",onClick:()=>q(x.virtualAccount,"Virtual Account"),children:s.jsx(m,{className:"h-4 w-4"})})]})}),x.qrCode&&(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-600 mb-2",children:"Scan QR Code"}),s.jsx("img",{src:x.qrCode,alt:"QR Code",className:"mx-auto border rounded-lg",style:{maxWidth:"200px"}})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:["PENDING"===x.status&&(0,s.jsxs)(s.Fragment,{children:[x.paymentUrl&&(0,s.jsxs)(n.z,{className:"w-full",size:"lg",onClick:()=>window.open(x.paymentUrl,"_blank"),children:[s.jsx(p,{className:"h-4 w-4 mr-2"}),"Lanjutkan Pembayaran"]}),s.jsx(n.z,{variant:"outline",className:"w-full",onClick:D,disabled:w,children:w?(0,s.jsxs)(s.Fragment,{children:[s.jsx(h.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Memeriksa..."]}):(0,s.jsxs)(s.Fragment,{children:[s.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Periksa Status Pembayaran"]})})]}),(0,s.jsxs)(n.z,{variant:"PAID"===x.status?"default":"outline",className:"w-full",onClick:()=>e.push("/uangtix"),children:[s.jsx(y.Z,{className:"h-4 w-4 mr-2"}),"PAID"===x.status?"Lihat Saldo UangtiX":"Kembali ke UangtiX"]})]}),"PENDING"===x.status&&s.jsx(l.Zb,{className:"mt-6 border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20",children:(0,s.jsxs)(l.aY,{className:"pt-6",children:[s.jsx("h3",{className:"font-medium text-yellow-800 dark:text-yellow-200 mb-2",children:"Instruksi Pembayaran:"}),(0,s.jsxs)("ul",{className:"text-sm text-yellow-700 dark:text-yellow-300 space-y-1",children:[s.jsx("li",{children:"• Selesaikan pembayaran sebelum waktu kedaluwarsa"}),s.jsx("li",{children:"• Gunakan nominal yang tepat sesuai yang tertera"}),s.jsx("li",{children:"• Saldo akan otomatis bertambah setelah pembayaran dikonfirmasi"}),s.jsx("li",{children:"• Jika ada kendala, hubungi customer service"})]})]})})]}):s.jsx("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:s.jsx(l.Zb,{children:(0,s.jsxs)(l.aY,{className:"pt-6 text-center",children:[s.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Data tidak ditemukan"}),s.jsx("p",{className:"text-gray-600 mb-4",children:"Data pembayaran tidak ditemukan atau sudah kedaluwarsa"}),(0,s.jsxs)(n.z,{onClick:()=>e.push("/uangtix"),children:[s.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Kembali ke UangtiX"]})]})})})}},69436:(e,a,t)=>{"use strict";t.d(a,{C:()=>l});var s=t(95344);t(3729);var r=t(92193),i=t(91626);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:a,...t}){return s.jsx("div",{className:(0,i.cn)(n({variant:a}),e),...t})}},61351:(e,a,t)=>{"use strict";t.d(a,{Ol:()=>l,SZ:()=>c,Zb:()=>n,aY:()=>o,ll:()=>d});var s=t(95344),r=t(3729),i=t(91626);let n=r.forwardRef(({className:e,elevated:a=!1,padding:t="md",...r},n)=>s.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",a&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===t,"p-3":"sm"===t,"p-6":"md"===t,"p-8":"lg"===t},e),...r}));n.displayName="Card";let l=r.forwardRef(({className:e,...a},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...a},t)=>s.jsx("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...a},t)=>s.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...a},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));o.displayName="CardContent",r.forwardRef(({className:e,...a},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},7060:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},72086:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},33733:(e,a,t)=>{"use strict";t.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,t(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},5647:(e,a,t)=>{"use strict";t.r(a),t.d(a,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let s=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\deposit\success\page.tsx`),{__esModule:r,$$typeof:i}=s,n=s.default}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[1638,3088,9205],()=>t(63499));module.exports=s})();