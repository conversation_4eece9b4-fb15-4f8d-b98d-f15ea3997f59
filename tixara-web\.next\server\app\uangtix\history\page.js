(()=>{var e={};e.id=6550,e.ids=[6550],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19183:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>d});var r=n(50482),a=n(69108),o=n(62563),s=n.n(o),i=n(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);n.d(t,c);let d=["",{children:["uangtix",{children:["history",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,40574)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\history\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\history\\page.tsx"],u="/uangtix/history/page",m={require:n,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/uangtix/history/page",pathname:"/uangtix/history",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},86544:(e,t,n)=>{Promise.resolve().then(n.bind(n,76929))},16509:(e,t,n)=>{Promise.resolve().then(n.t.bind(n,2583,23)),Promise.resolve().then(n.t.bind(n,26840,23)),Promise.resolve().then(n.t.bind(n,38771,23)),Promise.resolve().then(n.t.bind(n,13225,23)),Promise.resolve().then(n.t.bind(n,9295,23)),Promise.resolve().then(n.t.bind(n,43982,23))},23978:()=>{},76929:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>x});var r=n(95344),a=n(3729),o=n(47674),s=n(8428);(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}();var i=n(43617),c=n(29843),d=n(7060),l=n(25545),u=n(73229),m=n(63024),h=n(96885),O=n(28765),p=n(42739);function x(){let{data:e}=(0,o.useSession)(),t=(0,s.useRouter)(),{toast:n}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[x,f]=(0,a.useState)([]),[N,v]=(0,a.useState)(!0),[j,E]=(0,a.useState)(""),[D,g]=(0,a.useState)("ALL"),[U,_]=(0,a.useState)("ALL"),[w,b]=(0,a.useState)(1),[y,T]=(0,a.useState)(!0),[L,C]=(0,a.useState)(!1),M=async(e=1,t=!1)=>{try{1===e?v(!0):C(!0);let n=new URLSearchParams({page:e.toString(),limit:"20",search:j,type:"ALL"!==D?D:"",status:"ALL"!==U?U:""}),r=await fetch(`/api/uangtix/transactions?${n}`),a=await r.json();a.success&&(t||1===e?f(a.data):f(e=>[...e,...a.data]),T(20===a.data.length))}catch(e){n({title:"Error",description:"Gagal mengambil data transaksi",variant:"destructive"})}finally{v(!1),C(!1)}};(0,a.useEffect)(()=>{e?.user&&(b(1),M(1,!0))},[e,j,D,U]);let k=(e,t)=>"DEPOSIT"===e||t>0?r.jsx(i.Z,{className:"h-5 w-5 text-green-500"}):r.jsx(c.Z,{className:"h-5 w-5 text-red-500"}),F=e=>{switch(e){case"SUCCESS":return r.jsx(d.Z,{className:"h-4 w-4 text-green-500"});case"PENDING":return r.jsx(l.Z,{className:"h-4 w-4 text-yellow-500"});case"FAILED":return r.jsx(u.Z,{className:"h-4 w-4 text-red-500"});default:return r.jsx(l.Z,{className:"h-4 w-4 text-gray-500"})}},S=e=>{switch(e){case"SUCCESS":return"success";case"PENDING":return"warning";case"FAILED":return"destructive";default:return"secondary"}},P=e=>{switch(e){case"DEPOSIT":return"Top Up";case"WITHDRAW":return"Withdraw";case"TRANSFER":return"Transfer";case"PAYMENT":return"Pembayaran";case"REFUND":return"Refund";case"COMMISSION":return"Komisi";default:return e}},A=async()=>{try{let e=new URLSearchParams({search:j,type:"ALL"!==D?D:"",status:"ALL"!==U?U:"",export:"true"}),t=await fetch(`/api/uangtix/transactions?${e}`),r=await t.blob(),a=window.URL.createObjectURL(r),o=document.createElement("a");o.href=a,o.download=`uangtix-transactions-${new Date().toISOString().split("T")[0]}.csv`,document.body.appendChild(o),o.click(),window.URL.revokeObjectURL(a),document.body.removeChild(o),n({title:"Success",description:"Data transaksi berhasil diexport"})}catch(e){n({title:"Error",description:"Gagal export data transaksi",variant:"destructive"})}};return(0,r.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"sm",onClick:()=>t.back(),children:r.jsx(m.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[r.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:r.jsx(l.Z,{className:"h-6 w-6 text-primary"})}),(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold",children:"Riwayat Transaksi"}),r.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Semua transaksi UangtiX Anda"})]})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mb-6",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Filter & Pencarian"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Filter transaksi berdasarkan kriteria"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:A,children:[r.jsx(h.Z,{className:"h-4 w-4 mr-2"}),"Export"]})]})}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"relative",children:[r.jsx(O.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Cari transaksi...",value:j,onChange:e=>E(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:D,onValueChange:g,children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Semua Tipe"})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"ALL",children:"Semua Tipe"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"DEPOSIT",children:"Top Up"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"WITHDRAW",children:"Withdraw"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"TRANSFER",children:"Transfer"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"PAYMENT",children:"Pembayaran"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"REFUND",children:"Refund"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"COMMISSION",children:"Komisi"})]})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:U,onValueChange:_,children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Semua Status"})}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"ALL",children:"Semua Status"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"SUCCESS",children:"Berhasil"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"PENDING",children:"Pending"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"FAILED",children:"Gagal"}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/select'");throw e.code="MODULE_NOT_FOUND",e}()),{value:"CANCELLED",children:"Dibatalkan"})]})]})]})})]}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Transaksi"}),(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[x.length," transaksi ditemukan"]})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:N?r.jsx("div",{className:"flex items-center justify-center py-8",children:r.jsx(p.Z,{className:"h-8 w-8 animate-spin"})}):0===x.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[r.jsx(l.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Tidak ada transaksi"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Belum ada transaksi yang sesuai dengan filter"})]}):(0,r.jsxs)("div",{className:"space-y-4",children:[x.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[r.jsx("div",{className:"p-2 bg-gray-100 dark:bg-gray-800 rounded-lg",children:k(e.type,e.amount)}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[r.jsx("h4",{className:"font-medium",children:e.description}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",className:"text-xs",children:P(e.type)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400",children:[r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.createdAt)}),r.jsx("span",{children:"•"}),r.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.createdAt)}),e.payment&&(0,r.jsxs)(r.Fragment,{children:[r.jsx("span",{children:"•"}),r.jsx("span",{children:e.payment.gateway})]})]})]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,r.jsxs)("span",{className:`font-medium ${"DEPOSIT"===e.type||e.amount>0?"text-green-600":"text-red-600"}`,children:["DEPOSIT"===e.type||e.amount>0?"+":"-",Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(Math.abs(e.amount))]}),F(e.status)]}),r.jsx("div",{className:"flex items-center gap-2",children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:S(e.status),className:"text-xs",children:e.status})}),(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Saldo: ",Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.balanceAfter)]})]})]},e.id)),y&&r.jsx("div",{className:"text-center pt-4",children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",onClick:()=>{let e=w+1;b(e),M(e)},disabled:L,children:L?(0,r.jsxs)(r.Fragment,{children:[r.jsx(p.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Memuat..."]}):"Muat Lebih Banyak"})})]})})]})]})}(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},43617:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ArrowDownLeft",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]])},63024:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29843:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},7060:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},96885:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},28765:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},73229:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,n(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},82917:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d,metadata:()=>c});var r=n(25036),a=n(450),o=n.n(a),s=n(14824),i=n.n(s);n(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return r.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:r.jsx("body",{className:`${o().variable} ${i().variable} font-sans antialiased`,children:r.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,r.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),r.jsx("main",{className:"flex-1",children:e}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},40574:(e,t,n)=>{"use strict";n.r(t),n.d(t,{$$typeof:()=>o,__esModule:()=>a,default:()=>s});let r=(0,n(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\history\page.tsx`),{__esModule:a,$$typeof:o}=r,s=r.default},67272:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[1638,3293,5504],()=>n(19183));module.exports=r})();