(()=>{var e={};e.id=9299,e.ids=[9299],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},35436:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>d});var a=t(50482),r=t(69108),i=t(62563),n=t.n(i),l=t(68300),c={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);t.d(s,c);let d=["",{children:["uangtix",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,2081)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\page.tsx"],x="/uangtix/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/uangtix/page",pathname:"/uangtix",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96255:(e,s,t)=>{Promise.resolve().then(t.bind(t,32750))},32750:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>Z});var a=t(95344),r=t(3729),i=t(47674),n=t(8428),l=t(16212),c=t(61351),d=t(69436),o=t(43617),x=t(29843),u=t(7060),m=t(25545),h=t(73229),p=t(42739),g=t(67925),f=t(33733),y=t(1222),j=t(53148),v=t(51838),N=t(36135),w=t(96885),b=t(30692),k=t(91626);function Z(){let{data:e,status:s}=(0,i.useSession)(),t=(0,n.useRouter)(),{toast:Z}=(0,b.pm)(),[C,q]=(0,r.useState)(0),[M,P]=(0,r.useState)([]),[S,z]=(0,r.useState)(!0),[D,_]=(0,r.useState)(!0),[E,T]=(0,r.useState)(!1);(0,r.useEffect)(()=>{if("loading"!==s&&!e?.user){t.push("/auth/login");return}},[e,s,t]);let R=async()=>{try{z(!0);let[e,s]=await Promise.all([fetch("/api/uangtix/balance"),fetch("/api/uangtix/transactions?limit=10")]),t=await e.json(),a=await s.json();t.success&&q(t.data.balance),a.success&&P(a.data)}catch(e){Z({title:"Error",description:"Gagal mengambil data UangtiX",variant:"destructive"})}finally{z(!1)}},U=async()=>{T(!0),await R(),T(!1)};(0,r.useEffect)(()=>{e?.user&&R()},[e]);let A=(e,s)=>"DEPOSIT"===e||s>0?a.jsx(o.Z,{className:"h-4 w-4 text-green-500"}):a.jsx(x.Z,{className:"h-4 w-4 text-red-500"}),I=e=>{switch(e){case"SUCCESS":return a.jsx(u.Z,{className:"h-4 w-4 text-green-500"});case"PENDING":return a.jsx(m.Z,{className:"h-4 w-4 text-yellow-500"});case"FAILED":return a.jsx(h.Z,{className:"h-4 w-4 text-red-500"});default:return a.jsx(m.Z,{className:"h-4 w-4 text-gray-500"})}},L=e=>{switch(e){case"SUCCESS":return"success";case"PENDING":return"warning";case"FAILED":return"destructive";default:return"secondary"}};return"loading"===s||S?a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx(p.Z,{className:"h-8 w-8 animate-spin"})}):e?.user?(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("div",{className:"p-3 bg-primary/10 rounded-lg",children:a.jsx(g.Z,{className:"h-8 w-8 text-primary"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"UangtiX"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Dompet digital TiXara"})]})]}),(0,a.jsxs)(l.z,{variant:"outline",size:"sm",onClick:U,disabled:E,children:[a.jsx(f.Z,{className:`h-4 w-4 mr-2 ${E?"animate-spin":""}`}),"Refresh"]})]}),a.jsx(c.Zb,{className:"mb-8 bg-gradient-to-r from-primary to-primary/80 text-white",children:a.jsx(c.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white/80 text-sm mb-2",children:"Saldo UangtiX"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[D?a.jsx("h2",{className:"text-3xl font-bold",children:(0,k.formatCurrency)(C)}):a.jsx("h2",{className:"text-3xl font-bold",children:"Rp ••••••••"}),a.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>_(!D),className:"text-white hover:bg-white/20",children:D?a.jsx(y.Z,{className:"h-4 w-4"}):a.jsx(j.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.z,{variant:"secondary",size:"sm",onClick:()=>t.push("/uangtix/deposit"),children:[a.jsx(v.Z,{className:"h-4 w-4 mr-1"}),"Top Up"]}),(0,a.jsxs)(l.z,{variant:"secondary",size:"sm",onClick:()=>t.push("/uangtix/transfer"),children:[a.jsx(N.Z,{className:"h-4 w-4 mr-1"}),"Transfer"]})]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8",children:[a.jsx(c.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t.push("/uangtix/deposit"),children:(0,a.jsxs)(c.aY,{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-green-100 dark:bg-green-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(v.Z,{className:"h-6 w-6 text-green-600"})}),a.jsx("h3",{className:"font-medium",children:"Top Up"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Isi saldo"})]})}),a.jsx(c.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t.push("/uangtix/transfer"),children:(0,a.jsxs)(c.aY,{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(N.Z,{className:"h-6 w-6 text-blue-600"})}),a.jsx("h3",{className:"font-medium",children:"Transfer"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Kirim uang"})]})}),a.jsx(c.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t.push("/uangtix/withdraw"),children:(0,a.jsxs)(c.aY,{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(w.Z,{className:"h-6 w-6 text-orange-600"})}),a.jsx("h3",{className:"font-medium",children:"Withdraw"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Tarik dana"})]})}),a.jsx(c.Zb,{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>t.push("/uangtix/history"),children:(0,a.jsxs)(c.aY,{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(m.Z,{className:"h-6 w-6 text-purple-600"})}),a.jsx("h3",{className:"font-medium",children:"Riwayat"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Lihat semua"})]})})]}),(0,a.jsxs)(c.Zb,{children:[a.jsx(c.Ol,{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(c.ll,{children:"Transaksi Terbaru"}),a.jsx(c.SZ,{children:"10 transaksi terakhir"})]}),a.jsx(l.z,{variant:"outline",size:"sm",onClick:()=>t.push("/uangtix/history"),children:"Lihat Semua"})]})}),a.jsx(c.aY,{children:0===M.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(g.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Belum ada transaksi"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Mulai gunakan UangtiX untuk bertransaksi"}),(0,a.jsxs)(l.z,{onClick:()=>t.push("/uangtix/deposit"),children:[a.jsx(v.Z,{className:"h-4 w-4 mr-2"}),"Top Up Sekarang"]})]}):a.jsx("div",{className:"space-y-4",children:M.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[A(e.type,e.amount),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium",children:e.description}),a.jsx("p",{className:"text-sm text-gray-600",children:(0,k.formatRelativeTime)(e.createdAt)})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:`font-medium ${"DEPOSIT"===e.type||e.amount>0?"text-green-600":"text-red-600"}`,children:["DEPOSIT"===e.type||e.amount>0?"+":"-",(0,k.formatCurrency)(Math.abs(e.amount))]}),I(e.status)]}),a.jsx(d.C,{variant:L(e.status),className:"text-xs",children:e.status})]})]},e.id))})})]})]}):null}},69436:(e,s,t)=>{"use strict";t.d(s,{C:()=>l});var a=t(95344);t(3729);var r=t(92193),i=t(91626);let n=(0,r.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:s,...t}){return a.jsx("div",{className:(0,i.cn)(n({variant:s}),e),...t})}},61351:(e,s,t)=>{"use strict";t.d(s,{Ol:()=>l,SZ:()=>d,Zb:()=>n,aY:()=>o,ll:()=>c});var a=t(95344),r=t(3729),i=t(91626);let n=r.forwardRef(({className:e,elevated:s=!1,padding:t="md",...r},n)=>a.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",s&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===t,"p-3":"sm"===t,"p-6":"md"===t,"p-8":"lg"===t},e),...r}));n.displayName="Card";let l=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...s}));l.displayName="CardHeader";let c=r.forwardRef(({className:e,...s},t)=>a.jsx("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));c.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},t)=>a.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>a.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},43617:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("ArrowDownLeft",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]])},29843:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},7060:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},96885:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},1222:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},51838:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33733:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},36135:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},73229:(e,s,t)=>{"use strict";t.d(s,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,t(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},2081:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>i,__esModule:()=>r,default:()=>n});let a=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\page.tsx`),{__esModule:r,$$typeof:i}=a,n=a.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[1638,3088,9205],()=>t(35436));module.exports=a})();