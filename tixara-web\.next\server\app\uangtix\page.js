(()=>{var e={};e.id=9299,e.ids=[9299],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},35436:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>d});var a=r(50482),n=r(69108),s=r(62563),o=r.n(s),i=r(68300),c={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);r.d(t,c);let d=["",{children:["uangtix",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2081)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\page.tsx"],u="/uangtix/page",m={require:r,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/uangtix/page",pathname:"/uangtix",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96255:(e,t,r)=>{Promise.resolve().then(r.bind(r,32750))},16509:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,26840,23)),Promise.resolve().then(r.t.bind(r,38771,23)),Promise.resolve().then(r.t.bind(r,13225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,43982,23))},23978:()=>{},32750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>j});var a=r(95344),n=r(3729),s=r(47674),o=r(8428);(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}();var i=r(43617),c=r(29843),d=r(7060),l=r(25545),u=r(73229),m=r(42739),h=r(67925),x=r(33733),p=r(1222),f=r(53148),O=r(51838),N=r(36135),g=r(96885);function j(){let{data:e,status:t}=(0,s.useSession)(),r=(0,o.useRouter)(),{toast:j}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[v,w]=(0,n.useState)(0),[y,b]=(0,n.useState)([]),[E,D]=(0,n.useState)(!0),[_,U]=(0,n.useState)(!0),[k,T]=(0,n.useState)(!1);(0,n.useEffect)(()=>{if("loading"!==t&&!e?.user){r.push("/auth/login");return}},[e,t,r]);let M=async()=>{try{D(!0);let[e,t]=await Promise.all([fetch("/api/uangtix/balance"),fetch("/api/uangtix/transactions?limit=10")]),r=await e.json(),a=await t.json();r.success&&w(r.data.balance),a.success&&b(a.data)}catch(e){j({title:"Error",description:"Gagal mengambil data UangtiX",variant:"destructive"})}finally{D(!1)}},C=async()=>{T(!0),await M(),T(!1)};(0,n.useEffect)(()=>{e?.user&&M()},[e]);let L=(e,t)=>"DEPOSIT"===e||t>0?a.jsx(i.Z,{className:"h-4 w-4 text-green-500"}):a.jsx(c.Z,{className:"h-4 w-4 text-red-500"}),Z=e=>{switch(e){case"SUCCESS":return a.jsx(d.Z,{className:"h-4 w-4 text-green-500"});case"PENDING":return a.jsx(l.Z,{className:"h-4 w-4 text-yellow-500"});case"FAILED":return a.jsx(u.Z,{className:"h-4 w-4 text-red-500"});default:return a.jsx(l.Z,{className:"h-4 w-4 text-gray-500"})}},F=e=>{switch(e){case"SUCCESS":return"success";case"PENDING":return"warning";case"FAILED":return"destructive";default:return"secondary"}};return"loading"===t||E?a.jsx("div",{className:"flex items-center justify-center min-h-screen",children:a.jsx(m.Z,{className:"h-8 w-8 animate-spin"})}):e?.user?(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-4xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[a.jsx("div",{className:"p-3 bg-primary/10 rounded-lg",children:a.jsx(h.Z,{className:"h-8 w-8 text-primary"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"UangtiX"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400 mt-2",children:"Dompet digital TiXara"})]})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:C,disabled:k,children:[a.jsx(x.Z,{className:`h-4 w-4 mr-2 ${k?"animate-spin":""}`}),"Refresh"]})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mb-8 bg-gradient-to-r from-primary to-primary/80 text-white",children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white/80 text-sm mb-2",children:"Saldo UangtiX"}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[_?a.jsx("h2",{className:"text-3xl font-bold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(v)}):a.jsx("h2",{className:"text-3xl font-bold",children:"Rp ••••••••"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"sm",onClick:()=>U(!_),className:"text-white hover:bg-white/20",children:_?a.jsx(p.Z,{className:"h-4 w-4"}):a.jsx(f.Z,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",size:"sm",onClick:()=>r.push("/uangtix/deposit"),children:[a.jsx(O.Z,{className:"h-4 w-4 mr-1"}),"Top Up"]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"secondary",size:"sm",onClick:()=>r.push("/uangtix/transfer"),children:[a.jsx(N.Z,{className:"h-4 w-4 mr-1"}),"Transfer"]})]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-8",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>r.push("/uangtix/deposit"),children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-green-100 dark:bg-green-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(O.Z,{className:"h-6 w-6 text-green-600"})}),a.jsx("h3",{className:"font-medium",children:"Top Up"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Isi saldo"})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>r.push("/uangtix/transfer"),children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(N.Z,{className:"h-6 w-6 text-blue-600"})}),a.jsx("h3",{className:"font-medium",children:"Transfer"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Kirim uang"})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>r.push("/uangtix/withdraw"),children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(g.Z,{className:"h-6 w-6 text-orange-600"})}),a.jsx("h3",{className:"font-medium",children:"Withdraw"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Tarik dana"})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"cursor-pointer hover:shadow-md transition-shadow",onClick:()=>r.push("/uangtix/history"),children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6 text-center",children:[a.jsx("div",{className:"p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg w-fit mx-auto mb-3",children:a.jsx(l.Z,{className:"h-6 w-6 text-purple-600"})}),a.jsx("h3",{className:"font-medium",children:"Riwayat"}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Lihat semua"})]})})]}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Transaksi Terbaru"}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"10 transaksi terakhir"})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"outline",size:"sm",onClick:()=>r.push("/uangtix/history"),children:"Lihat Semua"})]})}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:0===y.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(h.Z,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Belum ada transaksi"}),a.jsx("p",{className:"text-gray-600 mb-4",children:"Mulai gunakan UangtiX untuk bertransaksi"}),(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{onClick:()=>r.push("/uangtix/deposit"),children:[a.jsx(O.Z,{className:"h-4 w-4 mr-2"}),"Top Up Sekarang"]})]}):a.jsx("div",{className:"space-y-4",children:y.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[L(e.type,e.amount),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium",children:e.description}),a.jsx("p",{className:"text-sm text-gray-600",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e.createdAt)})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsxs)("span",{className:`font-medium ${"DEPOSIT"===e.type||e.amount>0?"text-green-600":"text-red-600"}`,children:["DEPOSIT"===e.type||e.amount>0?"+":"-",Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(Math.abs(e.amount))]}),Z(e.status)]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/badge'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:F(e.status),className:"text-xs",children:e.status})]})]},e.id))})})]})]}):null}(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}()},43617:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("ArrowDownLeft",[["path",{d:"M17 7 7 17",key:"15tmo1"}],["path",{d:"M17 17H7V7",key:"1org7z"}]])},29843:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},7060:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},25545:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},96885:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},1222:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},51838:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},33733:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},36135:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},67925:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]])},73229:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,r(69224).Z)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},82917:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var a=r(25036),n=r(450),s=r.n(n),o=r(14824),i=r.n(o);r(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return a.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:a.jsx("body",{className:`${s().variable} ${i().variable} font-sans antialiased`,children:a.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,a.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,a.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[a.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),a.jsx("main",{className:"flex-1",children:e}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),a.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},2081:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>s,__esModule:()=>n,default:()=>o});let a=(0,r(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\page.tsx`),{__esModule:n,$$typeof:s}=a,o=a.default},67272:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[1638,3293,5504],()=>r(35436));module.exports=a})();