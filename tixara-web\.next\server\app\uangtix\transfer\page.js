(()=>{var e={};e.id=5745,e.ids=[5745],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},50852:e=>{"use strict";e.exports=require("async_hooks")},32081:e=>{"use strict";e.exports=require("child_process")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},73292:e=>{"use strict";e.exports=require("fs/promises")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},76224:e=>{"use strict";e.exports=require("tty")},73837:e=>{"use strict";e.exports=require("util")},23461:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>o});var a=s(50482),t=s(69108),i=s(62563),n=s.n(i),l=s(68300),d={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(r,d);let o=["",{children:["uangtix",{children:["transfer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,29310)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\transfer\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,27950)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],c=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\transfer\\page.tsx"],u="/uangtix/transfer/page",m={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:t.x.APP_PAGE,page:"/uangtix/transfer/page",pathname:"/uangtix/transfer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},17843:(e,r,s)=>{Promise.resolve().then(s.bind(s,71164))},71164:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>v});var a=s(95344),t=s(3729),i=s(47674),n=s(8428),l=s(16212),d=s(61351),o=s(92549),c=s(54572),u=s(93601),m=s(63024),x=s(36135),p=s(67925),f=s(42739),h=s(18822),g=s(30692),j=s(91626);let b=[25e3,5e4,1e5,25e4,5e5];function v(){let{data:e}=(0,i.useSession)(),r=(0,n.useRouter)(),{toast:s}=(0,g.pm)(),[v,y]=(0,t.useState)(0),[N,w]=(0,t.useState)(""),[k,C]=(0,t.useState)(""),[S,_]=(0,t.useState)(null),[q,Z]=(0,t.useState)(""),[P,R]=(0,t.useState)(!1),[T,D]=(0,t.useState)(!1),[E,M]=(0,t.useState)([]);(0,t.useEffect)(()=>{let r=async()=>{try{let e=await fetch("/api/uangtix/balance"),r=await e.json();r.success&&y(r.data.balance)}catch(e){console.error("Error fetching balance:",e)}};e?.user&&r()},[e]);let I=e=>{w(e.replace(/[^0-9]/g,""))},z=e=>{w(e.toString())},A=async r=>{if(!r||r.length<3){M([]);return}D(!0);try{let s=await fetch(`/api/users/search?email=${encodeURIComponent(r)}`),a=await s.json();a.success&&M(a.data.filter(r=>r.id!==e?.user?.id))}catch(e){console.error("Error searching users:",e)}finally{D(!1)}},O=e=>{C(e),_(null);let r=setTimeout(()=>{A(e)},500);return()=>clearTimeout(r)},U=e=>{_(e),C(e.email),M([])},F=async a=>{if(a.preventDefault(),!N||!k||!q){s({title:"Error",description:"Mohon lengkapi semua field",variant:"destructive"});return}let t=parseInt(N);if(t<1e3){s({title:"Error",description:"Minimum transfer Rp 1.000",variant:"destructive"});return}if(t>v){s({title:"Error",description:"Saldo tidak mencukupi",variant:"destructive"});return}if(k===e?.user?.email){s({title:"Error",description:"Tidak dapat transfer ke diri sendiri",variant:"destructive"});return}R(!0);try{let e=await fetch("/api/uangtix/transfer",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:t,recipientEmail:k,description:q})}),a=await e.json();a.success?(s({title:"Success",description:"Transfer berhasil"}),r.push("/uangtix")):s({title:"Error",description:a.message||"Transfer gagal",variant:"destructive"})}catch(e){s({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}finally{R(!1)}};return(0,a.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[a.jsx(l.z,{variant:"ghost",size:"sm",onClick:()=>r.back(),children:a.jsx(m.Z,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:a.jsx(x.Z,{className:"h-6 w-6 text-primary"})}),(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-2xl font-bold",children:"Transfer UangtiX"}),a.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Kirim uang ke pengguna lain"})]})]})]}),a.jsx(d.Zb,{className:"mb-6 bg-gradient-to-r from-primary to-primary/80 text-white",children:a.jsx(d.aY,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"text-white/80 text-sm mb-1",children:"Saldo Anda"}),a.jsx("h2",{className:"text-2xl font-bold",children:(0,j.formatCurrency)(v)})]}),a.jsx(p.Z,{className:"h-8 w-8 text-white/80"})]})})}),(0,a.jsxs)("form",{onSubmit:F,className:"space-y-6",children:[(0,a.jsxs)(d.Zb,{children:[(0,a.jsxs)(d.Ol,{children:[a.jsx(d.ll,{children:"Penerima"}),a.jsx(d.SZ,{children:"Masukkan email penerima"})]}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(c._,{htmlFor:"recipient",children:"Email Penerima"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(o.I,{id:"recipient",type:"email",placeholder:"<EMAIL>",value:k,onChange:e=>O(e.target.value),className:"pr-10"}),T&&a.jsx(f.Z,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin"})]}),E.length>0&&a.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border rounded-lg shadow-lg max-h-60 overflow-y-auto",children:E.map(e=>a.jsx("div",{className:"p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b last:border-b-0",onClick:()=>U(e),children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:a.jsx(h.Z,{className:"h-4 w-4 text-primary"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.name}),a.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.email})]})]})},e.id))})]}),S&&a.jsx("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[a.jsx("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900/40 rounded-full flex items-center justify-center",children:a.jsx(h.Z,{className:"h-5 w-5 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-green-800 dark:text-green-200",children:S.name}),a.jsx("p",{className:"text-sm text-green-600 dark:text-green-400",children:S.email})]})]})})]})]}),(0,a.jsxs)(d.Zb,{children:[(0,a.jsxs)(d.Ol,{children:[a.jsx(d.ll,{children:"Jumlah Transfer"}),a.jsx(d.SZ,{children:"Minimum Rp 1.000"})]}),(0,a.jsxs)(d.aY,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[a.jsx(c._,{htmlFor:"amount",children:"Jumlah (Rp)"}),a.jsx(o.I,{id:"amount",type:"text",placeholder:"0",value:N?parseInt(N).toLocaleString("id-ID"):"",onChange:e=>I(e.target.value),className:"text-lg"})]}),(0,a.jsxs)("div",{children:[a.jsx(c._,{children:"Jumlah Cepat"}),a.jsx("div",{className:"grid grid-cols-3 gap-2 mt-2",children:b.map(e=>a.jsx(l.z,{type:"button",variant:"outline",size:"sm",onClick:()=>z(e),className:N===e.toString()?"border-primary":"",disabled:e>v,children:(0,j.formatCurrency)(e)},e))})]}),N&&(0,a.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{children:"Jumlah Transfer:"}),a.jsx("span",{className:"font-medium",children:(0,j.formatCurrency)(parseInt(N))})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[a.jsx("span",{children:"Biaya Admin:"}),a.jsx("span",{className:"font-medium",children:"Rp 0"})]}),a.jsx("hr",{className:"my-2"}),(0,a.jsxs)("div",{className:"flex justify-between font-medium",children:[a.jsx("span",{children:"Total:"}),a.jsx("span",{children:(0,j.formatCurrency)(parseInt(N))})]})]})]})]}),(0,a.jsxs)(d.Zb,{children:[(0,a.jsxs)(d.Ol,{children:[a.jsx(d.ll,{children:"Catatan"}),a.jsx(d.SZ,{children:"Tambahkan catatan untuk transfer ini"})]}),a.jsx(d.aY,{children:a.jsx(u.g,{placeholder:"Contoh: Bayar makan siang",value:q,onChange:e=>Z(e.target.value),rows:3})})]}),a.jsx(l.z,{type:"submit",className:"w-full",size:"lg",disabled:!N||!k||!q||P||parseInt(N)>v,children:P?(0,a.jsxs)(a.Fragment,{children:[a.jsx(f.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Memproses..."]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(x.Z,{className:"h-4 w-4 mr-2"}),"Transfer ",N?(0,j.formatCurrency)(parseInt(N)):""]})})]})]})}},61351:(e,r,s)=>{"use strict";s.d(r,{Ol:()=>l,SZ:()=>o,Zb:()=>n,aY:()=>c,ll:()=>d});var a=s(95344),t=s(3729),i=s(91626);let n=t.forwardRef(({className:e,elevated:r=!1,padding:s="md",...t},n)=>a.jsx("div",{ref:n,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground",r&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===s,"p-3":"sm"===s,"p-6":"md"===s,"p-8":"lg"===s},e),...t}));n.displayName="Card";let l=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));l.displayName="CardHeader";let d=t.forwardRef(({className:e,...r},s)=>a.jsx("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));d.displayName="CardTitle";let o=t.forwardRef(({className:e,...r},s)=>a.jsx("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",t.forwardRef(({className:e,...r},s)=>a.jsx("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},92549:(e,r,s)=>{"use strict";s.d(r,{I:()=>n});var a=s(95344),t=s(3729),i=s(91626);let n=t.forwardRef(({className:e,type:r,...s},t)=>a.jsx("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));n.displayName="Input"},54572:(e,r,s)=>{"use strict";s.d(r,{_:()=>c});var a=s(95344),t=s(3729),i=s(62409),n=t.forwardRef((e,r)=>(0,a.jsx)(i.WV.label,{...e,ref:r,onMouseDown:r=>{r.target.closest("button, input, select, textarea")||(e.onMouseDown?.(r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));n.displayName="Label";var l=s(92193),d=s(91626);let o=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=t.forwardRef(({className:e,...r},s)=>a.jsx(n,{ref:s,className:(0,d.cn)(o(),e),...r}));c.displayName=n.displayName},93601:(e,r,s)=>{"use strict";s.d(r,{g:()=>n});var a=s(95344),t=s(3729),i=s(91626);let n=t.forwardRef(({className:e,...r},s)=>a.jsx("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...r}));n.displayName="Textarea"},63024:(e,r,s)=>{"use strict";s.d(r,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},36135:(e,r,s)=>{"use strict";s.d(r,{Z:()=>a});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let a=(0,s(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},29310:(e,r,s)=>{"use strict";s.r(r),s.d(r,{$$typeof:()=>i,__esModule:()=>t,default:()=>n});let a=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\transfer\page.tsx`),{__esModule:t,$$typeof:i}=a,n=a.default}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),a=r.X(0,[1638,3088,9205],()=>s(23461));module.exports=a})();