(()=>{var e={};e.id=5745,e.ids=[5745],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},23461:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>l,routeModule:()=>h,tree:()=>d});var n=t(50482),a=t(69108),o=t(62563),i=t.n(o),s=t(68300),c={};for(let e in s)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>s[e]);t.d(r,c);let d=["",{children:["uangtix",{children:["transfer",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,29310)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\transfer\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,82917)),"D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],l=["D:\\Users\\Downloads\\tixara-platform\\tixara-web\\app\\uangtix\\transfer\\page.tsx"],u="/uangtix/transfer/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/uangtix/transfer/page",pathname:"/uangtix/transfer",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},17843:(e,r,t)=>{Promise.resolve().then(t.bind(t,71164))},16509:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,2583,23)),Promise.resolve().then(t.t.bind(t,26840,23)),Promise.resolve().then(t.t.bind(t,38771,23)),Promise.resolve().then(t.t.bind(t,13225,23)),Promise.resolve().then(t.t.bind(t,9295,23)),Promise.resolve().then(t.t.bind(t,43982,23))},23978:()=>{},71164:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>h});var n=t(95344),a=t(3729),o=t(47674),i=t(8428);(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}();var s=t(63024),c=t(36135),d=t(67925),l=t(42739),u=t(18822);(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e})(),function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}();let m=[25e3,5e4,1e5,25e4,5e5];function h(){let{data:e}=(0,o.useSession)(),r=(0,i.useRouter)(),{toast:t}=Object(function(){var e=Error("Cannot find module '@/hooks/use-toast'");throw e.code="MODULE_NOT_FOUND",e}())(),[h,p]=(0,a.useState)(0),[f,x]=(0,a.useState)(""),[O,N]=(0,a.useState)(""),[v,j]=(0,a.useState)(null),[g,b]=(0,a.useState)(""),[_,E]=(0,a.useState)(!1),[D,w]=(0,a.useState)(!1),[U,y]=(0,a.useState)([]);(0,a.useEffect)(()=>{let r=async()=>{try{let e=await fetch("/api/uangtix/balance"),r=await e.json();r.success&&p(r.data.balance)}catch(e){console.error("Error fetching balance:",e)}};e?.user&&r()},[e]);let T=e=>{x(e.replace(/[^0-9]/g,""))},M=e=>{x(e.toString())},C=async r=>{if(!r||r.length<3){y([]);return}w(!0);try{let t=await fetch(`/api/users/search?email=${encodeURIComponent(r)}`),n=await t.json();n.success&&y(n.data.filter(r=>r.id!==e?.user?.id))}catch(e){console.error("Error searching users:",e)}finally{w(!1)}},k=e=>{N(e),j(null);let r=setTimeout(()=>{C(e)},500);return()=>clearTimeout(r)},F=e=>{j(e),N(e.email),y([])},L=async n=>{if(n.preventDefault(),!f||!O||!g){t({title:"Error",description:"Mohon lengkapi semua field",variant:"destructive"});return}let a=parseInt(f);if(a<1e3){t({title:"Error",description:"Minimum transfer Rp 1.000",variant:"destructive"});return}if(a>h){t({title:"Error",description:"Saldo tidak mencukupi",variant:"destructive"});return}if(O===e?.user?.email){t({title:"Error",description:"Tidak dapat transfer ke diri sendiri",variant:"destructive"});return}E(!0);try{let e=await fetch("/api/uangtix/transfer",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({amount:a,recipientEmail:O,description:g})}),n=await e.json();n.success?(t({title:"Success",description:"Transfer berhasil"}),r.push("/uangtix")):t({title:"Error",description:n.message||"Transfer gagal",variant:"destructive"})}catch(e){t({title:"Error",description:"Terjadi kesalahan server",variant:"destructive"})}finally{E(!1)}};return(0,n.jsxs)("div",{className:"container mx-auto py-8 px-4 max-w-2xl",children:[(0,n.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{variant:"ghost",size:"sm",onClick:()=>r.back(),children:n.jsx(s.Z,{className:"h-4 w-4"})}),(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"p-2 bg-primary/10 rounded-lg",children:n.jsx(c.Z,{className:"h-6 w-6 text-primary"})}),(0,n.jsxs)("div",{children:[n.jsx("h1",{className:"text-2xl font-bold",children:"Transfer UangtiX"}),n.jsx("p",{className:"text-gray-600 dark:text-gray-400",children:"Kirim uang ke pengguna lain"})]})]})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"mb-6 bg-gradient-to-r from-primary to-primary/80 text-white",children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"pt-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[n.jsx("p",{className:"text-white/80 text-sm mb-1",children:"Saldo Anda"}),n.jsx("h2",{className:"text-2xl font-bold",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(h)})]}),n.jsx(d.Z,{className:"h-8 w-8 text-white/80"})]})})}),(0,n.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Penerima"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Masukkan email penerima"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"relative",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"recipient",children:"Email Penerima"}),(0,n.jsxs)("div",{className:"relative",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"recipient",type:"email",placeholder:"<EMAIL>",value:O,onChange:e=>k(e.target.value),className:"pr-10"}),D&&n.jsx(l.Z,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin"})]}),U.length>0&&n.jsx("div",{className:"absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border rounded-lg shadow-lg max-h-60 overflow-y-auto",children:U.map(e=>n.jsx("div",{className:"p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b last:border-b-0",onClick:()=>F(e),children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center",children:n.jsx(u.Z,{className:"h-4 w-4 text-primary"})}),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"font-medium",children:e.name}),n.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.email})]})]})},e.id))})]}),v&&n.jsx("div",{className:"p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[n.jsx("div",{className:"w-10 h-10 bg-green-100 dark:bg-green-900/40 rounded-full flex items-center justify-center",children:n.jsx(u.Z,{className:"h-5 w-5 text-green-600"})}),(0,n.jsxs)("div",{children:[n.jsx("p",{className:"font-medium text-green-800 dark:text-green-200",children:v.name}),n.jsx("p",{className:"text-sm text-green-600 dark:text-green-400",children:v.email})]})]})})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Jumlah Transfer"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Minimum Rp 1.000"})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{htmlFor:"amount",children:"Jumlah (Rp)"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/input'");throw e.code="MODULE_NOT_FOUND",e}()),{id:"amount",type:"text",placeholder:"0",value:f?parseInt(f).toLocaleString("id-ID"):"",onChange:e=>T(e.target.value),className:"text-lg"})]}),(0,n.jsxs)("div",{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/label'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Jumlah Cepat"}),n.jsx("div",{className:"grid grid-cols-3 gap-2 mt-2",children:m.map(e=>n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"button",variant:"outline",size:"sm",onClick:()=>M(e),className:f===e.toString()?"border-primary":"",disabled:e>h,children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(e)},e))})]}),f&&(0,n.jsxs)("div",{className:"p-3 bg-gray-50 dark:bg-gray-800 rounded-lg",children:[(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Jumlah Transfer:"}),n.jsx("span",{className:"font-medium",children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(parseInt(f))})]}),(0,n.jsxs)("div",{className:"flex justify-between text-sm",children:[n.jsx("span",{children:"Biaya Admin:"}),n.jsx("span",{className:"font-medium",children:"Rp 0"})]}),n.jsx("hr",{className:"my-2"}),(0,n.jsxs)("div",{className:"flex justify-between font-medium",children:[n.jsx("span",{children:"Total:"}),n.jsx("span",{children:Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(parseInt(f))})]})]})]})]}),(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Catatan"}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:"Tambahkan catatan untuk transfer ini"})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/card'");throw e.code="MODULE_NOT_FOUND",e}()),{children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/textarea'");throw e.code="MODULE_NOT_FOUND",e}()),{placeholder:"Contoh: Bayar makan siang",value:g,onChange:e=>b(e.target.value),rows:3})})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/button'");throw e.code="MODULE_NOT_FOUND",e}()),{type:"submit",className:"w-full",size:"lg",disabled:!f||!O||!g||_||parseInt(f)>h,children:_?(0,n.jsxs)(n.Fragment,{children:[n.jsx(l.Z,{className:"h-4 w-4 mr-2 animate-spin"}),"Memproses..."]}):(0,n.jsxs)(n.Fragment,{children:[n.jsx(c.Z,{className:"h-4 w-4 mr-2"}),"Transfer ",f?Object(function(){var e=Error("Cannot find module '@/lib/utils'");throw e.code="MODULE_NOT_FOUND",e}())(parseInt(f)):""]})})]})]})}},63024:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},36135:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("Send",[["path",{d:"m22 2-7 20-4-9-9-4Z",key:"1q3vgg"}],["path",{d:"M22 2 11 13",key:"nzbqef"}]])},18822:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},67925:(e,r,t)=>{"use strict";t.d(r,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,t(69224).Z)("Wallet",[["path",{d:"M21 12V7H5a2 2 0 0 1 0-4h14v4",key:"195gfw"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h16v-5",key:"195n9w"}],["path",{d:"M18 12a2 2 0 0 0 0 4h4v-4Z",key:"vllfpd"}]])},82917:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d,metadata:()=>c});var n=t(25036),a=t(450),o=t.n(a),i=t(14824),s=t.n(i);t(67272),function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}(),function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}();let c={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function d({children:e}){return n.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:n.jsx("body",{className:`${o().variable} ${s().variable} font-sans antialiased`,children:n.jsx(Object(function(){var e=Error("Cannot find module '@/components/providers/theme-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,n.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/providers/auth-provider'");throw e.code="MODULE_NOT_FOUND",e}()),{children:[(0,n.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[n.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/header'");throw e.code="MODULE_NOT_FOUND",e}()),{}),n.jsx("main",{className:"flex-1",children:e}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/layout/footer'");throw e.code="MODULE_NOT_FOUND",e}()),{})]}),n.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/toaster'");throw e.code="MODULE_NOT_FOUND",e}()),{})]})})})})}},29310:(e,r,t)=>{"use strict";t.r(r),t.d(r,{$$typeof:()=>o,__esModule:()=>a,default:()=>i});let n=(0,t(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\uangtix\transfer\page.tsx`),{__esModule:a,$$typeof:o}=n,i=n.default},67272:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[1638,3293,5504],()=>t(23461));module.exports=n})();