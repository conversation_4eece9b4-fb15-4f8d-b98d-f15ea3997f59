exports.id=2295,exports.ids=[2295],exports.modules={9559:(e,a,s)=>{Promise.resolve().then(s.bind(s,80514))},80514:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>H});var r=s(95344),n=s(47674),t=s(8428),i=s(3729),l=s(56506),d=s(91626),c=s(2273),m=s(89895),o=s(55794),h=s(76755),x=s(70009),f=s(85674),u=s(50340),g=s(33037),p=s(13746),j=s(14513),y=s(98200),N=s(23485),v=s(25390),b=s(97751);let w=[{name:"Dashboard",href:"/admin",icon:c.Z},{name:"Manajemen User",icon:m.Z,children:[{name:"<PERSON><PERSON><PERSON> User",href:"/admin/users"},{name:"Verifikasi Organizer",href:"/admin/users/verification"},{name:"Role & Permission",href:"/admin/users/roles"}]},{name:"Event & Tiket",icon:o.Z,children:[{name:"Semua Event",href:"/admin/events"},{name:"Kategori Event",href:"/admin/categories"},{name:"Template Tiket",href:"/admin/ticket-templates"}]},{name:"Badge & Langganan",icon:h.Z,children:[{name:"Badge Plans",href:"/admin/badges/plans"},{name:"Subscription",href:"/admin/badges/subscriptions"},{name:"Pricing Management",href:"/admin/badges/pricing"}]},{name:"Jasa & Promosi",icon:x.Z,children:[{name:"Artposure Services",href:"/admin/artposure"},{name:"Event Booster",href:"/admin/booster"},{name:"Paket Promosi",href:"/admin/promotion-packages"}]},{name:"Keuangan",icon:f.Z,children:[{name:"UangtiX Transactions",href:"/admin/finance/uangtix"},{name:"Payment Gateway",href:"/admin/finance/payments"},{name:"Commission & Tax",href:"/admin/finance/commission"},{name:"Withdraw Requests",href:"/admin/finance/withdrawals"}]},{name:"Analytics & Reports",icon:u.Z,children:[{name:"Sales Analytics",href:"/admin/analytics/sales"},{name:"User Analytics",href:"/admin/analytics/users"},{name:"Event Performance",href:"/admin/analytics/events"},{name:"Revenue Reports",href:"/admin/analytics/revenue"}]},{name:"Notifikasi",icon:g.Z,children:[{name:"Broadcast Message",href:"/admin/notifications/broadcast"},{name:"Push Notifications",href:"/admin/notifications/push"},{name:"Email Templates",href:"/admin/notifications/email"}]},{name:"Pengaturan Sistem",icon:p.Z,children:[{name:"Platform Settings",href:"/admin/settings/platform"},{name:"Maintenance Mode",href:"/admin/settings/maintenance"},{name:"SEO & Branding",href:"/admin/settings/branding"},{name:"API Configuration",href:"/admin/settings/api"}]}];function k(){let e=(0,t.usePathname)(),[a,s]=(0,i.useState)(!1),[n,c]=(0,i.useState)([]),m=e=>{c(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},o=a=>"/admin"===a?"/admin"===e:e.startsWith(a),h=e=>e.some(e=>o(e.href));return(0,r.jsxs)(r.Fragment,{children:[a&&r.jsx("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>s(!1),children:r.jsx("div",{className:"absolute inset-0 bg-gray-600 opacity-75"})}),r.jsx("div",{className:"lg:hidden",children:r.jsx("button",{type:"button",className:"fixed top-4 left-4 z-50 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500",onClick:()=>s(!a),children:a?r.jsx(j.Z,{className:"h-6 w-6"}):r.jsx(y.Z,{className:"h-6 w-6"})})}),r.jsx("div",{className:(0,d.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",a?"translate-x-0":"-translate-x-full"),children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[r.jsx("div",{className:"flex items-center justify-center h-16 px-4 bg-primary-600 dark:bg-primary-700",children:(0,r.jsxs)(l.default,{href:"/admin",className:"flex items-center",children:[r.jsx(N.Z,{className:"h-8 w-8 text-white mr-2"}),r.jsx("span",{className:"text-xl font-bold text-white",children:"TiXara Admin"})]})}),r.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2 overflow-y-auto",children:w.map(e=>r.jsx("div",{children:e.children?(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>m(e.name),className:(0,d.cn)("w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors",h(e.children)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(e.icon,{className:"h-5 w-5 mr-3"}),e.name]}),n.includes(e.name)?r.jsx(v.Z,{className:"h-4 w-4"}):r.jsx(b.Z,{className:"h-4 w-4"})]}),n.includes(e.name)&&r.jsx("div",{className:"mt-2 ml-6 space-y-1",children:e.children.map(e=>r.jsx(l.default,{href:e.href,className:(0,d.cn)("block px-3 py-2 text-sm rounded-lg transition-colors",o(e.href)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),onClick:()=>s(!1),children:e.name},e.href))})]}):(0,r.jsxs)(l.default,{href:e.href,className:(0,d.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",o(e.href)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"),onClick:()=>s(!1),children:[r.jsx(e.icon,{className:"h-5 w-5 mr-3"}),e.name]})},e.name))})]})})]})}var Z=s(16212),C=s(20886),D=s(69436),A=s(92549),M=s(28765),O=s(98714),R=s(47180),S=s(69224);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let U=(0,S.Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);var P=s(72086),z=s(18822),E=s(48120),T=s(6256);function _(){let{data:e}=(0,n.useSession)(),a=(0,t.useRouter)(),{theme:s,setTheme:d}=(0,T.F)(),[c,m]=(0,i.useState)(""),o=e?.user,h=async()=>{await (0,n.signOut)({callbackUrl:"/"})};return r.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:r.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[r.jsx("div",{className:"flex flex-1 items-center",children:r.jsx("form",{onSubmit:e=>{e.preventDefault(),c.trim()&&a.push(`/admin/search?q=${encodeURIComponent(c)}`)},className:"w-full max-w-lg",children:(0,r.jsxs)("div",{className:"relative",children:[r.jsx("div",{className:"pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3",children:r.jsx(M.Z,{className:"h-5 w-5 text-gray-400"})}),r.jsx(A.I,{type:"search",placeholder:"Cari user, event, atau data lainnya...",className:"block w-full pl-10 pr-3 py-2",value:c,onChange:e=>m(e.target.value)})]})})}),(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)(C.h_,{children:[r.jsx(C.$F,{asChild:!0,children:(0,r.jsxs)(Z.z,{variant:"ghost",size:"sm",children:[r.jsx(O.Z,{className:"h-4 w-4 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),r.jsx(R.Z,{className:"absolute h-4 w-4 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),r.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,r.jsxs)(C.AW,{align:"end",children:[(0,r.jsxs)(C.Xi,{onClick:()=>d("light"),children:[r.jsx(O.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Light"})]}),(0,r.jsxs)(C.Xi,{onClick:()=>d("dark"),children:[r.jsx(R.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Dark"})]}),(0,r.jsxs)(C.Xi,{onClick:()=>d("system"),children:[r.jsx(U,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"System"})]})]})]}),(0,r.jsxs)(Z.z,{variant:"ghost",size:"sm",className:"relative",children:[r.jsx(g.Z,{className:"h-5 w-5"}),r.jsx("span",{className:"absolute -top-1 -right-1 h-4 w-4 rounded-full bg-red-500 text-xs text-white flex items-center justify-center",children:"3"})]}),(0,r.jsxs)(C.h_,{children:[r.jsx(C.$F,{asChild:!0,children:r.jsx(Z.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-8 w-8",children:[r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{src:o?.avatar||"",alt:o?.name||""}),r.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"bg-primary-100 text-primary-700",children:o?.name?.charAt(0)?.toUpperCase()||"A"})]})})}),(0,r.jsxs)(C.AW,{className:"w-56",align:"end",forceMount:!0,children:[r.jsx(C.Ju,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[r.jsx("p",{className:"text-sm font-medium leading-none",children:o?.name||"Admin"}),r.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:o?.email}),r.jsx("div",{className:"flex items-center gap-2 mt-2",children:(0,r.jsxs)(D.C,{variant:"secondary",className:"text-xs",children:[r.jsx(N.Z,{className:"h-3 w-3 mr-1"}),"Admin"]})})]})}),r.jsx(C.VD,{}),r.jsx(C.Xi,{asChild:!0,children:(0,r.jsxs)(l.default,{href:"/dashboard",className:"flex items-center",children:[r.jsx(P.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Dashboard Utama"})]})}),r.jsx(C.Xi,{asChild:!0,children:(0,r.jsxs)(l.default,{href:"/dashboard/profile",className:"flex items-center",children:[r.jsx(z.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Profil"})]})}),r.jsx(C.Xi,{asChild:!0,children:(0,r.jsxs)(l.default,{href:"/admin/settings/platform",className:"flex items-center",children:[r.jsx(p.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Pengaturan"})]})}),r.jsx(C.VD,{}),(0,r.jsxs)(C.Xi,{className:"text-red-600 dark:text-red-400",onClick:h,children:[r.jsx(E.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Keluar"})]})]})]})]})]})})})}!function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}();var F=s(38163),L=s(82264),X=s(61351);/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let I=(0,S.Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var q=s(45961);function B({children:e,allowedRoles:a,fallback:s,redirectTo:n="/auth/login"}){let{user:t,isLoading:i,isAuthenticated:d,hasRole:c}=(0,L.a)();return i?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"})}):d?c(a)?r.jsx(r.Fragment,{children:e}):s||r.jsx("div",{className:"flex items-center justify-center min-h-screen p-4",children:r.jsx(X.Zb,{className:"w-full max-w-md",children:(0,r.jsxs)(X.aY,{className:"p-6 text-center",children:[r.jsx(q.Z,{className:"h-12 w-12 text-destructive mx-auto mb-4"}),r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Akses Ditolak"}),r.jsx("p",{className:"text-muted-foreground mb-4",children:"Anda tidak memiliki izin untuk mengakses halaman ini."}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Role Anda: ",r.jsx("span",{className:"font-medium",children:t?.role})]}),(0,r.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Role yang diizinkan: ",r.jsx("span",{className:"font-medium",children:a.join(", ")})]})]}),r.jsx(Z.z,{asChild:!0,className:"mt-4",children:r.jsx(l.default,{href:"/dashboard",children:"Kembali ke Dashboard"})})]})})}):s||r.jsx("div",{className:"flex items-center justify-center min-h-screen p-4",children:r.jsx(X.Zb,{className:"w-full max-w-md",children:(0,r.jsxs)(X.aY,{className:"p-6 text-center",children:[r.jsx(I,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),r.jsx("h2",{className:"text-xl font-semibold mb-2",children:"Login Diperlukan"}),r.jsx("p",{className:"text-muted-foreground mb-4",children:"Anda harus login untuk mengakses halaman ini."}),r.jsx(Z.z,{asChild:!0,children:r.jsx(l.default,{href:n,children:"Login Sekarang"})})]})})})}function V({children:e,fallback:a}){return r.jsx(B,{allowedRoles:[F.UserRole.ADMIN],fallback:a,children:e})}var $=s(42739);function H({children:e}){let{data:a,status:s}=(0,n.useSession)(),i=(0,t.useRouter)();return"loading"===s?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx($.Z,{className:"h-8 w-8 animate-spin"})}):a?.user&&"ADMIN"===a.user.role?r.jsx(V,{children:(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[r.jsx(k,{}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[r.jsx(_,{}),r.jsx("main",{className:"py-6",children:r.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]})}):(i.push("/dashboard"),null)}},69436:(e,a,s)=>{"use strict";s.d(a,{C:()=>l});var r=s(95344);s(3729);var n=s(92193),t=s(91626);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:a,...s}){return r.jsx("div",{className:(0,t.cn)(i({variant:a}),e),...s})}},61351:(e,a,s)=>{"use strict";s.d(a,{Ol:()=>l,SZ:()=>c,Zb:()=>i,aY:()=>m,ll:()=>d});var r=s(95344),n=s(3729),t=s(91626);let i=n.forwardRef(({className:e,elevated:a=!1,padding:s="md",...n},i)=>r.jsx("div",{ref:i,className:(0,t.cn)("rounded-lg border bg-card text-card-foreground",a&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===s,"p-3":"sm"===s,"p-6":"md"===s,"p-8":"lg"===s},e),...n}));i.displayName="Card";let l=n.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,t.cn)("flex flex-col space-y-1.5 p-6",e),...a}));l.displayName="CardHeader";let d=n.forwardRef(({className:e,...a},s)=>r.jsx("h3",{ref:s,className:(0,t.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));d.displayName="CardTitle";let c=n.forwardRef(({className:e,...a},s)=>r.jsx("p",{ref:s,className:(0,t.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let m=n.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,t.cn)("p-6 pt-0",e),...a}));m.displayName="CardContent",n.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,t.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},92549:(e,a,s)=>{"use strict";s.d(a,{I:()=>i});var r=s(95344),n=s(3729),t=s(91626);let i=n.forwardRef(({className:e,type:a,...s},n)=>r.jsx("input",{type:a,className:(0,t.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...s}));i.displayName="Input"},45961:(e,a,s)=>{"use strict";s.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},72086:(e,a,s)=>{"use strict";s.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]])},28765:(e,a,s)=>{"use strict";s.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},89895:(e,a,s)=>{"use strict";s.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},66294:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>t,__esModule:()=>n,default:()=>i});let r=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\admin\layout.tsx`),{__esModule:n,$$typeof:t}=r,i=r.default}};