"use strict";exports.id=3750,exports.ids=[3750],exports.modules={50340:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},85674:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2273:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},70009:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},33733:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79200:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]])},88794:(e,t,r)=>{r.d(t,{Dx:()=>en,VY:()=>er,aV:()=>et,dk:()=>eo,fC:()=>J,h_:()=>ee,x8:()=>ea,xz:()=>Q});var n=r(3729),o=r(85222),a=r(31405),i=r(98462),l=r(99048),s=r(33183),d=r(44155),c=r(27386),u=r(31179),f=r(43234),p=r(62409),y=r(1106),h=r(71210),g=r(45904),x=r(32751),v=r(95344),m="Dialog",[b,C]=(0,i.b)(m),[D,k]=b(m),w=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[f,p]=(0,s.T)({prop:o,defaultProp:a??!1,onChange:i,caller:m});return(0,v.jsx)(D,{scope:t,triggerRef:c,contentRef:u,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};w.displayName=m;var M="DialogTrigger",j=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=k(M,r),l=(0,a.e)(t,i.triggerRef);return(0,v.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":H(i.open),...n,ref:l,onClick:(0,o.M)(e.onClick,i.onOpenToggle)})});j.displayName=M;var R="DialogPortal",[I,V]=b(R,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,i=k(R,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(f.z,{present:r||i.open,children:(0,v.jsx)(u.h,{asChild:!0,container:a,children:e})}))})};N.displayName=R;var Z="DialogOverlay",F=n.forwardRef((e,t)=>{let r=V(Z,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(Z,e.__scopeDialog);return a.modal?(0,v.jsx)(f.z,{present:n||a.open,children:(0,v.jsx)(E,{...o,ref:t})}):null});F.displayName=Z;var O=(0,x.Z8)("DialogOverlay.RemoveScroll"),E=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(Z,r);return(0,v.jsx)(h.Z,{as:O,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(p.WV.div,{"data-state":H(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),P="DialogContent",W=n.forwardRef((e,t)=>{let r=V(P,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=k(P,e.__scopeDialog);return(0,v.jsx)(f.z,{present:n||a.open,children:a.modal?(0,v.jsx)(_,{...o,ref:t}):(0,v.jsx)(T,{...o,ref:t})})});W.displayName=P;var _=n.forwardRef((e,t)=>{let r=k(P,e.__scopeDialog),i=n.useRef(null),l=(0,a.e)(t,r.contentRef,i);return n.useEffect(()=>{let e=i.current;if(e)return(0,g.Ry)(e)},[]),(0,v.jsx)(z,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=k(P,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),z=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:i,onCloseAutoFocus:l,...s}=e,u=k(P,r),f=n.useRef(null),p=(0,a.e)(t,f);return(0,y.EW)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,v.jsx)(d.XB,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":H(u.open),...s,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(X,{titleId:u.titleId}),(0,v.jsx)(G,{contentRef:f,descriptionId:u.descriptionId})]})]})}),A="DialogTitle",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(A,r);return(0,v.jsx)(p.WV.h2,{id:o.titleId,...n,ref:t})});$.displayName=A;var q="DialogDescription",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=k(q,r);return(0,v.jsx)(p.WV.p,{id:o.descriptionId,...n,ref:t})});B.displayName=q;var K="DialogClose",L=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=k(K,r);return(0,v.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})});function H(e){return e?"open":"closed"}L.displayName=K;var S="DialogTitleWarning",[Y,U]=(0,i.k)(S,{contentName:P,titleName:A,docsSlug:"dialog"}),X=({titleId:e})=>{let t=U(S),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},G=({contentRef:e,descriptionId:t})=>{let r=U("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=w,Q=j,ee=N,et=F,er=W,en=$,eo=B,ea=L},89128:(e,t,r)=>{r.d(t,{VY:()=>Z,aV:()=>V,fC:()=>I,xz:()=>N});var n=r(3729),o=r(85222),a=r(98462),i=r(34504),l=r(43234),s=r(62409),d=r(3975),c=r(33183),u=r(99048),f=r(95344),p="Tabs",[y,h]=(0,a.b)(p,[i.Pc]),g=(0,i.Pc)(),[x,v]=y(p),m=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:y="automatic",...h}=e,g=(0,d.gm)(l),[v,m]=(0,c.T)({prop:n,onChange:o,defaultProp:a??"",caller:p});return(0,f.jsx)(x,{scope:r,baseId:(0,u.M)(),value:v,onValueChange:m,orientation:i,dir:g,activationMode:y,children:(0,f.jsx)(s.WV.div,{dir:g,"data-orientation":i,...h,ref:t})})});m.displayName=p;var b="TabsList",C=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...o}=e,a=v(b,r),l=g(r);return(0,f.jsx)(i.fC,{asChild:!0,...l,orientation:a.orientation,dir:a.dir,loop:n,children:(0,f.jsx)(s.WV.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});C.displayName=b;var D="TabsTrigger",k=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:a=!1,...l}=e,d=v(D,r),c=g(r),u=j(d.baseId,n),p=R(d.baseId,n),y=n===d.value;return(0,f.jsx)(i.ck,{asChild:!0,...c,focusable:!a,active:y,children:(0,f.jsx)(s.WV.button,{type:"button",role:"tab","aria-selected":y,"aria-controls":p,"data-state":y?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:u,...l,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==d.activationMode;y||a||!e||d.onValueChange(n)})})})});k.displayName=D;var w="TabsContent",M=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:o,forceMount:a,children:i,...d}=e,c=v(w,r),u=j(c.baseId,o),p=R(c.baseId,o),y=o===c.value,h=n.useRef(y);return n.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.z,{present:a||y,children:({present:r})=>(0,f.jsx)(s.WV.div,{"data-state":y?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:p,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&i})})});function j(e,t){return`${e}-trigger-${t}`}function R(e,t){return`${e}-content-${t}`}M.displayName=w;var I=m,V=C,N=k,Z=M}};