"use strict";exports.id=4739,exports.ids=[4739],exports.modules={25390:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},12704:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},32116:(e,t,r)=>{r.d(t,{VY:()=>eL,ZA:()=>e_,JO:()=>eN,ck:()=>eA,wU:()=>eK,eT:()=>eB,__:()=>eH,h_:()=>eV,fC:()=>eD,$G:()=>eF,u_:()=>eO,Z0:()=>eU,xz:()=>eE,B4:()=>eP,l_:()=>eW});var n=r(3729),l=r(81202);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(85222),i=r(77411),s=r(31405),u=r(98462),d=r(3975),c=r(44155),p=r(1106),f=r(27386),h=r(99048),v=r(37574),m=r(31179),g=r(62409),w=r(32751),x=r(2256),y=r(33183),b=r(16069),S=r(92062),C=r(87298),M=r(45904),j=r(71210),T=r(95344),k=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],I="Select",[D,E,P]=(0,i.B)(I),[N,V]=(0,u.b)(I,[P,v.D7]),L=(0,v.D7)(),[W,_]=N(I),[H,A]=N(I),B=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:g,form:w}=e,x=L(t),[b,S]=n.useState(null),[C,M]=n.useState(null),[j,k]=n.useState(!1),R=(0,d.gm)(c),[E,P]=(0,y.T)({prop:l,defaultProp:o??!1,onChange:a,caller:I}),[N,V]=(0,y.T)({prop:i,defaultProp:s,onChange:u,caller:I}),_=n.useRef(null),A=!b||w||!!b.closest("form"),[B,K]=n.useState(new Set),O=Array.from(B).map(e=>e.props.value).join(";");return(0,T.jsx)(v.fC,{...x,children:(0,T.jsxs)(W,{required:g,scope:t,trigger:b,onTriggerChange:S,valueNode:C,onValueNodeChange:M,valueNodeHasChildren:j,onValueNodeHasChildrenChange:k,contentId:(0,h.M)(),value:N,onValueChange:V,open:E,onOpenChange:P,dir:R,triggerPointerDownPosRef:_,disabled:m,children:[(0,T.jsx)(D.Provider,{scope:t,children:(0,T.jsx)(H,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{K(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{K(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),A?(0,T.jsxs)(eT,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:N,onChange:e=>V(e.target.value),disabled:m,form:w,children:[void 0===N?(0,T.jsx)("option",{value:""}):null,Array.from(B)]},O):null]})})};B.displayName=I;var K="SelectTrigger",O=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=L(r),u=_(K,r),d=u.disabled||l,c=(0,s.e)(t,u.onTriggerChange),p=E(r),f=n.useRef("touch"),[h,m,w]=eR(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eI(t,e,r);void 0!==n&&u.onValueChange(n.value)}),x=e=>{d||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(v.ee,{asChild:!0,...i,children:(0,T.jsx)(g.WV.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ek(u.value)?"":void 0,...o,ref:c,onClick:(0,a.M)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.M)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.M)(o.onKeyDown,e=>{let t=""!==h.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&k.includes(e.key)&&(x(),e.preventDefault())})})})});O.displayName=K;var F="SelectValue",U=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=_(F,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.e)(t,u.onValueNodeChange);return(0,b.b)(()=>{d(c)},[d,c]),(0,T.jsx)(g.WV.span,{...i,ref:p,style:{pointerEvents:"none"},children:ek(u.value)?(0,T.jsx)(T.Fragment,{children:a}):o})});U.displayName=F;var Z=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,T.jsx)(g.WV.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});Z.displayName="SelectIcon";var z=e=>(0,T.jsx)(m.h,{asChild:!0,...e});z.displayName="SelectPortal";var q="SelectContent",Y=n.forwardRef((e,t)=>{let r=_(q,e.__scopeSelect),[o,a]=n.useState();return((0,b.b)(()=>{a(new DocumentFragment)},[]),r.open)?(0,T.jsx)($,{...e,ref:t}):o?l.createPortal((0,T.jsx)(X,{scope:e.__scopeSelect,children:(0,T.jsx)(D.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),o):null});Y.displayName=q;var[X,G]=N(q),J=(0,w.Z8)("SelectContent.RemoveScroll"),$=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:w,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S,...C}=e,k=_(q,r),[R,I]=n.useState(null),[D,P]=n.useState(null),N=(0,s.e)(t,e=>I(e)),[V,L]=n.useState(null),[W,H]=n.useState(null),A=E(r),[B,K]=n.useState(!1),O=n.useRef(!1);n.useEffect(()=>{if(R)return(0,M.Ry)(R)},[R]),(0,p.EW)();let F=n.useCallback(e=>{let[t,...r]=A().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(r?.scrollIntoView({block:"nearest"}),r===t&&D&&(D.scrollTop=0),r===n&&D&&(D.scrollTop=D.scrollHeight),r?.focus(),document.activeElement!==l))return},[A,D]),U=n.useCallback(()=>F([V,R]),[F,V,R]);n.useEffect(()=>{B&&U()},[B,U]);let{onOpenChange:Z,triggerPointerDownPosRef:z}=k;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{e={x:Math.abs(Math.round(t.pageX)-(z.current?.x??0)),y:Math.abs(Math.round(t.pageY)-(z.current?.y??0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||Z(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,Z,z]),n.useEffect(()=>{let e=()=>Z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[Z]);let[Y,G]=eR(e=>{let t=A().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eI(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),$=n.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==k.value&&k.value===t||n)&&(L(e),n&&(O.current=!0))},[k.value]),et=n.useCallback(()=>R?.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!O.current&&!r;(void 0!==k.value&&k.value===t||n)&&H(e)},[k.value]),en="popper"===l?ee:Q,el=en===ee?{side:d,sideOffset:h,align:v,alignOffset:m,arrowPadding:g,collisionBoundary:w,collisionPadding:x,sticky:y,hideWhenDetached:b,avoidCollisions:S}:{};return(0,T.jsx)(X,{scope:r,content:R,viewport:D,onViewportChange:P,itemRefCallback:$,selectedItem:V,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:U,selectedItemText:W,position:l,isPositioned:B,searchRef:Y,children:(0,T.jsx)(j.Z,{as:J,allowPinchZoom:!0,children:(0,T.jsx)(f.M,{asChild:!0,trapped:k.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.M)(o,e=>{k.trigger?.focus({preventScroll:!0}),e.preventDefault()}),children:(0,T.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>k.onOpenChange(!1),children:(0,T.jsx)(en,{role:"listbox",id:k.contentId,"data-state":k.open?"open":"closed",dir:k.dir,onContextMenu:e=>e.preventDefault(),...C,...el,onPlaced:()=>K(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,a.M)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||G(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=A().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>F(t)),e.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var Q=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(q,r),u=G(q,r),[d,c]=n.useState(null),[p,f]=n.useState(null),h=(0,s.e)(t,e=>f(e)),v=E(r),m=n.useRef(!1),w=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:S,focusSelectedItem:C}=u,M=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&y&&S){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=v(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+h+u+parseInt(c.paddingBottom,10)+g,b=Math.min(5*y.offsetHeight,w),C=window.getComputedStyle(x),M=parseInt(C.paddingTop,10),j=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,k=y.offsetHeight/2,R=f+h+(y.offsetTop+k);if(R<=T){let e=a.length>0&&y===a[a.length-1].ref.current;d.style.bottom="0px";let t=p.clientHeight-x.offsetTop-x.offsetHeight;d.style.height=R+Math.max(s-T,k+(e?j:0)+t+g)+"px"}else{let e=a.length>0&&y===a[0].ref.current;d.style.top="0px";let t=Math.max(T,f+x.offsetTop+(e?M:0)+k);d.style.height=t+(w-R)+"px",x.scrollTop=R-T+x.offsetTop}d.style.margin="10px 0",d.style.minHeight=b+"px",d.style.maxHeight=s+"px",l?.(),requestAnimationFrame(()=>m.current=!0)}},[v,i.trigger,i.valueNode,d,p,x,y,S,i.dir,l]);(0,b.b)(()=>M(),[M]);let[j,k]=n.useState();(0,b.b)(()=>{p&&k(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===w.current&&(M(),C?.(),w.current=!1)},[M,C]);return(0,T.jsx)(et,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,T.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:j},children:(0,T.jsx)(g.WV.div,{...a,ref:h,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});Q.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=L(r);return(0,T.jsx)(v.VY,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=N(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=G(en,r),u=er(en,r),d=(0,s.e)(t,i.onViewportChange),c=n.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,T.jsx)(D.Slot,{scope:r,children:(0,T.jsx)(g.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if(n?.current&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=N(eo),es=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,h.M)();return(0,T.jsx)(ea,{scope:r,id:l,children:(0,T.jsx)(g.WV.div,{role:"group","aria-labelledby":l,...n,ref:t})})});es.displayName=eo;var eu="SelectLabel",ed=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(eu,r);return(0,T.jsx)(g.WV.div,{id:l.id,...n,ref:t})});ed.displayName=eu;var ec="SelectItem",[ep,ef]=N(ec),eh=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,d=_(ec,r),c=G(ec,r),p=d.value===l,[f,v]=n.useState(i??""),[m,w]=n.useState(!1),x=(0,s.e)(t,e=>c.itemRefCallback?.(e,l,o)),y=(0,h.M)(),b=n.useRef("touch"),S=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(ep,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{v(t=>t||(e?.textContent??"").trim())},[]),children:(0,T.jsx)(D.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,T.jsx)(g.WV.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:x,onFocus:(0,a.M)(u.onFocus,()=>w(!0)),onBlur:(0,a.M)(u.onBlur,()=>w(!1)),onClick:(0,a.M)(u.onClick,()=>{"mouse"!==b.current&&S()}),onPointerUp:(0,a.M)(u.onPointerUp,()=>{"mouse"===b.current&&S()}),onPointerDown:(0,a.M)(u.onPointerDown,e=>{b.current=e.pointerType}),onPointerMove:(0,a.M)(u.onPointerMove,e=>{b.current=e.pointerType,o?c.onItemLeave?.():"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.M)(u.onPointerLeave,e=>{e.currentTarget===document.activeElement&&c.onItemLeave?.()}),onKeyDown:(0,a.M)(u.onKeyDown,e=>{c.searchRef?.current!==""&&" "===e.key||(R.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});eh.displayName=ec;var ev="SelectItemText",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=_(ev,r),d=G(ev,r),c=ef(ev,r),p=A(ev,r),[f,h]=n.useState(null),v=(0,s.e)(t,e=>h(e),c.onItemTextChange,e=>d.itemTextRefCallback?.(e,c.value,c.disabled)),m=f?.textContent,w=n.useMemo(()=>(0,T.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,b.b)(()=>(x(w),()=>y(w)),[x,y,w]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(g.WV.span,{id:c.textId,...i,ref:v}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});em.displayName=ev;var eg="SelectItemIndicator",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ef(eg,r).isSelected?(0,T.jsx)(g.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ew.displayName=eg;var ex="SelectScrollUpButton",ey=n.forwardRef((e,t)=>{let r=G(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ey.displayName=ex;var eb="SelectScrollDownButton",eS=n.forwardRef((e,t)=>{let r=G(eb,e.__scopeSelect),l=er(eb,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.e)(t,l.onScrollButtonChange);return(0,b.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eC,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eS.displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=G("SelectScrollButton",r),s=n.useRef(null),u=E(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,b.b)(()=>{let e=u().find(e=>e.ref.current===document.activeElement);e?.ref.current?.scrollIntoView({block:"nearest"})},[u]),(0,T.jsx)(g.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.M)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.M)(o.onPointerMove,()=>{i.onItemLeave?.(),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.M)(o.onPointerLeave,()=>{d()})})}),eM=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,T.jsx)(g.WV.div,{"aria-hidden":!0,...n,ref:t})});eM.displayName="SelectSeparator";var ej="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=L(r),o=_(ej,r),a=G(ej,r);return o.open&&"popper"===a.position?(0,T.jsx)(v.Eh,{...l,...n,ref:t}):null}).displayName=ej;var eT=n.forwardRef(({__scopeSelect:e,value:t,...r},l)=>{let o=n.useRef(null),a=(0,s.e)(l,o),i=(0,S.D)(t);return n.useEffect(()=>{let e=o.current;if(!e)return;let r=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==t&&r){let n=new Event("change",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,T.jsx)(g.WV.select,{...r,style:{...C.C2,...r.style},ref:a,defaultValue:t})});function ek(e){return""===e||void 0===e}function eR(e){let t=(0,x.W)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eI(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let a=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return a!==r?a:void 0}eT.displayName="SelectBubbleInput";var eD=B,eE=O,eP=U,eN=Z,eV=z,eL=Y,eW=el,e_=es,eH=ed,eA=eh,eB=em,eK=ew,eO=ey,eF=eS,eU=eM},92062:(e,t,r)=>{r.d(t,{D:()=>l});var n=r(3729);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}};