exports.id=5237,exports.ids=[5237],exports.modules={81367:(e,a,s)=>{Promise.resolve().then(s.bind(s,76120))},76120:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>A});var r=s(95344),t=s(47674),n=s(8428),l=s(3729),i=s(56506),d=s(91626),c=s(2273),o=s(55794),m=s(76196),x=s(70009),h=s(76755),f=s(85674),g=s(50340),u=s(33037),j=s(13746),p=s(14513),N=s(98200),y=s(25390),v=s(97751);let b=[{name:"Dashboard",href:"/organizer",icon:c.Z},{name:"Event Management",icon:o.Z,children:[{name:"Se<PERSON>a Event",href:"/organizer/events"},{name:"Buat Event Baru",href:"/organizer/events/create"},{name:"Template Tiket",href:"/organizer/ticket-templates"}]},{name:"Penjualan & Tiket",icon:m.Z,children:[{name:"Penjualan Tiket",href:"/organizer/sales"},{name:"Check-in Event",href:"/organizer/checkin"},{name:"Laporan Penjualan",href:"/organizer/reports"}]},{name:"Jasa & Promosi",icon:x.Z,children:[{name:"Jasa Artposure",href:"/organizer/artposure"},{name:"Event Booster",href:"/organizer/boost"},{name:"Paket Promosi",href:"/organizer/promotion"}]},{name:"Badge & Langganan",icon:h.Z,children:[{name:"Badge Saya",href:"/organizer/badges"},{name:"Upgrade Badge",href:"/organizer/badges/upgrade"},{name:"Riwayat Langganan",href:"/organizer/badges/history"}]},{name:"UangtiX Wallet",href:"/organizer/wallet",icon:f.Z},{name:"Analytics",href:"/organizer/analytics",icon:g.Z},{name:"Notifikasi",href:"/organizer/notifications",icon:u.Z},{name:"Pengaturan",icon:j.Z,children:[{name:"Profil Organizer",href:"/organizer/settings/profile"},{name:"Verifikasi Akun",href:"/organizer/settings/verification"},{name:"Keamanan",href:"/organizer/settings/security"}]}];function w(){let[e,a]=(0,l.useState)(!1),[s,t]=(0,l.useState)(["Event Management"]),c=(0,n.usePathname)(),m=e=>{t(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},x=e=>c===e,h=e=>e.some(e=>c.startsWith(e.href));return(0,r.jsxs)(r.Fragment,{children:[e&&r.jsx("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>a(!1),children:r.jsx("div",{className:"absolute inset-0 bg-gray-600 opacity-75"})}),r.jsx("div",{className:"lg:hidden",children:r.jsx("button",{type:"button",className:"fixed top-4 left-4 z-50 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500",onClick:()=>a(!e),children:e?r.jsx(p.Z,{className:"h-6 w-6"}):r.jsx(N.Z,{className:"h-6 w-6"})})}),r.jsx("div",{className:(0,d.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",e?"translate-x-0":"-translate-x-full"),children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[r.jsx("div",{className:"flex items-center justify-center h-16 px-4 bg-primary-600 dark:bg-primary-700",children:(0,r.jsxs)(i.default,{href:"/organizer",className:"flex items-center",children:[r.jsx(o.Z,{className:"h-8 w-8 text-white mr-2"}),r.jsx("span",{className:"text-xl font-bold text-white",children:"TiXara Organizer"})]})}),r.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2 overflow-y-auto",children:b.map(e=>r.jsx("div",{children:e.children?(0,r.jsxs)("div",{children:[(0,r.jsxs)("button",{onClick:()=>m(e.name),className:(0,d.cn)("w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors",h(e.children)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"),children:[(0,r.jsxs)("div",{className:"flex items-center",children:[r.jsx(e.icon,{className:"h-5 w-5 mr-3"}),e.name]}),s.includes(e.name)?r.jsx(y.Z,{className:"h-4 w-4"}):r.jsx(v.Z,{className:"h-4 w-4"})]}),s.includes(e.name)&&r.jsx("div",{className:"mt-2 space-y-1",children:e.children.map(e=>r.jsx(i.default,{href:e.href,onClick:()=>a(!1),className:(0,d.cn)("block px-3 py-2 ml-6 text-sm rounded-lg transition-colors",x(e.href)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),children:e.name},e.name))})]}):(0,r.jsxs)(i.default,{href:e.href,onClick:()=>a(!1),className:(0,d.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",x(e.href)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"),children:[r.jsx(e.icon,{className:"h-5 w-5 mr-3"}),e.name]})},e.name))}),r.jsx("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:(0,r.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center",children:["TiXara Organizer Panel",r.jsx("br",{}),"v1.0.0"]})})]})})]})}var k=s(16212),z=s(20886),Z=s(82885),C=s(69436),E=s(46064),R=s(23485),B=s(18822),O=s(48120);function X(){let{data:e}=(0,t.useSession)(),[a]=(0,l.useState)(3);return r.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:r.jsx("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"flex justify-between items-center h-16",children:[r.jsx("div",{className:"flex items-center",children:r.jsx("h1",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Organizer Dashboard"})}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[r.jsx(k.z,{size:"sm",variant:"outline",asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/events/create",children:[r.jsx(o.Z,{className:"h-4 w-4 mr-1"}),"Buat Event"]})}),r.jsx(k.z,{size:"sm",variant:"outline",asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/boost",children:[r.jsx(E.Z,{className:"h-4 w-4 mr-1"}),"Boost Event"]})})]}),(0,r.jsxs)(z.h_,{children:[r.jsx(z.$F,{asChild:!0,children:(0,r.jsxs)(k.z,{variant:"ghost",size:"sm",className:"relative",children:[r.jsx(u.Z,{className:"h-5 w-5"}),a>0&&r.jsx("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:a})]})}),(0,r.jsxs)(z.AW,{align:"end",className:"w-80",children:[r.jsx(z.Ju,{children:"Notifikasi"}),r.jsx(z.VD,{}),(0,r.jsxs)("div",{className:"max-h-64 overflow-y-auto",children:[(0,r.jsxs)(z.Xi,{className:"flex flex-col items-start p-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[r.jsx("div",{className:"h-2 w-2 bg-blue-500 rounded-full"}),r.jsx("span",{className:"font-medium text-sm",children:"Order Artposure Baru"}),r.jsx("span",{className:"text-xs text-gray-500 ml-auto",children:"2 jam lalu"})]}),r.jsx("p",{className:"text-xs text-gray-600 mt-1",children:'Order untuk desain poster event "Music Festival 2024"'})]}),(0,r.jsxs)(z.Xi,{className:"flex flex-col items-start p-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[r.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),r.jsx("span",{className:"font-medium text-sm",children:"Boost Event Aktif"}),r.jsx("span",{className:"text-xs text-gray-500 ml-auto",children:"1 hari lalu"})]}),r.jsx("p",{className:"text-xs text-gray-600 mt-1",children:'Event "Tech Conference" berhasil di-boost'})]}),(0,r.jsxs)(z.Xi,{className:"flex flex-col items-start p-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[r.jsx("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"}),r.jsx("span",{className:"font-medium text-sm",children:"Badge Upgrade"}),r.jsx("span",{className:"text-xs text-gray-500 ml-auto",children:"3 hari lalu"})]}),r.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"Selamat! Badge Anda telah upgrade ke Silver"})]})]}),r.jsx(z.VD,{}),r.jsx(z.Xi,{asChild:!0,children:r.jsx(i.default,{href:"/organizer/notifications",className:"w-full text-center",children:"Lihat Semua Notifikasi"})})]})]}),(0,r.jsxs)(z.h_,{children:[r.jsx(z.$F,{asChild:!0,children:r.jsx(k.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,r.jsxs)(Z.qE,{className:"h-8 w-8",children:[r.jsx(Z.F$,{src:e?.user?.image||"",alt:e?.user?.name||""}),r.jsx(Z.Q5,{children:e?.user?.name?e.user.name.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2):"OR"})]})})}),(0,r.jsxs)(z.AW,{className:"w-56",align:"end",forceMount:!0,children:[r.jsx(z.Ju,{className:"font-normal",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[r.jsx("p",{className:"text-sm font-medium leading-none",children:e?.user?.name||"Organizer"}),r.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:e?.user?.email}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,r.jsxs)(C.C,{className:(e=>{switch(e){case"BRONZE":return"bg-amber-100 text-amber-800";case"SILVER":default:return"bg-gray-100 text-gray-800";case"GOLD":return"bg-yellow-100 text-yellow-800";case"TITANIUM":return"bg-purple-100 text-purple-800"}})(e?.user?.badge||"BRONZE"),children:[r.jsx(h.Z,{className:"h-3 w-3 mr-1"}),e?.user?.badge||"BRONZE"]}),(0,r.jsxs)(C.C,{variant:"outline",children:[r.jsx(R.Z,{className:"h-3 w-3 mr-1"}),"Organizer"]})]})]})}),r.jsx(z.VD,{}),r.jsx(z.Xi,{asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/settings/profile",className:"flex items-center",children:[r.jsx(B.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Profil"})]})}),r.jsx(z.Xi,{asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/wallet",className:"flex items-center",children:[r.jsx(f.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"UangtiX Wallet"})]})}),r.jsx(z.Xi,{asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/badges",className:"flex items-center",children:[r.jsx(h.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Badge & Langganan"})]})}),r.jsx(z.Xi,{asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/artposure",className:"flex items-center",children:[r.jsx(x.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Jasa Artposure"})]})}),r.jsx(z.Xi,{asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/boost",className:"flex items-center",children:[r.jsx(E.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Event Booster"})]})}),r.jsx(z.VD,{}),r.jsx(z.Xi,{asChild:!0,children:(0,r.jsxs)(i.default,{href:"/organizer/settings",className:"flex items-center",children:[r.jsx(j.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Pengaturan"})]})}),r.jsx(z.VD,{}),(0,r.jsxs)(z.Xi,{className:"text-red-600 dark:text-red-400",onClick:()=>(0,t.signOut)({callbackUrl:"/"}),children:[r.jsx(O.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Keluar"})]})]})]})]})]})})})}var P=s(42739);function A({children:e}){let{data:a,status:s}=(0,t.useSession)(),l=(0,n.useRouter)();return"loading"===s?r.jsx("div",{className:"flex items-center justify-center min-h-screen",children:r.jsx(P.Z,{className:"h-8 w-8 animate-spin"})}):a?.user&&["ORGANIZER","ADMIN"].includes(a.user.role)?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[r.jsx(w,{}),(0,r.jsxs)("div",{className:"lg:pl-64",children:[r.jsx(X,{}),r.jsx("main",{className:"py-6",children:r.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]}):(l.push("/dashboard"),null)}},82885:(e,a,s)=>{"use strict";s.d(a,{F$:()=>d,Q5:()=>c,qE:()=>i});var r=s(95344),t=s(3729),n=s(15480),l=s(91626);let i=t.forwardRef(({className:e,...a},s)=>r.jsx(n.fC,{ref:s,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a}));i.displayName=n.fC.displayName;let d=t.forwardRef(({className:e,...a},s)=>r.jsx(n.Ee,{ref:s,className:(0,l.cn)("aspect-square h-full w-full",e),...a}));d.displayName=n.Ee.displayName;let c=t.forwardRef(({className:e,...a},s)=>r.jsx(n.NY,{ref:s,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a}));c.displayName=n.NY.displayName},69436:(e,a,s)=>{"use strict";s.d(a,{C:()=>i});var r=s(95344);s(3729);var t=s(92193),n=s(91626);let l=(0,t.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function i({className:e,variant:a,...s}){return r.jsx("div",{className:(0,n.cn)(l({variant:a}),e),...s})}},61351:(e,a,s)=>{"use strict";s.d(a,{Ol:()=>i,SZ:()=>c,Zb:()=>l,aY:()=>o,ll:()=>d});var r=s(95344),t=s(3729),n=s(91626);let l=t.forwardRef(({className:e,elevated:a=!1,padding:s="md",...t},l)=>r.jsx("div",{ref:l,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground",a&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===s,"p-3":"sm"===s,"p-6":"md"===s,"p-8":"lg"===s},e),...t}));l.displayName="Card";let i=t.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...a}));i.displayName="CardHeader";let d=t.forwardRef(({className:e,...a},s)=>r.jsx("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));d.displayName="CardTitle";let c=t.forwardRef(({className:e,...a},s)=>r.jsx("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let o=t.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("p-6 pt-0",e),...a}));o.displayName="CardContent",t.forwardRef(({className:e,...a},s)=>r.jsx("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},46064:(e,a,s)=>{"use strict";s.d(a,{Z:()=>r});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let r=(0,s(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},29146:(e,a,s)=>{"use strict";s.r(a),s.d(a,{$$typeof:()=>n,__esModule:()=>t,default:()=>l});let r=(0,s(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\organizer\layout.tsx`),{__esModule:t,$$typeof:n}=r,l=r.default}};