exports.id=5237,exports.ids=[5237],exports.modules={81367:(e,a,r)=>{Promise.resolve().then(r.bind(r,76120))},76120:(e,a,r)=>{"use strict";r.r(a),r.d(a,{default:()=>R});var s=r(95344),t=r(47674),n=r(8428),i=r(3729),l=r(56506),d=r(91626),c=r(2273),o=r(55794),m=r(76196),x=r(70009),h=r(76755),g=r(85674),f=r(50340),u=r(33037),j=r(13746),p=r(14513),N=r(98200),v=r(25390),y=r(97751);let b=[{name:"Dashboard",href:"/organizer",icon:c.Z},{name:"Event Management",icon:o.Z,children:[{name:"Se<PERSON>a Event",href:"/organizer/events"},{name:"Buat Event Baru",href:"/organizer/events/create"},{name:"Template Tiket",href:"/organizer/ticket-templates"}]},{name:"Penjualan & Tiket",icon:m.Z,children:[{name:"Penjualan Tiket",href:"/organizer/sales"},{name:"Check-in Event",href:"/organizer/checkin"},{name:"Laporan Penjualan",href:"/organizer/reports"}]},{name:"Jasa & Promosi",icon:x.Z,children:[{name:"Jasa Artposure",href:"/organizer/artposure"},{name:"Event Booster",href:"/organizer/boost"},{name:"Paket Promosi",href:"/organizer/promotion"}]},{name:"Badge & Langganan",icon:h.Z,children:[{name:"Badge Saya",href:"/organizer/badges"},{name:"Upgrade Badge",href:"/organizer/badges/upgrade"},{name:"Riwayat Langganan",href:"/organizer/badges/history"}]},{name:"UangtiX Wallet",href:"/organizer/wallet",icon:g.Z},{name:"Analytics",href:"/organizer/analytics",icon:f.Z},{name:"Notifikasi",href:"/organizer/notifications",icon:u.Z},{name:"Pengaturan",icon:j.Z,children:[{name:"Profil Organizer",href:"/organizer/settings/profile"},{name:"Verifikasi Akun",href:"/organizer/settings/verification"},{name:"Keamanan",href:"/organizer/settings/security"}]}];function w(){let[e,a]=(0,i.useState)(!1),[r,t]=(0,i.useState)(["Event Management"]),c=(0,n.usePathname)(),m=e=>{t(a=>a.includes(e)?a.filter(a=>a!==e):[...a,e])},x=e=>c===e,h=e=>e.some(e=>c.startsWith(e.href));return(0,s.jsxs)(s.Fragment,{children:[e&&s.jsx("div",{className:"fixed inset-0 z-40 lg:hidden",onClick:()=>a(!1),children:s.jsx("div",{className:"absolute inset-0 bg-gray-600 opacity-75"})}),s.jsx("div",{className:"lg:hidden",children:s.jsx("button",{type:"button",className:"fixed top-4 left-4 z-50 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500",onClick:()=>a(!e),children:e?s.jsx(p.Z,{className:"h-6 w-6"}):s.jsx(N.Z,{className:"h-6 w-6"})})}),s.jsx("div",{className:(0,d.cn)("fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",e?"translate-x-0":"-translate-x-full"),children:(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[s.jsx("div",{className:"flex items-center justify-center h-16 px-4 bg-primary-600 dark:bg-primary-700",children:(0,s.jsxs)(l.default,{href:"/organizer",className:"flex items-center",children:[s.jsx(o.Z,{className:"h-8 w-8 text-white mr-2"}),s.jsx("span",{className:"text-xl font-bold text-white",children:"TiXara Organizer"})]})}),s.jsx("nav",{className:"flex-1 px-4 py-6 space-y-2 overflow-y-auto",children:b.map(e=>s.jsx("div",{children:e.children?(0,s.jsxs)("div",{children:[(0,s.jsxs)("button",{onClick:()=>m(e.name),className:(0,d.cn)("w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors",h(e.children)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"),children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(e.icon,{className:"h-5 w-5 mr-3"}),e.name]}),r.includes(e.name)?s.jsx(v.Z,{className:"h-4 w-4"}):s.jsx(y.Z,{className:"h-4 w-4"})]}),r.includes(e.name)&&s.jsx("div",{className:"mt-2 space-y-1",children:e.children.map(e=>s.jsx(l.default,{href:e.href,onClick:()=>a(!1),className:(0,d.cn)("block px-3 py-2 ml-6 text-sm rounded-lg transition-colors",x(e.href)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"),children:e.name},e.name))})]}):(0,s.jsxs)(l.default,{href:e.href,onClick:()=>a(!1),className:(0,d.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",x(e.href)?"bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300":"text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"),children:[s.jsx(e.icon,{className:"h-5 w-5 mr-3"}),e.name]})},e.name))}),s.jsx("div",{className:"p-4 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400 text-center",children:["TiXara Organizer Panel",s.jsx("br",{}),"v1.0.0"]})})]})})]})}var k=r(16212),z=r(20886),Z=r(69436),C=r(46064),O=r(23485),E=r(18822),D=r(48120);function B(){let{data:e}=(0,t.useSession)(),[a]=(0,i.useState)(3);return s.jsx("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:s.jsx("div",{className:"px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between items-center h-16",children:[s.jsx("div",{className:"flex items-center",children:s.jsx("h1",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Organizer Dashboard"})}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,s.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[s.jsx(k.z,{size:"sm",variant:"outline",asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/events/create",children:[s.jsx(o.Z,{className:"h-4 w-4 mr-1"}),"Buat Event"]})}),s.jsx(k.z,{size:"sm",variant:"outline",asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/boost",children:[s.jsx(C.Z,{className:"h-4 w-4 mr-1"}),"Boost Event"]})})]}),(0,s.jsxs)(z.h_,{children:[s.jsx(z.$F,{asChild:!0,children:(0,s.jsxs)(k.z,{variant:"ghost",size:"sm",className:"relative",children:[s.jsx(u.Z,{className:"h-5 w-5"}),a>0&&s.jsx("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:a})]})}),(0,s.jsxs)(z.AW,{align:"end",className:"w-80",children:[s.jsx(z.Ju,{children:"Notifikasi"}),s.jsx(z.VD,{}),(0,s.jsxs)("div",{className:"max-h-64 overflow-y-auto",children:[(0,s.jsxs)(z.Xi,{className:"flex flex-col items-start p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[s.jsx("div",{className:"h-2 w-2 bg-blue-500 rounded-full"}),s.jsx("span",{className:"font-medium text-sm",children:"Order Artposure Baru"}),s.jsx("span",{className:"text-xs text-gray-500 ml-auto",children:"2 jam lalu"})]}),s.jsx("p",{className:"text-xs text-gray-600 mt-1",children:'Order untuk desain poster event "Music Festival 2024"'})]}),(0,s.jsxs)(z.Xi,{className:"flex flex-col items-start p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[s.jsx("div",{className:"h-2 w-2 bg-green-500 rounded-full"}),s.jsx("span",{className:"font-medium text-sm",children:"Boost Event Aktif"}),s.jsx("span",{className:"text-xs text-gray-500 ml-auto",children:"1 hari lalu"})]}),s.jsx("p",{className:"text-xs text-gray-600 mt-1",children:'Event "Tech Conference" berhasil di-boost'})]}),(0,s.jsxs)(z.Xi,{className:"flex flex-col items-start p-3",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2 w-full",children:[s.jsx("div",{className:"h-2 w-2 bg-yellow-500 rounded-full"}),s.jsx("span",{className:"font-medium text-sm",children:"Badge Upgrade"}),s.jsx("span",{className:"text-xs text-gray-500 ml-auto",children:"3 hari lalu"})]}),s.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"Selamat! Badge Anda telah upgrade ke Silver"})]})]}),s.jsx(z.VD,{}),s.jsx(z.Xi,{asChild:!0,children:s.jsx(l.default,{href:"/organizer/notifications",className:"w-full text-center",children:"Lihat Semua Notifikasi"})})]})]}),(0,s.jsxs)(z.h_,{children:[s.jsx(z.$F,{asChild:!0,children:s.jsx(k.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsxs)(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{className:"h-8 w-8",children:[s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{src:e?.user?.image||"",alt:e?.user?.name||""}),s.jsx(Object(function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}()),{children:e?.user?.name?e.user.name.split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2):"OR"})]})})}),(0,s.jsxs)(z.AW,{className:"w-56",align:"end",forceMount:!0,children:[s.jsx(z.Ju,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[s.jsx("p",{className:"text-sm font-medium leading-none",children:e?.user?.name||"Organizer"}),s.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:e?.user?.email}),(0,s.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[(0,s.jsxs)(Z.C,{className:(e=>{switch(e){case"BRONZE":return"bg-amber-100 text-amber-800";case"SILVER":default:return"bg-gray-100 text-gray-800";case"GOLD":return"bg-yellow-100 text-yellow-800";case"TITANIUM":return"bg-purple-100 text-purple-800"}})(e?.user?.badge||"BRONZE"),children:[s.jsx(h.Z,{className:"h-3 w-3 mr-1"}),e?.user?.badge||"BRONZE"]}),(0,s.jsxs)(Z.C,{variant:"outline",children:[s.jsx(O.Z,{className:"h-3 w-3 mr-1"}),"Organizer"]})]})]})}),s.jsx(z.VD,{}),s.jsx(z.Xi,{asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/settings/profile",className:"flex items-center",children:[s.jsx(E.Z,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Profil"})]})}),s.jsx(z.Xi,{asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/wallet",className:"flex items-center",children:[s.jsx(g.Z,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"UangtiX Wallet"})]})}),s.jsx(z.Xi,{asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/badges",className:"flex items-center",children:[s.jsx(h.Z,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Badge & Langganan"})]})}),s.jsx(z.Xi,{asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/artposure",className:"flex items-center",children:[s.jsx(x.Z,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Jasa Artposure"})]})}),s.jsx(z.Xi,{asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/boost",className:"flex items-center",children:[s.jsx(C.Z,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Event Booster"})]})}),s.jsx(z.VD,{}),s.jsx(z.Xi,{asChild:!0,children:(0,s.jsxs)(l.default,{href:"/organizer/settings",className:"flex items-center",children:[s.jsx(j.Z,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Pengaturan"})]})}),s.jsx(z.VD,{}),(0,s.jsxs)(z.Xi,{className:"text-red-600 dark:text-red-400",onClick:()=>(0,t.signOut)({callbackUrl:"/"}),children:[s.jsx(D.Z,{className:"mr-2 h-4 w-4"}),s.jsx("span",{children:"Keluar"})]})]})]})]})]})})})}!function(){var e=Error("Cannot find module '@/components/ui/avatar'");throw e.code="MODULE_NOT_FOUND",e}();var U=r(42739);function R({children:e}){let{data:a,status:r}=(0,t.useSession)(),i=(0,n.useRouter)();return"loading"===r?s.jsx("div",{className:"flex items-center justify-center min-h-screen",children:s.jsx(U.Z,{className:"h-8 w-8 animate-spin"})}):a?.user&&["ORGANIZER","ADMIN"].includes(a.user.role)?(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[s.jsx(w,{}),(0,s.jsxs)("div",{className:"lg:pl-64",children:[s.jsx(B,{}),s.jsx("main",{className:"py-6",children:s.jsx("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:e})})]})]}):(i.push("/dashboard"),null)}},69436:(e,a,r)=>{"use strict";r.d(a,{C:()=>l});var s=r(95344);r(3729);var t=r(92193),n=r(91626);let i=(0,t.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-600"}},defaultVariants:{variant:"default"}});function l({className:e,variant:a,...r}){return s.jsx("div",{className:(0,n.cn)(i({variant:a}),e),...r})}},61351:(e,a,r)=>{"use strict";r.d(a,{Ol:()=>l,SZ:()=>c,Zb:()=>i,aY:()=>o,ll:()=>d});var s=r(95344),t=r(3729),n=r(91626);let i=t.forwardRef(({className:e,elevated:a=!1,padding:r="md",...t},i)=>s.jsx("div",{ref:i,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground",a&&"shadow-soft hover:shadow-medium transition-shadow duration-300",{"p-0":"none"===r,"p-3":"sm"===r,"p-6":"md"===r,"p-8":"lg"===r},e),...t}));i.displayName="Card";let l=t.forwardRef(({className:e,...a},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...a}));l.displayName="CardHeader";let d=t.forwardRef(({className:e,...a},r)=>s.jsx("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));d.displayName="CardTitle";let c=t.forwardRef(({className:e,...a},r)=>s.jsx("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let o=t.forwardRef(({className:e,...a},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...a}));o.displayName="CardContent",t.forwardRef(({className:e,...a},r)=>s.jsx("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},46064:(e,a,r)=>{"use strict";r.d(a,{Z:()=>s});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=(0,r(69224).Z)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},29146:(e,a,r)=>{"use strict";r.r(a),r.d(a,{$$typeof:()=>n,__esModule:()=>t,default:()=>i});let s=(0,r(86843).createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\app\organizer\layout.tsx`),{__esModule:t,$$typeof:n}=s,i=s.default}};