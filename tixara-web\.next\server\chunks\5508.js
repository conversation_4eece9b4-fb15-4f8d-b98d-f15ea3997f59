"use strict";exports.id=5508,exports.ids=[5508],exports.modules={55508:(e,a,t)=>{t.d(a,{UangtiXWallet:()=>n}),t(6113),process.env.TRIPAY_BASE_URL,process.env.TRIPAY_MERCHANT_CODE,process.env.TRIPAY_API_KEY,process.env.TRIPAY_PRIVATE_KEY,process.env.MIDTRANS_BASE_URL,process.env.MIDTRANS_SERVER_KEY,process.env.MIDTRANS_CLIENT_KEY,process.env.XENDIT_BASE_URL,process.env.XENDIT_SECRET_KEY;class n{static async getBalance(e){let{prisma:a}=await t.e(7517).then(t.bind(t,57517)),n=await a.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});return n?.uangtixBalance||0}static async addBalance(e,a,n,r){let{prisma:i}=await t.e(7517).then(t.bind(t,57517));try{return await i.$transaction(async t=>{let i=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!i)throw Error("User not found");let s=i.uangtixBalance,c=s+a;await t.user.update({where:{id:e},data:{uangtixBalance:c}}),await t.uangtiXTransaction.create({data:{userId:e,type:a>0?"DEPOSIT":"WITHDRAW",amount:Math.abs(a),description:n,reference:r,status:"SUCCESS",balanceBefore:s,balanceAfter:c}})}),!0}catch(e){return console.error("UangtiX add balance error:",e),!1}}static async transfer(e,a,n,r){let{prisma:i}=await t.e(7517).then(t.bind(t,57517));try{return await i.$transaction(async t=>{let i=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!i||i.uangtixBalance<n)throw Error("Insufficient balance");let s=await t.user.findUnique({where:{id:a},select:{uangtixBalance:!0}});if(!s)throw Error("Receiver not found");let c=i.uangtixBalance,u=c-n;await t.user.update({where:{id:e},data:{uangtixBalance:u}});let o=s.uangtixBalance,l=o+n;await t.user.update({where:{id:a},data:{uangtixBalance:l}}),await t.uangtiXTransaction.createMany({data:[{userId:e,type:"TRANSFER",amount:-n,description:`Transfer ke ${a}: ${r}`,reference:a,status:"SUCCESS",balanceBefore:c,balanceAfter:u},{userId:a,type:"TRANSFER",amount:n,description:`Transfer dari ${e}: ${r}`,reference:e,status:"SUCCESS",balanceBefore:o,balanceAfter:l}]})}),!0}catch(e){return console.error("UangtiX transfer error:",e),!1}}}}};