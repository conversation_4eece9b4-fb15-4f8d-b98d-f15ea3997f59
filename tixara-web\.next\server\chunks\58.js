"use strict";exports.id=58,exports.ids=[58],exports.modules={58:(e,t,i)=>{i.d(t,{F8:()=>l,ZY:()=>s,a:()=>c,generateQRCode:()=>d,generateTicketCode:()=>o,kT:()=>n,qE:()=>g});var r=i(77670),a=i(63721);function o(){let e=(0,a.x0)(8).toUpperCase();return`TIX-${e}`}async function d(e){try{return await r.toDataURL(e,{errorCorrectionLevel:"M",type:"image/png",quality:.92,margin:1,color:{dark:"#000000",light:"#FFFFFF"},width:256})}catch(e){throw console.error("Error generating QR code:",e),Error("Failed to generate QR code")}}function n(e,t){let i=e;return Object.entries({"{{eventName}}":t.eventName,"{{category}}":t.category,"{{buyerName}}":t.buyerName,"{{qr}}":t.qr,"{{isVerified}}":t.isVerified?"✓ Verified":"","{{adminFee}}":`Rp ${t.adminFee.toLocaleString("id-ID")}`,"{{ticketCode}}":t.ticketCode,"{{eventDate}}":t.eventDate,"{{eventLocation}}":t.eventLocation,"{{organizerName}}":t.organizerName,"{{price}}":0===t.price?"Gratis":`Rp ${t.price.toLocaleString("id-ID")}`}).forEach(([e,t])=>{i=i.replace(RegExp(e,"g"),t.toString())}),i}function c(e){let t=e;return Object.entries({"<temptix>":'<div class="ticket-container">',"</temptix>":"</div>","<ticket-header>":'<div class="ticket-header">',"</ticket-header>":"</div>","<ticket-body>":'<div class="ticket-body">',"</ticket-body>":"</div>","<event>":'<div class="event-name">',"</event>":"</div>","<category>":'<div class="event-category">',"</category>":"</div>","<buyer>":'<div class="buyer-name">',"</buyer>":"</div>","<qr-code":'<img class="qr-code"',"/>":" />","<verified>":'<div class="verified-badge">',"</verified>":"</div>","<admin-fee>":'<div class="admin-fee">',"</admin-fee>":"</div>"}).forEach(([e,i])=>{t=t.replace(RegExp(e,"g"),i)}),`
    <style>
      .ticket-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 20px;
        border: 2px dashed #0066cc;
        border-radius: 10px;
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        font-family: 'Arial', sans-serif;
        color: #1e40af;
      }
      .ticket-header {
        text-align: center;
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #0066cc;
      }
      .ticket-body {
        text-align: center;
      }
      .event-name {
        font-size: 20px;
        font-weight: bold;
        margin: 10px 0;
        color: #1e40af;
      }
      .event-category {
        font-size: 14px;
        color: #64748b;
        margin: 5px 0;
      }
      .buyer-name {
        font-size: 16px;
        margin: 15px 0;
        padding: 10px;
        background: rgba(255, 255, 255, 0.7);
        border-radius: 5px;
      }
      .qr-code {
        width: 150px;
        height: 150px;
        margin: 15px 0;
        border: 2px solid #0066cc;
        border-radius: 5px;
      }
      .verified-badge {
        color: #059669;
        font-weight: bold;
        margin: 10px 0;
      }
      .admin-fee {
        font-size: 12px;
        color: #64748b;
        margin-top: 15px;
      }
    </style>
  `+t}let s=`<temptix>
  <ticket-header>🎫 TiXara</ticket-header>
  <ticket-body>
    <event>{{eventName}}</event>
    <category>{{category}}</category>
    <buyer>{{buyerName}}</buyer>
    <qr-code src="{{qr}}" />
    <verified>{{isVerified}}</verified>
    <admin-fee>Biaya Admin: {{adminFee}}</admin-fee>
  </ticket-body>
</temptix>`;function g(e){try{let t=e.split(":");if(4!==t.length||"TIXARA"!==t[0])return{isValid:!1,error:"Format QR code tidak valid"};let[,i,r,a]=t;if(!i||!r||!a)return{isValid:!1,error:"Data QR code tidak lengkap"};return{isValid:!0,ticketId:i,eventId:r}}catch(e){return{isValid:!1,error:"QR code tidak dapat dibaca"}}}function l(e,t){let i=Date.now();return`TIXARA:${e}:${t}:${i}`}}};