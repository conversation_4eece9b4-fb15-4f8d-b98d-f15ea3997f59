"use strict";exports.id=6112,exports.ids=[6112],exports.modules={76112:(e,t,a)=>{a.d(t,{H4:()=>u,PaymentFactory:()=>l,UangtiXWallet:()=>d,YZ:()=>c,Z4:()=>o});var r=a(6113),s=a.n(r),n=a(83949);let i={TRIPAY:{baseUrl:process.env.TRIPAY_BASE_URL||"https://tripay.co.id/api-sandbox",merchantCode:process.env.TRIPAY_MERCHANT_CODE||"",apiKey:process.env.TRIPAY_API_KEY||"",privateKey:process.env.TRIPAY_PRIVATE_KEY||""},MIDTRANS:{baseUrl:process.env.MIDTRANS_BASE_URL||"https://api.sandbox.midtrans.com/v2",serverKey:process.env.MIDTRANS_SERVER_KEY||"",clientKey:process.env.MIDTRANS_CLIENT_KEY||""},XENDIT:{baseUrl:process.env.XENDIT_BASE_URL||"https://api.xendit.co",secretKey:process.env.XENDIT_SECRET_KEY||""}};class c{generateSignature(e){let t=JSON.stringify(e);return s().createHmac("sha256",this.config.privateKey).update(t).digest("hex")}async getPaymentChannels(){try{let e=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+this.config.apiKey).digest("hex");return(await n.Z.get(`${this.config.baseUrl}/merchant/payment-channel`,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":e}})).data}catch(e){throw console.error("Tripay get channels error:",e),e}}async createPayment(e,t="QRIS"){try{let a=e.expiredTime||60,r=new Date(Date.now()+6e4*a),i={method:t,merchant_ref:e.orderId,amount:e.amount,customer_name:e.customerName,customer_email:e.customerEmail,customer_phone:e.customerPhone||"",order_items:[{sku:"TICKET",name:e.description,price:e.amount,quantity:1}],return_url:e.returnUrl||"http://localhost:3000/payment/success",expired_time:Math.floor(r.getTime()/1e3),signature:""},c=this.config.merchantCode+e.orderId+e.amount;i.signature=s().createHmac("sha256",this.config.privateKey).update(c).digest("hex");let o=await n.Z.post(`${this.config.baseUrl}/transaction/create`,i,{headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"}});if(o.data.success)return{success:!0,paymentId:o.data.data.reference,paymentUrl:o.data.data.checkout_url,qrCode:o.data.data.qr_url,expiredAt:r,data:o.data.data};return{success:!1,paymentId:"",message:o.data.message}}catch(e){return console.error("Tripay create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let t=s().createHmac("sha256",this.config.privateKey).update(this.config.merchantCode+e).digest("hex");return(await n.Z.get(`${this.config.baseUrl}/transaction/detail`,{params:{reference:e},headers:{Authorization:`Bearer ${this.config.apiKey}`,"X-Signature":t}})).data}catch(e){throw console.error("Tripay check status error:",e),e}}verifyCallback(e,t){return s().createHmac("sha256",this.config.privateKey).update(JSON.stringify(e)).digest("hex")===t}constructor(){this.config=i.TRIPAY}}class o{async createPayment(e){try{let t={transaction_details:{order_id:e.orderId,gross_amount:e.amount},customer_details:{first_name:e.customerName,email:e.customerEmail,phone:e.customerPhone||""},item_details:[{id:"TICKET",price:e.amount,quantity:1,name:e.description}],credit_card:{secure:!0}},a=Buffer.from(this.config.serverKey+":").toString("base64"),r=await n.Z.post(`${this.config.baseUrl}/charge`,t,{headers:{Authorization:`Basic ${a}`,"Content-Type":"application/json"}});if("201"===r.data.status_code)return{success:!0,paymentId:r.data.transaction_id,paymentUrl:r.data.redirect_url,data:r.data};return{success:!1,paymentId:"",message:r.data.status_message}}catch(e){return console.error("Midtrans create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.status_message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let t=Buffer.from(this.config.serverKey+":").toString("base64");return(await n.Z.get(`${this.config.baseUrl}/${e}/status`,{headers:{Authorization:`Basic ${t}`}})).data}catch(e){throw console.error("Midtrans check status error:",e),e}}verifyCallback(e,t){let a=e.order_id,r=e.status_code,n=e.gross_amount;return s().createHash("sha512").update(a+r+n+this.config.serverKey).digest("hex")===t}constructor(){this.config=i.MIDTRANS}}class u{async createPayment(e){try{let t=e.expiredTime||60,a=new Date(Date.now()+6e4*t),r={external_id:e.orderId,amount:e.amount,description:e.description,payer_email:e.customerEmail,success_redirect_url:e.returnUrl||"http://localhost:3000/payment/success",failure_redirect_url:e.returnUrl||"http://localhost:3000/payment/failed",invoice_duration:60*t},s=Buffer.from(this.config.secretKey+":").toString("base64"),i=await n.Z.post(`${this.config.baseUrl}/v2/invoices`,r,{headers:{Authorization:`Basic ${s}`,"Content-Type":"application/json"}});return{success:!0,paymentId:i.data.id,paymentUrl:i.data.invoice_url,expiredAt:a,data:i.data}}catch(e){return console.error("Xendit create payment error:",e),{success:!1,paymentId:"",message:e.response?.data?.message||"Payment creation failed"}}}async checkPaymentStatus(e){try{let t=Buffer.from(this.config.secretKey+":").toString("base64");return(await n.Z.get(`${this.config.baseUrl}/v2/invoices/${e}`,{headers:{Authorization:`Basic ${t}`}})).data}catch(e){throw console.error("Xendit check status error:",e),e}}verifyCallback(e,t){return t===process.env.XENDIT_WEBHOOK_TOKEN}constructor(){this.config=i.XENDIT}}class d{static async getBalance(e){let{prisma:t}=await Promise.resolve().then(a.bind(a,3214)),r=await t.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});return r?.uangtixBalance||0}static async addBalance(e,t,r,s){let{prisma:n}=await Promise.resolve().then(a.bind(a,3214));try{return await n.$transaction(async a=>{let n=await a.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!n)throw Error("User not found");let i=n.uangtixBalance,c=i+t;await a.user.update({where:{id:e},data:{uangtixBalance:c}}),await a.uangtiXTransaction.create({data:{userId:e,type:t>0?"DEPOSIT":"WITHDRAW",amount:Math.abs(t),description:r,reference:s,status:"SUCCESS",balanceBefore:i,balanceAfter:c}})}),!0}catch(e){return console.error("UangtiX add balance error:",e),!1}}static async transfer(e,t,r,s){let{prisma:n}=await Promise.resolve().then(a.bind(a,3214));try{return await n.$transaction(async a=>{let n=await a.user.findUnique({where:{id:e},select:{uangtixBalance:!0}});if(!n||n.uangtixBalance<r)throw Error("Insufficient balance");let i=await a.user.findUnique({where:{id:t},select:{uangtixBalance:!0}});if(!i)throw Error("Receiver not found");let c=n.uangtixBalance,o=c-r;await a.user.update({where:{id:e},data:{uangtixBalance:o}});let u=i.uangtixBalance,d=u+r;await a.user.update({where:{id:t},data:{uangtixBalance:d}}),await a.uangtiXTransaction.createMany({data:[{userId:e,type:"TRANSFER",amount:-r,description:`Transfer ke ${t}: ${s}`,reference:t,status:"SUCCESS",balanceBefore:c,balanceAfter:o},{userId:t,type:"TRANSFER",amount:r,description:`Transfer dari ${e}: ${s}`,reference:e,status:"SUCCESS",balanceBefore:u,balanceAfter:d}]})}),!0}catch(e){return console.error("UangtiX transfer error:",e),!1}}}class l{static createPaymentGateway(e){switch(e.toUpperCase()){case"TRIPAY":return new c;case"MIDTRANS":return new o;case"XENDIT":return new u;default:throw Error(`Unsupported payment gateway: ${e}`)}}}}};