exports.id=9205,exports.ids=[9205],exports.modules={47313:(e,n,m)=>{Promise.resolve().then(m.bind(m,64991)),Promise.resolve().then(m.bind(m,1108)),Promise.resolve().then(m.bind(m,12001)),Promise.resolve().then(m.bind(m,83263)),Promise.resolve().then(m.t.bind(m,61476,23))},16509:(e,n,m)=>{Promise.resolve().then(m.t.bind(m,2583,23)),Promise.resolve().then(m.t.bind(m,26840,23)),Promise.resolve().then(m.t.bind(m,38771,23)),Promise.resolve().then(m.t.bind(m,13225,23)),Promise.resolve().then(m.t.bind(m,9295,23)),Promise.resolve().then(m.t.bind(m,43982,23))},64991:(e,n,m)=>{"use strict";m.r(n),m.d(n,{Header:()=>S});var t=m(95344),x=m(3729),a=m(56506),r=m(8428),b=m(47674),i=m(16212),s=m(98714),o=m(47180),d=m(6256),l=m(20886);function f(){let{setTheme:e}=(0,d.F)();return(0,t.jsxs)(l.h_,{children:[t.jsx(l.$F,{asChild:!0,children:(0,t.jsxs)(i.z,{variant:"outline",size:"icon",children:[t.jsx(s.Z,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),t.jsx(o.Z,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),t.jsx("span",{className:"sr-only",children:"Toggle theme"})]})}),(0,t.jsxs)(l.AW,{align:"end",children:[t.jsx(l.Xi,{onClick:()=>e("light"),children:"Light"}),t.jsx(l.Xi,{onClick:()=>e("dark"),children:"Dark"}),t.jsx(l.Xi,{onClick:()=>e("system"),children:"System"})]})]})}var c=m(82264),u=m(76196),g=m(76755),h=m(18822),p=m(55794),D=m(67925),y=m(23485),w=m(13746),I=m(48120),j=m(14513),v=m(98200),N=m(91626);function S(){let[e,n]=(0,x.useState)(!1),{user:m,isAuthenticated:s}=(0,c.a)();(0,r.useRouter)();let o=async()=>{await (0,b.signOut)({callbackUrl:"/"})},d=[{name:"Beranda",href:"/"},{name:"Event",href:"/events"},{name:"Kategori",href:"/categories"},{name:"Tentang",href:"/about"}];return t.jsx("header",{className:"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container mx-auto px-4",children:[(0,t.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,t.jsxs)(a.default,{href:"/",className:"flex items-center space-x-2",children:[t.jsx("div",{className:"h-8 w-8 rounded-lg bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center",children:t.jsx(u.Z,{className:"h-5 w-5 text-white"})}),t.jsx("span",{className:"text-xl font-bold text-primary-600",children:"TiXara"})]}),t.jsx("nav",{className:"hidden md:flex items-center space-x-6",children:d.map(e=>t.jsx(a.default,{href:e.href,className:"text-sm font-medium text-muted-foreground hover:text-primary-600 transition-colors",children:e.name},e.name))}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[t.jsx(f,{}),s?(0,t.jsxs)(l.h_,{children:[t.jsx(l.$F,{asChild:!0,children:t.jsx(i.z,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:t.jsx("div",{className:(0,N.cn)("h-8 w-8 rounded-full flex items-center justify-center text-white text-sm font-medium",(e=>{switch(e){case"BRONZE":return"bg-amber-600";case"SILVER":return"bg-gray-400";case"GOLD":return"bg-yellow-500";case"TITANIUM":return"bg-slate-800";default:return"bg-primary-500"}})(m?.badge)),children:m?.avatar?t.jsx("img",{src:m.avatar,alt:m.name,className:"h-8 w-8 rounded-full object-cover"}):(m?.name||"").split(" ").map(e=>e.charAt(0)).join("").toUpperCase().substring(0,2)})})}),(0,t.jsxs)(l.AW,{className:"w-56",align:"end",forceMount:!0,children:[t.jsx(l.Ju,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[t.jsx("p",{className:"text-sm font-medium leading-none",children:m?.name}),t.jsx("p",{className:"text-xs leading-none text-muted-foreground",children:m?.email}),m?.badge&&(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[t.jsx(g.Z,{className:"h-3 w-3"}),t.jsx("span",{className:"text-xs font-medium",children:m.badge})]})]})}),t.jsx(l.VD,{}),t.jsx(l.Xi,{asChild:!0,children:(0,t.jsxs)(a.default,{href:"/dashboard",className:"flex items-center",children:[t.jsx(h.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Dashboard"})]})}),t.jsx(l.Xi,{asChild:!0,children:(0,t.jsxs)(a.default,{href:"/dashboard/tickets",className:"flex items-center",children:[t.jsx(u.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Tiket Saya"})]})}),m?.role==="ORGANIZER"&&(0,t.jsxs)(t.Fragment,{children:[t.jsx(l.Xi,{asChild:!0,children:(0,t.jsxs)(a.default,{href:"/organizer/events",className:"flex items-center",children:[t.jsx(p.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Event Saya"})]})}),t.jsx(l.Xi,{asChild:!0,children:(0,t.jsxs)(a.default,{href:"/badges",className:"flex items-center",children:[t.jsx(g.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Badge & Langganan"})]})})]}),t.jsx(l.Xi,{asChild:!0,children:(0,t.jsxs)(a.default,{href:"/dashboard/wallet",className:"flex items-center",children:[t.jsx(D.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"UangtiX"})]})}),m?.role==="ADMIN"&&t.jsx(l.Xi,{asChild:!0,children:(0,t.jsxs)(a.default,{href:"/admin",className:"flex items-center",children:[t.jsx(y.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Admin Panel"})]})}),t.jsx(l.Xi,{asChild:!0,children:(0,t.jsxs)(a.default,{href:"/dashboard/settings",className:"flex items-center",children:[t.jsx(w.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Pengaturan"})]})}),t.jsx(l.VD,{}),(0,t.jsxs)(l.Xi,{onClick:o,children:[t.jsx(I.Z,{className:"mr-2 h-4 w-4"}),t.jsx("span",{children:"Keluar"})]})]})]}):(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[t.jsx(i.z,{variant:"ghost",asChild:!0,children:t.jsx(a.default,{href:"/auth/login",children:"Masuk"})}),t.jsx(i.z,{variant:"primary",asChild:!0,children:t.jsx(a.default,{href:"/auth/register",children:"Daftar"})})]}),t.jsx(i.z,{variant:"ghost",size:"icon",className:"md:hidden",onClick:()=>n(!e),children:e?t.jsx(j.Z,{className:"h-5 w-5"}):t.jsx(v.Z,{className:"h-5 w-5"})})]})]}),e&&t.jsx("div",{className:"md:hidden border-t py-4",children:t.jsx("nav",{className:"flex flex-col space-y-2",children:d.map(e=>t.jsx(a.default,{href:e.href,className:"px-2 py-2 text-sm font-medium text-muted-foreground hover:text-primary-600 transition-colors",onClick:()=>n(!1),children:e.name},e.name))})})]})})}},1108:(e,n,m)=>{"use strict";m.r(n),m.d(n,{AuthProvider:()=>a});var t=m(95344),x=m(47674);function a({children:e}){return t.jsx(x.SessionProvider,{children:e})}},12001:(e,n,m)=>{"use strict";m.r(n),m.d(n,{ThemeProvider:()=>a});var t=m(95344);m(3729);var x=m(6256);function a({children:e,...n}){return t.jsx(x.f,{...n,children:e})}},16212:(e,n,m)=>{"use strict";m.d(n,{z:()=>o});var t=m(95344),x=m(3729),a=m(32751),r=m(92193),b=m(91626),i=m(42739);let s=(0,r.j)("inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",primary:"bg-primary-500 text-white hover:bg-primary-600 shadow-md hover:shadow-lg active:scale-95",success:"bg-secondary-500 text-white hover:bg-secondary-600 shadow-md hover:shadow-lg active:scale-95",warning:"bg-accent-500 text-white hover:bg-accent-600 shadow-md hover:shadow-lg active:scale-95"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",xl:"h-12 rounded-lg px-10 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=x.forwardRef(({className:e,variant:n,size:m,asChild:x=!1,loading:r=!1,children:o,disabled:d,...l},f)=>{let c=x?a.g7:"button";return(0,t.jsxs)(c,{className:(0,b.cn)(s({variant:n,size:m,className:e})),ref:f,disabled:d||r,...l,children:[r&&t.jsx(i.Z,{className:"mr-2 h-4 w-4 animate-spin"}),o]})});o.displayName="Button"},20886:(e,n,m)=>{"use strict";m.d(n,{$F:()=>d,AW:()=>l,Ju:()=>c,VD:()=>u,Xi:()=>f,h_:()=>o});var t=m(95344),x=m(3729),a=m(28473),r=m(97751),b=m(62312),i=m(82958),s=m(91626);let o=a.fC,d=a.xz;a.ZA,a.Uv,a.Tr,a.Ee,x.forwardRef(({className:e,inset:n,children:m,...x},b)=>(0,t.jsxs)(a.fF,{ref:b,className:(0,s.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",e),...x,children:[m,t.jsx(r.Z,{className:"ml-auto h-4 w-4"})]})).displayName=a.fF.displayName,x.forwardRef(({className:e,...n},m)=>t.jsx(a.tu,{ref:m,className:(0,s.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...n})).displayName=a.tu.displayName;let l=x.forwardRef(({className:e,sideOffset:n=4,...m},x)=>t.jsx(a.Uv,{children:t.jsx(a.VY,{ref:x,sideOffset:n,className:(0,s.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...m})}));l.displayName=a.VY.displayName;let f=x.forwardRef(({className:e,inset:n,...m},x)=>t.jsx(a.ck,{ref:x,className:(0,s.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n&&"pl-8",e),...m}));f.displayName=a.ck.displayName,x.forwardRef(({className:e,children:n,checked:m,...x},r)=>(0,t.jsxs)(a.oC,{ref:r,className:(0,s.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:m,...x,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(a.wU,{children:t.jsx(b.Z,{className:"h-4 w-4"})})}),n]})).displayName=a.oC.displayName,x.forwardRef(({className:e,children:n,...m},x)=>(0,t.jsxs)(a.Rk,{ref:x,className:(0,s.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...m,children:[t.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:t.jsx(a.wU,{children:t.jsx(i.Z,{className:"h-2 w-2 fill-current"})})}),n]})).displayName=a.Rk.displayName;let c=x.forwardRef(({className:e,inset:n,...m},x)=>t.jsx(a.__,{ref:x,className:(0,s.cn)("px-2 py-1.5 text-sm font-semibold",n&&"pl-8",e),...m}));c.displayName=a.__.displayName;let u=x.forwardRef(({className:e,...n},m)=>t.jsx(a.Z0,{ref:m,className:(0,s.cn)("-mx-1 my-1 h-px bg-muted",e),...n}));u.displayName=a.Z0.displayName},83263:(e,n,m)=>{"use strict";m.r(n),m.d(n,{Toaster:()=>h});var t=m(95344),x=m(30692),a=m(3729),r=m(33390),b=m(92193),i=m(14513),s=m(91626);let o=r.zt,d=a.forwardRef(({className:e,...n},m)=>t.jsx(r.l_,{ref:m,className:(0,s.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...n}));d.displayName=r.l_.displayName;let l=(0,b.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground",success:"border-green-500 bg-green-50 text-green-900 dark:border-green-600 dark:bg-green-950 dark:text-green-100",warning:"border-yellow-500 bg-yellow-50 text-yellow-900 dark:border-yellow-600 dark:bg-yellow-950 dark:text-yellow-100"}},defaultVariants:{variant:"default"}}),f=a.forwardRef(({className:e,variant:n,...m},x)=>t.jsx(r.fC,{ref:x,className:(0,s.cn)(l({variant:n}),e),...m}));f.displayName=r.fC.displayName,a.forwardRef(({className:e,...n},m)=>t.jsx(r.aU,{ref:m,className:(0,s.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...n})).displayName=r.aU.displayName;let c=a.forwardRef(({className:e,...n},m)=>t.jsx(r.x8,{ref:m,className:(0,s.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...n,children:t.jsx(i.Z,{className:"h-4 w-4"})}));c.displayName=r.x8.displayName;let u=a.forwardRef(({className:e,...n},m)=>t.jsx(r.Dx,{ref:m,className:(0,s.cn)("text-sm font-semibold",e),...n}));u.displayName=r.Dx.displayName;let g=a.forwardRef(({className:e,...n},m)=>t.jsx(r.dk,{ref:m,className:(0,s.cn)("text-sm opacity-90",e),...n}));function h(){let{toasts:e}=(0,x.pm)();return(0,t.jsxs)(o,{children:[e.map(function({id:e,title:n,description:m,action:x,...a}){return(0,t.jsxs)(f,{...a,children:[(0,t.jsxs)("div",{className:"grid gap-1",children:[n&&t.jsx(u,{children:n}),m&&t.jsx(g,{children:m})]}),x,t.jsx(c,{})]},e)}),t.jsx(d,{})]})}g.displayName=r.dk.displayName},82264:(e,n,m)=>{"use strict";m.d(n,{a:()=>a});var t=m(47674),x=m(38163);function a(){let{data:e,status:n}=(0,t.useSession)(),m=!!e?.user,a=e?.user,r=e=>!!a&&e.includes(a.role),b=()=>a?.role===x.UserRole.ADMIN;return{user:a,isLoading:"loading"===n,isAuthenticated:m,hasRole:r,isAdmin:b,isOrganizer:()=>a?.role===x.UserRole.ORGANIZER,isBuyer:()=>a?.role===x.UserRole.BUYER,isStaff:()=>a?.role===x.UserRole.STAFF,canAccessAdminPanel:()=>b(),canCreateEvent:()=>r([x.UserRole.ADMIN,x.UserRole.ORGANIZER]),canValidateTicket:()=>r([x.UserRole.ADMIN,x.UserRole.STAFF]),canPurchaseTicket:()=>r([x.UserRole.BUYER,x.UserRole.ORGANIZER])}}},30692:(e,n,m)=>{"use strict";m.d(n,{pm:()=>l});var t=m(3729);let x=0,a=new Map,r=e=>{if(a.has(e))return;let n=setTimeout(()=>{a.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,n)},b=(e,n)=>{switch(n.type){case"ADD_TOAST":return{...e,toasts:[n.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===n.toast.id?{...e,...n.toast}:e)};case"DISMISS_TOAST":{let{toastId:m}=n;return m?r(m):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===m||void 0===m?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===n.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==n.toastId)}}},i=[],s={toasts:[]};function o(e){s=b(s,e),i.forEach(e=>{e(s)})}function d({...e}){let n=(x=(x+1)%Number.MAX_SAFE_INTEGER).toString(),m=()=>o({type:"DISMISS_TOAST",toastId:n});return o({type:"ADD_TOAST",toast:{...e,id:n,open:!0,onOpenChange:e=>{e||m()}}}),{id:n,dismiss:m,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function l(){let[e,n]=t.useState(s);return t.useEffect(()=>(i.push(n),()=>{let e=i.indexOf(n);e>-1&&i.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},91626:()=>{throw Error("Module build failed (from ./node_modules/next/dist/build/webpack/loaders/next-swc-loader.js):\nError: \n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatCurrency` is defined multiple times\n    ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:15:1]\n \x1b[2m15\x1b[0m │   }).format(new Date(date))\n \x1b[2m16\x1b[0m │ }\n \x1b[2m17\x1b[0m │ \n \x1b[2m18\x1b[0m │ export function formatCurrency(amount: number) {\n    \xb7 \x1b[38;2;246;87;248m                ───────┬──────\x1b[0m\n    \xb7                        \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatCurrency` here\x1b[0m\x1b[0m\n \x1b[2m19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m20\x1b[0m │     style: 'currency',\n \x1b[2m21\x1b[0m │     currency: 'IDR',\n \x1b[2m22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m23\x1b[0m │   }).format(amount)\n \x1b[2m24\x1b[0m │ }\n \x1b[2m25\x1b[0m │ \n \x1b[2m26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m27\x1b[0m │   const now = new Date()\n \x1b[2m28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m30\x1b[0m │ \n \x1b[2m31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m37\x1b[0m │     return formatDate(date)\n \x1b[2m38\x1b[0m │   }\n \x1b[2m39\x1b[0m │ \n \x1b[2m40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m44\x1b[0m │ \n \x1b[2m45\x1b[0m │   return formatDate(date)\n \x1b[2m46\x1b[0m │ }\n \x1b[2m47\x1b[0m │ \n \x1b[2m48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m49\x1b[0m │ export function formatCurrency(amount: number): string {\n    \xb7 \x1b[38;2;30;201;212m                ───────┬──────\x1b[0m\n    \xb7                        \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatCurrency` redefined here\x1b[0m\x1b[0m\n \x1b[2m50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m51\x1b[0m │     style: 'currency',\n \x1b[2m52\x1b[0m │     currency: 'IDR',\n    ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatDate` is defined multiple times\n    ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:5:1]\n \x1b[2m 5\x1b[0m │   return twMerge(clsx(inputs))\n \x1b[2m 6\x1b[0m │ }\n \x1b[2m 7\x1b[0m │ \n \x1b[2m 8\x1b[0m │ export function formatDate(date: string | Date) {\n    \xb7 \x1b[38;2;246;87;248m                ─────┬────\x1b[0m\n    \xb7                      \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatDate` here\x1b[0m\x1b[0m\n \x1b[2m 9\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m10\x1b[0m │     year: 'numeric',\n \x1b[2m11\x1b[0m │     month: 'long',\n \x1b[2m12\x1b[0m │     day: 'numeric',\n \x1b[2m13\x1b[0m │     hour: '2-digit',\n \x1b[2m14\x1b[0m │     minute: '2-digit',\n \x1b[2m15\x1b[0m │   }).format(new Date(date))\n \x1b[2m16\x1b[0m │ }\n \x1b[2m17\x1b[0m │ \n \x1b[2m18\x1b[0m │ export function formatCurrency(amount: number) {\n \x1b[2m19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m20\x1b[0m │     style: 'currency',\n \x1b[2m21\x1b[0m │     currency: 'IDR',\n \x1b[2m22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m23\x1b[0m │   }).format(amount)\n \x1b[2m24\x1b[0m │ }\n \x1b[2m25\x1b[0m │ \n \x1b[2m26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m27\x1b[0m │   const now = new Date()\n \x1b[2m28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m30\x1b[0m │ \n \x1b[2m31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m37\x1b[0m │     return formatDate(date)\n \x1b[2m38\x1b[0m │   }\n \x1b[2m39\x1b[0m │ \n \x1b[2m40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m44\x1b[0m │ \n \x1b[2m45\x1b[0m │   return formatDate(date)\n \x1b[2m46\x1b[0m │ }\n \x1b[2m47\x1b[0m │ \n \x1b[2m48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m51\x1b[0m │     style: 'currency',\n \x1b[2m52\x1b[0m │     currency: 'IDR',\n \x1b[2m53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m55\x1b[0m │   }).format(amount)\n \x1b[2m56\x1b[0m │ }\n \x1b[2m57\x1b[0m │ \n \x1b[2m58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m59\x1b[0m │ export function formatDate(date: Date): string {\n    \xb7 \x1b[38;2;30;201;212m                ─────┬────\x1b[0m\n    \xb7                      \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatDate` redefined here\x1b[0m\x1b[0m\n \x1b[2m60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m61\x1b[0m │     weekday: 'long',\n \x1b[2m62\x1b[0m │     year: 'numeric',\n    ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatRelativeTime` is defined multiple times\n    ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:23:1]\n \x1b[2m23\x1b[0m │   }).format(amount)\n \x1b[2m24\x1b[0m │ }\n \x1b[2m25\x1b[0m │ \n \x1b[2m26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n    \xb7 \x1b[38;2;246;87;248m                ─────────┬────────\x1b[0m\n    \xb7                          \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatRelativeTime` here\x1b[0m\x1b[0m\n \x1b[2m27\x1b[0m │   const now = new Date()\n \x1b[2m28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m30\x1b[0m │ \n \x1b[2m31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m37\x1b[0m │     return formatDate(date)\n \x1b[2m38\x1b[0m │   }\n \x1b[2m39\x1b[0m │ \n \x1b[2m40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m44\x1b[0m │ \n \x1b[2m45\x1b[0m │   return formatDate(date)\n \x1b[2m46\x1b[0m │ }\n \x1b[2m47\x1b[0m │ \n \x1b[2m48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m51\x1b[0m │     style: 'currency',\n \x1b[2m52\x1b[0m │     currency: 'IDR',\n \x1b[2m53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m55\x1b[0m │   }).format(amount)\n \x1b[2m56\x1b[0m │ }\n \x1b[2m57\x1b[0m │ \n \x1b[2m58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m61\x1b[0m │     weekday: 'long',\n \x1b[2m62\x1b[0m │     year: 'numeric',\n \x1b[2m63\x1b[0m │     month: 'long',\n \x1b[2m64\x1b[0m │     day: 'numeric',\n \x1b[2m65\x1b[0m │   }).format(date)\n \x1b[2m66\x1b[0m │ }\n \x1b[2m67\x1b[0m │ \n \x1b[2m68\x1b[0m │ // Format date and time\n \x1b[2m69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m71\x1b[0m │     weekday: 'long',\n \x1b[2m72\x1b[0m │     year: 'numeric',\n \x1b[2m73\x1b[0m │     month: 'long',\n \x1b[2m74\x1b[0m │     day: 'numeric',\n \x1b[2m75\x1b[0m │     hour: '2-digit',\n \x1b[2m76\x1b[0m │     minute: '2-digit',\n \x1b[2m77\x1b[0m │   }).format(date)\n \x1b[2m78\x1b[0m │ }\n \x1b[2m79\x1b[0m │ \n \x1b[2m80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n    \xb7 \x1b[38;2;30;201;212m                ─────────┬────────\x1b[0m\n    \xb7                          \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatRelativeTime` redefined here\x1b[0m\x1b[0m\n \x1b[2m82\x1b[0m │   const now = new Date()\n \x1b[2m83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n    ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatCurrency` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:15:1]\n \x1b[2m 15\x1b[0m │   }).format(new Date(date))\n \x1b[2m 16\x1b[0m │ }\n \x1b[2m 17\x1b[0m │ \n \x1b[2m 18\x1b[0m │ export function formatCurrency(amount: number) {\n     \xb7 \x1b[38;2;246;87;248m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatCurrency` here\x1b[0m\x1b[0m\n \x1b[2m 19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 20\x1b[0m │     style: 'currency',\n \x1b[2m 21\x1b[0m │     currency: 'IDR',\n \x1b[2m 22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 23\x1b[0m │   }).format(amount)\n \x1b[2m 24\x1b[0m │ }\n \x1b[2m 25\x1b[0m │ \n \x1b[2m 26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m 27\x1b[0m │   const now = new Date()\n \x1b[2m 28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m 29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m 30\x1b[0m │ \n \x1b[2m 31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m 32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m 33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m 34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m 35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m 36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m 37\x1b[0m │     return formatDate(date)\n \x1b[2m 38\x1b[0m │   }\n \x1b[2m 39\x1b[0m │ \n \x1b[2m 40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m 41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m 42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m 43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m 44\x1b[0m │ \n \x1b[2m 45\x1b[0m │   return formatDate(date)\n \x1b[2m 46\x1b[0m │ }\n \x1b[2m 47\x1b[0m │ \n \x1b[2m 48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m 49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m 50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 51\x1b[0m │     style: 'currency',\n \x1b[2m 52\x1b[0m │     currency: 'IDR',\n \x1b[2m 53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m 55\x1b[0m │   }).format(amount)\n \x1b[2m 56\x1b[0m │ }\n \x1b[2m 57\x1b[0m │ \n \x1b[2m 58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m 59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m 60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 61\x1b[0m │     weekday: 'long',\n \x1b[2m 62\x1b[0m │     year: 'numeric',\n \x1b[2m 63\x1b[0m │     month: 'long',\n \x1b[2m 64\x1b[0m │     day: 'numeric',\n \x1b[2m 65\x1b[0m │   }).format(date)\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n     \xb7 \x1b[38;2;30;201;212m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatCurrency` redefined here\x1b[0m\x1b[0m\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatDate` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:5:1]\n \x1b[2m  5\x1b[0m │   return twMerge(clsx(inputs))\n \x1b[2m  6\x1b[0m │ }\n \x1b[2m  7\x1b[0m │ \n \x1b[2m  8\x1b[0m │ export function formatDate(date: string | Date) {\n     \xb7 \x1b[38;2;246;87;248m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatDate` here\x1b[0m\x1b[0m\n \x1b[2m  9\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 10\x1b[0m │     year: 'numeric',\n \x1b[2m 11\x1b[0m │     month: 'long',\n \x1b[2m 12\x1b[0m │     day: 'numeric',\n \x1b[2m 13\x1b[0m │     hour: '2-digit',\n \x1b[2m 14\x1b[0m │     minute: '2-digit',\n \x1b[2m 15\x1b[0m │   }).format(new Date(date))\n \x1b[2m 16\x1b[0m │ }\n \x1b[2m 17\x1b[0m │ \n \x1b[2m 18\x1b[0m │ export function formatCurrency(amount: number) {\n \x1b[2m 19\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 20\x1b[0m │     style: 'currency',\n \x1b[2m 21\x1b[0m │     currency: 'IDR',\n \x1b[2m 22\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 23\x1b[0m │   }).format(amount)\n \x1b[2m 24\x1b[0m │ }\n \x1b[2m 25\x1b[0m │ \n \x1b[2m 26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n \x1b[2m 27\x1b[0m │   const now = new Date()\n \x1b[2m 28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m 29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m 30\x1b[0m │ \n \x1b[2m 31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m 32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m 33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m 34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m 35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m 36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m 37\x1b[0m │     return formatDate(date)\n \x1b[2m 38\x1b[0m │   }\n \x1b[2m 39\x1b[0m │ \n \x1b[2m 40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m 41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m 42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m 43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m 44\x1b[0m │ \n \x1b[2m 45\x1b[0m │   return formatDate(date)\n \x1b[2m 46\x1b[0m │ }\n \x1b[2m 47\x1b[0m │ \n \x1b[2m 48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m 49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m 50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 51\x1b[0m │     style: 'currency',\n \x1b[2m 52\x1b[0m │     currency: 'IDR',\n \x1b[2m 53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m 55\x1b[0m │   }).format(amount)\n \x1b[2m 56\x1b[0m │ }\n \x1b[2m 57\x1b[0m │ \n \x1b[2m 58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m 59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m 60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 61\x1b[0m │     weekday: 'long',\n \x1b[2m 62\x1b[0m │     year: 'numeric',\n \x1b[2m 63\x1b[0m │     month: 'long',\n \x1b[2m 64\x1b[0m │     day: 'numeric',\n \x1b[2m 65\x1b[0m │   }).format(date)\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n     \xb7 \x1b[38;2;30;201;212m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatDate` redefined here\x1b[0m\x1b[0m\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatDateTime` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:66:1]\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n     \xb7 \x1b[38;2;246;87;248m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatDateTime` here\x1b[0m\x1b[0m\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n     \xb7 \x1b[38;2;30;201;212m                ───────┬──────\x1b[0m\n     \xb7                        \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatDateTime` redefined here\x1b[0m\x1b[0m\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `formatRelativeTime` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:23:1]\n \x1b[2m 23\x1b[0m │   }).format(amount)\n \x1b[2m 24\x1b[0m │ }\n \x1b[2m 25\x1b[0m │ \n \x1b[2m 26\x1b[0m │ export function formatRelativeTime(date: string | Date) {\n     \xb7 \x1b[38;2;246;87;248m                ─────────┬────────\x1b[0m\n     \xb7                          \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `formatRelativeTime` here\x1b[0m\x1b[0m\n \x1b[2m 27\x1b[0m │   const now = new Date()\n \x1b[2m 28\x1b[0m │   const targetDate = new Date(date)\n \x1b[2m 29\x1b[0m │   const diffInSeconds = Math.floor((targetDate.getTime() - now.getTime()) / 1000)\n \x1b[2m 30\x1b[0m │ \n \x1b[2m 31\x1b[0m │   if (diffInSeconds < 0) {\n \x1b[2m 32\x1b[0m │     const absDiff = Math.abs(diffInSeconds)\n \x1b[2m 33\x1b[0m │     if (absDiff < 60) return 'Baru saja'\n \x1b[2m 34\x1b[0m │     if (absDiff < 3600) return `${Math.floor(absDiff / 60)} menit yang lalu`\n \x1b[2m 35\x1b[0m │     if (absDiff < 86400) return `${Math.floor(absDiff / 3600)} jam yang lalu`\n \x1b[2m 36\x1b[0m │     if (absDiff < 2592000) return `${Math.floor(absDiff / 86400)} hari yang lalu`\n \x1b[2m 37\x1b[0m │     return formatDate(date)\n \x1b[2m 38\x1b[0m │   }\n \x1b[2m 39\x1b[0m │ \n \x1b[2m 40\x1b[0m │   if (diffInSeconds < 60) return 'Sebentar lagi'\n \x1b[2m 41\x1b[0m │   if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} menit lagi`\n \x1b[2m 42\x1b[0m │   if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} jam lagi`\n \x1b[2m 43\x1b[0m │   if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)} hari lagi`\n \x1b[2m 44\x1b[0m │ \n \x1b[2m 45\x1b[0m │   return formatDate(date)\n \x1b[2m 46\x1b[0m │ }\n \x1b[2m 47\x1b[0m │ \n \x1b[2m 48\x1b[0m │ // Format currency to Indonesian Rupiah\n \x1b[2m 49\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m 50\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m 51\x1b[0m │     style: 'currency',\n \x1b[2m 52\x1b[0m │     currency: 'IDR',\n \x1b[2m 53\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m 54\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m 55\x1b[0m │   }).format(amount)\n \x1b[2m 56\x1b[0m │ }\n \x1b[2m 57\x1b[0m │ \n \x1b[2m 58\x1b[0m │ // Format date to Indonesian locale\n \x1b[2m 59\x1b[0m │ export function formatDate(date: Date): string {\n \x1b[2m 60\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 61\x1b[0m │     weekday: 'long',\n \x1b[2m 62\x1b[0m │     year: 'numeric',\n \x1b[2m 63\x1b[0m │     month: 'long',\n \x1b[2m 64\x1b[0m │     day: 'numeric',\n \x1b[2m 65\x1b[0m │   }).format(date)\n \x1b[2m 66\x1b[0m │ }\n \x1b[2m 67\x1b[0m │ \n \x1b[2m 68\x1b[0m │ // Format date and time\n \x1b[2m 69\x1b[0m │ export function formatDateTime(date: Date): string {\n \x1b[2m 70\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m 71\x1b[0m │     weekday: 'long',\n \x1b[2m 72\x1b[0m │     year: 'numeric',\n \x1b[2m 73\x1b[0m │     month: 'long',\n \x1b[2m 74\x1b[0m │     day: 'numeric',\n \x1b[2m 75\x1b[0m │     hour: '2-digit',\n \x1b[2m 76\x1b[0m │     minute: '2-digit',\n \x1b[2m 77\x1b[0m │   }).format(date)\n \x1b[2m 78\x1b[0m │ }\n \x1b[2m 79\x1b[0m │ \n \x1b[2m 80\x1b[0m │ // Format relative time (e.g., \"2 hours ago\")\n \x1b[2m 81\x1b[0m │ export function formatRelativeTime(date: Date): string {\n \x1b[2m 82\x1b[0m │   const now = new Date()\n \x1b[2m 83\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)\n \x1b[2m 84\x1b[0m │ \n \x1b[2m 85\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m 86\x1b[0m │     return 'Baru saja'\n \x1b[2m 87\x1b[0m │   }\n \x1b[2m 88\x1b[0m │ \n \x1b[2m 89\x1b[0m │   const diffInMinutes = Math.floor(diffInSeconds / 60)\n \x1b[2m 90\x1b[0m │   if (diffInMinutes < 60) {\n \x1b[2m 91\x1b[0m │     return `${diffInMinutes} menit yang lalu`\n \x1b[2m 92\x1b[0m │   }\n \x1b[2m 93\x1b[0m │ \n \x1b[2m 94\x1b[0m │   const diffInHours = Math.floor(diffInMinutes / 60)\n \x1b[2m 95\x1b[0m │   if (diffInHours < 24) {\n \x1b[2m 96\x1b[0m │     return `${diffInHours} jam yang lalu`\n \x1b[2m 97\x1b[0m │   }\n \x1b[2m 98\x1b[0m │ \n \x1b[2m 99\x1b[0m │   const diffInDays = Math.floor(diffInHours / 24)\n \x1b[2m100\x1b[0m │   if (diffInDays < 7) {\n \x1b[2m101\x1b[0m │     return `${diffInDays} hari yang lalu`\n \x1b[2m102\x1b[0m │   }\n \x1b[2m103\x1b[0m │ \n \x1b[2m104\x1b[0m │   return formatDate(date)\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n     \xb7 \x1b[38;2;30;201;212m                ─────────┬────────\x1b[0m\n     \xb7                          \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`formatRelativeTime` redefined here\x1b[0m\x1b[0m\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `generateId` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:111:1]\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n     \xb7 \x1b[38;2;246;87;248m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `generateId` here\x1b[0m\x1b[0m\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n \x1b[2m175\x1b[0m │ \n \x1b[2m176\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m177\x1b[0m │     return 'Baru saja'\n \x1b[2m178\x1b[0m │   } else if (diffInSeconds < 3600) {\n \x1b[2m179\x1b[0m │     const minutes = Math.floor(diffInSeconds / 60)\n \x1b[2m180\x1b[0m │     return `${minutes} menit yang lalu`\n \x1b[2m181\x1b[0m │   } else if (diffInSeconds < 86400) {\n \x1b[2m182\x1b[0m │     const hours = Math.floor(diffInSeconds / 3600)\n \x1b[2m183\x1b[0m │     return `${hours} jam yang lalu`\n \x1b[2m184\x1b[0m │   } else if (diffInSeconds < 604800) {\n \x1b[2m185\x1b[0m │     const days = Math.floor(diffInSeconds / 86400)\n \x1b[2m186\x1b[0m │     return `${days} hari yang lalu`\n \x1b[2m187\x1b[0m │   } else {\n \x1b[2m188\x1b[0m │     return formatDate(dateObj)\n \x1b[2m189\x1b[0m │   }\n \x1b[2m190\x1b[0m │ }\n \x1b[2m191\x1b[0m │ \n \x1b[2m192\x1b[0m │ export function slugify(text: string): string {\n \x1b[2m193\x1b[0m │   return text\n \x1b[2m194\x1b[0m │     .toLowerCase()\n \x1b[2m195\x1b[0m │     .replace(/[^\\w\\s-]/g, '')\n \x1b[2m196\x1b[0m │     .replace(/[\\s_-]+/g, '-')\n \x1b[2m197\x1b[0m │     .replace(/^-+|-+$/g, '')\n \x1b[2m198\x1b[0m │ }\n \x1b[2m199\x1b[0m │ \n \x1b[2m200\x1b[0m │ export function generateId(): string {\n     \xb7 \x1b[38;2;30;201;212m                ─────┬────\x1b[0m\n     \xb7                      \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`generateId` redefined here\x1b[0m\x1b[0m\n \x1b[2m201\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m202\x1b[0m │ }\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `truncateText` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:105:1]\n \x1b[2m105\x1b[0m │ }\n \x1b[2m106\x1b[0m │ \n \x1b[2m107\x1b[0m │ // Truncate text with ellipsis\n \x1b[2m108\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n     \xb7 \x1b[38;2;246;87;248m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `truncateText` here\x1b[0m\x1b[0m\n \x1b[2m109\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m110\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m111\x1b[0m │ }\n \x1b[2m112\x1b[0m │ \n \x1b[2m113\x1b[0m │ // Generate random ID\n \x1b[2m114\x1b[0m │ export function generateId(): string {\n \x1b[2m115\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n \x1b[2m175\x1b[0m │ \n \x1b[2m176\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m177\x1b[0m │     return 'Baru saja'\n \x1b[2m178\x1b[0m │   } else if (diffInSeconds < 3600) {\n \x1b[2m179\x1b[0m │     const minutes = Math.floor(diffInSeconds / 60)\n \x1b[2m180\x1b[0m │     return `${minutes} menit yang lalu`\n \x1b[2m181\x1b[0m │   } else if (diffInSeconds < 86400) {\n \x1b[2m182\x1b[0m │     const hours = Math.floor(diffInSeconds / 3600)\n \x1b[2m183\x1b[0m │     return `${hours} jam yang lalu`\n \x1b[2m184\x1b[0m │   } else if (diffInSeconds < 604800) {\n \x1b[2m185\x1b[0m │     const days = Math.floor(diffInSeconds / 86400)\n \x1b[2m186\x1b[0m │     return `${days} hari yang lalu`\n \x1b[2m187\x1b[0m │   } else {\n \x1b[2m188\x1b[0m │     return formatDate(dateObj)\n \x1b[2m189\x1b[0m │   }\n \x1b[2m190\x1b[0m │ }\n \x1b[2m191\x1b[0m │ \n \x1b[2m192\x1b[0m │ export function slugify(text: string): string {\n \x1b[2m193\x1b[0m │   return text\n \x1b[2m194\x1b[0m │     .toLowerCase()\n \x1b[2m195\x1b[0m │     .replace(/[^\\w\\s-]/g, '')\n \x1b[2m196\x1b[0m │     .replace(/[\\s_-]+/g, '-')\n \x1b[2m197\x1b[0m │     .replace(/^-+|-+$/g, '')\n \x1b[2m198\x1b[0m │ }\n \x1b[2m199\x1b[0m │ \n \x1b[2m200\x1b[0m │ export function generateId(): string {\n \x1b[2m201\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m202\x1b[0m │ }\n \x1b[2m203\x1b[0m │ \n \x1b[2m204\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n     \xb7 \x1b[38;2;30;201;212m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`truncateText` redefined here\x1b[0m\x1b[0m\n \x1b[2m205\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m206\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m207\x1b[0m │ }\n     ╰────\n\n  \x1b[38;2;255;30;30m\xd7\x1b[0m the name `isValidEmail` is defined multiple times\n     ╭─[\x1b[38;2;92;157;255;1;4mD:\\Users\\Downloads\\tixara-platform\\tixara-web\\lib\\utils.ts\x1b[0m:116:1]\n \x1b[2m116\x1b[0m │ }\n \x1b[2m117\x1b[0m │ \n \x1b[2m118\x1b[0m │ // Validate email format\n \x1b[2m119\x1b[0m │ export function isValidEmail(email: string): boolean {\n     \xb7 \x1b[38;2;246;87;248m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;246;87;248m╰── \x1b[38;2;246;87;248mprevious definition of `isValidEmail` here\x1b[0m\x1b[0m\n \x1b[2m120\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m121\x1b[0m │   return emailRegex.test(email)\n \x1b[2m122\x1b[0m │ }\n \x1b[2m123\x1b[0m │ \n \x1b[2m124\x1b[0m │ // Format phone number to Indonesian format\n \x1b[2m125\x1b[0m │ export function formatPhoneNumber(phone: string): string {\n \x1b[2m126\x1b[0m │   // Remove all non-digit characters\n \x1b[2m127\x1b[0m │   const cleaned = phone.replace(/\\D/g, '')\n \x1b[2m128\x1b[0m │ \n \x1b[2m129\x1b[0m │   // Add +62 prefix if starts with 0\n \x1b[2m130\x1b[0m │   if (cleaned.startsWith('0')) {\n \x1b[2m131\x1b[0m │     return '+62' + cleaned.substring(1)\n \x1b[2m132\x1b[0m │   }\n \x1b[2m133\x1b[0m │ \n \x1b[2m134\x1b[0m │   // Add +62 prefix if doesn't start with 62\n \x1b[2m135\x1b[0m │   if (!cleaned.startsWith('62')) {\n \x1b[2m136\x1b[0m │     return '+62' + cleaned\n \x1b[2m137\x1b[0m │   }\n \x1b[2m138\x1b[0m │ \n \x1b[2m139\x1b[0m │   return '+' + cleaned\n \x1b[2m140\x1b[0m │ }\n \x1b[2m141\x1b[0m │ \n \x1b[2m142\x1b[0m │ export function formatCurrency(amount: number): string {\n \x1b[2m143\x1b[0m │   return new Intl.NumberFormat('id-ID', {\n \x1b[2m144\x1b[0m │     style: 'currency',\n \x1b[2m145\x1b[0m │     currency: 'IDR',\n \x1b[2m146\x1b[0m │     minimumFractionDigits: 0,\n \x1b[2m147\x1b[0m │     maximumFractionDigits: 0,\n \x1b[2m148\x1b[0m │   }).format(amount)\n \x1b[2m149\x1b[0m │ }\n \x1b[2m150\x1b[0m │ \n \x1b[2m151\x1b[0m │ export function formatDate(date: Date | string): string {\n \x1b[2m152\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m153\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m154\x1b[0m │     day: 'numeric',\n \x1b[2m155\x1b[0m │     month: 'long',\n \x1b[2m156\x1b[0m │     year: 'numeric',\n \x1b[2m157\x1b[0m │   }).format(dateObj)\n \x1b[2m158\x1b[0m │ }\n \x1b[2m159\x1b[0m │ \n \x1b[2m160\x1b[0m │ export function formatDateTime(date: Date | string): string {\n \x1b[2m161\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m162\x1b[0m │   return new Intl.DateTimeFormat('id-ID', {\n \x1b[2m163\x1b[0m │     day: 'numeric',\n \x1b[2m164\x1b[0m │     month: 'long',\n \x1b[2m165\x1b[0m │     year: 'numeric',\n \x1b[2m166\x1b[0m │     hour: '2-digit',\n \x1b[2m167\x1b[0m │     minute: '2-digit',\n \x1b[2m168\x1b[0m │   }).format(dateObj)\n \x1b[2m169\x1b[0m │ }\n \x1b[2m170\x1b[0m │ \n \x1b[2m171\x1b[0m │ export function formatRelativeTime(date: Date | string): string {\n \x1b[2m172\x1b[0m │   const dateObj = typeof date === 'string' ? new Date(date) : date\n \x1b[2m173\x1b[0m │   const now = new Date()\n \x1b[2m174\x1b[0m │   const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000)\n \x1b[2m175\x1b[0m │ \n \x1b[2m176\x1b[0m │   if (diffInSeconds < 60) {\n \x1b[2m177\x1b[0m │     return 'Baru saja'\n \x1b[2m178\x1b[0m │   } else if (diffInSeconds < 3600) {\n \x1b[2m179\x1b[0m │     const minutes = Math.floor(diffInSeconds / 60)\n \x1b[2m180\x1b[0m │     return `${minutes} menit yang lalu`\n \x1b[2m181\x1b[0m │   } else if (diffInSeconds < 86400) {\n \x1b[2m182\x1b[0m │     const hours = Math.floor(diffInSeconds / 3600)\n \x1b[2m183\x1b[0m │     return `${hours} jam yang lalu`\n \x1b[2m184\x1b[0m │   } else if (diffInSeconds < 604800) {\n \x1b[2m185\x1b[0m │     const days = Math.floor(diffInSeconds / 86400)\n \x1b[2m186\x1b[0m │     return `${days} hari yang lalu`\n \x1b[2m187\x1b[0m │   } else {\n \x1b[2m188\x1b[0m │     return formatDate(dateObj)\n \x1b[2m189\x1b[0m │   }\n \x1b[2m190\x1b[0m │ }\n \x1b[2m191\x1b[0m │ \n \x1b[2m192\x1b[0m │ export function slugify(text: string): string {\n \x1b[2m193\x1b[0m │   return text\n \x1b[2m194\x1b[0m │     .toLowerCase()\n \x1b[2m195\x1b[0m │     .replace(/[^\\w\\s-]/g, '')\n \x1b[2m196\x1b[0m │     .replace(/[\\s_-]+/g, '-')\n \x1b[2m197\x1b[0m │     .replace(/^-+|-+$/g, '')\n \x1b[2m198\x1b[0m │ }\n \x1b[2m199\x1b[0m │ \n \x1b[2m200\x1b[0m │ export function generateId(): string {\n \x1b[2m201\x1b[0m │   return Math.random().toString(36).substring(2) + Date.now().toString(36)\n \x1b[2m202\x1b[0m │ }\n \x1b[2m203\x1b[0m │ \n \x1b[2m204\x1b[0m │ export function truncateText(text: string, maxLength: number): string {\n \x1b[2m205\x1b[0m │   if (text.length <= maxLength) return text\n \x1b[2m206\x1b[0m │   return text.substring(0, maxLength) + '...'\n \x1b[2m207\x1b[0m │ }\n \x1b[2m208\x1b[0m │ \n \x1b[2m209\x1b[0m │ export function isValidEmail(email: string): boolean {\n     \xb7 \x1b[38;2;30;201;212m                ──────┬─────\x1b[0m\n     \xb7                       \x1b[38;2;30;201;212m╰── \x1b[38;2;30;201;212m`isValidEmail` redefined here\x1b[0m\x1b[0m\n \x1b[2m210\x1b[0m │   const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n \x1b[2m211\x1b[0m │   return emailRegex.test(email)\n \x1b[2m212\x1b[0m │ }\n     ╰────\n")},27950:(e,n,m)=>{"use strict";m.r(n),m.d(n,{default:()=>P,metadata:()=>A});var t=m(25036),x=m(450),a=m.n(x),r=m(14824),b=m.n(r);m(67272);var i=m(86843);let s=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\providers\theme-provider.tsx`),{__esModule:o,$$typeof:d}=s;s.default;let l=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\providers\theme-provider.tsx#ThemeProvider`),f=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\providers\auth-provider.tsx`),{__esModule:c,$$typeof:u}=f;f.default;let g=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\providers\auth-provider.tsx#AuthProvider`),h=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\ui\toaster.tsx`),{__esModule:p,$$typeof:D}=h;h.default;let y=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\ui\toaster.tsx#Toaster`),w=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\layout\header.tsx`),{__esModule:I,$$typeof:j}=w;w.default;let v=(0,i.createProxy)(String.raw`D:\Users\Downloads\tixara-platform\tixara-web\components\layout\header.tsx#Header`);var N=m(16274),S=m(77447),T=m(74490),M=m(30870),R=m(36161),F=m(16359),k=m(89273),$=m(59608),O=m(29003);function C(){let e=new Date().getFullYear(),n={platform:[{name:"Tentang TiXara",href:"/about"},{name:"Cara Kerja",href:"/how-it-works"},{name:"Syarat & Ketentuan",href:"/terms"},{name:"Kebijakan Privasi",href:"/privacy"}],organizer:[{name:"Jual Tiket",href:"/organizer/register"},{name:"Panduan Organizer",href:"/organizer/guide"},{name:"Jasa Artposure",href:"/artposure"},{name:"Boost Event",href:"/boost"}],buyer:[{name:"Cari Event",href:"/events"},{name:"Kategori",href:"/categories"},{name:"UangtiX Wallet",href:"/wallet"},{name:"Badge System",href:"/badges"}],support:[{name:"Pusat Bantuan",href:"/help"},{name:"Hubungi Kami",href:"/contact"},{name:"FAQ",href:"/faq"},{name:"Status Sistem",href:"/status"}]};return t.jsx("footer",{className:"bg-slate-900 text-white",children:(0,t.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8",children:[(0,t.jsxs)("div",{className:"lg:col-span-2",children:[(0,t.jsxs)(N.default,{href:"/",className:"flex items-center space-x-2 mb-4",children:[t.jsx("div",{className:"h-8 w-8 rounded-lg bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center",children:t.jsx(S.Z,{className:"h-5 w-5 text-white"})}),t.jsx("span",{className:"text-xl font-bold",children:"TiXara"})]}),t.jsx("p",{className:"text-slate-300 mb-6 max-w-sm",children:"Platform e-ticketing terdepan Indonesia untuk event, konser, workshop, dan seminar. Jual beli tiket dengan mudah dan aman."}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[t.jsx(N.default,{href:"https://facebook.com/tixara-id",className:"text-slate-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",children:t.jsx(T.Z,{className:"h-5 w-5"})}),t.jsx(N.default,{href:"https://instagram.com/tixara-id",className:"text-slate-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",children:t.jsx(M.Z,{className:"h-5 w-5"})}),t.jsx(N.default,{href:"https://twitter.com/tixara-id",className:"text-slate-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",children:t.jsx(R.Z,{className:"h-5 w-5"})}),t.jsx(N.default,{href:"https://youtube.com/tixara-id",className:"text-slate-400 hover:text-white transition-colors",target:"_blank",rel:"noopener noreferrer",children:t.jsx(F.Z,{className:"h-5 w-5"})})]})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold mb-4",children:"Platform"}),t.jsx("ul",{className:"space-y-2",children:n.platform.map(e=>t.jsx("li",{children:t.jsx(N.default,{href:e.href,className:"text-slate-300 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold mb-4",children:"Organizer"}),t.jsx("ul",{className:"space-y-2",children:n.organizer.map(e=>t.jsx("li",{children:t.jsx(N.default,{href:e.href,className:"text-slate-300 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold mb-4",children:"Pembeli"}),t.jsx("ul",{className:"space-y-2",children:n.buyer.map(e=>t.jsx("li",{children:t.jsx(N.default,{href:e.href,className:"text-slate-300 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]}),(0,t.jsxs)("div",{children:[t.jsx("h3",{className:"font-semibold mb-4",children:"Bantuan"}),t.jsx("ul",{className:"space-y-2",children:n.support.map(e=>t.jsx("li",{children:t.jsx(N.default,{href:e.href,className:"text-slate-300 hover:text-white transition-colors text-sm",children:e.name})},e.name))})]})]}),t.jsx("div",{className:"border-t border-slate-700 mt-8 pt-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(k.Z,{className:"h-5 w-5 text-primary-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-slate-300",children:"Email"}),t.jsx("p",{className:"text-sm font-medium",children:"<EMAIL>"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx($.Z,{className:"h-5 w-5 text-primary-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-slate-300",children:"Telepon"}),t.jsx("p",{className:"text-sm font-medium",children:"+62 812-3456-7890"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[t.jsx(O.Z,{className:"h-5 w-5 text-primary-400"}),(0,t.jsxs)("div",{children:[t.jsx("p",{className:"text-sm text-slate-300",children:"Alamat"}),t.jsx("p",{className:"text-sm font-medium",children:"Jakarta, Indonesia"})]})]})]})}),(0,t.jsxs)("div",{className:"border-t border-slate-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center",children:[(0,t.jsxs)("p",{className:"text-sm text-slate-400",children:["\xa9 ",e," TiXara. Semua hak cipta dilindungi."]}),(0,t.jsxs)("div",{className:"flex space-x-6 mt-4 md:mt-0",children:[t.jsx(N.default,{href:"/terms",className:"text-sm text-slate-400 hover:text-white transition-colors",children:"Syarat & Ketentuan"}),t.jsx(N.default,{href:"/privacy",className:"text-sm text-slate-400 hover:text-white transition-colors",children:"Kebijakan Privasi"}),t.jsx(N.default,{href:"/cookies",className:"text-sm text-slate-400 hover:text-white transition-colors",children:"Kebijakan Cookie"})]})]})]})})}let A={title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya. Sistem penjualan dan manajemen tiket yang mudah dan terpercaya.",keywords:"e-ticketing, tiket online, event, konser, workshop, seminar, TiXara",authors:[{name:"TiXara Team"}],creator:"TiXara",publisher:"TiXara",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL("http://localhost:3000"),openGraph:{title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",url:"/",siteName:"TiXara",images:[{url:"/images/og-image.jpg",width:1200,height:630,alt:"TiXara Platform"}],locale:"id_ID",type:"website"},twitter:{card:"summary_large_image",title:"TiXara - Platform E-Ticketing Terdepan",description:"Platform e-ticketing untuk konser, workshop, event, seminar, dan jasa lainnya.",images:["/images/og-image.jpg"]},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function P({children:e}){return t.jsx("html",{lang:"id",suppressHydrationWarning:!0,children:t.jsx("body",{className:`${a().variable} ${b().variable} font-sans antialiased`,children:t.jsx(l,{attribute:"class",defaultTheme:"light",enableSystem:!0,disableTransitionOnChange:!0,children:(0,t.jsxs)(g,{children:[(0,t.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[t.jsx(v,{}),t.jsx("main",{className:"flex-1",children:e}),t.jsx(C,{})]}),t.jsx(y,{})]})})})})}},67272:()=>{}};