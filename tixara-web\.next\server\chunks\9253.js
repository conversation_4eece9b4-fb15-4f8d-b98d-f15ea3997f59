"use strict";exports.id=9253,exports.ids=[9253],exports.modules={50340:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},33037:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},85674:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2273:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},70009:(e,t,r)=>{r.d(t,{Z:()=>n});/**
 * @license lucide-react v0.344.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let n=(0,r(69224).Z)("Palette",[["circle",{cx:"13.5",cy:"6.5",r:".5",fill:"currentColor",key:"1okk4w"}],["circle",{cx:"17.5",cy:"10.5",r:".5",fill:"currentColor",key:"f64h9f"}],["circle",{cx:"8.5",cy:"7.5",r:".5",fill:"currentColor",key:"fotxhn"}],["circle",{cx:"6.5",cy:"12.5",r:".5",fill:"currentColor",key:"qy21gx"}],["path",{d:"M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.461 2 12 2z",key:"12rzf8"}]])},30080:(e,t,r)=>{/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var n=r(3729);"function"==typeof Object.is&&Object.is,n.useState,n.useEffect,n.useLayoutEffect,n.useDebugValue,t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:function(e,t){return t()}},8145:(e,t,r)=>{e.exports=r(30080)},15480:(e,t,r)=>{r.d(t,{NY:()=>j,Ee:()=>C,fC:()=>D});var n=r(3729),o=r(98462),a=r(2256),l=r(16069),i=r(62409),s=r(8145);function d(){return()=>{}}var u=r(95344),c="Avatar",[f,p]=(0,o.b)(c),[g,y]=f(c),h=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,u.jsx)(g,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,u.jsx)(i.WV.span,{...o,ref:t})})});h.displayName=c;var x="AvatarImage",v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:c=()=>{},...f}=e,p=y(x,r),g=function(e,{referrerPolicy:t,crossOrigin:r}){let o=(0,s.useSyncExternalStore)(d,()=>!0,()=>!1),a=n.useRef(null),i=o?(a.current||(a.current=new window.Image),a.current):null,[u,c]=n.useState(()=>b(i,e));return(0,l.b)(()=>{c(b(i,e))},[i,e]),(0,l.b)(()=>{let e=e=>()=>{c(e)};if(!i)return;let n=e("loaded"),o=e("error");return i.addEventListener("load",n),i.addEventListener("error",o),t&&(i.referrerPolicy=t),"string"==typeof r&&(i.crossOrigin=r),()=>{i.removeEventListener("load",n),i.removeEventListener("error",o)}},[i,r,t]),u}(o,f),h=(0,a.W)(e=>{c(e),p.onImageLoadingStatusChange(e)});return(0,l.b)(()=>{"idle"!==g&&h(g)},[g,h]),"loaded"===g?(0,u.jsx)(i.WV.img,{...f,ref:t,src:o}):null});v.displayName=x;var m="AvatarFallback",w=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=y(m,r),[s,d]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>d(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==l.imageLoadingStatus?(0,u.jsx)(i.WV.span,{...a,ref:t}):null});function b(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}w.displayName=m;var D=h,C=v,j=w},88794:(e,t,r)=>{r.d(t,{Dx:()=>en,VY:()=>er,aV:()=>et,dk:()=>eo,fC:()=>J,h_:()=>ee,x8:()=>ea,xz:()=>Q});var n=r(3729),o=r(85222),a=r(31405),l=r(98462),i=r(99048),s=r(33183),d=r(44155),u=r(27386),c=r(31179),f=r(43234),p=r(62409),g=r(1106),y=r(71210),h=r(45904),x=r(32751),v=r(95344),m="Dialog",[w,b]=(0,l.b)(m),[D,C]=w(m),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[f,p]=(0,s.T)({prop:o,defaultProp:a??!1,onChange:l,caller:m});return(0,v.jsx)(D,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.M)(),titleId:(0,i.M)(),descriptionId:(0,i.M)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};j.displayName=m;var k="DialogTrigger",R=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=C(k,r),i=(0,a.e)(t,l.triggerRef);return(0,v.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":H(l.open),...n,ref:i,onClick:(0,o.M)(e.onClick,l.onOpenToggle)})});R.displayName=k;var E="DialogPortal",[I,M]=w(E,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=C(E,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(f.z,{present:r||l.open,children:(0,v.jsx)(c.h,{asChild:!0,container:a,children:e})}))})};N.displayName=E;var O="DialogOverlay",S=n.forwardRef((e,t)=>{let r=M(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(O,e.__scopeDialog);return a.modal?(0,v.jsx)(f.z,{present:n||a.open,children:(0,v.jsx)(V,{...o,ref:t})}):null});S.displayName=O;var F=(0,x.Z8)("DialogOverlay.RemoveScroll"),V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(O,r);return(0,v.jsx)(y.Z,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(p.WV.div,{"data-state":H(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),W="DialogContent",Z=n.forwardRef((e,t)=>{let r=M(W,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(W,e.__scopeDialog);return(0,v.jsx)(f.z,{present:n||a.open,children:a.modal?(0,v.jsx)(_,{...o,ref:t}):(0,v.jsx)(P,{...o,ref:t})})});Z.displayName=W;var _=n.forwardRef((e,t)=>{let r=C(W,e.__scopeDialog),l=n.useRef(null),i=(0,a.e)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,h.Ry)(e)},[]),(0,v.jsx)(A,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault())})}),P=n.forwardRef((e,t)=>{let r=C(W,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(A,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),A=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,c=C(W,r),f=n.useRef(null),p=(0,a.e)(t,f);return(0,g.EW)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.M,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,v.jsx)(d.XB,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(X,{titleId:c.titleId}),(0,v.jsx)(G,{contentRef:f,descriptionId:c.descriptionId})]})]})}),L="DialogTitle",z=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(L,r);return(0,v.jsx)(p.WV.h2,{id:o.titleId,...n,ref:t})});z.displayName=L;var T="DialogDescription",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(T,r);return(0,v.jsx)(p.WV.p,{id:o.descriptionId,...n,ref:t})});B.displayName=T;var $="DialogClose",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C($,r);return(0,v.jsx)(p.WV.button,{type:"button",...n,ref:t,onClick:(0,o.M)(e.onClick,()=>a.onOpenChange(!1))})});function H(e){return e?"open":"closed"}q.displayName=$;var Y="DialogTitleWarning",[K,U]=(0,l.k)(Y,{contentName:W,titleName:L,docsSlug:"dialog"}),X=({titleId:e})=>{let t=U(Y),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},G=({contentRef:e,descriptionId:t})=>{let r=U("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},J=j,Q=R,ee=N,et=S,er=Z,en=z,eo=B,ea=q}};