'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Palette,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  Clock,
  DollarSign,
  Users,
  RefreshCw,
  Loader2,
  Save,
  Upload,
  Image as ImageIcon,
  Video,
  Share2,
  Flag,
  Zap,
  Award
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate } from '@/lib/utils'

interface ArtposureService {
  id: string
  name: string
  description: string
  price: number
  duration: number
  category: string
  samples: string[]
  isActive: boolean
  createdAt: string
  _count: {
    orders: number
  }
}

interface ArtposureOrder {
  id: string
  service: {
    name: string
    category: string
  }
  organizer: {
    name: string
    email: string
  }
  event?: {
    title: string
  }
  requirements: string
  status: string
  price: number
  deliveryDate: string
  result?: string
  feedback?: string
  rating?: number
  createdAt: string
}

const ARTPOSURE_CATEGORIES = [
  { value: 'POSTER', label: 'Poster', icon: ImageIcon },
  { value: 'VIDEO', label: 'Video', icon: Video },
  { value: 'SOCIAL_MEDIA', label: 'Social Media', icon: Share2 },
  { value: 'BANNER', label: 'Banner', icon: Flag },
  { value: 'LOGO', label: 'Logo', icon: Zap },
  { value: 'BRANDING', label: 'Branding', icon: Award },
]

const ORDER_STATUSES = [
  { value: 'PENDING', label: 'Pending', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'IN_PROGRESS', label: 'In Progress', color: 'bg-blue-100 text-blue-800' },
  { value: 'COMPLETED', label: 'Completed', color: 'bg-green-100 text-green-800' },
  { value: 'CANCELLED', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
]

export default function ArtposureManagementPage() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [services, setServices] = useState<ArtposureService[]>([])
  const [orders, setOrders] = useState<ArtposureOrder[]>([])
  const [editingService, setEditingService] = useState<ArtposureService | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    duration: 3,
    category: 'POSTER',
    samples: [''],
    isActive: true
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [servicesResponse, ordersResponse] = await Promise.all([
        fetch('/api/admin/artposure/services'),
        fetch('/api/admin/artposure/orders')
      ])

      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json()
        setServices(servicesData)
      }

      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json()
        setOrders(ordersData)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat data Artposure',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSaveService = async () => {
    try {
      setSaving(true)
      
      const method = editingService ? 'PUT' : 'POST'
      const url = editingService 
        ? `/api/admin/artposure/services/${editingService.id}`
        : '/api/admin/artposure/services'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          samples: formData.samples.filter(s => s.trim() !== '')
        })
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: `Service Artposure berhasil ${editingService ? 'diupdate' : 'dibuat'}`
        })
        setIsDialogOpen(false)
        resetForm()
        fetchData()
      } else {
        throw new Error('Failed to save service')
      }
    } catch (error) {
      console.error('Error saving service:', error)
      toast({
        title: 'Error',
        description: 'Gagal menyimpan service Artposure',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditService = (service: ArtposureService) => {
    setEditingService(service)
    setFormData({
      name: service.name,
      description: service.description,
      price: service.price,
      duration: service.duration,
      category: service.category,
      samples: service.samples.length > 0 ? service.samples : [''],
      isActive: service.isActive
    })
    setIsDialogOpen(true)
  }

  const handleDeleteService = async (serviceId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus service ini?')) return

    try {
      const response = await fetch(`/api/admin/artposure/services/${serviceId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: 'Service Artposure berhasil dihapus'
        })
        fetchData()
      } else {
        throw new Error('Failed to delete service')
      }
    } catch (error) {
      console.error('Error deleting service:', error)
      toast({
        title: 'Error',
        description: 'Gagal menghapus service Artposure',
        variant: 'destructive'
      })
    }
  }

  const handleUpdateOrderStatus = async (orderId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/artposure/orders/${orderId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: 'Status order berhasil diupdate'
        })
        fetchData()
      } else {
        throw new Error('Failed to update order status')
      }
    } catch (error) {
      console.error('Error updating order status:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengupdate status order',
        variant: 'destructive'
      })
    }
  }

  const resetForm = () => {
    setEditingService(null)
    setFormData({
      name: '',
      description: '',
      price: 0,
      duration: 3,
      category: 'POSTER',
      samples: [''],
      isActive: true
    })
  }

  const addSample = () => {
    setFormData(prev => ({
      ...prev,
      samples: [...prev.samples, '']
    }))
  }

  const updateSample = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      samples: prev.samples.map((s, i) => i === index ? value : s)
    }))
  }

  const removeSample = (index: number) => {
    setFormData(prev => ({
      ...prev,
      samples: prev.samples.filter((_, i) => i !== index)
    }))
  }

  const getCategoryIcon = (category: string) => {
    const categoryData = ARTPOSURE_CATEGORIES.find(c => c.value === category)
    const IconComponent = categoryData?.icon || ImageIcon
    return <IconComponent className="h-4 w-4" />
  }

  const getStatusBadge = (status: string) => {
    const statusData = ORDER_STATUSES.find(s => s.value === status)
    return (
      <Badge className={statusData?.color || 'bg-gray-100 text-gray-800'}>
        {statusData?.label || status}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Artposure Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola layanan desain dan order Artposure
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button onClick={fetchData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Service
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingService ? 'Edit Service Artposure' : 'Tambah Service Artposure'}
                </DialogTitle>
                <DialogDescription>
                  Konfigurasi layanan desain untuk organizer
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nama Service</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Desain Poster Event"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="category">Kategori</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {ARTPOSURE_CATEGORIES.map((category) => (
                          <SelectItem key={category.value} value={category.value}>
                            <div className="flex items-center gap-2">
                              <category.icon className="h-4 w-4" />
                              {category.label}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    placeholder="Deskripsi detail service..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Harga (Rp)</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: parseInt(e.target.value) }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="duration">Durasi (hari)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={formData.duration}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <Label>Sample Portfolio</Label>
                  {formData.samples.map((sample, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={sample}
                        onChange={(e) => updateSample(index, e.target.value)}
                        placeholder="URL gambar sample"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeSample(index)}
                        disabled={formData.samples.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button type="button" variant="outline" onClick={addSample}>
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Sample
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <Label>Service Aktif</Label>
                  <Switch
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleSaveService} disabled={saving}>
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {editingService ? 'Update' : 'Simpan'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="services" className="space-y-6">
        <TabsList>
          <TabsTrigger value="services">Services ({services.length})</TabsTrigger>
          <TabsTrigger value="orders">Orders ({orders.length})</TabsTrigger>
        </TabsList>

        {/* Services Tab */}
        <TabsContent value="services">
          <Card>
            <CardHeader>
              <CardTitle>Artposure Services</CardTitle>
              <CardDescription>
                Kelola layanan desain yang tersedia untuk organizer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Service</TableHead>
                    <TableHead>Kategori</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Durasi</TableHead>
                    <TableHead>Orders</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {services.map((service) => (
                    <TableRow key={service.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{service.name}</div>
                          <div className="text-sm text-gray-500 line-clamp-2">{service.description}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getCategoryIcon(service.category)}
                          <span>{ARTPOSURE_CATEGORIES.find(c => c.value === service.category)?.label}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{formatCurrency(service.price)}</div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{service.duration} hari</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-gray-400" />
                          <span>{service._count.orders}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {service.isActive ? (
                          <Badge className="bg-green-100 text-green-800">Active</Badge>
                        ) : (
                          <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEditService(service)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeleteService(service.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Hapus
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {services.length === 0 && (
                <div className="text-center py-8">
                  <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada service Artposure yang dibuat</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Artposure Orders</CardTitle>
              <CardDescription>
                Kelola order dan progres layanan desain
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Order</TableHead>
                    <TableHead>Organizer</TableHead>
                    <TableHead>Service</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Deadline</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">#{order.id.slice(-8)}</div>
                          <div className="text-sm text-gray-500">{formatDate(order.createdAt)}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <div className="font-medium">{order.organizer.name}</div>
                          <div className="text-sm text-gray-500">{order.organizer.email}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <div className="font-medium">{order.service.name}</div>
                          <div className="text-sm text-gray-500">{order.service.category}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{formatCurrency(order.price)}</div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">{formatDate(order.deliveryDate)}</div>
                      </TableCell>
                      
                      <TableCell>
                        {getStatusBadge(order.status)}
                      </TableCell>
                      
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Update Status</DropdownMenuLabel>
                            {ORDER_STATUSES.map((status) => (
                              <DropdownMenuItem
                                key={status.value}
                                onClick={() => handleUpdateOrderStatus(order.id, status.value)}
                                disabled={order.status === status.value}
                              >
                                {status.label}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {orders.length === 0 && (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada order Artposure</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
