'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Star,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Users,
  CreditCard,
  TrendingUp,
  Crown,
  Shield,
  Zap,
  Loader2,
  Save,
  RefreshCw
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate } from '@/lib/utils'

interface BadgePlan {
  id: string
  name: string
  description: string
  price: number
  duration: number // in days
  features: string[]
  isActive: boolean
  color: string
  icon: string
  maxEvents: number
  maxTicketsPerEvent: number
  commissionDiscount: number
  prioritySupport: boolean
  customBranding: boolean
  analytics: boolean
  createdAt: string
  _count: {
    subscriptions: number
  }
}

interface BadgeSubscription {
  id: string
  badge: string
  status: 'ACTIVE' | 'EXPIRED' | 'CANCELLED'
  startDate: string
  endDate: string
  user: {
    id: string
    name: string
    email: string
  }
  createdAt: string
}

export default function BadgeManagementPage() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [badgePlans, setBadgePlans] = useState<BadgePlan[]>([])
  const [subscriptions, setSubscriptions] = useState<BadgeSubscription[]>([])
  const [editingPlan, setEditingPlan] = useState<BadgePlan | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    duration: 30,
    features: [''],
    maxEvents: 0,
    maxTicketsPerEvent: 0,
    commissionDiscount: 0,
    prioritySupport: false,
    customBranding: false,
    analytics: false,
    isActive: true,
    color: '#0ea5e9',
    icon: 'star'
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [plansResponse, subscriptionsResponse] = await Promise.all([
        fetch('/api/admin/badges/plans'),
        fetch('/api/admin/badges/subscriptions')
      ])

      if (plansResponse.ok) {
        const plansData = await plansResponse.json()
        setBadgePlans(plansData)
      }

      if (subscriptionsResponse.ok) {
        const subscriptionsData = await subscriptionsResponse.json()
        setSubscriptions(subscriptionsData)
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat data badge',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSavePlan = async () => {
    try {
      setSaving(true)
      
      const method = editingPlan ? 'PUT' : 'POST'
      const url = editingPlan 
        ? `/api/admin/badges/plans/${editingPlan.id}`
        : '/api/admin/badges/plans'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          features: formData.features.filter(f => f.trim() !== '')
        })
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: `Badge plan berhasil ${editingPlan ? 'diupdate' : 'dibuat'}`
        })
        setIsDialogOpen(false)
        resetForm()
        fetchData()
      } else {
        throw new Error('Failed to save plan')
      }
    } catch (error) {
      console.error('Error saving plan:', error)
      toast({
        title: 'Error',
        description: 'Gagal menyimpan badge plan',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditPlan = (plan: BadgePlan) => {
    setEditingPlan(plan)
    setFormData({
      name: plan.name,
      description: plan.description,
      price: plan.price,
      duration: plan.duration,
      features: plan.features.length > 0 ? plan.features : [''],
      maxEvents: plan.maxEvents,
      maxTicketsPerEvent: plan.maxTicketsPerEvent,
      commissionDiscount: plan.commissionDiscount,
      prioritySupport: plan.prioritySupport,
      customBranding: plan.customBranding,
      analytics: plan.analytics,
      isActive: plan.isActive,
      color: plan.color,
      icon: plan.icon
    })
    setIsDialogOpen(true)
  }

  const handleDeletePlan = async (planId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus badge plan ini?')) return

    try {
      const response = await fetch(`/api/admin/badges/plans/${planId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: 'Badge plan berhasil dihapus'
        })
        fetchData()
      } else {
        throw new Error('Failed to delete plan')
      }
    } catch (error) {
      console.error('Error deleting plan:', error)
      toast({
        title: 'Error',
        description: 'Gagal menghapus badge plan',
        variant: 'destructive'
      })
    }
  }

  const resetForm = () => {
    setEditingPlan(null)
    setFormData({
      name: '',
      description: '',
      price: 0,
      duration: 30,
      features: [''],
      maxEvents: 0,
      maxTicketsPerEvent: 0,
      commissionDiscount: 0,
      prioritySupport: false,
      customBranding: false,
      analytics: false,
      isActive: true,
      color: '#0ea5e9',
      icon: 'star'
    })
  }

  const addFeature = () => {
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, '']
    }))
  }

  const updateFeature = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map((f, i) => i === index ? value : f)
    }))
  }

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  const getBadgeIcon = (icon: string) => {
    switch (icon) {
      case 'crown': return <Crown className="h-4 w-4" />
      case 'shield': return <Shield className="h-4 w-4" />
      case 'zap': return <Zap className="h-4 w-4" />
      default: return <Star className="h-4 w-4" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'EXPIRED':
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>
      case 'CANCELLED':
        return <Badge className="bg-gray-100 text-gray-800">Cancelled</Badge>
      default:
        return <Badge>{status}</Badge>
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Badge & Subscription Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola badge plans dan subscription user
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button onClick={fetchData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Badge Plan
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingPlan ? 'Edit Badge Plan' : 'Tambah Badge Plan'}
                </DialogTitle>
                <DialogDescription>
                  Konfigurasi badge plan untuk subscription user
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nama Badge</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Gold Plan"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="price">Harga (Rp)</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="duration">Durasi (hari)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={formData.duration}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="commissionDiscount">Diskon Komisi (%)</Label>
                    <Input
                      id="commissionDiscount"
                      type="number"
                      min="0"
                      max="100"
                      value={formData.commissionDiscount}
                      onChange={(e) => setFormData(prev => ({ ...prev, commissionDiscount: parseFloat(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="maxEvents">Max Events</Label>
                    <Input
                      id="maxEvents"
                      type="number"
                      value={formData.maxEvents}
                      onChange={(e) => setFormData(prev => ({ ...prev, maxEvents: parseInt(e.target.value) }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="maxTicketsPerEvent">Max Tiket per Event</Label>
                    <Input
                      id="maxTicketsPerEvent"
                      type="number"
                      value={formData.maxTicketsPerEvent}
                      onChange={(e) => setFormData(prev => ({ ...prev, maxTicketsPerEvent: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <Label>Features</Label>
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={feature}
                        onChange={(e) => updateFeature(index, e.target.value)}
                        placeholder="Feature description"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeFeature(index)}
                        disabled={formData.features.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button type="button" variant="outline" onClick={addFeature}>
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Feature
                  </Button>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label>Priority Support</Label>
                    <Switch
                      checked={formData.prioritySupport}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, prioritySupport: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label>Custom Branding</Label>
                    <Switch
                      checked={formData.customBranding}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, customBranding: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label>Advanced Analytics</Label>
                    <Switch
                      checked={formData.analytics}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, analytics: checked }))}
                    />
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <Label>Plan Aktif</Label>
                    <Switch
                      checked={formData.isActive}
                      onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                    />
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleSavePlan} disabled={saving}>
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {editingPlan ? 'Update' : 'Simpan'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="plans" className="space-y-6">
        <TabsList>
          <TabsTrigger value="plans">Badge Plans</TabsTrigger>
          <TabsTrigger value="subscriptions">Active Subscriptions</TabsTrigger>
        </TabsList>

        {/* Badge Plans */}
        <TabsContent value="plans">
          <Card>
            <CardHeader>
              <CardTitle>Badge Plans ({badgePlans.length})</CardTitle>
              <CardDescription>
                Kelola badge plans yang tersedia untuk subscription
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Badge</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Durasi</TableHead>
                    <TableHead>Subscribers</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {badgePlans.map((plan) => (
                    <TableRow key={plan.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center justify-center w-8 h-8 rounded-full" style={{ backgroundColor: plan.color }}>
                            {getBadgeIcon(plan.icon)}
                          </div>
                          <div>
                            <div className="font-medium">{plan.name}</div>
                            <div className="text-sm text-gray-500">{plan.description}</div>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{formatCurrency(plan.price)}</div>
                      </TableCell>
                      
                      <TableCell>
                        {plan.duration} hari
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Users className="h-4 w-4 text-gray-400" />
                          <span>{plan._count.subscriptions}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {plan.isActive ? (
                          <Badge className="bg-green-100 text-green-800">Active</Badge>
                        ) : (
                          <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEditPlan(plan)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeletePlan(plan.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Hapus
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {badgePlans.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">Belum ada badge plan yang dibuat</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Active Subscriptions */}
        <TabsContent value="subscriptions">
          <Card>
            <CardHeader>
              <CardTitle>Active Subscriptions ({subscriptions.length})</CardTitle>
              <CardDescription>
                Daftar subscription badge yang sedang aktif
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>User</TableHead>
                    <TableHead>Badge</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Start Date</TableHead>
                    <TableHead>End Date</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {subscriptions.map((subscription) => (
                    <TableRow key={subscription.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{subscription.user.name}</div>
                          <div className="text-sm text-gray-500">{subscription.user.email}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge className="bg-yellow-100 text-yellow-800">
                          <Star className="h-3 w-3 mr-1" />
                          {subscription.badge}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        {getStatusBadge(subscription.status)}
                      </TableCell>
                      
                      <TableCell>
                        {formatDate(subscription.startDate)}
                      </TableCell>
                      
                      <TableCell>
                        {formatDate(subscription.endDate)}
                      </TableCell>
                      
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Detail
                            </DropdownMenuItem>
                            {subscription.status === 'ACTIVE' && (
                              <DropdownMenuItem className="text-red-600">
                                <Trash2 className="mr-2 h-4 w-4" />
                                Cancel
                              </DropdownMenuItem>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {subscriptions.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-gray-500">Belum ada subscription aktif</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
