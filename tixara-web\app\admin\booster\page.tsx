'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Megaphone,
  Plus,
  Edit,
  Trash2,
  MoreHorizontal,
  Eye,
  Clock,
  DollarSign,
  TrendingUp,
  RefreshCw,
  Loader2,
  Save,
  Star,
  Zap,
  Target,
  Calendar,
  Users,
  Activity
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate } from '@/lib/utils'

interface BoosterPackage {
  id: string
  name: string
  description: string
  duration: number
  price: number
  features: string[]
  priority: number
  isActive: boolean
  createdAt: string
  _count: {
    boosts: number
  }
}

interface EventBoost {
  id: string
  event: {
    id: string
    title: string
    slug: string
    startDate: string
  }
  package: {
    name: string
    priority: number
  }
  organizer: {
    name: string
    email: string
  }
  startDate: string
  endDate: string
  price: number
  status: string
  createdAt: string
}

const BOOST_STATUSES = [
  { value: 'ACTIVE', label: 'Active', color: 'bg-green-100 text-green-800' },
  { value: 'EXPIRED', label: 'Expired', color: 'bg-gray-100 text-gray-800' },
  { value: 'CANCELLED', label: 'Cancelled', color: 'bg-red-100 text-red-800' },
]

export default function BoosterManagementPage() {
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [packages, setPackages] = useState<BoosterPackage[]>([])
  const [boosts, setBoosts] = useState<EventBoost[]>([])
  const [editingPackage, setEditingPackage] = useState<BoosterPackage | null>(null)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: 3,
    price: 0,
    features: [''],
    priority: 1,
    isActive: true
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [packagesResponse, boostsResponse] = await Promise.all([
        fetch('/api/admin/booster/packages'),
        fetch('/api/admin/booster/boosts')
      ])

      if (packagesResponse.ok) {
        const packagesData = await packagesResponse.json()
        setPackages(packagesData.data || [])
      }

      if (boostsResponse.ok) {
        const boostsData = await boostsResponse.json()
        setBoosts(boostsData.data || [])
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat data Booster',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleSavePackage = async () => {
    try {
      setSaving(true)
      
      const method = editingPackage ? 'PUT' : 'POST'
      const url = editingPackage 
        ? `/api/admin/booster/packages/${editingPackage.id}`
        : '/api/admin/booster/packages'

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          features: formData.features.filter(f => f.trim() !== '')
        })
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: `Paket Booster berhasil ${editingPackage ? 'diupdate' : 'dibuat'}`
        })
        setIsDialogOpen(false)
        resetForm()
        fetchData()
      } else {
        throw new Error('Failed to save package')
      }
    } catch (error) {
      console.error('Error saving package:', error)
      toast({
        title: 'Error',
        description: 'Gagal menyimpan paket Booster',
        variant: 'destructive'
      })
    } finally {
      setSaving(false)
    }
  }

  const handleEditPackage = (pkg: BoosterPackage) => {
    setEditingPackage(pkg)
    setFormData({
      name: pkg.name,
      description: pkg.description,
      duration: pkg.duration,
      price: pkg.price,
      features: pkg.features.length > 0 ? pkg.features : [''],
      priority: pkg.priority,
      isActive: pkg.isActive
    })
    setIsDialogOpen(true)
  }

  const handleDeletePackage = async (packageId: string) => {
    if (!confirm('Apakah Anda yakin ingin menghapus paket ini?')) return

    try {
      const response = await fetch(`/api/admin/booster/packages/${packageId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: 'Paket Booster berhasil dihapus'
        })
        fetchData()
      } else {
        throw new Error('Failed to delete package')
      }
    } catch (error) {
      console.error('Error deleting package:', error)
      toast({
        title: 'Error',
        description: 'Gagal menghapus paket Booster',
        variant: 'destructive'
      })
    }
  }

  const handleUpdateBoostStatus = async (boostId: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/booster/boosts/${boostId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: 'Status boost berhasil diupdate'
        })
        fetchData()
      } else {
        throw new Error('Failed to update boost status')
      }
    } catch (error) {
      console.error('Error updating boost status:', error)
      toast({
        title: 'Error',
        description: 'Gagal mengupdate status boost',
        variant: 'destructive'
      })
    }
  }

  const resetForm = () => {
    setEditingPackage(null)
    setFormData({
      name: '',
      description: '',
      duration: 3,
      price: 0,
      features: [''],
      priority: 1,
      isActive: true
    })
  }

  const addFeature = () => {
    setFormData(prev => ({
      ...prev,
      features: [...prev.features, '']
    }))
  }

  const updateFeature = (index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.map((f, i) => i === index ? value : f)
    }))
  }

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }))
  }

  const getStatusBadge = (status: string) => {
    const statusData = BOOST_STATUSES.find(s => s.value === status)
    return (
      <Badge className={statusData?.color || 'bg-gray-100 text-gray-800'}>
        {statusData?.label || status}
      </Badge>
    )
  }

  const getPriorityIcon = (priority: number) => {
    if (priority >= 3) return <Star className="h-4 w-4 text-yellow-500" />
    if (priority >= 2) return <Zap className="h-4 w-4 text-blue-500" />
    return <Target className="h-4 w-4 text-gray-500" />
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Event Booster Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola paket boost dan promosi event
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button onClick={fetchData} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Tambah Paket
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>
                  {editingPackage ? 'Edit Paket Booster' : 'Tambah Paket Booster'}
                </DialogTitle>
                <DialogDescription>
                  Konfigurasi paket promosi untuk event organizer
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nama Paket</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="e.g., Boost Premium"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="priority">Prioritas (1-5)</Label>
                    <Input
                      id="priority"
                      type="number"
                      min="1"
                      max="5"
                      value={formData.priority}
                      onChange={(e) => setFormData(prev => ({ ...prev, priority: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                    placeholder="Deskripsi detail paket boost..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">Harga (Rp)</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: parseInt(e.target.value) }))}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="duration">Durasi (hari)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={formData.duration}
                      onChange={(e) => setFormData(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <Label>Fitur Paket</Label>
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2">
                      <Input
                        value={feature}
                        onChange={(e) => updateFeature(index, e.target.value)}
                        placeholder="Fitur paket boost"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeFeature(index)}
                        disabled={formData.features.length === 1}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                  <Button type="button" variant="outline" onClick={addFeature}>
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Fitur
                  </Button>
                </div>

                <div className="flex items-center justify-between">
                  <Label>Paket Aktif</Label>
                  <Switch
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isActive: checked }))}
                  />
                </div>
              </div>

              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                  Batal
                </Button>
                <Button onClick={handleSavePackage} disabled={saving}>
                  {saving ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {editingPackage ? 'Update' : 'Simpan'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="packages" className="space-y-6">
        <TabsList>
          <TabsTrigger value="packages">Paket Boost ({packages.length})</TabsTrigger>
          <TabsTrigger value="boosts">Active Boosts ({boosts.length})</TabsTrigger>
        </TabsList>

        {/* Packages Tab */}
        <TabsContent value="packages">
          <Card>
            <CardHeader>
              <CardTitle>Booster Packages</CardTitle>
              <CardDescription>
                Kelola paket promosi yang tersedia untuk organizer
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Paket</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Durasi</TableHead>
                    <TableHead>Prioritas</TableHead>
                    <TableHead>Boosts</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {packages.map((pkg) => (
                    <TableRow key={pkg.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {getPriorityIcon(pkg.priority)}
                            {pkg.name}
                          </div>
                          <div className="text-sm text-gray-500 line-clamp-2">{pkg.description}</div>
                          <div className="flex flex-wrap gap-1 mt-2">
                            {pkg.features.slice(0, 2).map((feature, index) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {feature}
                              </Badge>
                            ))}
                            {pkg.features.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{pkg.features.length - 2} more
                              </Badge>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{formatCurrency(pkg.price)}</div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{pkg.duration} hari</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getPriorityIcon(pkg.priority)}
                          <span>Level {pkg.priority}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Activity className="h-4 w-4 text-gray-400" />
                          <span>{pkg._count.boosts}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        {pkg.isActive ? (
                          <Badge className="bg-green-100 text-green-800">Active</Badge>
                        ) : (
                          <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>
                        )}
                      </TableCell>
                      
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEditPackage(pkg)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-red-600"
                              onClick={() => handleDeletePackage(pkg.id)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Hapus
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {packages.length === 0 && (
                <div className="text-center py-8">
                  <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada paket booster yang dibuat</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Boosts Tab */}
        <TabsContent value="boosts">
          <Card>
            <CardHeader>
              <CardTitle>Active Event Boosts</CardTitle>
              <CardDescription>
                Monitor dan kelola boost event yang sedang aktif
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Organizer</TableHead>
                    <TableHead>Paket</TableHead>
                    <TableHead>Periode</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {boosts.map((boost) => (
                    <TableRow key={boost.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{boost.event.title}</div>
                          <div className="text-sm text-gray-500">
                            {formatDate(boost.event.startDate)}
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div>
                          <div className="font-medium">{boost.organizer.name}</div>
                          <div className="text-sm text-gray-500">{boost.organizer.email}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getPriorityIcon(boost.package.priority)}
                          <span>{boost.package.name}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">
                          <div>{formatDate(boost.startDate)}</div>
                          <div className="text-gray-500">s/d {formatDate(boost.endDate)}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{formatCurrency(boost.price)}</div>
                      </TableCell>
                      
                      <TableCell>
                        {getStatusBadge(boost.status)}
                      </TableCell>
                      
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Update Status</DropdownMenuLabel>
                            {BOOST_STATUSES.map((status) => (
                              <DropdownMenuItem
                                key={status.value}
                                onClick={() => handleUpdateBoostStatus(boost.id, status.value)}
                                disabled={boost.status === status.value}
                              >
                                {status.label}
                              </DropdownMenuItem>
                            ))}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {boosts.length === 0 && (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada boost event yang aktif</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
