'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { ArrowLeft, Save, Eye, Code, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { DEFAULT_TEMPTIX_TEMPLATE } from '@/lib/ticket-utils'

export default function CreateTemplatePage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [loading, setLoading] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    templateCode: DEFAULT_TEMPTIX_TEMPLATE,
    category: 'general',
    isPremium: false,
    requiredBadge: '',
    price: 0,
    isActive: true,
  })

  // Redirect jika bukan admin
  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user || session.user.role !== 'ADMIN') {
    router.push('/dashboard')
    return null
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch('/api/ticket-templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })
      const data = await response.json()
      
      if (data.success) {
        toast({
          title: 'Berhasil',
          description: 'Template berhasil dibuat',
        })
        router.push('/admin/ticket-templates')
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal membuat template',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat membuat template',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const renderPreview = () => {
    // Simple preview of the template
    let preview = formData.templateCode
    
    // Replace variables with sample data
    const sampleData = {
      '{{eventName}}': 'Sample Event Name',
      '{{category}}': 'Konser',
      '{{buyerName}}': 'John Doe',
      '{{qr}}': '[QR CODE]',
      '{{isVerified}}': '✓ Verified',
      '{{adminFee}}': 'Rp 5.000',
      '{{ticketCode}}': 'TIX-ABC123',
      '{{eventDate}}': '25 Desember 2024',
      '{{eventLocation}}': 'Jakarta Convention Center',
      '{{organizerName}}': 'Event Organizer',
      '{{price}}': 'Rp 100.000',
    }

    Object.entries(sampleData).forEach(([variable, value]) => {
      preview = preview.replace(new RegExp(variable, 'g'), value)
    })

    return (
      <div className="border rounded-lg p-4 bg-gray-50 dark:bg-gray-900">
        <div dangerouslySetInnerHTML={{ __html: preview }} />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Kembali
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Buat Template Tiket
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Buat template tiket baru dengan format .temptix
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Form Fields */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Informasi Template</CardTitle>
                <CardDescription>
                  Atur informasi dasar template tiket
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="name">Nama Template *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Masukkan nama template"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="description">Deskripsi</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Deskripsi template (opsional)"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="category">Kategori</Label>
                  <select
                    id="category"
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="general">General</option>
                    <option value="concert">Konser</option>
                    <option value="conference">Konferensi</option>
                    <option value="workshop">Workshop</option>
                    <option value="sports">Olahraga</option>
                    <option value="exhibition">Pameran</option>
                  </select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isPremium"
                    checked={formData.isPremium}
                    onCheckedChange={(checked) => handleInputChange('isPremium', checked)}
                  />
                  <Label htmlFor="isPremium">Template Premium</Label>
                </div>

                {formData.isPremium && (
                  <>
                    <div>
                      <Label htmlFor="price">Harga (Rp)</Label>
                      <Input
                        id="price"
                        type="number"
                        value={formData.price}
                        onChange={(e) => handleInputChange('price', parseInt(e.target.value) || 0)}
                        placeholder="0"
                        min="0"
                      />
                    </div>

                    <div>
                      <Label htmlFor="requiredBadge">Badge yang Diperlukan</Label>
                      <select
                        id="requiredBadge"
                        value={formData.requiredBadge}
                        onChange={(e) => handleInputChange('requiredBadge', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                      >
                        <option value="">Tidak ada</option>
                        <option value="BRONZE">Bronze</option>
                        <option value="SILVER">Silver</option>
                        <option value="GOLD">Gold</option>
                        <option value="TITANIUM">Titanium</option>
                      </select>
                    </div>
                  </>
                )}

                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => handleInputChange('isActive', checked)}
                  />
                  <Label htmlFor="isActive">Aktif</Label>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Template Code */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle>Kode Template</CardTitle>
                    <CardDescription>
                      Tulis kode .temptix untuk template tiket
                    </CardDescription>
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setPreviewMode(!previewMode)}
                  >
                    {previewMode ? <Code className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
                    {previewMode ? 'Kode' : 'Preview'}
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                {previewMode ? (
                  renderPreview()
                ) : (
                  <Textarea
                    value={formData.templateCode}
                    onChange={(e) => handleInputChange('templateCode', e.target.value)}
                    placeholder="Masukkan kode .temptix"
                    rows={20}
                    className="font-mono text-sm"
                    required
                  />
                )}
              </CardContent>
            </Card>

            {/* Variables Help */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">Variabel yang Tersedia</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-xs space-y-1 text-gray-600">
                  <div><code>{'{{eventName}}'}</code> - Nama event</div>
                  <div><code>{'{{category}}'}</code> - Kategori event</div>
                  <div><code>{'{{buyerName}}'}</code> - Nama pembeli</div>
                  <div><code>{'{{qr}}'}</code> - QR code (wajib)</div>
                  <div><code>{'{{isVerified}}'}</code> - Status verifikasi</div>
                  <div><code>{'{{adminFee}}'}</code> - Biaya admin</div>
                  <div><code>{'{{ticketCode}}'}</code> - Kode tiket</div>
                  <div><code>{'{{eventDate}}'}</code> - Tanggal event</div>
                  <div><code>{'{{eventLocation}}'}</code> - Lokasi event</div>
                  <div><code>{'{{organizerName}}'}</code> - Nama organizer</div>
                  <div><code>{'{{price}}'}</code> - Harga tiket</div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
          >
            Batal
          </Button>
          <Button type="submit" disabled={loading}>
            {loading ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Simpan Template
          </Button>
        </div>
      </form>
    </div>
  )
}
