'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { 
  Plus, 
  Search, 
  Filter, 
  Edit, 
  Trash2, 
  Eye,
  Loader2,
  FileText,
  Crown,
  Users,
  Calendar
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatDate, formatRelativeTime } from '@/lib/utils'

interface TicketTemplate {
  id: string
  name: string
  description?: string
  category: string
  isPremium: boolean
  requiredBadge?: string
  price: number
  isActive: boolean
  createdAt: string
  creator: {
    id: string
    name: string
    email: string
  }
  _count: {
    tickets: number
    events: number
  }
}

export default function TicketTemplatesPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [templates, setTemplates] = useState<TicketTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [premiumFilter, setPremiumFilter] = useState('all')

  // Redirect jika bukan admin
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user || session.user.role !== 'ADMIN') {
      router.push('/dashboard')
      return
    }
  }, [session, status, router])

  // Fetch templates
  const fetchTemplates = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (searchTerm) params.append('search', searchTerm)
      if (categoryFilter !== 'all') params.append('category', categoryFilter)
      if (premiumFilter !== 'all') params.append('isPremium', premiumFilter)

      const response = await fetch(`/api/ticket-templates?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setTemplates(data.data)
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal mengambil data template',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat mengambil data',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (session?.user?.role === 'ADMIN') {
      fetchTemplates()
    }
  }, [session, searchTerm, categoryFilter, premiumFilter])

  const handleDelete = async (templateId: string, templateName: string) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus template "${templateName}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/ticket-templates/${templateId}`, {
        method: 'DELETE',
      })
      const data = await response.json()
      
      if (data.success) {
        toast({
          title: 'Berhasil',
          description: 'Template berhasil dihapus',
        })
        fetchTemplates()
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal menghapus template',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat menghapus template',
        variant: 'destructive',
      })
    }
  }

  const toggleStatus = async (templateId: string, currentStatus: boolean) => {
    try {
      const response = await fetch(`/api/ticket-templates/${templateId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isActive: !currentStatus }),
      })
      const data = await response.json()
      
      if (data.success) {
        toast({
          title: 'Berhasil',
          description: `Template ${!currentStatus ? 'diaktifkan' : 'dinonaktifkan'}`,
        })
        fetchTemplates()
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal mengubah status template',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat mengubah status',
        variant: 'destructive',
      })
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user || session.user.role !== 'ADMIN') {
    return null
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-7xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Template Tiket
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola template tiket untuk berbagai jenis event
          </p>
        </div>
        <Button onClick={() => router.push('/admin/ticket-templates/create')}>
          <Plus className="h-4 w-4 mr-2" />
          Buat Template
        </Button>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari template..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <select
              value={categoryFilter}
              onChange={(e) => setCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="all">Semua Kategori</option>
              <option value="general">General</option>
              <option value="concert">Konser</option>
              <option value="conference">Konferensi</option>
              <option value="workshop">Workshop</option>
              <option value="sports">Olahraga</option>
            </select>
            
            <select
              value={premiumFilter}
              onChange={(e) => setPremiumFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
            >
              <option value="all">Semua Tipe</option>
              <option value="true">Premium</option>
              <option value="false">Gratis</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Templates Grid */}
      {templates.length === 0 ? (
        <div className="text-center py-12">
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Belum ada template
          </h3>
          <p className="text-gray-600 mb-4">
            Buat template tiket pertama untuk memulai
          </p>
          <Button onClick={() => router.push('/admin/ticket-templates/create')}>
            <Plus className="h-4 w-4 mr-2" />
            Buat Template
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <Card key={template.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <CardTitle className="text-lg truncate">
                      {template.name}
                    </CardTitle>
                    <CardDescription className="mt-1">
                      {template.description || 'Tidak ada deskripsi'}
                    </CardDescription>
                  </div>
                  
                  <div className="flex items-center gap-1 ml-2">
                    {template.isPremium && (
                      <Crown className="h-4 w-4 text-yellow-500" />
                    )}
                    <Badge 
                      variant={template.isActive ? 'default' : 'secondary'}
                      className="text-xs"
                    >
                      {template.isActive ? 'Aktif' : 'Nonaktif'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Kategori:</span>
                    <Badge variant="outline">{template.category}</Badge>
                  </div>
                  
                  {template.isPremium && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Harga:</span>
                      <span className="font-medium">
                        Rp {template.price.toLocaleString('id-ID')}
                      </span>
                    </div>
                  )}
                  
                  {template.requiredBadge && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Badge:</span>
                      <Badge variant="outline">{template.requiredBadge}</Badge>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      <span>{template._count.tickets} tiket</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{template._count.events} event</span>
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500">
                    Dibuat {formatRelativeTime(template.createdAt)} oleh {template.creator.name}
                  </div>
                  
                  <div className="flex items-center gap-2 pt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/admin/ticket-templates/${template.id}`)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      Lihat
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => router.push(`/admin/ticket-templates/${template.id}/edit`)}
                    >
                      <Edit className="h-3 w-3 mr-1" />
                      Edit
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => toggleStatus(template.id, template.isActive)}
                    >
                      {template.isActive ? 'Nonaktifkan' : 'Aktifkan'}
                    </Button>
                    
                    {template._count.tickets === 0 && template._count.events === 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDelete(template.id, template.name)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
