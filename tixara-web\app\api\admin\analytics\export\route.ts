import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'csv'
    const range = searchParams.get('range') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate: Date

    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        break
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
    }

    // Get comprehensive analytics data
    const [
      transactions,
      events,
      users,
      tickets
    ] = await Promise.all([
      // Transactions data
      prisma.transaction.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        select: {
          id: true,
          amount: true,
          type: true,
          status: true,
          createdAt: true,
          user: {
            select: {
              name: true,
              email: true
            }
          },
          event: {
            select: {
              title: true,
              category: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),

      // Events data
      prisma.event.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        select: {
          id: true,
          title: true,
          category: true,
          status: true,
          startDate: true,
          createdAt: true,
          organizer: {
            select: {
              name: true,
              email: true
            }
          },
          _count: {
            select: {
              tickets: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),

      // Users data
      prisma.user.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          badge: true,
          isVerified: true,
          createdAt: true,
          _count: {
            select: {
              events: true,
              tickets: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      }),

      // Tickets data
      prisma.ticket.findMany({
        where: {
          createdAt: { gte: startDate }
        },
        select: {
          id: true,
          status: true,
          createdAt: true,
          user: {
            select: {
              name: true,
              email: true
            }
          },
          event: {
            select: {
              title: true,
              category: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      })
    ])

    if (type === 'csv') {
      return generateCSV({
        transactions,
        events,
        users,
        tickets,
        range
      })
    } else if (type === 'pdf') {
      return generatePDF({
        transactions,
        events,
        users,
        tickets,
        range
      })
    }

    return NextResponse.json(
      { error: 'Invalid export type' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error exporting analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function generateCSV(data: any) {
  const { transactions, events, users, tickets, range } = data

  // Create CSV content
  let csvContent = `TiXara Analytics Report - ${range}\n`
  csvContent += `Generated on: ${new Date().toISOString()}\n\n`

  // Transactions section
  csvContent += `TRANSACTIONS\n`
  csvContent += `ID,Amount,Type,Status,Date,User,Event\n`
  transactions.forEach((t: any) => {
    csvContent += `${t.id},${t.amount},${t.type},${t.status},${t.createdAt.toISOString()},${t.user?.name || 'N/A'},${t.event?.title || 'N/A'}\n`
  })

  csvContent += `\nEVENTS\n`
  csvContent += `ID,Title,Category,Status,Start Date,Created Date,Organizer,Tickets Sold\n`
  events.forEach((e: any) => {
    csvContent += `${e.id},${e.title},${e.category || 'N/A'},${e.status},${e.startDate?.toISOString() || 'N/A'},${e.createdAt.toISOString()},${e.organizer.name},${e._count.tickets}\n`
  })

  csvContent += `\nUSERS\n`
  csvContent += `ID,Name,Email,Role,Badge,Verified,Created Date,Events,Tickets\n`
  users.forEach((u: any) => {
    csvContent += `${u.id},${u.name},${u.email},${u.role},${u.badge},${u.isVerified},${u.createdAt.toISOString()},${u._count.events},${u._count.tickets}\n`
  })

  csvContent += `\nTICKETS\n`
  csvContent += `ID,Status,Created Date,User,Event\n`
  tickets.forEach((t: any) => {
    csvContent += `${t.id},${t.status},${t.createdAt.toISOString()},${t.user?.name || 'N/A'},${t.event?.title || 'N/A'}\n`
  })

  return new NextResponse(csvContent, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename="tixara-analytics-${range}.csv"`
    }
  })
}

function generatePDF(data: any) {
  const { transactions, events, users, tickets, range } = data

  // Simple HTML to PDF conversion (in a real app, use a proper PDF library)
  const htmlContent = `
    <!DOCTYPE html>
    <html>
    <head>
      <title>TiXara Analytics Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1 { color: #0ea5e9; }
        h2 { color: #374151; margin-top: 30px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f3f4f6; }
        .summary { background-color: #f0f9ff; padding: 15px; border-radius: 5px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <h1>TiXara Analytics Report</h1>
      <p><strong>Period:</strong> ${range}</p>
      <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
      
      <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Transactions:</strong> ${transactions.length}</p>
        <p><strong>Total Events:</strong> ${events.length}</p>
        <p><strong>New Users:</strong> ${users.length}</p>
        <p><strong>Tickets Sold:</strong> ${tickets.length}</p>
        <p><strong>Total Revenue:</strong> Rp ${transactions.filter((t: any) => t.status === 'SUCCESS').reduce((sum: number, t: any) => sum + t.amount, 0).toLocaleString()}</p>
      </div>

      <h2>Recent Transactions</h2>
      <table>
        <tr>
          <th>Date</th>
          <th>Amount</th>
          <th>Type</th>
          <th>Status</th>
          <th>User</th>
        </tr>
        ${transactions.slice(0, 20).map((t: any) => `
          <tr>
            <td>${new Date(t.createdAt).toLocaleDateString()}</td>
            <td>Rp ${t.amount.toLocaleString()}</td>
            <td>${t.type}</td>
            <td>${t.status}</td>
            <td>${t.user?.name || 'N/A'}</td>
          </tr>
        `).join('')}
      </table>

      <h2>Recent Events</h2>
      <table>
        <tr>
          <th>Title</th>
          <th>Category</th>
          <th>Organizer</th>
          <th>Tickets Sold</th>
          <th>Created</th>
        </tr>
        ${events.slice(0, 20).map((e: any) => `
          <tr>
            <td>${e.title}</td>
            <td>${e.category || 'N/A'}</td>
            <td>${e.organizer.name}</td>
            <td>${e._count.tickets}</td>
            <td>${new Date(e.createdAt).toLocaleDateString()}</td>
          </tr>
        `).join('')}
      </table>
    </body>
    </html>
  `

  return new NextResponse(htmlContent, {
    headers: {
      'Content-Type': 'text/html',
      'Content-Disposition': `attachment; filename="tixara-analytics-${range}.html"`
    }
  })
}
