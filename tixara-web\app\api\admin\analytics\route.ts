import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '30d'

    // Calculate date ranges
    const now = new Date()
    let startDate: Date
    let previousStartDate: Date

    switch (range) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000)
        break
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 180 * 24 * 60 * 60 * 1000)
        break
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 730 * 24 * 60 * 60 * 1000)
        break
      default: // 30d
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
        previousStartDate = new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000)
    }

    // Parallel queries for better performance
    const [
      currentRevenue,
      previousRevenue,
      currentTickets,
      previousTickets,
      currentEvents,
      previousEvents,
      currentUsers,
      previousUsers,
      revenueData,
      userGrowthData,
      eventCategoriesData,
      topEvents,
      topOrganizers
    ] = await Promise.all([
      // Current period revenue
      prisma.transaction.aggregate({
        where: {
          status: 'SUCCESS',
          createdAt: { gte: startDate }
        },
        _sum: { amount: true }
      }),

      // Previous period revenue
      prisma.transaction.aggregate({
        where: {
          status: 'SUCCESS',
          createdAt: { gte: previousStartDate, lt: startDate }
        },
        _sum: { amount: true }
      }),

      // Current period tickets
      prisma.ticket.count({
        where: {
          status: 'ACTIVE',
          createdAt: { gte: startDate }
        }
      }),

      // Previous period tickets
      prisma.ticket.count({
        where: {
          status: 'ACTIVE',
          createdAt: { gte: previousStartDate, lt: startDate }
        }
      }),

      // Current period events
      prisma.event.count({
        where: { createdAt: { gte: startDate } }
      }),

      // Previous period events
      prisma.event.count({
        where: { createdAt: { gte: previousStartDate, lt: startDate } }
      }),

      // Current period users
      prisma.user.count({
        where: { createdAt: { gte: startDate } }
      }),

      // Previous period users
      prisma.user.count({
        where: { createdAt: { gte: previousStartDate, lt: startDate } }
      }),

      // Revenue chart data
      getRevenueChartData(startDate, range),

      // User growth chart data
      getUserGrowthChartData(startDate, range),

      // Event categories data
      getEventCategoriesData(startDate),

      // Top events
      getTopEvents(startDate),

      // Top organizers
      getTopOrganizers(startDate)
    ])

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return current > 0 ? 100 : 0
      return Math.round(((current - previous) / previous) * 100)
    }

    const analytics = {
      overview: {
        totalRevenue: currentRevenue._sum.amount || 0,
        totalTicketsSold: currentTickets,
        totalEvents: currentEvents,
        totalUsers: currentUsers,
        revenueGrowth: calculateGrowth(
          currentRevenue._sum.amount || 0,
          previousRevenue._sum.amount || 0
        ),
        ticketsGrowth: calculateGrowth(currentTickets, previousTickets),
        eventsGrowth: calculateGrowth(currentEvents, previousEvents),
        usersGrowth: calculateGrowth(currentUsers, previousUsers)
      },
      revenueChart: revenueData,
      userGrowthChart: userGrowthData,
      eventCategoriesChart: eventCategoriesData,
      topEvents,
      topOrganizers
    }

    return NextResponse.json(analytics)

  } catch (error) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

async function getRevenueChartData(startDate: Date, range: string) {
  const groupBy = range === '1y' ? 'month' : 'day'
  const format = range === '1y' ? '%Y-%m' : '%Y-%m-%d'

  const data = await prisma.$queryRaw`
    SELECT 
      DATE_FORMAT(t.createdAt, ${format}) as date,
      COALESCE(SUM(CASE WHEN t.status = 'SUCCESS' THEN t.amount ELSE 0 END), 0) as revenue,
      COUNT(CASE WHEN tk.status = 'ACTIVE' THEN 1 END) as tickets
    FROM transactions t
    LEFT JOIN tickets tk ON DATE(t.createdAt) = DATE(tk.createdAt)
    WHERE t.createdAt >= ${startDate}
    GROUP BY DATE_FORMAT(t.createdAt, ${format})
    ORDER BY date ASC
  ` as Array<{ date: string; revenue: number; tickets: number }>

  return data.map(item => ({
    date: item.date,
    revenue: Number(item.revenue),
    tickets: Number(item.tickets)
  }))
}

async function getUserGrowthChartData(startDate: Date, range: string) {
  const groupBy = range === '1y' ? 'month' : 'day'
  const format = range === '1y' ? '%Y-%m' : '%Y-%m-%d'

  const data = await prisma.$queryRaw`
    SELECT 
      DATE_FORMAT(createdAt, ${format}) as date,
      COUNT(*) as users,
      COUNT(CASE WHEN role = 'ORGANIZER' THEN 1 END) as organizers
    FROM users
    WHERE createdAt >= ${startDate}
    GROUP BY DATE_FORMAT(createdAt, ${format})
    ORDER BY date ASC
  ` as Array<{ date: string; users: number; organizers: number }>

  return data.map(item => ({
    date: item.date,
    users: Number(item.users),
    organizers: Number(item.organizers)
  }))
}

async function getEventCategoriesData(startDate: Date) {
  const data = await prisma.$queryRaw`
    SELECT 
      e.category,
      COUNT(e.id) as count,
      COALESCE(SUM(t.amount), 0) as revenue
    FROM events e
    LEFT JOIN transactions t ON e.id = t.eventId AND t.status = 'SUCCESS'
    WHERE e.createdAt >= ${startDate}
    GROUP BY e.category
    ORDER BY count DESC
  ` as Array<{ category: string; count: number; revenue: number }>

  return data.map(item => ({
    category: item.category || 'Lainnya',
    count: Number(item.count),
    revenue: Number(item.revenue)
  }))
}

async function getTopEvents(startDate: Date) {
  const events = await prisma.event.findMany({
    where: { createdAt: { gte: startDate } },
    select: {
      id: true,
      title: true,
      organizer: {
        select: { name: true }
      },
      _count: {
        select: { tickets: true }
      },
      transactions: {
        where: { status: 'SUCCESS' },
        select: { amount: true }
      }
    },
    orderBy: {
      tickets: { _count: 'desc' }
    },
    take: 5
  })

  return events.map(event => ({
    id: event.id,
    title: event.title,
    organizer: event.organizer.name,
    ticketsSold: event._count.tickets,
    revenue: event.transactions.reduce((sum, t) => sum + t.amount, 0)
  }))
}

async function getTopOrganizers(startDate: Date) {
  const organizers = await prisma.user.findMany({
    where: {
      role: UserRole.ORGANIZER,
      events: {
        some: {
          createdAt: { gte: startDate }
        }
      }
    },
    select: {
      id: true,
      name: true,
      _count: {
        select: { events: true }
      },
      events: {
        select: {
          transactions: {
            where: { status: 'SUCCESS' },
            select: { amount: true }
          },
          _count: {
            select: { tickets: true }
          }
        }
      }
    },
    take: 5
  })

  return organizers.map(organizer => ({
    id: organizer.id,
    name: organizer.name,
    eventsCount: organizer._count.events,
    totalRevenue: organizer.events.reduce((sum, event) => 
      sum + event.transactions.reduce((eventSum, t) => eventSum + t.amount, 0), 0
    ),
    totalTickets: organizer.events.reduce((sum, event) => sum + event._count.tickets, 0)
  })).sort((a, b) => b.totalRevenue - a.totalRevenue)
}
