import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ArtposureStatus } from '@prisma/client'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const order = await prisma.artposureOrder.findUnique({
      where: { id: params.id },
      include: {
        service: true,
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            verified: true,
            badgeType: true
          }
        },
        event: {
          select: {
            id: true,
            title: true,
            slug: true,
            startDate: true,
            location: true
          }
        }
      }
    })

    if (!order) {
      return NextResponse.json(
        { success: false, message: 'Order tidak ditemukan' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: order
    })
  } catch (error) {
    console.error('Get artposure order error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { status, result, feedback } = body

    // Validation
    if (!status) {
      return NextResponse.json(
        { success: false, message: 'Status wajib diisi' },
        { status: 400 }
      )
    }

    if (!Object.values(ArtposureStatus).includes(status)) {
      return NextResponse.json(
        { success: false, message: 'Status tidak valid' },
        { status: 400 }
      )
    }

    // Check if order exists
    const existingOrder = await prisma.artposureOrder.findUnique({
      where: { id: params.id },
      include: {
        organizer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        service: {
          select: {
            name: true
          }
        }
      }
    })

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, message: 'Order tidak ditemukan' },
        { status: 404 }
      )
    }

    const updateData: any = { status }

    if (result !== undefined) {
      updateData.result = result
    }

    if (feedback !== undefined) {
      updateData.feedback = feedback
    }

    const order = await prisma.artposureOrder.update({
      where: { id: params.id },
      data: updateData,
      include: {
        service: true,
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            verified: true
          }
        },
        event: {
          select: {
            id: true,
            title: true,
            slug: true
          }
        }
      }
    })

    // Create notification for organizer
    await prisma.notification.create({
      data: {
        userId: existingOrder.organizer.id,
        title: 'Update Status Artposure',
        message: `Status order ${existingOrder.service.name} telah diupdate menjadi ${status}`,
        type: 'ARTPOSURE_UPDATE',
        data: {
          orderId: order.id,
          status: status,
          serviceName: existingOrder.service.name
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: order,
      message: 'Status order berhasil diupdate'
    })
  } catch (error) {
    console.error('Update artposure order error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
