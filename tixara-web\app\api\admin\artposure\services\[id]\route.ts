import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ArtposureCategory } from '@prisma/client'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const service = await prisma.artposureService.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            orders: true
          }
        },
        orders: {
          include: {
            organizer: {
              select: {
                id: true,
                name: true,
                email: true
              }
            },
            event: {
              select: {
                id: true,
                title: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }
      }
    })

    if (!service) {
      return NextResponse.json(
        { success: false, message: 'Service tidak ditemukan' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: service
    })
  } catch (error) {
    console.error('Get artposure service error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description, price, duration, category, samples, isActive } = body

    // Validation
    if (!name || !description || price === undefined || !duration || !category) {
      return NextResponse.json(
        { success: false, message: 'Semua field wajib diisi' },
        { status: 400 }
      )
    }

    if (price < 0) {
      return NextResponse.json(
        { success: false, message: 'Harga tidak boleh negatif' },
        { status: 400 }
      )
    }

    if (duration < 1) {
      return NextResponse.json(
        { success: false, message: 'Durasi minimal 1 hari' },
        { status: 400 }
      )
    }

    if (!Object.values(ArtposureCategory).includes(category)) {
      return NextResponse.json(
        { success: false, message: 'Kategori tidak valid' },
        { status: 400 }
      )
    }

    // Check if service exists
    const existingService = await prisma.artposureService.findUnique({
      where: { id: params.id }
    })

    if (!existingService) {
      return NextResponse.json(
        { success: false, message: 'Service tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if name is already used by another service
    const duplicateService = await prisma.artposureService.findFirst({
      where: {
        name,
        id: { not: params.id }
      }
    })

    if (duplicateService) {
      return NextResponse.json(
        { success: false, message: 'Nama service sudah digunakan' },
        { status: 400 }
      )
    }

    const service = await prisma.artposureService.update({
      where: { id: params.id },
      data: {
        name,
        description,
        price,
        duration,
        category,
        samples: samples || [],
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: service,
      message: 'Service Artposure berhasil diupdate'
    })
  } catch (error) {
    console.error('Update artposure service error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if service exists
    const existingService = await prisma.artposureService.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    })

    if (!existingService) {
      return NextResponse.json(
        { success: false, message: 'Service tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if service has orders
    if (existingService._count.orders > 0) {
      return NextResponse.json(
        { success: false, message: 'Tidak dapat menghapus service yang memiliki order' },
        { status: 400 }
      )
    }

    await prisma.artposureService.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Service Artposure berhasil dihapus'
    })
  } catch (error) {
    console.error('Delete artposure service error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
