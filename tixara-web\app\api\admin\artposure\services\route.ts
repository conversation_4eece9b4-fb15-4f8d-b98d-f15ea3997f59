import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ArtposureCategory } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const category = searchParams.get('category')
    const isActive = searchParams.get('isActive')
    const skip = (page - 1) * limit

    const where: any = {}

    if (category) {
      where.category = category
    }

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    const [services, total] = await Promise.all([
      prisma.artposureService.findMany({
        where,
        include: {
          _count: {
            select: {
              orders: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.artposureService.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: services,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get artposure services error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description, price, duration, category, samples, isActive } = body

    // Validation
    if (!name || !description || !price || !duration || !category) {
      return NextResponse.json(
        { success: false, message: 'Semua field wajib diisi' },
        { status: 400 }
      )
    }

    if (price < 0) {
      return NextResponse.json(
        { success: false, message: 'Harga tidak boleh negatif' },
        { status: 400 }
      )
    }

    if (duration < 1) {
      return NextResponse.json(
        { success: false, message: 'Durasi minimal 1 hari' },
        { status: 400 }
      )
    }

    if (!Object.values(ArtposureCategory).includes(category)) {
      return NextResponse.json(
        { success: false, message: 'Kategori tidak valid' },
        { status: 400 }
      )
    }

    // Check if service name already exists
    const existingService = await prisma.artposureService.findFirst({
      where: { name }
    })

    if (existingService) {
      return NextResponse.json(
        { success: false, message: 'Nama service sudah digunakan' },
        { status: 400 }
      )
    }

    const service = await prisma.artposureService.create({
      data: {
        name,
        description,
        price,
        duration,
        category,
        samples: samples || [],
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        _count: {
          select: {
            orders: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: service,
      message: 'Service Artposure berhasil dibuat'
    })
  } catch (error) {
    console.error('Create artposure service error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
