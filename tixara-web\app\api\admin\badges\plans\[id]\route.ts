import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const badgePlan = await prisma.badgePlan.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            subscriptions: true
          }
        },
        subscriptions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      }
    })

    if (!badgePlan) {
      return NextResponse.json(
        { error: 'Badge plan not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(badgePlan)

  } catch (error) {
    console.error('Error fetching badge plan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      price,
      duration,
      features,
      maxEvents,
      maxTicketsPerEvent,
      commissionDiscount,
      prioritySupport,
      customBranding,
      analytics,
      isActive,
      color,
      icon
    } = body

    // Validation
    if (!name || !description || price < 0 || duration <= 0) {
      return NextResponse.json(
        { error: 'Invalid input data' },
        { status: 400 }
      )
    }

    // Check if badge plan exists
    const existingPlan = await prisma.badgePlan.findUnique({
      where: { id: params.id }
    })

    if (!existingPlan) {
      return NextResponse.json(
        { error: 'Badge plan not found' },
        { status: 404 }
      )
    }

    // Check if another badge plan with same name exists
    const duplicatePlan = await prisma.badgePlan.findFirst({
      where: {
        name,
        id: { not: params.id }
      }
    })

    if (duplicatePlan) {
      return NextResponse.json(
        { error: 'Badge plan dengan nama tersebut sudah ada' },
        { status: 400 }
      )
    }

    const updatedPlan = await prisma.badgePlan.update({
      where: { id: params.id },
      data: {
        name,
        description,
        price,
        duration,
        features: features || [],
        maxEvents: maxEvents || 0,
        maxTicketsPerEvent: maxTicketsPerEvent || 0,
        commissionDiscount: commissionDiscount || 0,
        prioritySupport: prioritySupport || false,
        customBranding: customBranding || false,
        analytics: analytics || false,
        isActive: isActive !== false,
        color: color || '#0ea5e9',
        icon: icon || 'star'
      },
      include: {
        _count: {
          select: {
            subscriptions: true
          }
        }
      }
    })

    return NextResponse.json(updatedPlan)

  } catch (error) {
    console.error('Error updating badge plan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if badge plan exists
    const existingPlan = await prisma.badgePlan.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            subscriptions: true
          }
        }
      }
    })

    if (!existingPlan) {
      return NextResponse.json(
        { error: 'Badge plan not found' },
        { status: 404 }
      )
    }

    // Check if there are active subscriptions
    const activeSubscriptions = await prisma.badgeSubscription.count({
      where: {
        badgePlanId: params.id,
        status: 'ACTIVE'
      }
    })

    if (activeSubscriptions > 0) {
      return NextResponse.json(
        { error: 'Tidak dapat menghapus badge plan yang memiliki subscription aktif' },
        { status: 400 }
      )
    }

    // Delete the badge plan
    await prisma.badgePlan.delete({
      where: { id: params.id }
    })

    return NextResponse.json({ message: 'Badge plan berhasil dihapus' })

  } catch (error) {
    console.error('Error deleting badge plan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
