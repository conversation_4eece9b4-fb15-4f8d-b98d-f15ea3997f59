import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const badgePlans = await prisma.badgePlan.findMany({
      include: {
        _count: {
          select: {
            subscriptions: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    return NextResponse.json(badgePlans)

  } catch (error) {
    console.error('Error fetching badge plans:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const {
      name,
      description,
      price,
      duration,
      features,
      maxEvents,
      maxTicketsPerEvent,
      commissionDiscount,
      prioritySupport,
      customBranding,
      analytics,
      isActive,
      color,
      icon
    } = body

    // Validation
    if (!name || !description || price < 0 || duration <= 0) {
      return NextResponse.json(
        { error: 'Invalid input data' },
        { status: 400 }
      )
    }

    // Check if badge plan with same name exists
    const existingPlan = await prisma.badgePlan.findFirst({
      where: { name }
    })

    if (existingPlan) {
      return NextResponse.json(
        { error: 'Badge plan dengan nama tersebut sudah ada' },
        { status: 400 }
      )
    }

    const badgePlan = await prisma.badgePlan.create({
      data: {
        name,
        description,
        price,
        duration,
        features: features || [],
        maxEvents: maxEvents || 0,
        maxTicketsPerEvent: maxTicketsPerEvent || 0,
        commissionDiscount: commissionDiscount || 0,
        prioritySupport: prioritySupport || false,
        customBranding: customBranding || false,
        analytics: analytics || false,
        isActive: isActive !== false,
        color: color || '#0ea5e9',
        icon: icon || 'star'
      },
      include: {
        _count: {
          select: {
            subscriptions: true
          }
        }
      }
    })

    return NextResponse.json(badgePlan, { status: 201 })

  } catch (error) {
    console.error('Error creating badge plan:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
