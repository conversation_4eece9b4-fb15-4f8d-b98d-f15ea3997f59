import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const badge = searchParams.get('badge')
    const search = searchParams.get('search')

    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {}

    if (status) {
      where.status = status
    }

    if (badge) {
      where.badge = badge
    }

    if (search) {
      where.OR = [
        {
          user: {
            name: {
              contains: search,
              mode: 'insensitive'
            }
          }
        },
        {
          user: {
            email: {
              contains: search,
              mode: 'insensitive'
            }
          }
        }
      ]
    }

    const [subscriptions, total] = await Promise.all([
      prisma.badgeSubscription.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true
            }
          },
          badgePlan: {
            select: {
              id: true,
              name: true,
              color: true,
              icon: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit
      }),
      prisma.badgeSubscription.count({ where })
    ])

    // Transform data to match frontend interface
    const transformedSubscriptions = subscriptions.map(sub => ({
      id: sub.id,
      badge: sub.badgePlan?.name || sub.badge,
      status: sub.status,
      startDate: sub.startDate.toISOString(),
      endDate: sub.endDate.toISOString(),
      user: {
        id: sub.user.id,
        name: sub.user.name,
        email: sub.user.email
      },
      createdAt: sub.createdAt.toISOString()
    }))

    return NextResponse.json({
      subscriptions: transformedSubscriptions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Error fetching badge subscriptions:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userId, badgePlanId, duration } = body

    // Validation
    if (!userId || !badgePlanId) {
      return NextResponse.json(
        { error: 'User ID and Badge Plan ID are required' },
        { status: 400 }
      )
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Check if badge plan exists
    const badgePlan = await prisma.badgePlan.findUnique({
      where: { id: badgePlanId }
    })

    if (!badgePlan) {
      return NextResponse.json(
        { error: 'Badge plan not found' },
        { status: 404 }
      )
    }

    // Check if user already has an active subscription
    const existingSubscription = await prisma.badgeSubscription.findFirst({
      where: {
        userId,
        status: 'ACTIVE'
      }
    })

    if (existingSubscription) {
      return NextResponse.json(
        { error: 'User sudah memiliki subscription aktif' },
        { status: 400 }
      )
    }

    // Calculate dates
    const startDate = new Date()
    const endDate = new Date()
    endDate.setDate(startDate.getDate() + (duration || badgePlan.duration))

    // Create subscription
    const subscription = await prisma.badgeSubscription.create({
      data: {
        userId,
        badgePlanId,
        badge: badgePlan.name,
        status: 'ACTIVE',
        startDate,
        endDate
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        badgePlan: {
          select: {
            id: true,
            name: true,
            color: true,
            icon: true
          }
        }
      }
    })

    // Update user badge
    await prisma.user.update({
      where: { id: userId },
      data: { badge: badgePlan.name }
    })

    return NextResponse.json(subscription, { status: 201 })

  } catch (error) {
    console.error('Error creating badge subscription:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
