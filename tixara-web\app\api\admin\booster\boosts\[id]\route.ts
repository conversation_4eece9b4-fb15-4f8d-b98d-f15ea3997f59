import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { BoostStatus } from '@prisma/client'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const boost = await prisma.eventBoost.findUnique({
      where: { id: params.id },
      include: {
        event: {
          select: {
            id: true,
            title: true,
            slug: true,
            startDate: true,
            endDate: true,
            location: true,
            image: true,
            soldTickets: true,
            totalTickets: true
          }
        },
        package: true,
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            verified: true,
            badgeType: true
          }
        }
      }
    })

    if (!boost) {
      return NextResponse.json(
        { success: false, message: 'Boost tidak ditemukan' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: boost
    })
  } catch (error) {
    console.error('Get event boost error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { status } = body

    // Validation
    if (!status) {
      return NextResponse.json(
        { success: false, message: 'Status wajib diisi' },
        { status: 400 }
      )
    }

    if (!Object.values(BoostStatus).includes(status)) {
      return NextResponse.json(
        { success: false, message: 'Status tidak valid' },
        { status: 400 }
      )
    }

    // Check if boost exists
    const existingBoost = await prisma.eventBoost.findUnique({
      where: { id: params.id },
      include: {
        event: {
          select: {
            id: true,
            title: true
          }
        },
        organizer: {
          select: {
            id: true,
            name: true,
            email: true
          }
        },
        package: {
          select: {
            name: true
          }
        }
      }
    })

    if (!existingBoost) {
      return NextResponse.json(
        { success: false, message: 'Boost tidak ditemukan' },
        { status: 404 }
      )
    }

    // Update boost status and event boost status if needed
    const updateData: any = { status }

    const boost = await prisma.$transaction(async (tx) => {
      // Update boost status
      const updatedBoost = await tx.eventBoost.update({
        where: { id: params.id },
        data: updateData,
        include: {
          event: true,
          package: true,
          organizer: {
            select: {
              id: true,
              name: true,
              email: true,
              verified: true
            }
          }
        }
      })

      // Update event boost status if boost is cancelled or expired
      if (status === 'CANCELLED' || status === 'EXPIRED') {
        await tx.event.update({
          where: { id: existingBoost.event.id },
          data: {
            isBoosted: false,
            boostEndDate: null
          }
        })
      }

      return updatedBoost
    })

    // Create notification for organizer
    await prisma.notification.create({
      data: {
        userId: existingBoost.organizer.id,
        title: 'Update Status Boost',
        message: `Status boost untuk event "${existingBoost.event.title}" telah diupdate menjadi ${status}`,
        type: 'BOOST_UPDATE',
        data: {
          boostId: boost.id,
          eventId: existingBoost.event.id,
          status: status,
          packageName: existingBoost.package.name
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: boost,
      message: 'Status boost berhasil diupdate'
    })
  } catch (error) {
    console.error('Update event boost error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
