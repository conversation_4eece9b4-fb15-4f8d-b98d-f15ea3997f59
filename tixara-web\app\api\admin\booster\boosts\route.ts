import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const status = searchParams.get('status')
    const packageId = searchParams.get('packageId')
    const organizerId = searchParams.get('organizerId')
    const skip = (page - 1) * limit

    const where: any = {}

    if (status) {
      where.status = status
    }

    if (packageId) {
      where.packageId = packageId
    }

    if (organizerId) {
      where.organizerId = organizerId
    }

    const [boosts, total] = await Promise.all([
      prisma.eventBoost.findMany({
        where,
        include: {
          event: {
            select: {
              id: true,
              title: true,
              slug: true,
              startDate: true,
              image: true
            }
          },
          package: {
            select: {
              id: true,
              name: true,
              priority: true,
              features: true
            }
          },
          organizer: {
            select: {
              id: true,
              name: true,
              email: true,
              verified: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.eventBoost.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: boosts,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get event boosts error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
