import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const boosterPackage = await prisma.boosterPackage.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            boosts: true
          }
        },
        boosts: {
          include: {
            event: {
              select: {
                id: true,
                title: true,
                slug: true,
                startDate: true
              }
            },
            organizer: {
              select: {
                id: true,
                name: true,
                email: true
              }
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 10
        }
      }
    })

    if (!boosterPackage) {
      return NextResponse.json(
        { success: false, message: 'Paket tidak ditemukan' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      data: boosterPackage
    })
  } catch (error) {
    console.error('Get booster package error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description, duration, price, features, priority, isActive } = body

    // Validation
    if (!name || !description || duration === undefined || price === undefined || priority === undefined) {
      return NextResponse.json(
        { success: false, message: 'Semua field wajib diisi' },
        { status: 400 }
      )
    }

    if (price < 0) {
      return NextResponse.json(
        { success: false, message: 'Harga tidak boleh negatif' },
        { status: 400 }
      )
    }

    if (duration < 1) {
      return NextResponse.json(
        { success: false, message: 'Durasi minimal 1 hari' },
        { status: 400 }
      )
    }

    if (priority < 1 || priority > 5) {
      return NextResponse.json(
        { success: false, message: 'Prioritas harus antara 1-5' },
        { status: 400 }
      )
    }

    // Check if package exists
    const existingPackage = await prisma.boosterPackage.findUnique({
      where: { id: params.id }
    })

    if (!existingPackage) {
      return NextResponse.json(
        { success: false, message: 'Paket tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if name is already used by another package
    const duplicatePackage = await prisma.boosterPackage.findFirst({
      where: {
        name,
        id: { not: params.id }
      }
    })

    if (duplicatePackage) {
      return NextResponse.json(
        { success: false, message: 'Nama paket sudah digunakan' },
        { status: 400 }
      )
    }

    const boosterPackage = await prisma.boosterPackage.update({
      where: { id: params.id },
      data: {
        name,
        description,
        duration,
        price,
        features: features || [],
        priority,
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        _count: {
          select: {
            boosts: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: boosterPackage,
      message: 'Paket Booster berhasil diupdate'
    })
  } catch (error) {
    console.error('Update booster package error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Check if package exists
    const existingPackage = await prisma.boosterPackage.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            boosts: true
          }
        }
      }
    })

    if (!existingPackage) {
      return NextResponse.json(
        { success: false, message: 'Paket tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if package has active boosts
    const activeBoosts = await prisma.eventBoost.count({
      where: {
        packageId: params.id,
        status: 'ACTIVE'
      }
    })

    if (activeBoosts > 0) {
      return NextResponse.json(
        { success: false, message: 'Tidak dapat menghapus paket yang memiliki boost aktif' },
        { status: 400 }
      )
    }

    await prisma.boosterPackage.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Paket Booster berhasil dihapus'
    })
  } catch (error) {
    console.error('Delete booster package error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
