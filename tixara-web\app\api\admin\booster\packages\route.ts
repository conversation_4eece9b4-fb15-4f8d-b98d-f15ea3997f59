import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const isActive = searchParams.get('isActive')
    const skip = (page - 1) * limit

    const where: any = {}

    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }

    const [packages, total] = await Promise.all([
      prisma.boosterPackage.findMany({
        where,
        include: {
          _count: {
            select: {
              boosts: true
            }
          }
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'desc' }
        ],
        skip,
        take: limit
      }),
      prisma.boosterPackage.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: packages,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get booster packages error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { name, description, duration, price, features, priority, isActive } = body

    // Validation
    if (!name || !description || !duration || !price || !priority) {
      return NextResponse.json(
        { success: false, message: 'Semua field wajib diisi' },
        { status: 400 }
      )
    }

    if (price < 0) {
      return NextResponse.json(
        { success: false, message: 'Harga tidak boleh negatif' },
        { status: 400 }
      )
    }

    if (duration < 1) {
      return NextResponse.json(
        { success: false, message: 'Durasi minimal 1 hari' },
        { status: 400 }
      )
    }

    if (priority < 1 || priority > 5) {
      return NextResponse.json(
        { success: false, message: 'Prioritas harus antara 1-5' },
        { status: 400 }
      )
    }

    // Check if package name already exists
    const existingPackage = await prisma.boosterPackage.findFirst({
      where: { name }
    })

    if (existingPackage) {
      return NextResponse.json(
        { success: false, message: 'Nama paket sudah digunakan' },
        { status: 400 }
      )
    }

    const boosterPackage = await prisma.boosterPackage.create({
      data: {
        name,
        description,
        duration,
        price,
        features: features || [],
        priority,
        isActive: isActive !== undefined ? isActive : true
      },
      include: {
        _count: {
          select: {
            boosts: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: boosterPackage,
      message: 'Paket Booster berhasil dibuat'
    })
  } catch (error) {
    console.error('Create booster package error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
