import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'
import { formatRelativeTime } from '@/lib/utils'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const limit = 20 // Number of activities to fetch

    // Get recent activities from different sources
    const [
      recentUsers,
      recentEvents,
      recentTickets,
      recentSubscriptions
    ] = await Promise.all([
      // Recent user registrations
      prisma.user.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          createdAt: true
        }
      }),

      // Recent events created
      prisma.event.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          title: true,
          createdAt: true,
          organizer: {
            select: {
              name: true
            }
          }
        }
      }),

      // Recent ticket sales
      prisma.ticket.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        where: { status: 'ACTIVE' },
        select: {
          id: true,
          createdAt: true,
          event: {
            select: {
              title: true
            }
          },
          user: {
            select: {
              name: true
            }
          }
        }
      }),

      // Recent badge subscriptions
      prisma.badgeSubscription.findMany({
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          badge: true,
          createdAt: true,
          user: {
            select: {
              name: true
            }
          }
        }
      })
    ])

    // Combine and format activities
    const activities: any[] = []

    // Add user registrations
    recentUsers.forEach(user => {
      activities.push({
        id: `user-${user.id}`,
        type: 'user_registration',
        description: `${user.name} mendaftar sebagai ${user.role.toLowerCase()}`,
        timestamp: user.createdAt,
        user: user.name
      })
    })

    // Add event creations
    recentEvents.forEach(event => {
      activities.push({
        id: `event-${event.id}`,
        type: 'event_created',
        description: `Event "${event.title}" dibuat oleh ${event.organizer.name}`,
        timestamp: event.createdAt,
        user: event.organizer.name
      })
    })

    // Add ticket sales
    recentTickets.forEach(ticket => {
      activities.push({
        id: `ticket-${ticket.id}`,
        type: 'ticket_sold',
        description: `Tiket untuk "${ticket.event.title}" dibeli oleh ${ticket.user.name}`,
        timestamp: ticket.createdAt,
        user: ticket.user.name
      })
    })

    // Add subscription creations
    recentSubscriptions.forEach(subscription => {
      activities.push({
        id: `subscription-${subscription.id}`,
        type: 'subscription_created',
        description: `${subscription.user.name} berlangganan badge ${subscription.badge}`,
        timestamp: subscription.createdAt,
        user: subscription.user.name
      })
    })

    // Sort by timestamp (newest first) and limit
    const sortedActivities = activities
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit)
      .map(activity => ({
        ...activity,
        timestamp: formatRelativeTime(activity.timestamp)
      }))

    return NextResponse.json(sortedActivities)

  } catch (error) {
    console.error('Error fetching dashboard activity:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
