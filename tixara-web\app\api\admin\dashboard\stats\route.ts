import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get current date for calculations
    const now = new Date()
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
    const startOfLastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1)
    const endOfLastMonth = new Date(now.getFullYear(), now.getMonth(), 0)

    // Parallel queries for better performance
    const [
      totalUsers,
      totalEvents,
      totalTicketsSold,
      totalRevenue,
      activeSubscriptions,
      pendingVerifications,
      recentTransactions,
      lastMonthUsers,
      lastMonthEvents,
      lastMonthTickets,
      lastMonthRevenue
    ] = await Promise.all([
      // Total users
      prisma.user.count(),
      
      // Total events
      prisma.event.count(),
      
      // Total tickets sold
      prisma.ticket.count({
        where: { status: 'ACTIVE' }
      }),
      
      // Total revenue (sum of all successful transactions)
      prisma.transaction.aggregate({
        where: { status: 'SUCCESS' },
        _sum: { amount: true }
      }),
      
      // Active badge subscriptions
      prisma.badgeSubscription.count({
        where: {
          endDate: { gt: now },
          status: 'ACTIVE'
        }
      }),
      
      // Pending organizer verifications
      prisma.user.count({
        where: {
          role: UserRole.ORGANIZER,
          isVerified: false
        }
      }),
      
      // Recent transactions (last 24 hours)
      prisma.transaction.count({
        where: {
          createdAt: {
            gte: new Date(now.getTime() - 24 * 60 * 60 * 1000)
          }
        }
      }),
      
      // Last month comparisons
      prisma.user.count({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),
      
      prisma.event.count({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          }
        }
      }),
      
      prisma.ticket.count({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          },
          status: 'ACTIVE'
        }
      }),
      
      prisma.transaction.aggregate({
        where: {
          createdAt: {
            gte: startOfLastMonth,
            lte: endOfLastMonth
          },
          status: 'SUCCESS'
        },
        _sum: { amount: true }
      })
    ])

    // Calculate growth percentages
    const calculateGrowth = (current: number, previous: number) => {
      if (previous === 0) return 0
      return Math.round(((current - previous) / previous) * 100)
    }

    // Current month data
    const currentMonthUsers = await prisma.user.count({
      where: {
        createdAt: { gte: startOfMonth }
      }
    })

    const currentMonthEvents = await prisma.event.count({
      where: {
        createdAt: { gte: startOfMonth }
      }
    })

    const currentMonthTickets = await prisma.ticket.count({
      where: {
        createdAt: { gte: startOfMonth },
        status: 'ACTIVE'
      }
    })

    const currentMonthRevenue = await prisma.transaction.aggregate({
      where: {
        createdAt: { gte: startOfMonth },
        status: 'SUCCESS'
      },
      _sum: { amount: true }
    })

    // System health check (simplified)
    let systemStatus: 'healthy' | 'warning' | 'error' = 'healthy'
    
    // Check for any critical issues
    const errorTransactions = await prisma.transaction.count({
      where: {
        status: 'FAILED',
        createdAt: {
          gte: new Date(now.getTime() - 60 * 60 * 1000) // Last hour
        }
      }
    })

    if (errorTransactions > 10) {
      systemStatus = 'warning'
    }

    if (errorTransactions > 50) {
      systemStatus = 'error'
    }

    const stats = {
      totalUsers,
      totalEvents,
      totalTicketsSold,
      totalRevenue: totalRevenue._sum.amount || 0,
      activeSubscriptions,
      pendingVerifications,
      recentTransactions,
      systemStatus,
      growth: {
        users: calculateGrowth(currentMonthUsers, lastMonthUsers),
        events: calculateGrowth(currentMonthEvents, lastMonthEvents),
        tickets: calculateGrowth(currentMonthTickets, lastMonthTickets),
        revenue: calculateGrowth(
          currentMonthRevenue._sum.amount || 0,
          lastMonthRevenue._sum.amount || 0
        )
      }
    }

    return NextResponse.json(stats)

  } catch (error) {
    console.error('Error fetching dashboard stats:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
