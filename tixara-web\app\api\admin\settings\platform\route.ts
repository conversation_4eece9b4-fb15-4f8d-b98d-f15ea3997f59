import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Get all platform settings
    const settings = await prisma.platformSetting.findMany({
      select: {
        key: true,
        value: true,
        type: true
      }
    })

    // Convert to object format
    const settingsObject: any = {}
    settings.forEach(setting => {
      let value = setting.value
      
      // Parse value based on type
      switch (setting.type) {
        case 'BOOLEAN':
          value = value === 'true'
          break
        case 'NUMBER':
          value = parseFloat(value)
          break
        case 'INTEGER':
          value = parseInt(value)
          break
        case 'JSON':
          try {
            value = JSON.parse(value)
          } catch {
            value = null
          }
          break
        default:
          // STRING - keep as is
          break
      }
      
      settingsObject[setting.key] = value
    })

    return NextResponse.json(settingsObject)

  } catch (error) {
    console.error('Error fetching platform settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()

    // Define setting types
    const settingTypes: Record<string, 'STRING' | 'BOOLEAN' | 'NUMBER' | 'INTEGER' | 'JSON'> = {
      platformName: 'STRING',
      platformDescription: 'STRING',
      platformUrl: 'STRING',
      supportEmail: 'STRING',
      maintenanceMode: 'BOOLEAN',
      maintenanceMessage: 'STRING',
      defaultCommissionRate: 'NUMBER',
      defaultTaxRate: 'NUMBER',
      withdrawalFee: 'INTEGER',
      minimumWithdrawal: 'INTEGER',
      allowUserRegistration: 'BOOLEAN',
      requireEmailVerification: 'BOOLEAN',
      allowOrganizerSelfVerification: 'BOOLEAN',
      enableNotifications: 'BOOLEAN',
      primaryColor: 'STRING',
      secondaryColor: 'STRING',
      logoUrl: 'STRING',
      faviconUrl: 'STRING',
      metaTitle: 'STRING',
      metaDescription: 'STRING',
      metaKeywords: 'STRING',
      facebookUrl: 'STRING',
      twitterUrl: 'STRING',
      instagramUrl: 'STRING',
      linkedinUrl: 'STRING'
    }

    // Update settings in transaction
    await prisma.$transaction(async (tx) => {
      for (const [key, value] of Object.entries(body)) {
        if (settingTypes[key]) {
          let stringValue = String(value)
          
          // Convert value to string based on type
          if (settingTypes[key] === 'JSON') {
            stringValue = JSON.stringify(value)
          }

          await tx.platformSetting.upsert({
            where: { key },
            update: {
              value: stringValue,
              updatedAt: new Date()
            },
            create: {
              key,
              value: stringValue,
              type: settingTypes[key],
              description: getSettingDescription(key)
            }
          })
        }
      }
    })

    return NextResponse.json({
      message: 'Platform settings updated successfully'
    })

  } catch (error) {
    console.error('Error updating platform settings:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

function getSettingDescription(key: string): string {
  const descriptions: Record<string, string> = {
    platformName: 'Nama platform',
    platformDescription: 'Deskripsi platform',
    platformUrl: 'URL utama platform',
    supportEmail: 'Email support customer service',
    maintenanceMode: 'Mode maintenance platform',
    maintenanceMessage: 'Pesan yang ditampilkan saat maintenance',
    defaultCommissionRate: 'Rate komisi default untuk organizer (%)',
    defaultTaxRate: 'Rate pajak default (%)',
    withdrawalFee: 'Biaya withdrawal dalam rupiah',
    minimumWithdrawal: 'Minimum amount untuk withdrawal',
    allowUserRegistration: 'Izinkan registrasi user baru',
    requireEmailVerification: 'Wajibkan verifikasi email',
    allowOrganizerSelfVerification: 'Izinkan organizer verifikasi sendiri',
    enableNotifications: 'Aktifkan sistem notifikasi',
    primaryColor: 'Warna primary theme',
    secondaryColor: 'Warna secondary theme',
    logoUrl: 'URL logo platform',
    faviconUrl: 'URL favicon platform',
    metaTitle: 'Meta title untuk SEO',
    metaDescription: 'Meta description untuk SEO',
    metaKeywords: 'Meta keywords untuk SEO',
    facebookUrl: 'URL halaman Facebook',
    twitterUrl: 'URL halaman Twitter',
    instagramUrl: 'URL halaman Instagram',
    linkedinUrl: 'URL halaman LinkedIn'
  }
  
  return descriptions[key] || key
}
