import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()
    const { verified } = body

    if (typeof verified !== 'boolean') {
      return NextResponse.json(
        { error: 'Invalid verified value' },
        { status: 400 }
      )
    }

    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { id }
    })

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Update verification status
    const updatedUser = await prisma.user.update({
      where: { id },
      data: { isVerified: verified },
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        isVerified: true
      }
    })

    // Create notification for user
    await prisma.notification.create({
      data: {
        userId: id,
        title: verified ? 'Akun Terverifikasi' : 'Verifikasi Dibatalkan',
        message: verified 
          ? 'Selamat! Akun Anda telah berhasil diverifikasi oleh admin.'
          : 'Verifikasi akun Anda telah dibatalkan. Silakan hubungi admin untuk informasi lebih lanjut.',
        type: 'SYSTEM_ANNOUNCEMENT'
      }
    })

    return NextResponse.json({
      message: `User ${verified ? 'verified' : 'unverified'} successfully`,
      user: updatedUser
    })

  } catch (error) {
    console.error('Error updating user verification:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
