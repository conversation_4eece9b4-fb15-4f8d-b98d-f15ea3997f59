import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const eventSchema = z.object({
  title: z.string().min(5, 'Judul event minimal 5 karakter').max(200, 'Judul event maksimal 200 karakter'),
  description: z.string().min(10, 'Deskripsi minimal 10 karakter'),
  categoryId: z.string().min(1, 'Kategori wajib dipilih'),
  location: z.string().min(5, 'Lokasi minimal 5 karakter'),
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Format tanggal tidak valid'),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Format tanggal tidak valid'),
  price: z.number().min(0, 'Harga tidak boleh negatif'),
  maxTickets: z.number().min(1, 'Maksimal tiket minimal 1'),
  image: z.string().optional(),
  isActive: z.boolean().default(true),
  requiresApproval: z.boolean().default(false),
})

// GET /api/events/[id] - Ambil event berdasarkan ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const event = await prisma.event.findUnique({
      where: { id: params.id },
      include: {
        category: true,
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            isVerified: true,
            badge: true,
            _count: {
              select: {
                events: true,
              },
            },
          },
        },
        staff: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        _count: {
          select: {
            tickets: true,
          },
        },
      },
    })

    if (!event) {
      return NextResponse.json(
        { success: false, message: 'Event tidak ditemukan' },
        { status: 404 }
      )
    }

    // Hitung tiket yang sudah terjual
    const soldTickets = await prisma.ticket.count({
      where: {
        eventId: params.id,
        status: 'ACTIVE',
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        ...event,
        soldTickets,
        availableTickets: event.maxTickets - soldTickets,
      },
    })
  } catch (error) {
    console.error('Error fetching event:', error)
    return NextResponse.json(
      { success: false, message: 'Gagal mengambil data event' },
      { status: 500 }
    )
  }
}

// PUT /api/events/[id] - Update event (Organizer/Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = eventSchema.parse(body)

    // Cek apakah event ada
    const existingEvent = await prisma.event.findUnique({
      where: { id: params.id },
    })

    if (!existingEvent) {
      return NextResponse.json(
        { success: false, message: 'Event tidak ditemukan' },
        { status: 404 }
      )
    }

    // Cek permission - organizer hanya bisa edit event sendiri
    if (session.user.role === 'ORGANIZER' && existingEvent.organizerId !== session.user.id) {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses untuk mengedit event ini' },
        { status: 403 }
      )
    }

    // Validasi tanggal
    const startDate = new Date(validatedData.startDate)
    const endDate = new Date(validatedData.endDate)

    if (endDate <= startDate) {
      return NextResponse.json(
        { success: false, message: 'Tanggal selesai harus setelah tanggal mulai' },
        { status: 400 }
      )
    }

    // Cek apakah kategori ada
    const category = await prisma.category.findUnique({
      where: { id: validatedData.categoryId },
    })

    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Kategori tidak ditemukan' },
        { status: 400 }
      )
    }

    // Cek apakah ada tiket yang sudah terjual jika mengurangi maxTickets
    if (validatedData.maxTickets < existingEvent.maxTickets) {
      const soldTickets = await prisma.ticket.count({
        where: {
          eventId: params.id,
          status: 'ACTIVE',
        },
      })

      if (validatedData.maxTickets < soldTickets) {
        return NextResponse.json(
          { 
            success: false, 
            message: `Tidak dapat mengurangi maksimal tiket karena sudah ada ${soldTickets} tiket terjual`,
          },
          { status: 400 }
        )
      }
    }

    const event = await prisma.event.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        startDate,
        endDate,
      },
      include: {
        category: true,
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            isVerified: true,
            badge: true,
          },
        },
      },
    })

    // Buat notifikasi
    await prisma.notification.create({
      data: {
        userId: session.user.id,
        type: 'EVENT',
        title: 'Event Diperbarui',
        message: `Event "${event.title}" berhasil diperbarui`,
        isRead: false,
        relatedId: event.id,
      },
    })

    return NextResponse.json({
      success: true,
      data: event,
      message: 'Event berhasil diperbarui',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Data tidak valid',
          errors: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('Error updating event:', error)
    return NextResponse.json(
      { success: false, message: 'Gagal memperbarui event' },
      { status: 500 }
    )
  }
}

// DELETE /api/events/[id] - Hapus event (Organizer/Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Cek apakah event ada
    const existingEvent = await prisma.event.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            tickets: true,
          },
        },
      },
    })

    if (!existingEvent) {
      return NextResponse.json(
        { success: false, message: 'Event tidak ditemukan' },
        { status: 404 }
      )
    }

    // Cek permission - organizer hanya bisa hapus event sendiri
    if (session.user.role === 'ORGANIZER' && existingEvent.organizerId !== session.user.id) {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses untuk menghapus event ini' },
        { status: 403 }
      )
    }

    // Cek apakah ada tiket yang sudah terjual
    const soldTickets = await prisma.ticket.count({
      where: {
        eventId: params.id,
        status: 'ACTIVE',
      },
    })

    if (soldTickets > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: `Event tidak dapat dihapus karena sudah ada ${soldTickets} tiket terjual`,
        },
        { status: 400 }
      )
    }

    await prisma.event.delete({
      where: { id: params.id },
    })

    // Buat notifikasi
    await prisma.notification.create({
      data: {
        userId: session.user.id,
        type: 'EVENT',
        title: 'Event Dihapus',
        message: `Event "${existingEvent.title}" berhasil dihapus`,
        isRead: false,
      },
    })

    return NextResponse.json({
      success: true,
      message: 'Event berhasil dihapus',
    })
  } catch (error) {
    console.error('Error deleting event:', error)
    return NextResponse.json(
      { success: false, message: 'Gagal menghapus event' },
      { status: 500 }
    )
  }
}
