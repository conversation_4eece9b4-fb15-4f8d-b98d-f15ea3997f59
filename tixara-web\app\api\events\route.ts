import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Schema validation untuk event
const eventSchema = z.object({
  title: z.string().min(5, 'Judul event minimal 5 karakter').max(200, 'Judul event maksimal 200 karakter'),
  description: z.string().min(10, 'Deskripsi minimal 10 karakter'),
  categoryId: z.string().min(1, 'Kategori wajib dipilih'),
  location: z.string().min(5, 'Lokasi minimal 5 karakter'),
  startDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Format tanggal tidak valid'),
  endDate: z.string().refine((date) => !isNaN(Date.parse(date)), 'Format tanggal tidak valid'),
  price: z.number().min(0, 'Harga tidak boleh negatif'),
  maxTickets: z.number().min(1, 'Maksimal tiket minimal 1'),
  image: z.string().optional(),
  isActive: z.boolean().default(true),
  requiresApproval: z.boolean().default(false),
})

// GET /api/events - Ambil semua event dengan filter
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const categoryId = searchParams.get('categoryId')
    const organizerId = searchParams.get('organizerId')
    const isActive = searchParams.get('active')
    const search = searchParams.get('search')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const sortBy = searchParams.get('sortBy') || 'createdAt'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const where: any = {}
    
    if (categoryId) {
      where.categoryId = categoryId
    }
    
    if (organizerId) {
      where.organizerId = organizerId
    }
    
    if (isActive !== null) {
      where.isActive = isActive === 'true'
    }
    
    if (search) {
      where.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { location: { contains: search, mode: 'insensitive' } },
      ]
    }

    // Filter hanya event yang belum berakhir untuk public
    const session = await getServerSession(authOptions)
    if (!session || session.user.role === 'BUYER') {
      where.endDate = {
        gte: new Date(),
      }
    }

    const skip = (page - 1) * limit

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where,
        include: {
          category: true,
          organizer: {
            select: {
              id: true,
              name: true,
              email: true,
              isVerified: true,
              badge: true,
              _count: {
                select: {
                  events: true,
                },
              },
            },
          },
          _count: {
            select: {
              tickets: true,
            },
          },
        },
        orderBy: {
          [sortBy]: sortOrder,
        },
        skip,
        take: limit,
      }),
      prisma.event.count({ where }),
    ])

    return NextResponse.json({
      success: true,
      data: events,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching events:', error)
    return NextResponse.json(
      { success: false, message: 'Gagal mengambil data event' },
      { status: 500 }
    )
  }
}

// POST /api/events - Buat event baru (Organizer only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const validatedData = eventSchema.parse(body)

    // Validasi tanggal
    const startDate = new Date(validatedData.startDate)
    const endDate = new Date(validatedData.endDate)
    const now = new Date()

    if (startDate < now) {
      return NextResponse.json(
        { success: false, message: 'Tanggal mulai tidak boleh di masa lalu' },
        { status: 400 }
      )
    }

    if (endDate <= startDate) {
      return NextResponse.json(
        { success: false, message: 'Tanggal selesai harus setelah tanggal mulai' },
        { status: 400 }
      )
    }

    // Cek apakah kategori ada
    const category = await prisma.category.findUnique({
      where: { id: validatedData.categoryId },
    })

    if (!category) {
      return NextResponse.json(
        { success: false, message: 'Kategori tidak ditemukan' },
        { status: 400 }
      )
    }

    const event = await prisma.event.create({
      data: {
        ...validatedData,
        startDate,
        endDate,
        organizerId: session.user.id,
      },
      include: {
        category: true,
        organizer: {
          select: {
            id: true,
            name: true,
            email: true,
            isVerified: true,
            badge: true,
          },
        },
      },
    })

    // Buat notifikasi untuk organizer
    await prisma.notification.create({
      data: {
        userId: session.user.id,
        type: 'EVENT',
        title: 'Event Berhasil Dibuat',
        message: `Event "${event.title}" berhasil dibuat dan sedang menunggu persetujuan`,
        isRead: false,
        relatedId: event.id,
      },
    })

    return NextResponse.json({
      success: true,
      data: event,
      message: 'Event berhasil dibuat',
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Data tidak valid',
          errors: error.errors,
        },
        { status: 400 }
      )
    }

    console.error('Error creating event:', error)
    return NextResponse.json(
      { success: false, message: 'Gagal membuat event' },
      { status: 500 }
    )
  }
}
