import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Only staff and admin can access this endpoint
    if (!['STAFF', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Hanya staff yang dapat mengakses endpoint ini' },
        { status: 403 }
      )
    }

    let events = []

    if (session.user.role === 'ADMIN') {
      // Admin can validate tickets for all events
      events = await prisma.event.findMany({
        where: {
          isActive: true,
          endDate: {
            gte: new Date(), // Only active events
          }
        },
        select: {
          id: true,
          title: true,
          startDate: true,
          endDate: true,
          location: true,
          organizer: {
            select: {
              id: true,
              name: true,
            }
          },
          _count: {
            select: {
              tickets: true,
            }
          }
        },
        orderBy: { startDate: 'asc' },
      })
    } else {
      // Staff can only validate tickets for events they're assigned to
      const staffAssignments = await prisma.eventStaff.findMany({
        where: {
          staffId: session.user.id,
        },
        include: {
          event: {
            where: {
              isActive: true,
              endDate: {
                gte: new Date(), // Only active events
              }
            },
            select: {
              id: true,
              title: true,
              startDate: true,
              endDate: true,
              location: true,
              organizer: {
                select: {
                  id: true,
                  name: true,
                }
              },
              _count: {
                select: {
                  tickets: true,
                }
              }
            }
          }
        }
      })

      events = staffAssignments
        .filter(assignment => assignment.event) // Filter out null events
        .map(assignment => assignment.event)
    }

    return NextResponse.json({
      success: true,
      data: events,
    })
  } catch (error) {
    console.error('Error fetching staff events:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
