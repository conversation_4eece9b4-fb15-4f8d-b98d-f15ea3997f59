import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    const where: any = {
      organizerId: session.user.id
    }

    if (status) {
      where.status = status
    }

    const [orders, total] = await Promise.all([
      prisma.artposureOrder.findMany({
        where,
        include: {
          service: {
            select: {
              name: true,
              category: true,
              price: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.artposureOrder.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get artposure orders error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { serviceId, eventId, eventTitle, requirements, paymentMethod = 'UANGTIX' } = body

    // Validation
    if (!serviceId || (!eventId && !eventTitle) || !requirements) {
      return NextResponse.json(
        { success: false, message: 'Semua field wajib diisi' },
        { status: 400 }
      )
    }

    // Check if service exists and is active
    const service = await prisma.artposureService.findUnique({
      where: { id: serviceId }
    })

    if (!service) {
      return NextResponse.json(
        { success: false, message: 'Layanan tidak ditemukan' },
        { status: 404 }
      )
    }

    if (!service.isActive) {
      return NextResponse.json(
        { success: false, message: 'Layanan tidak tersedia' },
        { status: 400 }
      )
    }

    // Get event title if eventId is provided
    let finalEventTitle = eventTitle
    if (eventId) {
      const event = await prisma.event.findUnique({
        where: { id: eventId },
        select: { title: true }
      })
      if (event) {
        finalEventTitle = event.title
      }
    }

    // Handle payment processing
    if (paymentMethod === 'UANGTIX') {
      // Check UangtiX balance
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { uangtixBalance: true }
      })

      if (!user || user.uangtixBalance < service.price) {
        return NextResponse.json(
          { success: false, message: 'Saldo UangtiX tidak mencukupi' },
          { status: 400 }
        )
      }

      // Process payment with UangtiX
      const result = await prisma.$transaction(async (tx) => {
        // Create order
        const order = await tx.artposureOrder.create({
          data: {
            serviceId,
            organizerId: session.user.id,
            eventId: eventId || null,
            eventTitle: finalEventTitle,
            requirements,
            price: service.price,
            status: 'PAID', // Paid immediately with UangtiX
            paidAt: new Date()
          },
          include: {
            service: {
              select: {
                name: true,
                category: true,
                price: true
              }
            },
            organizer: {
              select: {
                name: true,
                email: true
              }
            }
          }
        })

        // Deduct UangtiX balance
        const newBalance = user.uangtixBalance - service.price
        await tx.user.update({
          where: { id: session.user.id },
          data: { uangtixBalance: newBalance }
        })

        // Create UangtiX transaction record
        await tx.uangtiXTransaction.create({
          data: {
            userId: session.user.id,
            type: 'PAYMENT',
            amount: service.price,
            description: `Pembayaran Artposure - ${service.name}`,
            reference: order.id,
            status: 'SUCCESS',
            balanceBefore: user.uangtixBalance,
            balanceAfter: newBalance,
          }
        })

        // Create notification for organizer
        await tx.notification.create({
          data: {
            userId: session.user.id,
            title: 'Order Artposure Berhasil',
            message: `Order untuk layanan ${service.name} berhasil dibayar`,
            type: 'ARTPOSURE_ORDER',
            data: {
              orderId: order.id,
              serviceName: service.name,
              eventTitle: finalEventTitle
            }
          }
        })

        return order
      })

      return NextResponse.json({
        success: true,
        data: result,
        message: 'Order Artposure berhasil dibuat dan dibayar'
      })

    } else {
      // Handle external payment gateway
      const { PaymentFactory } = await import('@/lib/payment-utils')

      const orderId = `ARTPOSURE-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      try {
        const paymentGateway = PaymentFactory.createPaymentGateway(paymentMethod)

        const paymentRequest = {
          amount: service.price,
          description: `Artposure - ${service.name}`,
          customerName: session.user.name || '',
          customerEmail: session.user.email || '',
          customerPhone: session.user.phone || '',
          orderId,
          returnUrl: `${process.env.NEXTAUTH_URL}/organizer/artposure/orders`,
          expiredTime: 60, // 1 hour
        }

        const paymentResponse = await paymentGateway.createPayment(paymentRequest)

        if (!paymentResponse.success) {
          return NextResponse.json(
            { success: false, message: paymentResponse.message || 'Gagal membuat pembayaran' },
            { status: 400 }
          )
        }

        // Create order and payment record
        const result = await prisma.$transaction(async (tx) => {
          // Create order
          const order = await tx.artposureOrder.create({
            data: {
              serviceId,
              organizerId: session.user.id,
              eventId: eventId || null,
              eventTitle: finalEventTitle,
              requirements,
              price: service.price,
              status: 'PENDING'
            },
            include: {
              service: {
                select: {
                  name: true,
                  category: true,
                  price: true
                }
              },
              organizer: {
                select: {
                  name: true,
                  email: true
                }
              }
            }
          })

          // Create payment record
          await tx.payment.create({
            data: {
              userId: session.user.id,
              amount: service.price,
              gateway: paymentMethod,
              status: 'PENDING',
              externalId: orderId,
              paymentUrl: paymentResponse.paymentUrl,
              expiredAt: paymentResponse.expiredAt,
              metadata: paymentResponse.data,
              description: `Artposure - ${service.name}`,
              reference: order.id,
              type: 'ARTPOSURE'
            }
          })

          return { order, paymentUrl: paymentResponse.paymentUrl }
        })

        return NextResponse.json({
          success: true,
          data: result.order,
          paymentUrl: result.paymentUrl,
          message: 'Order Artposure berhasil dibuat. Silakan lanjutkan pembayaran.'
        })

      } catch (paymentError) {
        console.error('Payment creation error:', paymentError)
        return NextResponse.json(
          { success: false, message: 'Gagal membuat pembayaran' },
          { status: 500 }
        )
      }
    }
  } catch (error) {
    console.error('Create artposure order error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
