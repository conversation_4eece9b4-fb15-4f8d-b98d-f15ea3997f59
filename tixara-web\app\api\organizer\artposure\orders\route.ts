import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    const where: any = {
      organizerId: session.user.id
    }

    if (status) {
      where.status = status
    }

    const [orders, total] = await Promise.all([
      prisma.artposureOrder.findMany({
        where,
        include: {
          service: {
            select: {
              name: true,
              category: true,
              price: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip,
        take: limit
      }),
      prisma.artposureOrder.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })
  } catch (error) {
    console.error('Get artposure orders error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { serviceId, eventId, eventTitle, requirements } = body

    // Validation
    if (!serviceId || (!eventId && !eventTitle) || !requirements) {
      return NextResponse.json(
        { success: false, message: 'Semua field wajib diisi' },
        { status: 400 }
      )
    }

    // Check if service exists and is active
    const service = await prisma.artposureService.findUnique({
      where: { id: serviceId }
    })

    if (!service) {
      return NextResponse.json(
        { success: false, message: 'Layanan tidak ditemukan' },
        { status: 404 }
      )
    }

    if (!service.isActive) {
      return NextResponse.json(
        { success: false, message: 'Layanan tidak tersedia' },
        { status: 400 }
      )
    }

    // Get event title if eventId is provided
    let finalEventTitle = eventTitle
    if (eventId) {
      const event = await prisma.event.findUnique({
        where: { id: eventId },
        select: { title: true }
      })
      if (event) {
        finalEventTitle = event.title
      }
    }

    // Create order
    const order = await prisma.artposureOrder.create({
      data: {
        serviceId,
        organizerId: session.user.id,
        eventId: eventId || null,
        eventTitle: finalEventTitle,
        requirements,
        price: service.price,
        status: 'PENDING'
      },
      include: {
        service: {
          select: {
            name: true,
            category: true,
            price: true
          }
        },
        organizer: {
          select: {
            name: true,
            email: true
          }
        }
      }
    })

    // Create notification for admin
    await prisma.notification.create({
      data: {
        userId: session.user.id, // Will be updated to admin later
        title: 'Order Artposure Baru',
        message: `Order baru untuk layanan ${service.name} dari ${session.user.name}`,
        type: 'ARTPOSURE_ORDER',
        data: {
          orderId: order.id,
          serviceName: service.name,
          organizerName: session.user.name,
          eventTitle: finalEventTitle
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: order,
      message: 'Order Artposure berhasil dibuat'
    })
  } catch (error) {
    console.error('Create artposure order error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
