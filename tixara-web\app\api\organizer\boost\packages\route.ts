import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const packages = await prisma.boosterPackage.findMany({
      where: {
        isActive: true
      },
      orderBy: {
        priority: 'desc'
      }
    })

    return NextResponse.json({
      success: true,
      data: packages
    })
  } catch (error) {
    console.error('Get booster packages error:', error)
    return NextResponse.json(
      { success: false, message: 'Ter<PERSON><PERSON> kesalahan server' },
      { status: 500 }
    )
  }
}
