import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { packageId, eventId } = body

    // Validation
    if (!packageId || !eventId) {
      return NextResponse.json(
        { success: false, message: 'Package ID dan Event ID wajib diisi' },
        { status: 400 }
      )
    }

    // Check if package exists and is active
    const boosterPackage = await prisma.boosterPackage.findUnique({
      where: { id: packageId }
    })

    if (!boosterPackage) {
      return NextResponse.json(
        { success: false, message: 'Paket boost tidak ditemukan' },
        { status: 404 }
      )
    }

    if (!boosterPackage.isActive) {
      return NextResponse.json(
        { success: false, message: 'Paket boost tidak tersedia' },
        { status: 400 }
      )
    }

    // Check if event exists and belongs to organizer
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: {
        organizer: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!event) {
      return NextResponse.json(
        { success: false, message: 'Event tidak ditemukan' },
        { status: 404 }
      )
    }

    if (event.organizerId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses ke event ini' },
        { status: 403 }
      )
    }

    // Check if event is already boosted
    if (event.isBoosted) {
      return NextResponse.json(
        { success: false, message: 'Event sudah dalam status boost' },
        { status: 400 }
      )
    }

    // Check if event has active boost
    const existingBoost = await prisma.eventBoost.findFirst({
      where: {
        eventId,
        status: 'ACTIVE'
      }
    })

    if (existingBoost) {
      return NextResponse.json(
        { success: false, message: 'Event sudah memiliki boost aktif' },
        { status: 400 }
      )
    }

    // Check if event start date is in the future
    if (new Date(event.startDate) <= new Date()) {
      return NextResponse.json(
        { success: false, message: 'Tidak dapat boost event yang sudah dimulai' },
        { status: 400 }
      )
    }

    // Calculate boost dates
    const startDate = new Date()
    const endDate = new Date()
    endDate.setDate(endDate.getDate() + boosterPackage.duration)

    // Create boost transaction
    const boost = await prisma.$transaction(async (tx) => {
      // Create event boost
      const newBoost = await tx.eventBoost.create({
        data: {
          packageId,
          eventId,
          organizerId: session.user.id,
          startDate,
          endDate,
          price: boosterPackage.price,
          status: 'ACTIVE'
        },
        include: {
          event: {
            select: {
              title: true,
              slug: true
            }
          },
          package: {
            select: {
              name: true,
              priority: true
            }
          }
        }
      })

      // Update event boost status
      await tx.event.update({
        where: { id: eventId },
        data: {
          isBoosted: true,
          boostEndDate: endDate
        }
      })

      // Create notification for organizer
      await tx.notification.create({
        data: {
          userId: session.user.id,
          title: 'Boost Event Berhasil',
          message: `Event "${event.title}" berhasil di-boost dengan paket ${boosterPackage.name}`,
          type: 'BOOST_ACTIVATED',
          data: {
            boostId: newBoost.id,
            eventId,
            packageName: boosterPackage.name,
            endDate: endDate.toISOString()
          }
        }
      })

      return newBoost
    })

    return NextResponse.json({
      success: true,
      data: boost,
      message: 'Event boost berhasil dibeli'
    })
  } catch (error) {
    console.error('Purchase boost error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
