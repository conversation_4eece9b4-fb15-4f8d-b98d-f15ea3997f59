import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { packageId, eventId, paymentMethod = 'UANGTIX' } = body

    // Validation
    if (!packageId || !eventId) {
      return NextResponse.json(
        { success: false, message: 'Package ID dan Event ID wajib diisi' },
        { status: 400 }
      )
    }

    // Check if package exists and is active
    const boosterPackage = await prisma.boosterPackage.findUnique({
      where: { id: packageId }
    })

    if (!boosterPackage) {
      return NextResponse.json(
        { success: false, message: 'Paket boost tidak ditemukan' },
        { status: 404 }
      )
    }

    if (!boosterPackage.isActive) {
      return NextResponse.json(
        { success: false, message: 'Paket boost tidak tersedia' },
        { status: 400 }
      )
    }

    // Check if event exists and belongs to organizer
    const event = await prisma.event.findUnique({
      where: { id: eventId },
      include: {
        organizer: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    if (!event) {
      return NextResponse.json(
        { success: false, message: 'Event tidak ditemukan' },
        { status: 404 }
      )
    }

    if (event.organizerId !== session.user.id && session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses ke event ini' },
        { status: 403 }
      )
    }

    // Check if event is already boosted
    if (event.isBoosted) {
      return NextResponse.json(
        { success: false, message: 'Event sudah dalam status boost' },
        { status: 400 }
      )
    }

    // Check if event has active boost
    const existingBoost = await prisma.eventBoost.findFirst({
      where: {
        eventId,
        status: 'ACTIVE'
      }
    })

    if (existingBoost) {
      return NextResponse.json(
        { success: false, message: 'Event sudah memiliki boost aktif' },
        { status: 400 }
      )
    }

    // Check if event start date is in the future
    if (new Date(event.startDate) <= new Date()) {
      return NextResponse.json(
        { success: false, message: 'Tidak dapat boost event yang sudah dimulai' },
        { status: 400 }
      )
    }

    // Calculate boost dates
    const startDate = new Date()
    const endDate = new Date()
    endDate.setDate(endDate.getDate() + boosterPackage.duration)

    // Handle payment processing
    if (paymentMethod === 'UANGTIX') {
      // Check UangtiX balance
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { uangtixBalance: true }
      })

      if (!user || user.uangtixBalance < boosterPackage.price) {
        return NextResponse.json(
          { success: false, message: 'Saldo UangtiX tidak mencukupi' },
          { status: 400 }
        )
      }

      // Process payment with UangtiX
      const boost = await prisma.$transaction(async (tx) => {
        // Create event boost
        const newBoost = await tx.eventBoost.create({
          data: {
            packageId,
            eventId,
            organizerId: session.user.id,
            startDate,
            endDate,
            price: boosterPackage.price,
            status: 'ACTIVE'
          },
          include: {
            event: {
              select: {
                title: true,
                slug: true
              }
            },
            package: {
              select: {
                name: true,
                priority: true
              }
            }
          }
        })

        // Update event boost status
        await tx.event.update({
          where: { id: eventId },
          data: {
            isBoosted: true,
            boostEndDate: endDate
          }
        })

        // Deduct UangtiX balance
        const newBalance = user.uangtixBalance - boosterPackage.price
        await tx.user.update({
          where: { id: session.user.id },
          data: { uangtixBalance: newBalance }
        })

        // Create UangtiX transaction record
        await tx.uangtiXTransaction.create({
          data: {
            userId: session.user.id,
            type: 'PAYMENT',
            amount: boosterPackage.price,
            description: `Boost Event - ${boosterPackage.name}`,
            reference: newBoost.id,
            status: 'SUCCESS',
            balanceBefore: user.uangtixBalance,
            balanceAfter: newBalance,
          }
        })

        // Create notification for organizer
        await tx.notification.create({
          data: {
            userId: session.user.id,
            title: 'Boost Event Berhasil',
            message: `Event "${event.title}" berhasil di-boost dengan paket ${boosterPackage.name}`,
            type: 'BOOST_ACTIVATED',
            data: {
              boostId: newBoost.id,
              eventId,
              packageName: boosterPackage.name,
              endDate: endDate.toISOString()
            }
          }
        })

        return newBoost
      })

      return NextResponse.json({
        success: true,
        data: boost,
        message: 'Event boost berhasil dibeli dan diaktifkan'
      })

    } else {
      // Handle external payment gateway
      const { PaymentFactory } = await import('@/lib/payment-utils')

      const orderId = `BOOST-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

      try {
        const paymentGateway = PaymentFactory.createPaymentGateway(paymentMethod)

        const paymentRequest = {
          amount: boosterPackage.price,
          description: `Event Boost - ${boosterPackage.name}`,
          customerName: session.user.name || '',
          customerEmail: session.user.email || '',
          customerPhone: session.user.phone || '',
          orderId,
          returnUrl: `${process.env.NEXTAUTH_URL}/organizer/boost`,
          expiredTime: 60, // 1 hour
        }

        const paymentResponse = await paymentGateway.createPayment(paymentRequest)

        if (!paymentResponse.success) {
          return NextResponse.json(
            { success: false, message: paymentResponse.message || 'Gagal membuat pembayaran' },
            { status: 400 }
          )
        }

        // Create pending boost and payment record
        const result = await prisma.$transaction(async (tx) => {
          // Create event boost with PENDING status
          const newBoost = await tx.eventBoost.create({
            data: {
              packageId,
              eventId,
              organizerId: session.user.id,
              startDate,
              endDate,
              price: boosterPackage.price,
              status: 'PENDING' // Will be activated after payment
            },
            include: {
              event: {
                select: {
                  title: true,
                  slug: true
                }
              },
              package: {
                select: {
                  name: true,
                  priority: true
                }
              }
            }
          })

          // Create payment record
          await tx.payment.create({
            data: {
              userId: session.user.id,
              amount: boosterPackage.price,
              gateway: paymentMethod,
              status: 'PENDING',
              externalId: orderId,
              paymentUrl: paymentResponse.paymentUrl,
              expiredAt: paymentResponse.expiredAt,
              metadata: paymentResponse.data,
              description: `Event Boost - ${boosterPackage.name}`,
              reference: newBoost.id,
              type: 'BOOST'
            }
          })

          return { boost: newBoost, paymentUrl: paymentResponse.paymentUrl }
        })

        return NextResponse.json({
          success: true,
          data: result.boost,
          paymentUrl: result.paymentUrl,
          message: 'Order boost berhasil dibuat. Silakan lanjutkan pembayaran untuk mengaktifkan boost.'
        })

      } catch (paymentError) {
        console.error('Payment creation error:', paymentError)
        return NextResponse.json(
          { success: false, message: 'Gagal membuat pembayaran' },
          { status: 500 }
        )
      }
    }
  } catch (error) {
    console.error('Purchase boost error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
