import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { orderId } = params

    // Find payment by external ID (order ID)
    const payment = await prisma.payment.findFirst({
      where: {
        externalId: orderId,
        userId: session.user.id
      },
      include: {
        uangtiXTransaction: true
      }
    })

    if (!payment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      )
    }

    // Extract additional data from metadata
    const metadata = payment.metadata as any
    const paymentData = {
      id: payment.id,
      orderId: payment.externalId,
      amount: payment.amount,
      gateway: payment.gateway,
      status: payment.status,
      paymentUrl: payment.paymentUrl,
      qrCode: metadata?.qr_url || metadata?.qrCode,
      virtualAccount: metadata?.virtual_account || metadata?.va_number,
      expiredAt: payment.expiredAt,
      createdAt: payment.createdAt,
    }

    return NextResponse.json({
      success: true,
      data: paymentData
    })

  } catch (error) {
    console.error('Get payment error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
