import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { PaymentFactory } from '@/lib/payment-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { orderId } = params

    // Find payment by external ID (order ID)
    const payment = await prisma.payment.findFirst({
      where: {
        externalId: orderId,
        userId: session.user.id
      },
      include: {
        uangtiXTransaction: true
      }
    })

    if (!payment) {
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      )
    }

    // If already paid, return current status
    if (payment.status === 'PAID') {
      return NextResponse.json({
        success: true,
        data: {
          status: payment.status,
          paidAt: payment.paidAt
        }
      })
    }

    // Check status from payment gateway
    try {
      const gateway = PaymentFactory.createPaymentGateway(payment.gateway)
      let gatewayStatus

      switch (payment.gateway) {
        case 'TRIPAY':
          gatewayStatus = await gateway.checkPaymentStatus(payment.externalId)
          break
        case 'MIDTRANS':
          gatewayStatus = await gateway.checkPaymentStatus(payment.externalId)
          break
        case 'XENDIT':
          // For Xendit, we need to use the payment ID from metadata
          const metadata = payment.metadata as any
          const xenditId = metadata?.id || payment.externalId
          gatewayStatus = await gateway.checkPaymentStatus(xenditId)
          break
        default:
          throw new Error(`Unsupported gateway: ${payment.gateway}`)
      }

      // Map gateway status to our status
      let newStatus = payment.status
      let paidAt = payment.paidAt

      if (payment.gateway === 'TRIPAY') {
        if (gatewayStatus.data?.status === 'PAID') {
          newStatus = 'PAID'
          paidAt = new Date()
        } else if (gatewayStatus.data?.status === 'EXPIRED') {
          newStatus = 'EXPIRED'
        } else if (gatewayStatus.data?.status === 'FAILED') {
          newStatus = 'FAILED'
        }
      } else if (payment.gateway === 'MIDTRANS') {
        if (gatewayStatus.transaction_status === 'settlement' || gatewayStatus.transaction_status === 'capture') {
          newStatus = 'PAID'
          paidAt = new Date()
        } else if (gatewayStatus.transaction_status === 'expire') {
          newStatus = 'EXPIRED'
        } else if (gatewayStatus.transaction_status === 'cancel' || gatewayStatus.transaction_status === 'deny') {
          newStatus = 'FAILED'
        }
      } else if (payment.gateway === 'XENDIT') {
        if (gatewayStatus.status === 'SETTLED') {
          newStatus = 'PAID'
          paidAt = new Date()
        } else if (gatewayStatus.status === 'EXPIRED') {
          newStatus = 'EXPIRED'
        }
      }

      // Update payment status if changed
      if (newStatus !== payment.status) {
        await prisma.$transaction(async (tx) => {
          // Update payment
          await tx.payment.update({
            where: { id: payment.id },
            data: {
              status: newStatus,
              paidAt: newStatus === 'PAID' ? paidAt : null,
            }
          })

          // If paid, update UangtiX transaction and user balance
          if (newStatus === 'PAID' && payment.uangtiXTransaction) {
            const user = await tx.user.findUnique({
              where: { id: payment.userId },
              select: { uangtixBalance: true }
            })

            if (user) {
              const newBalance = user.uangtixBalance + payment.amount

              await tx.user.update({
                where: { id: payment.userId },
                data: { uangtixBalance: newBalance }
              })

              await tx.uangtiXTransaction.update({
                where: { id: payment.uangtiXTransaction.id },
                data: {
                  status: 'SUCCESS',
                  balanceBefore: user.uangtixBalance,
                  balanceAfter: newBalance,
                }
              })

              // Create notification
              await tx.notification.create({
                data: {
                  userId: payment.userId,
                  title: 'Top Up Berhasil',
                  message: `Top Up UangtiX sebesar ${payment.amount.toLocaleString('id-ID')} berhasil. Saldo Anda sekarang ${newBalance.toLocaleString('id-ID')}`,
                  type: 'PAYMENT_SUCCESS',
                  isRead: false,
                }
              })
            }
          }
        })
      }

      return NextResponse.json({
        success: true,
        data: {
          status: newStatus,
          paidAt,
          gatewayData: gatewayStatus
        }
      })

    } catch (gatewayError) {
      console.error('Gateway status check error:', gatewayError)
      
      // Return current status if gateway check fails
      return NextResponse.json({
        success: true,
        data: {
          status: payment.status,
          paidAt: payment.paidAt,
          error: 'Failed to check gateway status'
        }
      })
    }

  } catch (error) {
    console.error('Check payment status error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
