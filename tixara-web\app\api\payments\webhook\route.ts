import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { TripayPayment, MidtransPayment, XenditPayment, UangtiXWallet } from '@/lib/payment-utils'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const signature = request.headers.get('x-signature') || request.headers.get('x-callback-signature') || ''
    const gateway = request.headers.get('x-gateway') || 'TRIPAY'

    console.log('Webhook received:', { gateway, body })

    let paymentData: any = null
    let isValidSignature = false

    // Verify signature based on gateway
    switch (gateway.toUpperCase()) {
      case 'TRIPAY':
        const tripay = new TripayPayment()
        isValidSignature = tripay.verifyCallback(body, signature)
        paymentData = body
        break

      case 'MIDTRANS':
        const midtrans = new MidtransPayment()
        isValidSignature = midtrans.verifyCallback(body, signature)
        paymentData = body
        break

      case 'XENDIT':
        const xendit = new XenditPayment()
        const webhookToken = request.headers.get('x-callback-token') || ''
        isValidSignature = xendit.verifyCallback(body, webhookToken)
        paymentData = body
        break

      default:
        return NextResponse.json(
          { success: false, message: 'Unsupported gateway' },
          { status: 400 }
        )
    }

    if (!isValidSignature) {
      console.error('Invalid webhook signature')
      return NextResponse.json(
        { success: false, message: 'Invalid signature' },
        { status: 401 }
      )
    }

    // Process payment based on gateway
    let orderId: string = ''
    let paymentStatus: string = ''
    let externalId: string = ''

    switch (gateway.toUpperCase()) {
      case 'TRIPAY':
        orderId = paymentData.merchant_ref
        externalId = paymentData.reference
        paymentStatus = paymentData.status
        break

      case 'MIDTRANS':
        orderId = paymentData.order_id
        externalId = paymentData.transaction_id
        paymentStatus = paymentData.transaction_status
        break

      case 'XENDIT':
        orderId = paymentData.external_id
        externalId = paymentData.id
        paymentStatus = paymentData.status
        break
    }

    // Find payment record
    const payment = await prisma.payment.findFirst({
      where: {
        OR: [
          { externalId },
          { description: { contains: orderId } }
        ]
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        tickets: {
          include: {
            event: {
              include: {
                organizer: {
                  select: { id: true, name: true }
                }
              }
            }
          }
        }
      }
    })

    if (!payment) {
      console.error('Payment not found:', { orderId, externalId })
      return NextResponse.json(
        { success: false, message: 'Payment not found' },
        { status: 404 }
      )
    }

    // Determine if payment is successful
    const isPaymentSuccess = isPaymentSuccessful(paymentStatus, gateway)
    const isPaymentFailed = isPaymentFailed(paymentStatus, gateway)

    if (isPaymentSuccess && payment.status !== 'PAID') {
      // Process successful payment
      await prisma.$transaction(async (tx) => {
        // Update payment status
        await tx.payment.update({
          where: { id: payment.id },
          data: {
            status: 'PAID',
            paidAt: new Date(),
            metadata: paymentData,
          }
        })

        // Handle different payment types
        if (payment.type === 'TICKET' && payment.tickets.length > 0) {
          // Update event sold tickets
          const eventId = payment.tickets[0].eventId
          await tx.event.update({
            where: { id: eventId },
            data: { soldTickets: { increment: payment.tickets.length } }
          })

          // Add commission to organizer
          const event = payment.tickets[0].event
          const organizerCommission = payment.amount // amount after admin fee

          await UangtiXWallet.addBalance(
            event.organizer.id,
            organizerCommission,
            `Komisi penjualan tiket ${event.title}`,
            orderId
          )
        } else if (payment.type === 'ARTPOSURE') {
          // Update Artposure order status
          await tx.artposureOrder.update({
            where: { id: payment.reference },
            data: {
              status: 'PAID',
              paidAt: new Date()
            }
          })

          // Create notification for organizer
          await tx.notification.create({
            data: {
              userId: payment.userId,
              title: 'Pembayaran Artposure Berhasil',
              message: `Pembayaran untuk layanan Artposure berhasil. Order Anda akan segera diproses.`,
              type: 'ARTPOSURE_PAYMENT',
              data: {
                orderId: payment.reference,
                amount: payment.amount
              }
            }
          })
        } else if (payment.type === 'BOOST') {
          // Activate event boost
          const boost = await tx.eventBoost.update({
            where: { id: payment.reference },
            data: { status: 'ACTIVE' },
            include: {
              event: true,
              package: true
            }
          })

          // Update event boost status
          await tx.event.update({
            where: { id: boost.eventId },
            data: {
              isBoosted: true,
              boostEndDate: boost.endDate
            }
          })

          // Create notification for organizer
          await tx.notification.create({
            data: {
              userId: payment.userId,
              title: 'Boost Event Diaktifkan',
              message: `Event "${boost.event.title}" berhasil di-boost dengan paket ${boost.package.name}`,
              type: 'BOOST_ACTIVATED',
              data: {
                boostId: boost.id,
                eventId: boost.eventId,
                packageName: boost.package.name,
                endDate: boost.endDate.toISOString()
              }
            }
          })
        } else if (payment.type === 'UANGTIX_DEPOSIT') {
          // Add balance to UangtiX wallet
          await UangtiXWallet.addBalance(
            payment.userId,
            payment.amount,
            `Top up UangtiX via ${payment.gateway}`,
            orderId
          )
        }

        // Create notification for user
        await tx.notification.create({
          data: {
            userId: payment.userId,
            title: 'Pembayaran Berhasil',
            message: `Pembayaran untuk ${payment.description} telah berhasil diproses`,
            type: 'PAYMENT_SUCCESS',
            data: {
              paymentId: payment.id,
              orderId,
              amount: payment.totalAmount,
            }
          }
        })
      })

      console.log('Payment processed successfully:', { orderId, paymentId: payment.id })

    } else if (isPaymentFailed && payment.status === 'PENDING') {
      // Process failed payment
      await prisma.$transaction(async (tx) => {
        // Update payment status
        await tx.payment.update({
          where: { id: payment.id },
          data: {
            status: 'FAILED',
            metadata: paymentData,
          }
        })

        // Handle failed payment cleanup based on type
        if (payment.type === 'TICKET') {
          // Delete pending tickets
          await tx.ticket.deleteMany({
            where: { paymentId: payment.id }
          })
        } else if (payment.type === 'ARTPOSURE') {
          // Update Artposure order status to failed
          await tx.artposureOrder.update({
            where: { id: payment.reference },
            data: { status: 'FAILED' }
          })
        } else if (payment.type === 'BOOST') {
          // Delete pending boost
          await tx.eventBoost.delete({
            where: { id: payment.reference }
          })
        }

        // Create notification for user
        await tx.notification.create({
          data: {
            userId: payment.userId,
            title: 'Pembayaran Gagal',
            message: `Pembayaran untuk ${payment.description} gagal diproses`,
            type: 'PAYMENT_FAILED',
            data: {
              paymentId: payment.id,
              orderId,
              reason: paymentStatus,
            }
          }
        })
      })

      console.log('Payment failed:', { orderId, paymentId: payment.id, status: paymentStatus })
    }

    return NextResponse.json({ success: true, message: 'Webhook processed' })

  } catch (error) {
    console.error('Webhook processing error:', error)
    return NextResponse.json(
      { success: false, message: 'Webhook processing failed' },
      { status: 500 }
    )
  }
}

function isPaymentSuccessful(status: string, gateway: string): boolean {
  switch (gateway.toUpperCase()) {
    case 'TRIPAY':
      return status === 'PAID'
    case 'MIDTRANS':
      return ['capture', 'settlement'].includes(status)
    case 'XENDIT':
      return status === 'PAID'
    default:
      return false
  }
}

function isPaymentFailed(status: string, gateway: string): boolean {
  switch (gateway.toUpperCase()) {
    case 'TRIPAY':
      return ['EXPIRED', 'FAILED', 'CANCELED'].includes(status)
    case 'MIDTRANS':
      return ['deny', 'cancel', 'expire', 'failure'].includes(status)
    case 'XENDIT':
      return ['EXPIRED', 'FAILED'].includes(status)
    default:
      return false
  }
}
