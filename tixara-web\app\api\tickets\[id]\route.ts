import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET - Get single ticket
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const ticket = await prisma.ticket.findUnique({
      where: { id: params.id },
      include: {
        event: {
          include: {
            organizer: {
              select: {
                id: true,
                name: true,
                isVerified: true,
              }
            },
            category: {
              select: {
                id: true,
                name: true,
                color: true,
              }
            }
          }
        },
        template: {
          select: {
            id: true,
            name: true,
            preview: true,
          }
        },
        validator: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    })

    if (!ticket) {
      return NextResponse.json(
        { success: false, message: 'Tiket tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check if user owns this ticket or has permission to view it
    const canView = 
      ticket.buyerId === session.user.id || // Owner
      session.user.role === 'ADMIN' || // Admin
      (session.user.role === 'STAFF' && await checkStaffPermission(session.user.id, ticket.eventId)) || // Staff with permission
      ticket.event.organizerId === session.user.id // Event organizer

    if (!canView) {
      return NextResponse.json(
        { success: false, message: 'Anda tidak memiliki akses untuk melihat tiket ini' },
        { status: 403 }
      )
    }

    return NextResponse.json({
      success: true,
      data: ticket,
    })
  } catch (error) {
    console.error('Error fetching ticket:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}

// Helper function to check staff permission
async function checkStaffPermission(staffId: string, eventId: string): Promise<boolean> {
  const staffAssignment = await prisma.eventStaff.findFirst({
    where: {
      eventId,
      staffId,
    }
  })
  return !!staffAssignment
}
