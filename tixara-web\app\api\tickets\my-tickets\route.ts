import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // 'active', 'used', 'all'
    const eventId = searchParams.get('eventId')
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const skip = (page - 1) * limit

    // Build where clause
    const where: any = {
      buyerId: session.user.id
    }

    if (status === 'active') {
      where.isUsed = false
    } else if (status === 'used') {
      where.isUsed = true
    }

    if (eventId) {
      where.eventId = eventId
    }

    const [tickets, total] = await Promise.all([
      prisma.ticket.findMany({
        where,
        include: {
          event: {
            include: {
              organizer: {
                select: {
                  id: true,
                  name: true,
                  isVerified: true,
                }
              },
              category: {
                select: {
                  id: true,
                  name: true,
                  color: true,
                }
              }
            }
          },
          template: {
            select: {
              id: true,
              name: true,
              preview: true,
            }
          },
          validator: {
            select: {
              id: true,
              name: true,
              email: true,
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
      }),
      prisma.ticket.count({ where }),
    ])

    // Group tickets by event for better organization
    const ticketsByEvent = tickets.reduce((acc, ticket) => {
      const eventId = ticket.event.id
      if (!acc[eventId]) {
        acc[eventId] = {
          event: ticket.event,
          tickets: []
        }
      }
      acc[eventId].tickets.push(ticket)
      return acc
    }, {} as Record<string, any>)

    return NextResponse.json({
      success: true,
      data: {
        tickets,
        ticketsByEvent: Object.values(ticketsByEvent),
        summary: {
          total,
          active: tickets.filter(t => !t.isUsed).length,
          used: tickets.filter(t => t.isUsed).length,
        }
      },
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      },
    })
  } catch (error) {
    console.error('Error fetching user tickets:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
