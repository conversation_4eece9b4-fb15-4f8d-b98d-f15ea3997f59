import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { validateTicketQR } from '@/lib/ticket-utils'

// Schema untuk validasi QR
const validateQRSchema = z.object({
  qrData: z.string().min(1, 'QR data wajib diisi'),
  eventId: z.string().min(1, 'Event ID wajib diisi'),
})

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Only staff and admin can validate tickets
    if (!['STAFF', 'ADMIN'].includes(session.user.role)) {
      return NextResponse.json(
        { success: false, message: 'Hanya staff yang dapat memvalidasi tiket' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = validateQRSchema.parse(body)

    // Validate QR format
    const qrValidation = validateTicketQR(validatedData.qrData)
    if (!qrValidation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: qrValidation.error || 'QR code tidak valid',
          status: 'INVALID_QR'
        },
        { status: 400 }
      )
    }

    // Get ticket details
    const ticket = await prisma.ticket.findUnique({
      where: { 
        qrCode: validatedData.qrData 
      },
      include: {
        event: {
          include: {
            organizer: true,
            category: true,
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        validator: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    })

    if (!ticket) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Tiket tidak ditemukan',
          status: 'NOT_FOUND'
        },
        { status: 404 }
      )
    }

    // Check if ticket belongs to the correct event
    if (ticket.eventId !== validatedData.eventId) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Tiket tidak valid untuk event ini',
          status: 'WRONG_EVENT'
        },
        { status: 400 }
      )
    }

    // Check if staff has permission to validate this event
    if (session.user.role === 'STAFF') {
      const staffAssignment = await prisma.eventStaff.findFirst({
        where: {
          eventId: validatedData.eventId,
          staffId: session.user.id,
        }
      })

      if (!staffAssignment) {
        return NextResponse.json(
          { 
            success: false, 
            message: 'Anda tidak memiliki akses untuk memvalidasi tiket event ini',
            status: 'NO_PERMISSION'
          },
          { status: 403 }
        )
      }
    }

    // Check if ticket is already used
    if (ticket.isUsed) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Tiket sudah pernah digunakan',
          status: 'ALREADY_USED',
          data: {
            ticket: {
              id: ticket.id,
              ticketCode: ticket.ticketCode,
              usedAt: ticket.usedAt,
              validatedBy: ticket.validator,
            },
            event: {
              id: ticket.event.id,
              title: ticket.event.title,
            },
            buyer: ticket.buyer,
          }
        },
        { status: 400 }
      )
    }

    // Check if event has started (optional validation)
    const now = new Date()
    const eventStart = new Date(ticket.event.startDate)
    const eventEnd = new Date(ticket.event.endDate)

    if (now < eventStart) {
      // Event hasn't started yet - warning but allow validation
      return NextResponse.json({
        success: true,
        warning: 'Event belum dimulai',
        status: 'EARLY_VALIDATION',
        data: {
          ticket: {
            id: ticket.id,
            ticketCode: ticket.ticketCode,
            price: ticket.price,
            createdAt: ticket.createdAt,
          },
          event: {
            id: ticket.event.id,
            title: ticket.event.title,
            startDate: ticket.event.startDate,
            endDate: ticket.event.endDate,
            location: ticket.event.location,
            organizer: ticket.event.organizer.name,
          },
          buyer: ticket.buyer,
        }
      })
    }

    if (now > eventEnd) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Event sudah berakhir',
          status: 'EVENT_ENDED'
        },
        { status: 400 }
      )
    }

    // Validate ticket - mark as used
    const validatedTicket = await prisma.ticket.update({
      where: { id: ticket.id },
      data: {
        isUsed: true,
        usedAt: new Date(),
        validatedBy: session.user.id,
      },
      include: {
        event: {
          include: {
            organizer: true,
            category: true,
          }
        },
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        },
        validator: {
          select: {
            id: true,
            name: true,
            email: true,
          }
        }
      }
    })

    // Log validation activity
    await prisma.notification.create({
      data: {
        userId: ticket.buyerId,
        title: 'Tiket Divalidasi',
        message: `Tiket Anda untuk "${ticket.event.title}" telah divalidasi`,
        type: 'TICKET',
        data: { 
          ticketId: ticket.id,
          eventId: ticket.eventId,
          validatedBy: session.user.id
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Tiket berhasil divalidasi',
      status: 'VALIDATED',
      data: {
        ticket: {
          id: validatedTicket.id,
          ticketCode: validatedTicket.ticketCode,
          price: validatedTicket.price,
          usedAt: validatedTicket.usedAt,
          createdAt: validatedTicket.createdAt,
        },
        event: {
          id: validatedTicket.event.id,
          title: validatedTicket.event.title,
          startDate: validatedTicket.event.startDate,
          endDate: validatedTicket.event.endDate,
          location: validatedTicket.event.location,
          organizer: validatedTicket.event.organizer.name,
          category: validatedTicket.event.category.name,
        },
        buyer: validatedTicket.buyer,
        validator: validatedTicket.validator,
      }
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, message: error.errors[0].message },
        { status: 400 }
      )
    }

    console.error('Error validating ticket:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
