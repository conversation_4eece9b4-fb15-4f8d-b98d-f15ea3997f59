import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { UangtiXWallet } from '@/lib/payment-utils'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const balance = await UangtiXWallet.getBalance(session.user.id)

    return NextResponse.json({
      success: true,
      data: {
        balance,
        formattedBalance: new Intl.NumberFormat('id-ID', {
          style: 'currency',
          currency: 'IDR',
          minimumFractionDigits: 0,
        }).format(balance)
      }
    })
  } catch (error) {
    console.error('Get balance error:', error)
    return NextResponse.json(
      { success: false, message: '<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> server' },
      { status: 500 }
    )
  }
}
