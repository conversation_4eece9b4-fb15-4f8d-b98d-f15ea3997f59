import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { PaymentFactory } from '@/lib/payment-utils'
import { nanoid } from 'nanoid'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { amount, gateway = 'TRIPAY', paymentMethod = 'QRIS' } = body

    // Validate amount
    if (!amount || amount < 10000) {
      return NextResponse.json(
        { success: false, message: 'Minimum deposit Rp 10.000' },
        { status: 400 }
      )
    }

    if (amount > 10000000) {
      return NextResponse.json(
        { success: false, message: 'Maksimum deposit Rp 10.000.000' },
        { status: 400 }
      )
    }

    // Generate unique order ID
    const orderId = `DEP-${nanoid(10)}`

    try {
      const paymentGateway = PaymentFactory.createPaymentGateway(gateway)
      
      const paymentRequest = {
        amount,
        description: `Deposit UangtiX - ${session.user.name}`,
        customerName: session.user.name,
        customerEmail: session.user.email,
        customerPhone: session.user.phone || '',
        orderId,
        returnUrl: `${process.env.NEXTAUTH_URL}/uangtix/deposit/success?orderId=${orderId}`,
        expiredTime: 60, // 1 hour
      }

      const paymentResponse = await paymentGateway.createPayment(paymentRequest, paymentMethod)

      if (!paymentResponse.success) {
        return NextResponse.json(
          { success: false, message: paymentResponse.message },
          { status: 400 }
        )
      }

      // Create payment record
      const payment = await prisma.payment.create({
        data: {
          userId: session.user.id,
          gateway: gateway as any,
          externalId: paymentResponse.paymentId,
          amount,
          adminFee: 0, // No admin fee for deposits
          totalAmount: amount,
          description: `Deposit UangtiX`,
          status: 'PENDING',
          paymentUrl: paymentResponse.paymentUrl,
          expiredAt: paymentResponse.expiredAt,
          metadata: paymentResponse.data,
        }
      })

      // Create pending UangtiX transaction
      await prisma.uangtiXTransaction.create({
        data: {
          userId: session.user.id,
          type: 'DEPOSIT',
          amount,
          description: `Deposit via ${gateway}`,
          reference: orderId,
          status: 'PENDING',
          paymentId: payment.id,
          balanceBefore: 0, // Will be updated when payment is confirmed
          balanceAfter: 0,
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Deposit request created successfully',
        data: {
          paymentId: payment.id,
          orderId,
          paymentUrl: paymentResponse.paymentUrl,
          qrCode: paymentResponse.qrCode,
          expiredAt: paymentResponse.expiredAt,
          amount,
          gateway,
        }
      })

    } catch (error: any) {
      console.error('Deposit creation error:', error)
      return NextResponse.json(
        { success: false, message: error.message || 'Gagal membuat deposit' },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('Deposit API error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
