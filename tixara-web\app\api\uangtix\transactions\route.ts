import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type')
    const status = searchParams.get('status')
    const search = searchParams.get('search')
    const exportData = searchParams.get('export') === 'true'
    const skip = (page - 1) * limit

    const where: any = {
      userId: session.user.id,
    }

    if (type && type !== 'ALL') {
      where.type = type
    }

    if (status && status !== 'ALL') {
      where.status = status
    }

    if (search) {
      where.description = {
        contains: search,
        mode: 'insensitive'
      }
    }

    // Handle export
    if (exportData) {
      const allTransactions = await prisma.uangtiXTransaction.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        include: {
          payment: {
            select: {
              id: true,
              gateway: true,
              status: true,
            }
          }
        }
      })

      // Generate CSV
      const csvHeaders = 'Date,Type,Description,Amount,Status,Balance Before,Balance After,Gateway\n'
      const csvRows = allTransactions.map(tx => {
        const date = new Date(tx.createdAt).toLocaleDateString('id-ID')
        const amount = tx.type === 'DEPOSIT' || tx.amount > 0 ? tx.amount : -tx.amount
        const gateway = tx.payment?.gateway || ''

        return `"${date}","${tx.type}","${tx.description}","${amount}","${tx.status}","${tx.balanceBefore}","${tx.balanceAfter}","${gateway}"`
      }).join('\n')

      const csvContent = csvHeaders + csvRows

      return new NextResponse(csvContent, {
        headers: {
          'Content-Type': 'text/csv',
          'Content-Disposition': `attachment; filename="uangtix-transactions-${new Date().toISOString().split('T')[0]}.csv"`
        }
      })
    }

    const [transactions, total] = await Promise.all([
      prisma.uangtiXTransaction.findMany({
        where,
        orderBy: { createdAt: 'desc' },
        skip,
        take: limit,
        include: {
          payment: {
            select: {
              id: true,
              gateway: true,
              status: true,
            }
          }
        }
      }),
      prisma.uangtiXTransaction.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: transactions,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
      }
    })
  } catch (error) {
    console.error('Get transactions error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
