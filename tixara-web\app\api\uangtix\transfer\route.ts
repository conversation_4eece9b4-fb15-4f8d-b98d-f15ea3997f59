import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UangtiXWallet } from '@/lib/payment-utils'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { recipientEmail, amount, description = 'Transfer UangtiX' } = body

    // Validate input
    if (!recipientEmail || !amount) {
      return NextResponse.json(
        { success: false, message: 'Email penerima dan jumlah diperlukan' },
        { status: 400 }
      )
    }

    if (amount < 1000) {
      return NextResponse.json(
        { success: false, message: 'Minimum transfer Rp 1.000' },
        { status: 400 }
      )
    }

    if (recipientEmail === session.user.email) {
      return NextResponse.json(
        { success: false, message: 'Tidak dapat transfer ke diri sendiri' },
        { status: 400 }
      )
    }

    // Find recipient
    const recipient = await prisma.user.findUnique({
      where: { email: recipientEmail },
      select: { id: true, name: true, email: true }
    })

    if (!recipient) {
      return NextResponse.json(
        { success: false, message: 'Pengguna penerima tidak ditemukan' },
        { status: 404 }
      )
    }

    // Check sender balance
    const senderBalance = await UangtiXWallet.getBalance(session.user.id)
    if (senderBalance < amount) {
      return NextResponse.json(
        { success: false, message: 'Saldo tidak mencukupi' },
        { status: 400 }
      )
    }

    // Process transfer
    const transferSuccess = await UangtiXWallet.transfer(
      session.user.id,
      recipient.id,
      amount,
      description
    )

    if (!transferSuccess) {
      return NextResponse.json(
        { success: false, message: 'Gagal memproses transfer' },
        { status: 500 }
      )
    }

    // Create notifications
    await prisma.notification.createMany({
      data: [
        {
          userId: session.user.id,
          title: 'Transfer Berhasil',
          message: `Transfer Rp ${amount.toLocaleString('id-ID')} ke ${recipient.name} berhasil`,
          type: 'PAYMENT_SUCCESS',
          data: {
            type: 'TRANSFER_OUT',
            amount,
            recipient: recipient.name,
          }
        },
        {
          userId: recipient.id,
          title: 'Menerima Transfer',
          message: `Anda menerima transfer Rp ${amount.toLocaleString('id-ID')} dari ${session.user.name}`,
          type: 'PAYMENT_SUCCESS',
          data: {
            type: 'TRANSFER_IN',
            amount,
            sender: session.user.name,
          }
        }
      ]
    })

    return NextResponse.json({
      success: true,
      message: 'Transfer berhasil',
      data: {
        amount,
        recipient: {
          name: recipient.name,
          email: recipient.email,
        },
        description,
      }
    })

  } catch (error) {
    console.error('Transfer error:', error)
    return NextResponse.json(
      { success: false, message: 'Terjadi kesalahan server' },
      { status: 500 }
    )
  }
}
