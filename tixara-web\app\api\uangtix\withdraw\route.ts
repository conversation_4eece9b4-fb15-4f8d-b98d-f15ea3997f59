import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { UangtiXWallet } from '@/lib/payment-utils'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { amount, bankCode, accountNumber, accountName } = body

    // Validation
    if (!amount || !bankCode || !accountNumber || !accountName) {
      return NextResponse.json(
        { success: false, message: 'Missing required fields' },
        { status: 400 }
      )
    }

    if (amount < 50000) {
      return NextResponse.json(
        { success: false, message: 'Minimum withdraw amount is Rp 50.000' },
        { status: 400 }
      )
    }

    if (accountNumber.length < 8) {
      return NextResponse.json(
        { success: false, message: 'Account number must be at least 8 digits' },
        { status: 400 }
      )
    }

    // Calculate fee
    const calculateFee = (amount: number) => {
      return amount <= 100000 ? 2500 : 5000
    }

    const fee = calculateFee(amount)
    const totalDeduction = amount + fee

    // Check user balance
    const currentBalance = await UangtiXWallet.getBalance(session.user.id)
    
    if (currentBalance < totalDeduction) {
      return NextResponse.json(
        { success: false, message: `Insufficient balance. Required: Rp ${totalDeduction.toLocaleString('id-ID')} (including admin fee Rp ${fee.toLocaleString('id-ID')})` },
        { status: 400 }
      )
    }

    // Create withdraw transaction
    const result = await prisma.$transaction(async (tx) => {
      // Get current user balance
      const user = await tx.user.findUnique({
        where: { id: session.user.id },
        select: { uangtixBalance: true }
      })

      if (!user) {
        throw new Error('User not found')
      }

      const balanceBefore = user.uangtixBalance
      const balanceAfter = balanceBefore - totalDeduction

      // Update user balance
      await tx.user.update({
        where: { id: session.user.id },
        data: { uangtixBalance: balanceAfter }
      })

      // Create withdraw transaction record
      const withdrawTransaction = await tx.uangtiXTransaction.create({
        data: {
          userId: session.user.id,
          type: 'WITHDRAW',
          amount: amount,
          description: `Withdraw to ${bankCode} ${accountNumber} - ${accountName}`,
          reference: `${bankCode}-${accountNumber}`,
          status: 'PENDING', // Withdraw needs manual processing
          balanceBefore,
          balanceAfter,
        }
      })

      // Create admin fee transaction
      await tx.uangtiXTransaction.create({
        data: {
          userId: session.user.id,
          type: 'PAYMENT',
          amount: fee,
          description: `Admin fee for withdraw ${withdrawTransaction.id}`,
          reference: withdrawTransaction.id,
          status: 'SUCCESS',
          balanceBefore: balanceAfter,
          balanceAfter: balanceAfter, // Fee already deducted above
        }
      })

      // Create notification for admin
      await tx.notification.create({
        data: {
          userId: session.user.id,
          title: 'Withdraw Request Submitted',
          message: `Your withdraw request of Rp ${amount.toLocaleString('id-ID')} has been submitted and will be processed within 1-3 business days.`,
          type: 'TRANSACTION',
          isRead: false,
        }
      })

      return withdrawTransaction
    })

    return NextResponse.json({
      success: true,
      message: 'Withdraw request submitted successfully',
      data: {
        transactionId: result.id,
        amount,
        fee,
        totalDeduction,
        bankCode,
        accountNumber,
        accountName,
        status: 'PENDING'
      }
    })

  } catch (error) {
    console.error('Withdraw error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
