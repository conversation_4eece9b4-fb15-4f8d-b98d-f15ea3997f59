import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user) {
      return NextResponse.json(
        { success: false, message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')
    const name = searchParams.get('name')
    const limit = parseInt(searchParams.get('limit') || '10')

    if (!email && !name) {
      return NextResponse.json(
        { success: false, message: 'Email or name parameter required' },
        { status: 400 }
      )
    }

    // Build search conditions
    const whereConditions: any = {
      AND: [
        {
          id: {
            not: session.user.id // Exclude current user
          }
        },
        {
          isVerified: true // Only verified users
        }
      ]
    }

    if (email) {
      whereConditions.AND.push({
        email: {
          contains: email,
          mode: 'insensitive'
        }
      })
    }

    if (name) {
      whereConditions.AND.push({
        name: {
          contains: name,
          mode: 'insensitive'
        }
      })
    }

    const users = await prisma.user.findMany({
      where: whereConditions,
      select: {
        id: true,
        name: true,
        email: true,
        avatar: true,
        role: true,
        badge: true,
      },
      take: limit,
      orderBy: {
        name: 'asc'
      }
    })

    return NextResponse.json({
      success: true,
      data: users
    })

  } catch (error) {
    console.error('User search error:', error)
    return NextResponse.json(
      { success: false, message: 'Internal server error' },
      { status: 500 }
    )
  }
}
