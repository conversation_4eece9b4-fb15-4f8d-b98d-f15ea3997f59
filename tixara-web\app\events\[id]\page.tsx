'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useParams, useRouter } from 'next/navigation'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock, 
  Star,
  Share2,
  Heart,
  Loader2,
  AlertCircle,
  CheckCircle,
  User,
  Tag
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatDate, formatCurrency, formatRelativeTime } from '@/lib/utils'

interface Event {
  id: string
  title: string
  description: string
  location: string
  startDate: string
  endDate: string
  price: number
  maxTickets: number
  image?: string
  isActive: boolean
  createdAt: string
  category: {
    id: string
    name: string
    color?: string
  }
  organizer: {
    id: string
    name: string
    email: string
    isVerified: boolean
    badge?: string
    _count: {
      events: number
    }
  }
  soldTickets: number
  availableTickets: number
}

export default function EventDetailPage() {
  const { data: session } = useSession()
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()

  const [event, setEvent] = useState<Event | null>(null)
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState(false)

  useEffect(() => {
    const fetchEvent = async () => {
      try {
        const response = await fetch(`/api/events/${params.id}`)
        const data = await response.json()
        
        if (data.success) {
          setEvent(data.data)
        } else {
          toast({
            title: 'Error',
            description: data.message || 'Event tidak ditemukan',
            variant: 'destructive',
          })
          router.push('/events')
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Terjadi kesalahan saat mengambil data event',
          variant: 'destructive',
        })
        router.push('/events')
      } finally {
        setLoading(false)
      }
    }

    if (params.id) {
      fetchEvent()
    }
  }, [params.id, router, toast])

  const handlePurchase = async () => {
    if (!session) {
      router.push('/auth/login')
      return
    }

    setPurchasing(true)
    
    try {
      // TODO: Implement ticket purchase logic
      toast({
        title: 'Fitur Segera Hadir',
        description: 'Pembelian tiket akan segera tersedia',
        variant: 'default',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat membeli tiket',
        variant: 'destructive',
      })
    } finally {
      setPurchasing(false)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!event) {
    return (
      <div className="container mx-auto py-8 px-4 text-center">
        <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h1 className="text-2xl font-bold mb-2">Event Tidak Ditemukan</h1>
        <p className="text-gray-600 mb-4">Event yang Anda cari tidak tersedia</p>
        <Button onClick={() => router.push('/events')}>
          Kembali ke Daftar Event
        </Button>
      </div>
    )
  }

  const isEventEnded = new Date(event.endDate) < new Date()
  const isEventStarted = new Date(event.startDate) < new Date()
  const isSoldOut = event.availableTickets <= 0
  const canPurchase = !isEventEnded && !isSoldOut && event.isActive

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Event Image */}
          {event.image && (
            <div className="relative aspect-video rounded-lg overflow-hidden">
              <Image
                src={event.image}
                alt={event.title}
                fill
                className="object-cover"
              />
            </div>
          )}

          {/* Event Header */}
          <div>
            <div className="flex items-center gap-2 mb-2">
              <Badge 
                variant="secondary"
                style={{ backgroundColor: event.category.color + '20', color: event.category.color }}
              >
                <Tag className="h-3 w-3 mr-1" />
                {event.category.name}
              </Badge>
              {!event.isActive && (
                <Badge variant="secondary">Nonaktif</Badge>
              )}
              {isEventEnded && (
                <Badge variant="secondary">Berakhir</Badge>
              )}
              {isSoldOut && (
                <Badge variant="destructive">Sold Out</Badge>
              )}
            </div>
            
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              {event.title}
            </h1>
            
            <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Calendar className="h-4 w-4" />
                <span>{formatDate(event.startDate)}</span>
              </div>
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                <span>{formatRelativeTime(event.startDate)}</span>
              </div>
            </div>
          </div>

          {/* Event Description */}
          <Card>
            <CardHeader>
              <CardTitle>Tentang Event</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                {event.description}
              </p>
            </CardContent>
          </Card>

          {/* Event Details */}
          <Card>
            <CardHeader>
              <CardTitle>Detail Event</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-primary-500" />
                  <div>
                    <p className="font-medium">Tanggal Mulai</p>
                    <p className="text-sm text-gray-600">{formatDate(event.startDate)}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Calendar className="h-5 w-5 text-primary-500" />
                  <div>
                    <p className="font-medium">Tanggal Selesai</p>
                    <p className="text-sm text-gray-600">{formatDate(event.endDate)}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <MapPin className="h-5 w-5 text-primary-500" />
                  <div>
                    <p className="font-medium">Lokasi</p>
                    <p className="text-sm text-gray-600">{event.location}</p>
                  </div>
                </div>
                
                <div className="flex items-center gap-3">
                  <Users className="h-5 w-5 text-primary-500" />
                  <div>
                    <p className="font-medium">Kapasitas</p>
                    <p className="text-sm text-gray-600">
                      {event.soldTickets} / {event.maxTickets} tiket terjual
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Organizer Info */}
          <Card>
            <CardHeader>
              <CardTitle>Organizer</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-primary-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <h3 className="font-medium">{event.organizer.name}</h3>
                    {event.organizer.isVerified && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                    {event.organizer.badge && (
                      <Badge variant="secondary">{event.organizer.badge}</Badge>
                    )}
                  </div>
                  <p className="text-sm text-gray-600">
                    {event.organizer._count.events} event dibuat
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Purchase Card */}
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">
                {event.price === 0 ? 'Gratis' : formatCurrency(event.price)}
              </CardTitle>
              <CardDescription>
                {event.availableTickets} tiket tersisa
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {!canPurchase ? (
                <div className="space-y-2">
                  {isEventEnded && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Event ini telah berakhir
                      </AlertDescription>
                    </Alert>
                  )}
                  {isSoldOut && !isEventEnded && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Tiket sudah habis terjual
                      </AlertDescription>
                    </Alert>
                  )}
                  {!event.isActive && (
                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        Event ini sedang tidak aktif
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <Button 
                  className="w-full" 
                  size="lg"
                  onClick={handlePurchase}
                  disabled={purchasing}
                >
                  {purchasing && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {event.price === 0 ? 'Daftar Gratis' : 'Beli Tiket'}
                </Button>
              )}
              
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="flex-1">
                  <Heart className="h-4 w-4 mr-2" />
                  Simpan
                </Button>
                <Button variant="outline" size="sm" className="flex-1">
                  <Share2 className="h-4 w-4 mr-2" />
                  Bagikan
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Event Stats */}
          <Card>
            <CardHeader>
              <CardTitle>Statistik Event</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Tiket Terjual</span>
                <span className="font-medium">{event.soldTickets}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Tiket Tersisa</span>
                <span className="font-medium">{event.availableTickets}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Kapasitas</span>
                <span className="font-medium">{event.maxTickets}</span>
              </div>
              
              {/* Progress Bar */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress Penjualan</span>
                  <span>{((event.soldTickets / event.maxTickets) * 100).toFixed(1)}%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-primary-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${(event.soldTickets / event.maxTickets) * 100}%` }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Info */}
          <Card>
            <CardHeader>
              <CardTitle>Info Cepat</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Kategori</span>
                <span className="font-medium">{event.category.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Dibuat</span>
                <span className="font-medium">{formatRelativeTime(event.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Status</span>
                <Badge variant={event.isActive ? 'success' : 'secondary'}>
                  {event.isActive ? 'Aktif' : 'Nonaktif'}
                </Badge>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
