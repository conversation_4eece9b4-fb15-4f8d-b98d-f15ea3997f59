'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Search, 
  Filter, 
  Calendar, 
  MapPin, 
  Users, 
  Loader2, 
  AlertCircle,
  Grid,
  List,
  SlidersHorizontal
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatDate, formatCurrency, formatRelativeTime } from '@/lib/utils'

interface Event {
  id: string
  title: string
  description: string
  location: string
  startDate: string
  endDate: string
  price: number
  maxTickets: number
  image?: string
  isActive: boolean
  category: {
    id: string
    name: string
    color?: string
  }
  organizer: {
    id: string
    name: string
    isVerified: boolean
  }
  _count: {
    tickets: number
  }
}

interface Category {
  id: string
  name: string
  color?: string
}

export default function EventsPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  const [events, setEvents] = useState<Event[]>([])
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    category: searchParams.get('category') || '',
    priceMin: searchParams.get('priceMin') || '',
    priceMax: searchParams.get('priceMax') || '',
    sortBy: searchParams.get('sortBy') || 'startDate',
  })

  // Fetch events
  const fetchEvents = async () => {
    try {
      const params = new URLSearchParams()
      
      if (filters.search) params.append('search', filters.search)
      if (filters.category) params.append('categoryId', filters.category)
      if (filters.priceMin) params.append('priceMin', filters.priceMin)
      if (filters.priceMax) params.append('priceMax', filters.priceMax)
      if (filters.sortBy) params.append('sortBy', filters.sortBy)
      
      params.append('active', 'true') // Only show active events
      params.append('limit', '50')

      const response = await fetch(`/api/events?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setEvents(data.data)
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal mengambil data event',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat mengambil data',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/categories?active=true')
      const data = await response.json()
      
      if (data.success) {
        setCategories(data.data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  useEffect(() => {
    fetchEvents()
    fetchCategories()
  }, [filters])

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams()
    
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value)
    })
    
    const newUrl = params.toString() ? `?${params.toString()}` : '/events'
    router.replace(newUrl, { scroll: false })
  }, [filters, router])

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const clearFilters = () => {
    setFilters({
      search: '',
      category: '',
      priceMin: '',
      priceMax: '',
      sortBy: 'startDate',
    })
  }

  const isEventSoldOut = (event: Event) => {
    return event._count.tickets >= event.maxTickets
  }

  const isEventEnded = (event: Event) => {
    return new Date(event.endDate) < new Date()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Jelajahi Event
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Temukan event menarik di sekitar Anda
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Grid className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'list' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            <List className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <SlidersHorizontal className="h-5 w-5" />
            Filter & Pencarian
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari event..."
                  value={filters.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Category */}
            <div>
              <Select
                value={filters.category}
                onValueChange={(value) => handleFilterChange('category', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Semua Kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Semua Kategori</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Price Range */}
            <div className="flex gap-2">
              <Input
                type="number"
                placeholder="Harga min"
                value={filters.priceMin}
                onChange={(e) => handleFilterChange('priceMin', e.target.value)}
              />
              <Input
                type="number"
                placeholder="Harga max"
                value={filters.priceMax}
                onChange={(e) => handleFilterChange('priceMax', e.target.value)}
              />
            </div>

            {/* Sort */}
            <div>
              <Select
                value={filters.sortBy}
                onValueChange={(value) => handleFilterChange('sortBy', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="startDate">Tanggal Terdekat</SelectItem>
                  <SelectItem value="price">Harga Terendah</SelectItem>
                  <SelectItem value="title">Nama A-Z</SelectItem>
                  <SelectItem value="createdAt">Terbaru</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Active Filters */}
          {(filters.search || filters.category || filters.priceMin || filters.priceMax) && (
            <div className="flex items-center gap-2 mt-4 pt-4 border-t">
              <span className="text-sm text-gray-600">Filter aktif:</span>
              {filters.search && (
                <Badge variant="secondary">
                  Pencarian: {filters.search}
                </Badge>
              )}
              {filters.category && (
                <Badge variant="secondary">
                  Kategori: {categories.find(c => c.id === filters.category)?.name}
                </Badge>
              )}
              {(filters.priceMin || filters.priceMax) && (
                <Badge variant="secondary">
                  Harga: {filters.priceMin || '0'} - {filters.priceMax || '∞'}
                </Badge>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearFilters}
                className="text-red-600 hover:text-red-700"
              >
                Hapus Semua
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Results */}
      <div className="mb-4">
        <p className="text-gray-600">
          Menampilkan {events.length} event
        </p>
      </div>

      {/* Events Grid/List */}
      {events.length === 0 ? (
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Tidak ada event ditemukan
          </h3>
          <p className="text-gray-600 mb-4">
            Coba ubah filter pencarian atau jelajahi kategori lain
          </p>
          <Button onClick={clearFilters}>
            Hapus Filter
          </Button>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'
            : 'space-y-4'
        }>
          {events.map((event) => (
            <Card key={event.id} className={`overflow-hidden hover:shadow-lg transition-shadow ${
              viewMode === 'list' ? 'flex' : ''
            }`}>
              {event.image && (
                <div className={`relative ${
                  viewMode === 'list' ? 'w-48 h-32' : 'aspect-video'
                }`}>
                  <Image
                    src={event.image}
                    alt={event.title}
                    fill
                    className="object-cover"
                  />
                  {(isEventSoldOut(event) || isEventEnded(event)) && (
                    <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                      <Badge variant="destructive">
                        {isEventEnded(event) ? 'Berakhir' : 'Sold Out'}
                      </Badge>
                    </div>
                  )}
                </div>
              )}
              
              <div className="flex-1">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge 
                      variant="secondary"
                      style={{ 
                        backgroundColor: event.category.color + '20', 
                        color: event.category.color 
                      }}
                    >
                      {event.category.name}
                    </Badge>
                    {event.organizer.isVerified && (
                      <Badge variant="outline">Verified</Badge>
                    )}
                  </div>
                  
                  <CardTitle className="line-clamp-2">
                    <Link 
                      href={`/events/${event.id}`}
                      className="hover:text-primary-600 transition-colors"
                    >
                      {event.title}
                    </Link>
                  </CardTitle>
                  
                  <CardDescription className="line-clamp-2">
                    {event.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="pt-0">
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>{formatRelativeTime(event.startDate)}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span className="line-clamp-1">{event.location}</span>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>
                        {event._count.tickets} / {event.maxTickets} tiket terjual
                      </span>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-4">
                    <div className="text-lg font-bold text-primary-600">
                      {event.price === 0 ? 'Gratis' : formatCurrency(event.price)}
                    </div>
                    
                    <Link href={`/events/${event.id}`}>
                      <Button 
                        size="sm"
                        disabled={isEventSoldOut(event) || isEventEnded(event)}
                      >
                        {isEventEnded(event) ? 'Berakhir' : 
                         isEventSoldOut(event) ? 'Sold Out' : 'Lihat Detail'}
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
