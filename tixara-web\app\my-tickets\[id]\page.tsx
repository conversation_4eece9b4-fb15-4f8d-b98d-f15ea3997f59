'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  User, 
  QrCode, 
  Download, 
  Share2,
  CheckCircle,
  Clock,
  Loader2,
  AlertCircle,
  Ticket as TicketIcon
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatDate, formatCurrency, formatRelativeTime } from '@/lib/utils'

interface TicketData {
  id: string
  ticketCode: string
  qrCode: string
  isUsed: boolean
  usedAt?: string
  price: number
  adminFee: number
  totalPaid: number
  createdAt: string
  event: {
    id: string
    title: string
    description: string
    startDate: string
    endDate: string
    location: string
    image?: string
    organizer: {
      id: string
      name: string
      isVerified: boolean
    }
    category: {
      id: string
      name: string
      color?: string
    }
  }
  template?: {
    id: string
    name: string
    preview?: string
  }
  validator?: {
    id: string
    name: string
    email: string
  }
}

export default function TicketDetailPage() {
  const { data: session, status } = useSession()
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()

  const [ticket, setTicket] = useState<TicketData | null>(null)
  const [loading, setLoading] = useState(true)
  const [qrCodeImage, setQrCodeImage] = useState<string>('')

  // Redirect jika belum login
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user) {
      router.push('/auth/login')
      return
    }
  }, [session, status, router])

  // Fetch ticket detail
  useEffect(() => {
    const fetchTicket = async () => {
      try {
        const response = await fetch(`/api/tickets/${params.id}`)
        const data = await response.json()
        
        if (data.success) {
          setTicket(data.data)
          // Generate QR code image from QR data
          if (data.data.qrCode) {
            // In real implementation, you would generate QR image from qrCode data
            // For now, we'll use a placeholder
            setQrCodeImage(`data:image/svg+xml;base64,${btoa(`
              <svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
                <rect width="200" height="200" fill="white"/>
                <text x="100" y="100" text-anchor="middle" font-family="Arial" font-size="12">
                  QR Code: ${data.data.ticketCode}
                </text>
              </svg>
            `)}`)
          }
        } else {
          toast({
            title: 'Error',
            description: data.message || 'Tiket tidak ditemukan',
            variant: 'destructive',
          })
          router.push('/my-tickets')
        }
      } catch (error) {
        toast({
          title: 'Error',
          description: 'Terjadi kesalahan saat mengambil data tiket',
          variant: 'destructive',
        })
        router.push('/my-tickets')
      } finally {
        setLoading(false)
      }
    }

    if (params.id && session?.user) {
      fetchTicket()
    }
  }, [params.id, session, router, toast])

  const isEventStarted = (startDate: string) => {
    return new Date(startDate) <= new Date()
  }

  const isEventEnded = (endDate: string) => {
    return new Date(endDate) < new Date()
  }

  const getTicketStatus = () => {
    if (!ticket) return null
    
    if (ticket.isUsed) {
      return { label: 'Sudah Digunakan', variant: 'success' as const, icon: CheckCircle }
    }
    
    if (isEventEnded(ticket.event.endDate)) {
      return { label: 'Event Berakhir', variant: 'destructive' as const, icon: AlertCircle }
    }
    
    if (isEventStarted(ticket.event.startDate)) {
      return { label: 'Dapat Digunakan', variant: 'default' as const, icon: QrCode }
    }
    
    return { label: 'Menunggu Event', variant: 'secondary' as const, icon: Clock }
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Tiket ${ticket?.event.title}`,
          text: `Saya akan menghadiri ${ticket?.event.title}!`,
          url: window.location.href,
        })
      } catch (error) {
        // User cancelled sharing
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: 'Link disalin',
        description: 'Link tiket berhasil disalin ke clipboard',
      })
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user || !ticket) {
    return null
  }

  const status_info = getTicketStatus()
  const StatusIcon = status_info?.icon || TicketIcon

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="outline"
          size="sm"
          onClick={() => router.back()}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Kembali
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Detail Tiket
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            {ticket.ticketCode}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Event Info */}
          <Card>
            <CardHeader>
              <div className="flex items-start gap-4">
                {ticket.event.image && (
                  <div className="relative w-24 h-24 rounded-lg overflow-hidden flex-shrink-0">
                    <Image
                      src={ticket.event.image}
                      alt={ticket.event.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                )}
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <Badge 
                      variant="secondary"
                      style={{ 
                        backgroundColor: ticket.event.category.color + '20', 
                        color: ticket.event.category.color 
                      }}
                    >
                      {ticket.event.category.name}
                    </Badge>
                    {ticket.event.organizer.isVerified && (
                      <Badge variant="outline">Verified</Badge>
                    )}
                  </div>
                  
                  <CardTitle className="text-xl mb-2">
                    {ticket.event.title}
                  </CardTitle>
                  
                  <CardDescription>
                    {ticket.event.description}
                  </CardDescription>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">Tanggal & Waktu</div>
                    <div className="text-sm text-gray-600">
                      {formatDate(ticket.event.startDate)} - {formatDate(ticket.event.endDate)}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">Lokasi</div>
                    <div className="text-sm text-gray-600">
                      {ticket.event.location}
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <User className="h-5 w-5 text-gray-400" />
                  <div>
                    <div className="font-medium">Organizer</div>
                    <div className="text-sm text-gray-600">
                      {ticket.event.organizer.name}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Ticket Details */}
          <Card>
            <CardHeader>
              <CardTitle>Informasi Tiket</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm text-gray-600">Kode Tiket</div>
                  <div className="font-mono font-medium">{ticket.ticketCode}</div>
                </div>
                
                <div>
                  <div className="text-sm text-gray-600">Status</div>
                  <Badge variant={status_info?.variant}>
                    <StatusIcon className="h-3 w-3 mr-1" />
                    {status_info?.label}
                  </Badge>
                </div>
                
                <div>
                  <div className="text-sm text-gray-600">Harga Tiket</div>
                  <div className="font-medium">{formatCurrency(ticket.price)}</div>
                </div>
                
                <div>
                  <div className="text-sm text-gray-600">Biaya Admin</div>
                  <div className="font-medium">{formatCurrency(ticket.adminFee)}</div>
                </div>
                
                <div>
                  <div className="text-sm text-gray-600">Total Dibayar</div>
                  <div className="font-medium text-primary">{formatCurrency(ticket.totalPaid)}</div>
                </div>
                
                <div>
                  <div className="text-sm text-gray-600">Tanggal Pembelian</div>
                  <div className="font-medium">{formatDate(ticket.createdAt)}</div>
                </div>
                
                {ticket.isUsed && ticket.usedAt && (
                  <>
                    <div>
                      <div className="text-sm text-gray-600">Tanggal Digunakan</div>
                      <div className="font-medium">{formatDate(ticket.usedAt)}</div>
                    </div>
                    
                    {ticket.validator && (
                      <div>
                        <div className="text-sm text-gray-600">Divalidasi Oleh</div>
                        <div className="font-medium">{ticket.validator.name}</div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* QR Code */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                QR Code
              </CardTitle>
              <CardDescription>
                Tunjukkan QR code ini kepada staff untuk validasi
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center">
              {qrCodeImage && (
                <div className="mb-4">
                  <Image
                    src={qrCodeImage}
                    alt="QR Code"
                    width={200}
                    height={200}
                    className="mx-auto border rounded-lg"
                  />
                </div>
              )}
              
              <div className="text-xs text-gray-500 font-mono break-all mb-4">
                {ticket.qrCode}
              </div>
              
              {!ticket.isUsed && !isEventEnded(ticket.event.endDate) && (
                <div className="text-sm text-green-600 font-medium">
                  ✓ Siap untuk divalidasi
                </div>
              )}
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Aksi</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full" variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download PDF
              </Button>
              
              <Button className="w-full" variant="outline" onClick={handleShare}>
                <Share2 className="h-4 w-4 mr-2" />
                Bagikan
              </Button>
              
              <Button 
                className="w-full" 
                variant="outline"
                onClick={() => router.push(`/events/${ticket.event.id}`)}
              >
                Lihat Event
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
