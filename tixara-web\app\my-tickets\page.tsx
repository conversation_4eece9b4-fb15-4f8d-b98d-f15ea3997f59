'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Ticket, 
  Calendar, 
  MapPin, 
  User, 
  QrCode, 
  Download, 
  Eye,
  CheckCircle,
  Clock,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatDate, formatCurrency, formatRelativeTime } from '@/lib/utils'

interface TicketData {
  id: string
  ticketCode: string
  qrCode: string
  isUsed: boolean
  usedAt?: string
  price: number
  adminFee: number
  totalPaid: number
  createdAt: string
  event: {
    id: string
    title: string
    startDate: string
    endDate: string
    location: string
    image?: string
    organizer: {
      id: string
      name: string
      isVerified: boolean
    }
    category: {
      id: string
      name: string
      color?: string
    }
  }
  template?: {
    id: string
    name: string
    preview?: string
  }
  validator?: {
    id: string
    name: string
    email: string
  }
}

interface TicketsByEvent {
  event: TicketData['event']
  tickets: TicketData[]
}

export default function MyTicketsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [tickets, setTickets] = useState<TicketData[]>([])
  const [ticketsByEvent, setTicketsByEvent] = useState<TicketsByEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('all')
  const [summary, setSummary] = useState({
    total: 0,
    active: 0,
    used: 0,
  })

  // Redirect jika belum login
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user) {
      router.push('/auth/login')
      return
    }
  }, [session, status, router])

  // Fetch tickets
  const fetchTickets = async (statusFilter = 'all') => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (statusFilter !== 'all') {
        params.append('status', statusFilter)
      }

      const response = await fetch(`/api/tickets/my-tickets?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setTickets(data.data.tickets)
        setTicketsByEvent(data.data.ticketsByEvent)
        setSummary(data.data.summary)
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal mengambil data tiket',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat mengambil data',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (session?.user) {
      fetchTickets(activeTab)
    }
  }, [session, activeTab])

  const handleTabChange = (value: string) => {
    setActiveTab(value)
  }

  const isEventStarted = (startDate: string) => {
    return new Date(startDate) <= new Date()
  }

  const isEventEnded = (endDate: string) => {
    return new Date(endDate) < new Date()
  }

  const getTicketStatus = (ticket: TicketData) => {
    if (ticket.isUsed) {
      return { label: 'Sudah Digunakan', variant: 'success' as const, icon: CheckCircle }
    }
    
    if (isEventEnded(ticket.event.endDate)) {
      return { label: 'Event Berakhir', variant: 'destructive' as const, icon: AlertCircle }
    }
    
    if (isEventStarted(ticket.event.startDate)) {
      return { label: 'Dapat Digunakan', variant: 'default' as const, icon: QrCode }
    }
    
    return { label: 'Menunggu Event', variant: 'secondary' as const, icon: Clock }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user) {
    return null
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <div className="p-3 bg-primary/10 rounded-lg">
          <Ticket className="h-8 w-8 text-primary" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Tiket Saya
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola dan lihat semua tiket yang Anda miliki
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Tiket
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.total}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">
              Tiket Aktif
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.active}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-gray-600">
              Sudah Digunakan
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-500">{summary.used}</div>
          </CardContent>
        </Card>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">Semua Tiket</TabsTrigger>
          <TabsTrigger value="active">Aktif</TabsTrigger>
          <TabsTrigger value="used">Sudah Digunakan</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-6">
          {ticketsByEvent.length === 0 ? (
            <div className="text-center py-12">
              <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Belum ada tiket
              </h3>
              <p className="text-gray-600 mb-4">
                Anda belum memiliki tiket. Jelajahi event menarik dan beli tiket sekarang!
              </p>
              <Button onClick={() => router.push('/events')}>
                Jelajahi Event
              </Button>
            </div>
          ) : (
            <div className="space-y-8">
              {ticketsByEvent.map((eventGroup) => (
                <Card key={eventGroup.event.id} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-start gap-4">
                      {eventGroup.event.image && (
                        <div className="relative w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                          <Image
                            src={eventGroup.event.image}
                            alt={eventGroup.event.title}
                            fill
                            className="object-cover"
                          />
                        </div>
                      )}
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge 
                            variant="secondary"
                            style={{ 
                              backgroundColor: eventGroup.event.category.color + '20', 
                              color: eventGroup.event.category.color 
                            }}
                          >
                            {eventGroup.event.category.name}
                          </Badge>
                          {eventGroup.event.organizer.isVerified && (
                            <Badge variant="outline">Verified</Badge>
                          )}
                        </div>
                        
                        <CardTitle className="text-xl mb-2">
                          {eventGroup.event.title}
                        </CardTitle>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatRelativeTime(eventGroup.event.startDate)}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-4 w-4" />
                            <span className="truncate">{eventGroup.event.location}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <User className="h-4 w-4" />
                            <span>{eventGroup.event.organizer.name}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent>
                    <div className="space-y-4">
                      <div className="text-sm text-gray-600 mb-4">
                        {eventGroup.tickets.length} tiket untuk event ini
                      </div>
                      
                      <div className="grid gap-4">
                        {eventGroup.tickets.map((ticket) => {
                          const status = getTicketStatus(ticket)
                          const StatusIcon = status.icon
                          
                          return (
                            <div 
                              key={ticket.id}
                              className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                            >
                              <div className="flex items-center gap-4">
                                <div className="p-2 bg-primary/10 rounded-lg">
                                  <StatusIcon className="h-5 w-5 text-primary" />
                                </div>
                                
                                <div>
                                  <div className="font-medium">{ticket.ticketCode}</div>
                                  <div className="text-sm text-gray-600">
                                    {formatCurrency(ticket.totalPaid)} • 
                                    Dibeli {formatRelativeTime(ticket.createdAt)}
                                  </div>
                                  {ticket.isUsed && ticket.usedAt && (
                                    <div className="text-sm text-green-600">
                                      Digunakan {formatRelativeTime(ticket.usedAt)}
                                    </div>
                                  )}
                                </div>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Badge variant={status.variant}>
                                  {status.label}
                                </Badge>
                                
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => router.push(`/my-tickets/${ticket.id}`)}
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  Detail
                                </Button>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
