'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  Palette,
  Plus,
  Eye,
  Clock,
  DollarSign,
  Star,
  RefreshCw,
  Loader2,
  Send,
  Image,
  Video,
  Share2,
  Layout,
  Zap,
  Award,
  CheckCircle,
  XCircle,
  AlertCircle,
  Download
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate } from '@/lib/utils'

interface ArtposureService {
  id: string
  name: string
  description: string
  category: string
  price: number
  duration: number
  samples: string[]
  isActive: boolean
}

interface ArtposureOrder {
  id: string
  service: {
    name: string
    category: string
    price: number
  }
  eventTitle: string
  requirements: string
  status: string
  price: number
  createdAt: string
  updatedAt: string
  deliveryFiles?: string[]
}

interface Event {
  id: string
  title: string
  slug: string
  startDate: string
}

const CATEGORY_ICONS = {
  POSTER: Image,
  VIDEO: Video,
  SOCIAL_MEDIA: Share2,
  BANNER: Layout,
  LOGO: Zap,
  BRANDING: Award
}

const STATUS_CONFIG = {
  PENDING: { label: 'Menunggu', color: 'bg-yellow-100 text-yellow-800', icon: Clock },
  IN_PROGRESS: { label: 'Dikerjakan', color: 'bg-blue-100 text-blue-800', icon: Loader2 },
  REVIEW: { label: 'Review', color: 'bg-purple-100 text-purple-800', icon: Eye },
  REVISION: { label: 'Revisi', color: 'bg-orange-100 text-orange-800', icon: AlertCircle },
  COMPLETED: { label: 'Selesai', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  CANCELLED: { label: 'Dibatalkan', color: 'bg-red-100 text-red-800', icon: XCircle },
}

export default function OrganizerArtposurePage() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  const [services, setServices] = useState<ArtposureService[]>([])
  const [orders, setOrders] = useState<ArtposureOrder[]>([])
  const [events, setEvents] = useState<Event[]>([])
  const [selectedService, setSelectedService] = useState<ArtposureService | null>(null)
  const [isOrderDialogOpen, setIsOrderDialogOpen] = useState(false)

  const [orderForm, setOrderForm] = useState({
    serviceId: '',
    eventId: '',
    eventTitle: '',
    requirements: ''
  })

  useEffect(() => {
    if (session?.user) {
      fetchData()
    }
  }, [session])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [servicesResponse, ordersResponse, eventsResponse] = await Promise.all([
        fetch('/api/organizer/artposure/services'),
        fetch('/api/organizer/artposure/orders'),
        fetch('/api/organizer/events')
      ])

      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json()
        setServices(servicesData.data || [])
      }

      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json()
        setOrders(ordersData.data || [])
      }

      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json()
        setEvents(eventsData.data || [])
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat data Artposure',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleOrderService = (service: ArtposureService) => {
    setSelectedService(service)
    setOrderForm({
      serviceId: service.id,
      eventId: '',
      eventTitle: '',
      requirements: ''
    })
    setIsOrderDialogOpen(true)
  }

  const handleSubmitOrder = async () => {
    try {
      setSubmitting(true)

      if (!orderForm.serviceId || (!orderForm.eventId && !orderForm.eventTitle) || !orderForm.requirements) {
        toast({
          title: 'Error',
          description: 'Semua field wajib diisi',
          variant: 'destructive'
        })
        return
      }

      const response = await fetch('/api/organizer/artposure/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orderForm)
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: 'Order Artposure berhasil dibuat'
        })
        setIsOrderDialogOpen(false)
        resetOrderForm()
        fetchData()
      } else {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create order')
      }
    } catch (error) {
      console.error('Error creating order:', error)
      toast({
        title: 'Error',
        description: 'Gagal membuat order Artposure',
        variant: 'destructive'
      })
    } finally {
      setSubmitting(false)
    }
  }

  const resetOrderForm = () => {
    setSelectedService(null)
    setOrderForm({
      serviceId: '',
      eventId: '',
      eventTitle: '',
      requirements: ''
    })
  }

  const getCategoryIcon = (category: string) => {
    const IconComponent = CATEGORY_ICONS[category as keyof typeof CATEGORY_ICONS] || Palette
    return <IconComponent className="h-4 w-4" />
  }

  const getStatusBadge = (status: string) => {
    const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]
    if (!config) return <Badge>{status}</Badge>

    const IconComponent = config.icon
    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Jasa Artposure
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Pesan jasa desain profesional untuk event Anda
          </p>
        </div>
        
        <Button onClick={fetchData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="services" className="space-y-6">
        <TabsList>
          <TabsTrigger value="services">Layanan Tersedia ({services.length})</TabsTrigger>
          <TabsTrigger value="orders">Order Saya ({orders.length})</TabsTrigger>
        </TabsList>

        {/* Services Tab */}
        <TabsContent value="services">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service) => (
              <Card key={service.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getCategoryIcon(service.category)}
                      <CardTitle className="text-lg">{service.name}</CardTitle>
                    </div>
                    <Badge variant="outline">{service.category}</Badge>
                  </div>
                  <CardDescription className="line-clamp-3">
                    {service.description}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Samples */}
                  {service.samples.length > 0 && (
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Portfolio:</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {service.samples.slice(0, 4).map((sample, index) => (
                          <div key={index} className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                            <img
                              src={sample}
                              alt={`Sample ${index + 1}`}
                              className="w-full h-full object-cover"
                              onError={(e) => {
                                e.currentTarget.src = '/placeholder-image.jpg'
                              }}
                            />
                          </div>
                        ))}
                      </div>
                      {service.samples.length > 4 && (
                        <p className="text-sm text-gray-500">
                          +{service.samples.length - 4} portfolio lainnya
                        </p>
                      )}
                    </div>
                  )}

                  {/* Price & Duration */}
                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-gray-400" />
                      <span className="font-medium">{formatCurrency(service.price)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>{service.duration} hari</span>
                    </div>
                  </div>

                  <Button 
                    onClick={() => handleOrderService(service)}
                    className="w-full"
                    disabled={!service.isActive}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    {service.isActive ? 'Pesan Sekarang' : 'Tidak Tersedia'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {services.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Belum ada layanan Artposure yang tersedia</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Orders Tab */}
        <TabsContent value="orders">
          <Card>
            <CardHeader>
              <CardTitle>Riwayat Order</CardTitle>
              <CardDescription>
                Pantau progress dan status order Artposure Anda
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Layanan</TableHead>
                    <TableHead>Event</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Tanggal Order</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {orders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium flex items-center gap-2">
                            {getCategoryIcon(order.service.category)}
                            {order.service.name}
                          </div>
                          <div className="text-sm text-gray-500">{order.service.category}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{order.eventTitle}</div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{formatCurrency(order.price)}</div>
                      </TableCell>
                      
                      <TableCell>
                        {getStatusBadge(order.status)}
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">{formatDate(order.createdAt)}</div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {order.status === 'COMPLETED' && order.deliveryFiles && order.deliveryFiles.length > 0 && (
                            <Button size="sm" variant="outline">
                              <Download className="h-4 w-4 mr-1" />
                              Download
                            </Button>
                          )}
                          <Button size="sm" variant="ghost">
                            <Eye className="h-4 w-4 mr-1" />
                            Detail
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {orders.length === 0 && (
                <div className="text-center py-8">
                  <Palette className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada order Artposure</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Order Dialog */}
      <Dialog open={isOrderDialogOpen} onOpenChange={setIsOrderDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Pesan Jasa Artposure</DialogTitle>
            <DialogDescription>
              Isi detail order untuk layanan {selectedService?.name}
            </DialogDescription>
          </DialogHeader>
          
          {selectedService && (
            <div className="space-y-6">
              {/* Service Info */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getCategoryIcon(selectedService.category)}
                        <h3 className="font-semibold">{selectedService.name}</h3>
                        <Badge variant="outline">{selectedService.category}</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">{selectedService.description}</p>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">{formatCurrency(selectedService.price)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{selectedService.duration} hari pengerjaan</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Order Form */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label>Pilih Event</Label>
                  <Select
                    value={orderForm.eventId}
                    onValueChange={(value) => {
                      const selectedEvent = events.find(e => e.id === value)
                      setOrderForm(prev => ({
                        ...prev,
                        eventId: value,
                        eventTitle: selectedEvent?.title || ''
                      }))
                    }}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih event yang akan didesain" />
                    </SelectTrigger>
                    <SelectContent>
                      {events.map((event) => (
                        <SelectItem key={event.id} value={event.id}>
                          {event.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>Atau Tulis Nama Event</Label>
                  <Input
                    value={orderForm.eventTitle}
                    onChange={(e) => setOrderForm(prev => ({ ...prev, eventTitle: e.target.value, eventId: '' }))}
                    placeholder="Nama event jika tidak ada di daftar"
                  />
                </div>

                <div className="space-y-2">
                  <Label>Requirements & Brief</Label>
                  <Textarea
                    value={orderForm.requirements}
                    onChange={(e) => setOrderForm(prev => ({ ...prev, requirements: e.target.value }))}
                    rows={6}
                    placeholder="Jelaskan detail requirements, konsep, warna, style, dan hal-hal penting lainnya untuk desain Anda..."
                  />
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsOrderDialogOpen(false)}>
              Batal
            </Button>
            <Button onClick={handleSubmitOrder} disabled={submitting}>
              {submitting ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Kirim Order
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
