'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs'
import {
  TrendingUp,
  Zap,
  Star,
  Target,
  Clock,
  DollarSign,
  Calendar,
  Eye,
  RefreshCw,
  Loader2,
  CreditCard,
  CheckCircle,
  XCircle,
  AlertCircle,
  BarChart3,
  Users,
  Activity
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate } from '@/lib/utils'

interface BoosterPackage {
  id: string
  name: string
  description: string
  duration: number
  price: number
  features: string[]
  priority: number
  isActive: boolean
}

interface Event {
  id: string
  title: string
  slug: string
  startDate: string
  endDate: string
  location: string
  isActive: boolean
  isBoosted: boolean
  soldTickets?: number
  totalTickets?: number
}

interface EventBoost {
  id: string
  event: {
    id: string
    title: string
    slug: string
    startDate: string
  }
  package: {
    name: string
    priority: number
    features: string[]
  }
  startDate: string
  endDate: string
  price: number
  status: string
  createdAt: string
}

const PRIORITY_CONFIG = {
  1: { icon: Target, color: 'text-gray-500', label: 'Basic' },
  2: { icon: Zap, color: 'text-blue-500', label: 'Premium' },
  3: { icon: Star, color: 'text-yellow-500', label: 'Ultimate' },
  4: { icon: Star, color: 'text-purple-500', label: 'Elite' },
  5: { icon: Star, color: 'text-red-500', label: 'Supreme' }
}

const STATUS_CONFIG = {
  ACTIVE: { label: 'Aktif', color: 'bg-green-100 text-green-800', icon: CheckCircle },
  EXPIRED: { label: 'Berakhir', color: 'bg-gray-100 text-gray-800', icon: Clock },
  CANCELLED: { label: 'Dibatalkan', color: 'bg-red-100 text-red-800', icon: XCircle },
}

export default function OrganizerBoostPage() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [purchasing, setPurchasing] = useState(false)
  const [packages, setPackages] = useState<BoosterPackage[]>([])
  const [events, setEvents] = useState<Event[]>([])
  const [boosts, setBoosts] = useState<EventBoost[]>([])
  const [selectedPackage, setSelectedPackage] = useState<BoosterPackage | null>(null)
  const [selectedEventId, setSelectedEventId] = useState('')
  const [isBoostDialogOpen, setIsBoostDialogOpen] = useState(false)

  useEffect(() => {
    if (session?.user) {
      fetchData()
    }
  }, [session])

  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [packagesResponse, eventsResponse, boostsResponse] = await Promise.all([
        fetch('/api/organizer/boost/packages'),
        fetch('/api/organizer/events?active=true'),
        fetch('/api/organizer/boost/boosts')
      ])

      if (packagesResponse.ok) {
        const packagesData = await packagesResponse.json()
        setPackages(packagesData.data || [])
      }

      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json()
        setEvents(eventsData.data || [])
      }

      if (boostsResponse.ok) {
        const boostsData = await boostsResponse.json()
        setBoosts(boostsData.data || [])
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      toast({
        title: 'Error',
        description: 'Gagal memuat data Boost',
        variant: 'destructive'
      })
    } finally {
      setLoading(false)
    }
  }

  const handleBoostEvent = (pkg: BoosterPackage) => {
    setSelectedPackage(pkg)
    setSelectedEventId('')
    setIsBoostDialogOpen(true)
  }

  const handlePurchaseBoost = async () => {
    try {
      setPurchasing(true)

      if (!selectedPackage || !selectedEventId) {
        toast({
          title: 'Error',
          description: 'Pilih event yang akan di-boost',
          variant: 'destructive'
        })
        return
      }

      const response = await fetch('/api/organizer/boost/purchase', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          packageId: selectedPackage.id,
          eventId: selectedEventId
        })
      })

      if (response.ok) {
        toast({
          title: 'Berhasil',
          description: 'Event boost berhasil dibeli'
        })
        setIsBoostDialogOpen(false)
        setSelectedPackage(null)
        setSelectedEventId('')
        fetchData()
      } else {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to purchase boost')
      }
    } catch (error) {
      console.error('Error purchasing boost:', error)
      toast({
        title: 'Error',
        description: 'Gagal membeli boost event',
        variant: 'destructive'
      })
    } finally {
      setPurchasing(false)
    }
  }

  const getPriorityIcon = (priority: number) => {
    const config = PRIORITY_CONFIG[priority as keyof typeof PRIORITY_CONFIG] || PRIORITY_CONFIG[1]
    const IconComponent = config.icon
    return <IconComponent className={`h-4 w-4 ${config.color}`} />
  }

  const getStatusBadge = (status: string) => {
    const config = STATUS_CONFIG[status as keyof typeof STATUS_CONFIG]
    if (!config) return <Badge>{status}</Badge>

    const IconComponent = config.icon
    return (
      <Badge className={config.color}>
        <IconComponent className="h-3 w-3 mr-1" />
        {config.label}
      </Badge>
    )
  }

  const getAvailableEvents = () => {
    const boostedEventIds = boosts
      .filter(boost => boost.status === 'ACTIVE')
      .map(boost => boost.event.id)
    
    return events.filter(event => 
      event.isActive && 
      !event.isBoosted && 
      !boostedEventIds.includes(event.id) &&
      new Date(event.startDate) > new Date()
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Event Booster
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Tingkatkan visibilitas event Anda dengan paket boost
          </p>
        </div>
        
        <Button onClick={fetchData} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      <Tabs defaultValue="packages" className="space-y-6">
        <TabsList>
          <TabsTrigger value="packages">Paket Boost ({packages.length})</TabsTrigger>
          <TabsTrigger value="boosts">Boost Aktif ({boosts.filter(b => b.status === 'ACTIVE').length})</TabsTrigger>
          <TabsTrigger value="history">Riwayat ({boosts.length})</TabsTrigger>
        </TabsList>

        {/* Packages Tab */}
        <TabsContent value="packages">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {packages.map((pkg) => (
              <Card key={pkg.id} className="hover:shadow-lg transition-shadow relative">
                {pkg.priority >= 3 && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white">
                      Recommended
                    </Badge>
                  </div>
                )}

                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {getPriorityIcon(pkg.priority)}
                      <CardTitle className="text-xl">{pkg.name}</CardTitle>
                    </div>
                    <Badge variant="outline">
                      Level {pkg.priority}
                    </Badge>
                  </div>
                  <CardDescription className="line-clamp-3">
                    {pkg.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* Price & Duration */}
                  <div className="text-center py-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
                    <div className="text-3xl font-bold text-primary mb-1">
                      {formatCurrency(pkg.price)}
                    </div>
                    <div className="text-sm text-gray-600 dark:text-gray-400 flex items-center justify-center gap-1">
                      <Clock className="h-4 w-4" />
                      {pkg.duration} hari boost
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm">Fitur yang didapat:</h4>
                    <ul className="space-y-1">
                      {pkg.features.slice(0, 3).map((feature, index) => (
                        <li key={index} className="text-sm flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>{feature}</span>
                        </li>
                      ))}
                      {pkg.features.length > 3 && (
                        <li className="text-sm text-gray-500">
                          +{pkg.features.length - 3} fitur lainnya
                        </li>
                      )}
                    </ul>
                  </div>

                  <Button
                    onClick={() => handleBoostEvent(pkg)}
                    className="w-full"
                    disabled={!pkg.isActive || getAvailableEvents().length === 0}
                  >
                    <TrendingUp className="h-4 w-4 mr-2" />
                    {!pkg.isActive ? 'Tidak Tersedia' :
                     getAvailableEvents().length === 0 ? 'Tidak Ada Event' :
                     'Boost Event'}
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {packages.length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Belum ada paket boost yang tersedia</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Active Boosts Tab */}
        <TabsContent value="boosts">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {boosts.filter(boost => boost.status === 'ACTIVE').map((boost) => (
              <Card key={boost.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{boost.event.title}</CardTitle>
                    {getStatusBadge(boost.status)}
                  </div>
                  <CardDescription>
                    Paket: {boost.package.name}
                  </CardDescription>
                </CardHeader>
                
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-500">Mulai Boost</div>
                      <div className="font-medium">{formatDate(boost.startDate)}</div>
                    </div>
                    <div>
                      <div className="text-gray-500">Berakhir</div>
                      <div className="font-medium">{formatDate(boost.endDate)}</div>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="text-sm font-medium">Fitur Aktif:</div>
                    <div className="flex flex-wrap gap-1">
                      {boost.package.features.slice(0, 3).map((feature, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {feature}
                        </Badge>
                      ))}
                      {boost.package.features.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{boost.package.features.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2 border-t">
                    <div className="text-sm">
                      <span className="text-gray-500">Harga: </span>
                      <span className="font-medium">{formatCurrency(boost.price)}</span>
                    </div>
                    <Button size="sm" variant="outline">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Analytics
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {boosts.filter(boost => boost.status === 'ACTIVE').length === 0 && (
            <Card>
              <CardContent className="text-center py-12">
                <Activity className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500">Tidak ada boost yang sedang aktif</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* History Tab */}
        <TabsContent value="history">
          <Card>
            <CardHeader>
              <CardTitle>Riwayat Boost</CardTitle>
              <CardDescription>
                Semua boost event yang pernah Anda beli
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Event</TableHead>
                    <TableHead>Paket</TableHead>
                    <TableHead>Periode</TableHead>
                    <TableHead>Harga</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {boosts.map((boost) => (
                    <TableRow key={boost.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{boost.event.title}</div>
                          <div className="text-sm text-gray-500">
                            Event: {formatDate(boost.event.startDate)}
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {getPriorityIcon(boost.package.priority)}
                          <span>{boost.package.name}</span>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">
                          <div>{formatDate(boost.startDate)}</div>
                          <div className="text-gray-500">s/d {formatDate(boost.endDate)}</div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="font-medium">{formatCurrency(boost.price)}</div>
                      </TableCell>
                      
                      <TableCell>
                        {getStatusBadge(boost.status)}
                      </TableCell>
                      
                      <TableCell>
                        <Button size="sm" variant="ghost">
                          <Eye className="h-4 w-4 mr-1" />
                          Detail
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {boosts.length === 0 && (
                <div className="text-center py-8">
                  <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500">Belum ada riwayat boost</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Boost Purchase Dialog */}
      <Dialog open={isBoostDialogOpen} onOpenChange={setIsBoostDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Boost Event</DialogTitle>
            <DialogDescription>
              Pilih event yang akan di-boost dengan paket {selectedPackage?.name}
            </DialogDescription>
          </DialogHeader>
          
          {selectedPackage && (
            <div className="space-y-6">
              {/* Package Info */}
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-start gap-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getPriorityIcon(selectedPackage.priority)}
                        <h3 className="font-semibold">{selectedPackage.name}</h3>
                        <Badge variant="outline">Level {selectedPackage.priority}</Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-4">{selectedPackage.description}</p>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4 text-gray-400" />
                          <span className="font-medium">{formatCurrency(selectedPackage.price)}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{selectedPackage.duration} hari boost</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Event Selection */}
              <div className="space-y-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Pilih Event</label>
                  <Select value={selectedEventId} onValueChange={setSelectedEventId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Pilih event yang akan di-boost" />
                    </SelectTrigger>
                    <SelectContent>
                      {getAvailableEvents().map((event) => (
                        <SelectItem key={event.id} value={event.id}>
                          <div>
                            <div className="font-medium">{event.title}</div>
                            <div className="text-sm text-gray-500">
                              {formatDate(event.startDate)} • {event.location}
                            </div>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {getAvailableEvents().length === 0 && (
                    <p className="text-sm text-gray-500">
                      Tidak ada event yang tersedia untuk di-boost
                    </p>
                  )}
                </div>

                {/* Features List */}
                <div className="space-y-2">
                  <label className="text-sm font-medium">Fitur yang akan didapat:</label>
                  <ul className="space-y-1">
                    {selectedPackage.features.map((feature, index) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBoostDialogOpen(false)}>
              Batal
            </Button>
            <Button 
              onClick={handlePurchaseBoost} 
              disabled={purchasing || !selectedEventId}
            >
              {purchasing ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <CreditCard className="h-4 w-4 mr-2" />
              )}
              Beli Boost - {selectedPackage ? formatCurrency(selectedPackage.price) : ''}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
