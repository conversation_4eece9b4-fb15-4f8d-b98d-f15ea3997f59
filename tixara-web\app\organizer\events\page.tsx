'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Loader2, 
  AlertCircle,
  Calendar,
  MapPin,
  Users,
  Eye,
  TrendingUp
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatDate, formatCurrency } from '@/lib/utils'

interface Event {
  id: string
  title: string
  description: string
  location: string
  startDate: string
  endDate: string
  price: number
  maxTickets: number
  image?: string
  isActive: boolean
  createdAt: string
  category: {
    id: string
    name: string
    color?: string
  }
  _count: {
    tickets: number
  }
  soldTickets?: number
  availableTickets?: number
}

export default function OrganizerEventsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [events, setEvents] = useState<Event[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')

  // Redirect jika bukan organizer
  useEffect(() => {
    if (status === 'loading') return
    if (!session || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
      router.push('/auth/login')
      return
    }
  }, [session, status, router])

  // Fetch events
  const fetchEvents = async () => {
    try {
      const params = new URLSearchParams({
        organizerId: session?.user.id || '',
      })

      const response = await fetch(`/api/events?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setEvents(data.data)
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal mengambil data event',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat mengambil data',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (session?.user.id) {
      fetchEvents()
    }
  }, [session])

  // Handle delete
  const handleDelete = async (event: Event) => {
    if (!confirm(`Apakah Anda yakin ingin menghapus event "${event.title}"?`)) {
      return
    }

    try {
      const response = await fetch(`/api/events/${event.id}`, {
        method: 'DELETE',
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: 'Berhasil',
          description: data.message,
          variant: 'success',
        })
        fetchEvents()
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal menghapus event',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat menghapus event',
        variant: 'destructive',
      })
    }
  }

  // Filter events
  const filteredEvents = events.filter(event =>
    event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
    event.category.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  // Calculate stats
  const totalEvents = events.length
  const activeEvents = events.filter(e => e.isActive && new Date(e.endDate) > new Date()).length
  const totalTicketsSold = events.reduce((sum, event) => sum + (event._count.tickets || 0), 0)
  const totalRevenue = events.reduce((sum, event) => sum + (event._count.tickets * event.price), 0)

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
    return null
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Event Saya
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola event yang Anda buat
          </p>
        </div>
        
        <Link href="/organizer/events/create">
          <Button className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Buat Event Baru
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Event</p>
                <p className="text-2xl font-bold">{totalEvents}</p>
              </div>
              <Calendar className="h-8 w-8 text-primary-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Event Aktif</p>
                <p className="text-2xl font-bold">{activeEvents}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Tiket Terjual</p>
                <p className="text-2xl font-bold">{totalTicketsSold}</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Pendapatan</p>
                <p className="text-2xl font-bold">{formatCurrency(totalRevenue)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder="Cari event..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </CardContent>
      </Card>

      {/* Events Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Daftar Event ({filteredEvents.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">
                {searchTerm ? 'Tidak ada event yang ditemukan' : 'Belum ada event'}
              </p>
              {!searchTerm && (
                <Link href="/organizer/events/create">
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Buat Event Pertama
                  </Button>
                </Link>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Event</TableHead>
                  <TableHead>Kategori</TableHead>
                  <TableHead>Tanggal</TableHead>
                  <TableHead>Lokasi</TableHead>
                  <TableHead>Harga</TableHead>
                  <TableHead>Tiket</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Aksi</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEvents.map((event) => (
                  <TableRow key={event.id}>
                    <TableCell>
                      <div className="font-medium">{event.title}</div>
                      <div className="text-sm text-gray-500 mt-1">
                        {event.description.substring(0, 50)}...
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="secondary"
                        style={{ backgroundColor: event.category.color + '20', color: event.category.color }}
                      >
                        {event.category.name}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{formatDate(event.startDate)}</div>
                        <div className="text-gray-500">s/d {formatDate(event.endDate)}</div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{event.location}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <span className="font-medium">{formatCurrency(event.price)}</span>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        <div>{event._count.tickets} / {event.maxTickets}</div>
                        <div className="text-gray-500">
                          {((event._count.tickets / event.maxTickets) * 100).toFixed(1)}% terjual
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={event.isActive ? 'success' : 'secondary'}>
                        {event.isActive ? 'Aktif' : 'Nonaktif'}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Link href={`/events/${event.id}`}>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Link href={`/organizer/events/${event.id}/edit`}>
                          <Button size="sm" variant="outline">
                            <Edit className="h-4 w-4" />
                          </Button>
                        </Link>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDelete(event)}
                          disabled={event._count.tickets > 0}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
