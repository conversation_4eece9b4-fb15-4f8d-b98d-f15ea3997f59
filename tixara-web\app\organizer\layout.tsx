'use client'

import { ReactNode } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { OrganizerSidebar } from '@/components/organizer/organizer-sidebar'
import { OrganizerHeader } from '@/components/organizer/organizer-header'
import { Loader2 } from 'lucide-react'

interface OrganizerLayoutProps {
  children: ReactNode
}

export default function OrganizerLayout({ children }: OrganizerLayoutProps) {
  const { data: session, status } = useSession()
  const router = useRouter()

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user || !['ORGANIZER', 'ADMIN'].includes(session.user.role)) {
    router.push('/dashboard')
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <OrganizerSidebar />
      
      <div className="lg:pl-64">
        <OrganizerHeader />
        
        <main className="py-6">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
