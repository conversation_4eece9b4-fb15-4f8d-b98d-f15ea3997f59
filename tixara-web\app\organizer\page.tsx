'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Calendar,
  TrendingUp,
  Users,
  DollarSign,
  Star,
  Palette,
  Plus,
  Eye,
  BarChart3,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

interface DashboardStats {
  totalEvents: number
  activeEvents: number
  totalTicketsSold: number
  totalRevenue: number
  pendingOrders: number
  activeBoosts: number
}

interface RecentEvent {
  id: string
  title: string
  startDate: string
  ticketsSold: number
  totalTickets: number
  revenue: number
  status: string
}

interface RecentOrder {
  id: string
  service: {
    name: string
    category: string
  }
  eventTitle: string
  status: string
  createdAt: string
}

export default function OrganizerDashboard() {
  const { data: session } = useSession()
  const [loading, setLoading] = useState(true)
  const [stats, setStats] = useState<DashboardStats>({
    totalEvents: 0,
    activeEvents: 0,
    totalTicketsSold: 0,
    totalRevenue: 0,
    pendingOrders: 0,
    activeBoosts: 0
  })
  const [recentEvents, setRecentEvents] = useState<RecentEvent[]>([])
  const [recentOrders, setRecentOrders] = useState<RecentOrder[]>([])

  useEffect(() => {
    if (session?.user) {
      fetchDashboardData()
    }
  }, [session])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      
      // Fetch dashboard stats
      const statsResponse = await fetch('/api/organizer/dashboard/stats')
      if (statsResponse.ok) {
        const statsData = await statsResponse.json()
        setStats(statsData.data || stats)
      }

      // Fetch recent events
      const eventsResponse = await fetch('/api/organizer/dashboard/recent-events')
      if (eventsResponse.ok) {
        const eventsData = await eventsResponse.json()
        setRecentEvents(eventsData.data || [])
      }

      // Fetch recent orders
      const ordersResponse = await fetch('/api/organizer/dashboard/recent-orders')
      if (ordersResponse.ok) {
        const ordersData = await ordersResponse.json()
        setRecentOrders(ordersData.data || [])
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const config = {
      ACTIVE: { label: 'Aktif', color: 'bg-green-100 text-green-800' },
      DRAFT: { label: 'Draft', color: 'bg-gray-100 text-gray-800' },
      ENDED: { label: 'Berakhir', color: 'bg-red-100 text-red-800' },
      PENDING: { label: 'Menunggu', color: 'bg-yellow-100 text-yellow-800' },
      IN_PROGRESS: { label: 'Dikerjakan', color: 'bg-blue-100 text-blue-800' },
      COMPLETED: { label: 'Selesai', color: 'bg-green-100 text-green-800' },
    }
    
    const statusConfig = config[status as keyof typeof config] || { label: status, color: 'bg-gray-100 text-gray-800' }
    return <Badge className={statusConfig.color}>{statusConfig.label}</Badge>
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Selamat datang, {session?.user?.name}!
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Kelola event dan tingkatkan bisnis Anda dengan TiXara
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button asChild>
            <Link href="/organizer/events/create">
              <Plus className="h-4 w-4 mr-2" />
              Buat Event Baru
            </Link>
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Event</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalEvents}</div>
            <p className="text-xs text-muted-foreground">
              {stats.activeEvents} event aktif
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tiket Terjual</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTicketsSold.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              Total penjualan tiket
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalRevenue)}</div>
            <p className="text-xs text-muted-foreground">
              Pendapatan kotor
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Badge</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{session?.user?.badge || 'BRONZE'}</div>
            <p className="text-xs text-muted-foreground">
              Status langganan
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Order Artposure</CardTitle>
            <Palette className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingOrders}</div>
            <p className="text-xs text-muted-foreground">
              Order menunggu
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Boost Aktif</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeBoosts}</div>
            <p className="text-xs text-muted-foreground">
              Event di-boost
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Aksi Cepat</CardTitle>
          <CardDescription>
            Akses fitur-fitur utama dengan cepat
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button variant="outline" className="h-20 flex-col" asChild>
              <Link href="/organizer/events/create">
                <Plus className="h-6 w-6 mb-2" />
                Buat Event
              </Link>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col" asChild>
              <Link href="/organizer/artposure">
                <Palette className="h-6 w-6 mb-2" />
                Jasa Artposure
              </Link>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col" asChild>
              <Link href="/organizer/boost">
                <TrendingUp className="h-6 w-6 mb-2" />
                Boost Event
              </Link>
            </Button>
            
            <Button variant="outline" className="h-20 flex-col" asChild>
              <Link href="/organizer/analytics">
                <BarChart3 className="h-6 w-6 mb-2" />
                Analytics
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Events */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Event Terbaru</CardTitle>
              <CardDescription>
                Event yang baru dibuat atau diupdate
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/organizer/events">
                <Eye className="h-4 w-4 mr-1" />
                Lihat Semua
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentEvents.slice(0, 5).map((event) => (
                <div key={event.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium">{event.title}</div>
                    <div className="text-sm text-gray-500 flex items-center gap-2">
                      <Clock className="h-3 w-3" />
                      {formatDate(event.startDate)}
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-sm font-medium">
                      {event.ticketsSold}/{event.totalTickets} tiket
                    </div>
                    <div className="text-xs text-gray-500">
                      {formatCurrency(event.revenue)}
                    </div>
                  </div>
                  {getStatusBadge(event.status)}
                </div>
              ))}
              
              {recentEvents.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Belum ada event</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Recent Artposure Orders */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Order Artposure Terbaru</CardTitle>
              <CardDescription>
                Order jasa desain yang baru masuk
              </CardDescription>
            </div>
            <Button variant="outline" size="sm" asChild>
              <Link href="/organizer/artposure">
                <Eye className="h-4 w-4 mr-1" />
                Lihat Semua
              </Link>
            </Button>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentOrders.slice(0, 5).map((order) => (
                <div key={order.id} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="font-medium">{order.service.name}</div>
                    <div className="text-sm text-gray-500">
                      {order.eventTitle}
                    </div>
                    <div className="text-xs text-gray-400">
                      {formatDate(order.createdAt)}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{order.service.category}</Badge>
                    {getStatusBadge(order.status)}
                  </div>
                </div>
              ))}
              
              {recentOrders.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Palette className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Belum ada order Artposure</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
