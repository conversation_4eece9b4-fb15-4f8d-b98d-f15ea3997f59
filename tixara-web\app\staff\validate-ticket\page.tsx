'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  QrCode, 
  Scan, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  User,
  Calendar,
  MapPin,
  Ticket,
  Loader2,
  Camera
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatDate, formatCurrency, formatRelativeTime } from '@/lib/utils'

interface ValidationResult {
  success: boolean
  message: string
  status: string
  warning?: string
  data?: {
    ticket: {
      id: string
      ticketCode: string
      price: number
      usedAt?: string
      createdAt: string
    }
    event: {
      id: string
      title: string
      startDate: string
      endDate: string
      location: string
      organizer: string
      category: string
    }
    buyer: {
      id: string
      name: string
      email: string
    }
    validator?: {
      id: string
      name: string
      email: string
    }
  }
}

export default function ValidateTicketPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [qrInput, setQrInput] = useState('')
  const [eventId, setEventId] = useState('')
  const [loading, setLoading] = useState(false)
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)
  const [events, setEvents] = useState<any[]>([])

  // Redirect jika bukan staff atau admin
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user || !['STAFF', 'ADMIN'].includes(session.user.role)) {
      router.push('/dashboard')
      return
    }
  }, [session, status, router])

  // Fetch events yang bisa divalidasi staff
  useEffect(() => {
    const fetchEvents = async () => {
      if (!session?.user) return

      try {
        const response = await fetch('/api/events/staff-events')
        const data = await response.json()
        
        if (data.success) {
          setEvents(data.data)
          if (data.data.length === 1) {
            setEventId(data.data[0].id)
          }
        }
      } catch (error) {
        console.error('Error fetching staff events:', error)
      }
    }

    if (session?.user?.role === 'STAFF') {
      fetchEvents()
    }
  }, [session])

  const handleValidate = async () => {
    if (!qrInput.trim()) {
      toast({
        title: 'Error',
        description: 'Masukkan data QR code',
        variant: 'destructive',
      })
      return
    }

    if (!eventId) {
      toast({
        title: 'Error',
        description: 'Pilih event terlebih dahulu',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)
    setValidationResult(null)

    try {
      const response = await fetch('/api/tickets/validate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          qrData: qrInput.trim(),
          eventId,
        }),
      })
      const data = await response.json()
      
      setValidationResult(data)
      
      if (data.success) {
        toast({
          title: 'Berhasil',
          description: data.message,
        })
        // Clear input after successful validation
        setQrInput('')
      } else {
        toast({
          title: 'Validasi Gagal',
          description: data.message,
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan saat validasi',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'VALIDATED':
        return <CheckCircle className="h-8 w-8 text-green-500" />
      case 'ALREADY_USED':
        return <XCircle className="h-8 w-8 text-red-500" />
      case 'EARLY_VALIDATION':
        return <AlertTriangle className="h-8 w-8 text-yellow-500" />
      case 'INVALID_QR':
      case 'NOT_FOUND':
      case 'WRONG_EVENT':
      case 'EVENT_ENDED':
      case 'NO_PERMISSION':
        return <XCircle className="h-8 w-8 text-red-500" />
      default:
        return <QrCode className="h-8 w-8 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'VALIDATED':
        return 'success'
      case 'EARLY_VALIDATION':
        return 'warning'
      case 'ALREADY_USED':
      case 'INVALID_QR':
      case 'NOT_FOUND':
      case 'WRONG_EVENT':
      case 'EVENT_ENDED':
      case 'NO_PERMISSION':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user || !['STAFF', 'ADMIN'].includes(session.user.role)) {
    return null
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <div className="p-3 bg-primary/10 rounded-lg">
          <Scan className="h-8 w-8 text-primary" />
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Validasi Tiket
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Scan atau masukkan QR code untuk memvalidasi tiket
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Validation Form */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <QrCode className="h-5 w-5" />
                Validasi Tiket
              </CardTitle>
              <CardDescription>
                Masukkan data QR code dan pilih event untuk validasi
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Event Selection */}
              {session.user.role === 'STAFF' && events.length > 1 && (
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Pilih Event
                  </label>
                  <select
                    value={eventId}
                    onChange={(e) => setEventId(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                  >
                    <option value="">Pilih event...</option>
                    {events.map((event) => (
                      <option key={event.id} value={event.id}>
                        {event.title}
                      </option>
                    ))}
                  </select>
                </div>
              )}

              {/* QR Input */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  QR Code Data
                </label>
                <Input
                  value={qrInput}
                  onChange={(e) => setQrInput(e.target.value)}
                  placeholder="Masukkan atau scan QR code..."
                  className="font-mono"
                />
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3">
                <Button
                  onClick={handleValidate}
                  disabled={loading || !qrInput.trim() || !eventId}
                  className="flex-1"
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Scan className="h-4 w-4 mr-2" />
                  )}
                  Validasi
                </Button>
                
                <Button
                  variant="outline"
                  onClick={() => {
                    // In real implementation, this would open camera for QR scanning
                    toast({
                      title: 'Info',
                      description: 'Fitur scan kamera akan segera tersedia',
                    })
                  }}
                >
                  <Camera className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-sm">Aksi Cepat</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setQrInput('')}
                  className="w-full"
                >
                  Clear Input
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setValidationResult(null)}
                  className="w-full"
                >
                  Clear Result
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Validation Result */}
        <div>
          {validationResult ? (
            <Card>
              <CardHeader>
                <div className="flex items-center gap-3">
                  {getStatusIcon(validationResult.status)}
                  <div>
                    <CardTitle>
                      Hasil Validasi
                    </CardTitle>
                    <CardDescription>
                      <Badge variant={getStatusColor(validationResult.status) as any}>
                        {validationResult.status}
                      </Badge>
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="p-3 rounded-lg bg-gray-50 dark:bg-gray-800">
                    <p className="font-medium">{validationResult.message}</p>
                    {validationResult.warning && (
                      <p className="text-yellow-600 text-sm mt-1">
                        ⚠️ {validationResult.warning}
                      </p>
                    )}
                  </div>

                  {validationResult.data && (
                    <div className="space-y-4">
                      {/* Ticket Info */}
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <Ticket className="h-4 w-4" />
                          Informasi Tiket
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Kode:</span>
                            <span className="font-mono">{validationResult.data.ticket.ticketCode}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Harga:</span>
                            <span>{formatCurrency(validationResult.data.ticket.price)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Dibeli:</span>
                            <span>{formatRelativeTime(validationResult.data.ticket.createdAt)}</span>
                          </div>
                          {validationResult.data.ticket.usedAt && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">Digunakan:</span>
                              <span>{formatRelativeTime(validationResult.data.ticket.usedAt)}</span>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Event Info */}
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <Calendar className="h-4 w-4" />
                          Informasi Event
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">{validationResult.data.event.title}</span>
                          </div>
                          <div className="flex items-center gap-2 text-gray-600">
                            <Calendar className="h-3 w-3" />
                            <span>{formatDate(validationResult.data.event.startDate)}</span>
                          </div>
                          <div className="flex items-center gap-2 text-gray-600">
                            <MapPin className="h-3 w-3" />
                            <span>{validationResult.data.event.location}</span>
                          </div>
                          <div className="flex items-center gap-2 text-gray-600">
                            <User className="h-3 w-3" />
                            <span>{validationResult.data.event.organizer}</span>
                          </div>
                        </div>
                      </div>

                      {/* Buyer Info */}
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2">
                          <User className="h-4 w-4" />
                          Informasi Pembeli
                        </h4>
                        <div className="space-y-2 text-sm">
                          <div>
                            <span className="font-medium">{validationResult.data.buyer.name}</span>
                          </div>
                          <div className="text-gray-600">
                            {validationResult.data.buyer.email}
                          </div>
                        </div>
                      </div>

                      {/* Validator Info */}
                      {validationResult.data.validator && (
                        <div>
                          <h4 className="font-medium mb-2">Divalidasi Oleh</h4>
                          <div className="text-sm">
                            <div className="font-medium">{validationResult.data.validator.name}</div>
                            <div className="text-gray-600">{validationResult.data.validator.email}</div>
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ) : (
            <Card>
              <CardContent className="text-center py-12">
                <QrCode className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Siap untuk Validasi
                </h3>
                <p className="text-gray-600">
                  Masukkan QR code tiket untuk memulai validasi
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
