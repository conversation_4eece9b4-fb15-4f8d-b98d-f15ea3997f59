'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { ArrowLeft, Wallet, CreditCard, Smartphone, QrCode, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency } from '@/lib/utils'

const QUICK_AMOUNTS = [50000, 100000, 200000, 500000, 1000000]

const PAYMENT_METHODS = {
  TRIPAY: [
    { id: 'QRIS', name: 'QRI<PERSON>', icon: QrCode, description: 'Scan QR dengan aplikasi e-wallet' },
    { id: 'GOPAY', name: 'GoPay', icon: Smartphone, description: 'Bayar dengan GoPay' },
    { id: 'OVO', name: 'OVO', icon: Smartphone, description: 'Bayar dengan OVO' },
    { id: 'DANA', name: 'DANA', icon: Smartphone, description: 'Bayar dengan DANA' },
    { id: 'SHOPEEPAY', name: 'ShopeePay', icon: Smartphone, description: 'Bayar dengan ShopeePay' },
    { id: 'BCAVA', name: 'BCA Virtual Account', icon: CreditCard, description: 'Transfer ke VA BCA' },
    { id: 'BNIVA', name: 'BNI Virtual Account', icon: CreditCard, description: 'Transfer ke VA BNI' },
    { id: 'BRIVA', name: 'BRI Virtual Account', icon: CreditCard, description: 'Transfer ke VA BRI' },
  ],
  MIDTRANS: [
    { id: 'gopay', name: 'GoPay', icon: Smartphone, description: 'Bayar dengan GoPay' },
    { id: 'shopeepay', name: 'ShopeePay', icon: Smartphone, description: 'Bayar dengan ShopeePay' },
    { id: 'bca_va', name: 'BCA Virtual Account', icon: CreditCard, description: 'Transfer ke VA BCA' },
    { id: 'bni_va', name: 'BNI Virtual Account', icon: CreditCard, description: 'Transfer ke VA BNI' },
    { id: 'bri_va', name: 'BRI Virtual Account', icon: CreditCard, description: 'Transfer ke VA BRI' },
  ],
  XENDIT: [
    { id: 'ID_OVO', name: 'OVO', icon: Smartphone, description: 'Bayar dengan OVO' },
    { id: 'ID_DANA', name: 'DANA', icon: Smartphone, description: 'Bayar dengan DANA' },
    { id: 'ID_LINKAJA', name: 'LinkAja', icon: Smartphone, description: 'Bayar dengan LinkAja' },
    { id: 'BCA', name: 'BCA Virtual Account', icon: CreditCard, description: 'Transfer ke VA BCA' },
    { id: 'BNI', name: 'BNI Virtual Account', icon: CreditCard, description: 'Transfer ke VA BNI' },
    { id: 'BRI', name: 'BRI Virtual Account', icon: CreditCard, description: 'Transfer ke VA BRI' },
  ]
}

export default function DepositPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [amount, setAmount] = useState('')
  const [gateway, setGateway] = useState('TRIPAY')
  const [paymentMethod, setPaymentMethod] = useState('')
  const [loading, setLoading] = useState(false)

  const handleAmountChange = (value: string) => {
    // Remove non-numeric characters
    const numericValue = value.replace(/[^0-9]/g, '')
    setAmount(numericValue)
  }

  const handleQuickAmount = (value: number) => {
    setAmount(value.toString())
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!amount || !paymentMethod) {
      toast({
        title: 'Error',
        description: 'Mohon lengkapi semua field',
        variant: 'destructive',
      })
      return
    }

    const numericAmount = parseInt(amount)
    if (numericAmount < 10000) {
      toast({
        title: 'Error',
        description: 'Minimum deposit Rp 10.000',
        variant: 'destructive',
      })
      return
    }

    if (numericAmount > 10000000) {
      toast({
        title: 'Error',
        description: 'Maksimum deposit Rp 10.000.000',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/uangtix/deposit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: numericAmount,
          gateway,
          paymentMethod,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Deposit berhasil dibuat',
        })

        // Redirect to payment URL
        if (data.data.paymentUrl) {
          window.open(data.data.paymentUrl, '_blank')
        }

        // Redirect to success page
        router.push(`/uangtix/deposit/success?orderId=${data.data.orderId}`)
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Gagal membuat deposit',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan server',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const selectedMethods = PAYMENT_METHODS[gateway as keyof typeof PAYMENT_METHODS] || []

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Wallet className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Top Up UangtiX</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Isi saldo UangtiX Anda
            </p>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Amount Input */}
        <Card>
          <CardHeader>
            <CardTitle>Jumlah Top Up</CardTitle>
            <CardDescription>
              Minimum Rp 10.000, maksimum Rp 10.000.000
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="amount">Jumlah (Rp)</Label>
              <Input
                id="amount"
                type="text"
                placeholder="0"
                value={amount ? parseInt(amount).toLocaleString('id-ID') : ''}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="text-lg"
              />
            </div>

            {/* Quick Amount Buttons */}
            <div>
              <Label>Jumlah Cepat</Label>
              <div className="grid grid-cols-3 gap-2 mt-2">
                {QUICK_AMOUNTS.map((quickAmount) => (
                  <Button
                    key={quickAmount}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAmount(quickAmount)}
                    className={amount === quickAmount.toString() ? 'border-primary' : ''}
                  >
                    {formatCurrency(quickAmount)}
                  </Button>
                ))}
              </div>
            </div>

            {amount && (
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex justify-between text-sm">
                  <span>Jumlah Top Up:</span>
                  <span className="font-medium">{formatCurrency(parseInt(amount))}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Biaya Admin:</span>
                  <span className="font-medium">Rp 0</span>
                </div>
                <hr className="my-2" />
                <div className="flex justify-between font-medium">
                  <span>Total Bayar:</span>
                  <span>{formatCurrency(parseInt(amount))}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Payment Gateway Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Pilih Payment Gateway</CardTitle>
            <CardDescription>
              Pilih penyedia layanan pembayaran
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Select value={gateway} onValueChange={(value) => {
              setGateway(value)
              setPaymentMethod('') // Reset payment method when gateway changes
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Pilih gateway" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="TRIPAY">Tripay</SelectItem>
                <SelectItem value="MIDTRANS">Midtrans</SelectItem>
                <SelectItem value="XENDIT">Xendit</SelectItem>
              </SelectContent>
            </Select>
          </CardContent>
        </Card>

        {/* Payment Method Selection */}
        {gateway && (
          <Card>
            <CardHeader>
              <CardTitle>Metode Pembayaran</CardTitle>
              <CardDescription>
                Pilih metode pembayaran yang Anda inginkan
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RadioGroup value={paymentMethod} onValueChange={setPaymentMethod}>
                <div className="space-y-3">
                  {selectedMethods.map((method) => (
                    <div key={method.id} className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800">
                      <RadioGroupItem value={method.id} id={method.id} />
                      <div className="flex items-center gap-3 flex-1">
                        <method.icon className="h-6 w-6 text-gray-600" />
                        <div>
                          <Label htmlFor={method.id} className="font-medium cursor-pointer">
                            {method.name}
                          </Label>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {method.description}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </RadioGroup>
            </CardContent>
          </Card>
        )}

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          size="lg"
          disabled={!amount || !paymentMethod || loading}
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Memproses...
            </>
          ) : (
            <>
              <Wallet className="h-4 w-4 mr-2" />
              Top Up {amount ? formatCurrency(parseInt(amount)) : ''}
            </>
          )}
        </Button>
      </form>
    </div>
  )
}
