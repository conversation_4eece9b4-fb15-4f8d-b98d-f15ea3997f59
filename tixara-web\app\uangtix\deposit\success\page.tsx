'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, Clock, Copy, ExternalLink, Home, Wallet, RefreshCw } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate } from '@/lib/utils'

interface PaymentData {
  id: string
  orderId: string
  amount: number
  gateway: string
  status: string
  paymentUrl?: string
  qrCode?: string
  virtualAccount?: string
  expiredAt?: string
  createdAt: string
}

export default function DepositSuccessPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()
  
  const [paymentData, setPaymentData] = useState<PaymentData | null>(null)
  const [loading, setLoading] = useState(true)
  const [checking, setChecking] = useState(false)

  const orderId = searchParams.get('orderId')

  useEffect(() => {
    if (orderId) {
      fetchPaymentData()
    }
  }, [orderId])

  const fetchPaymentData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/payments/${orderId}`)
      const data = await response.json()

      if (data.success) {
        setPaymentData(data.data)
      } else {
        toast({
          title: 'Error',
          description: 'Gagal mengambil data pembayaran',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan server',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const checkPaymentStatus = async () => {
    if (!paymentData) return

    setChecking(true)
    try {
      const response = await fetch(`/api/payments/${paymentData.orderId}/status`)
      const data = await response.json()

      if (data.success) {
        setPaymentData(prev => prev ? { ...prev, status: data.data.status } : null)
        
        if (data.data.status === 'PAID') {
          toast({
            title: 'Success',
            description: 'Pembayaran berhasil! Saldo UangtiX Anda telah bertambah.',
          })
        }
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Gagal memeriksa status pembayaran',
        variant: 'destructive',
      })
    } finally {
      setChecking(false)
    }
  }

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: 'Copied',
      description: `${label} berhasil disalin`,
    })
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PAID':
        return 'success'
      case 'PENDING':
        return 'warning'
      case 'EXPIRED':
      case 'FAILED':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PAID':
        return <CheckCircle className="h-5 w-5 text-green-500" />
      case 'PENDING':
        return <Clock className="h-5 w-5 text-yellow-500" />
      default:
        return <Clock className="h-5 w-5 text-gray-500" />
    }
  }

  if (loading) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-2xl">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Memuat data pembayaran...</p>
        </div>
      </div>
    )
  }

  if (!paymentData) {
    return (
      <div className="container mx-auto py-8 px-4 max-w-2xl">
        <Card>
          <CardContent className="pt-6 text-center">
            <h2 className="text-xl font-semibold mb-2">Data tidak ditemukan</h2>
            <p className="text-gray-600 mb-4">Data pembayaran tidak ditemukan atau sudah kedaluwarsa</p>
            <Button onClick={() => router.push('/uangtix')}>
              <Home className="h-4 w-4 mr-2" />
              Kembali ke UangtiX
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      {/* Header */}
      <div className="text-center mb-8">
        <div className="p-4 bg-primary/10 rounded-full w-fit mx-auto mb-4">
          {getStatusIcon(paymentData.status)}
        </div>
        <h1 className="text-2xl font-bold mb-2">
          {paymentData.status === 'PAID' ? 'Pembayaran Berhasil!' : 'Menunggu Pembayaran'}
        </h1>
        <p className="text-gray-600">
          {paymentData.status === 'PAID' 
            ? 'Saldo UangtiX Anda telah bertambah'
            : 'Silakan selesaikan pembayaran Anda'
          }
        </p>
      </div>

      {/* Payment Details */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Detail Pembayaran</CardTitle>
              <CardDescription>
                Order ID: {paymentData.orderId}
              </CardDescription>
            </div>
            <Badge variant={getStatusColor(paymentData.status) as any}>
              {paymentData.status}
            </Badge>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-600">Jumlah</p>
              <p className="font-semibold text-lg">{formatCurrency(paymentData.amount)}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Gateway</p>
              <p className="font-semibold">{paymentData.gateway}</p>
            </div>
            <div>
              <p className="text-sm text-gray-600">Tanggal</p>
              <p className="font-semibold">{formatDate(paymentData.createdAt)}</p>
            </div>
            {paymentData.expiredAt && (
              <div>
                <p className="text-sm text-gray-600">Kedaluwarsa</p>
                <p className="font-semibold">{formatDate(paymentData.expiredAt)}</p>
              </div>
            )}
          </div>

          {/* Virtual Account */}
          {paymentData.virtualAccount && (
            <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-blue-600 dark:text-blue-400">Virtual Account</p>
                  <p className="font-mono font-semibold text-blue-800 dark:text-blue-200">
                    {paymentData.virtualAccount}
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(paymentData.virtualAccount!, 'Virtual Account')}
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}

          {/* QR Code */}
          {paymentData.qrCode && (
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-2">Scan QR Code</p>
              <img 
                src={paymentData.qrCode} 
                alt="QR Code" 
                className="mx-auto border rounded-lg"
                style={{ maxWidth: '200px' }}
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Actions */}
      <div className="space-y-4">
        {paymentData.status === 'PENDING' && (
          <>
            {paymentData.paymentUrl && (
              <Button
                className="w-full"
                size="lg"
                onClick={() => window.open(paymentData.paymentUrl, '_blank')}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Lanjutkan Pembayaran
              </Button>
            )}
            
            <Button
              variant="outline"
              className="w-full"
              onClick={checkPaymentStatus}
              disabled={checking}
            >
              {checking ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Memeriksa...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Periksa Status Pembayaran
                </>
              )}
            </Button>
          </>
        )}

        <Button
          variant={paymentData.status === 'PAID' ? 'default' : 'outline'}
          className="w-full"
          onClick={() => router.push('/uangtix')}
        >
          <Wallet className="h-4 w-4 mr-2" />
          {paymentData.status === 'PAID' ? 'Lihat Saldo UangtiX' : 'Kembali ke UangtiX'}
        </Button>
      </div>

      {/* Instructions */}
      {paymentData.status === 'PENDING' && (
        <Card className="mt-6 border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20">
          <CardContent className="pt-6">
            <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              Instruksi Pembayaran:
            </h3>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• Selesaikan pembayaran sebelum waktu kedaluwarsa</li>
              <li>• Gunakan nominal yang tepat sesuai yang tertera</li>
              <li>• Saldo akan otomatis bertambah setelah pembayaran dikonfirmasi</li>
              <li>• Jika ada kendala, hubungi customer service</li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
