'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  ArrowLeft, 
  Clock, 
  Search,
  Filter,
  ArrowUpRight,
  ArrowDownLeft,
  CheckCircle,
  XCircle,
  Loader2,
  Download,
  Calendar
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate, formatRelativeTime } from '@/lib/utils'

interface UangtiXTransaction {
  id: string
  type: string
  amount: number
  description: string
  status: string
  balanceBefore: number
  balanceAfter: number
  createdAt: string
  payment?: {
    id: string
    gateway: string
    status: string
  }
}

export default function HistoryPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [transactions, setTransactions] = useState<UangtiXTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [typeFilter, setTypeFilter] = useState('ALL')
  const [statusFilter, setStatusFilter] = useState('ALL')
  const [page, setPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [loadingMore, setLoadingMore] = useState(false)

  const fetchTransactions = async (pageNum = 1, reset = false) => {
    try {
      if (pageNum === 1) setLoading(true)
      else setLoadingMore(true)

      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '20',
        search: searchTerm,
        type: typeFilter !== 'ALL' ? typeFilter : '',
        status: statusFilter !== 'ALL' ? statusFilter : '',
      })

      const response = await fetch(`/api/uangtix/transactions?${params}`)
      const data = await response.json()

      if (data.success) {
        if (reset || pageNum === 1) {
          setTransactions(data.data)
        } else {
          setTransactions(prev => [...prev, ...data.data])
        }
        setHasMore(data.data.length === 20)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Gagal mengambil data transaksi',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
      setLoadingMore(false)
    }
  }

  useEffect(() => {
    if (session?.user) {
      setPage(1)
      fetchTransactions(1, true)
    }
  }, [session, searchTerm, typeFilter, statusFilter])

  const loadMore = () => {
    const nextPage = page + 1
    setPage(nextPage)
    fetchTransactions(nextPage)
  }

  const getTransactionIcon = (type: string, amount: number) => {
    if (type === 'DEPOSIT' || amount > 0) {
      return <ArrowDownLeft className="h-5 w-5 text-green-500" />
    } else {
      return <ArrowUpRight className="h-5 w-5 text-red-500" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return 'success'
      case 'PENDING':
        return 'warning'
      case 'FAILED':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'DEPOSIT':
        return 'Top Up'
      case 'WITHDRAW':
        return 'Withdraw'
      case 'TRANSFER':
        return 'Transfer'
      case 'PAYMENT':
        return 'Pembayaran'
      case 'REFUND':
        return 'Refund'
      case 'COMMISSION':
        return 'Komisi'
      default:
        return type
    }
  }

  const exportTransactions = async () => {
    try {
      const params = new URLSearchParams({
        search: searchTerm,
        type: typeFilter !== 'ALL' ? typeFilter : '',
        status: statusFilter !== 'ALL' ? statusFilter : '',
        export: 'true',
      })

      const response = await fetch(`/api/uangtix/transactions?${params}`)
      const blob = await response.blob()
      
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `uangtix-transactions-${new Date().toISOString().split('T')[0]}.csv`
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(url)
      document.body.removeChild(a)

      toast({
        title: 'Success',
        description: 'Data transaksi berhasil diexport',
      })
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Gagal export data transaksi',
        variant: 'destructive',
      })
    }
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Clock className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Riwayat Transaksi</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Semua transaksi UangtiX Anda
            </p>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Filter & Pencarian</CardTitle>
              <CardDescription>
                Filter transaksi berdasarkan kriteria
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={exportTransactions}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Cari transaksi..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Type Filter */}
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Semua Tipe" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Semua Tipe</SelectItem>
                <SelectItem value="DEPOSIT">Top Up</SelectItem>
                <SelectItem value="WITHDRAW">Withdraw</SelectItem>
                <SelectItem value="TRANSFER">Transfer</SelectItem>
                <SelectItem value="PAYMENT">Pembayaran</SelectItem>
                <SelectItem value="REFUND">Refund</SelectItem>
                <SelectItem value="COMMISSION">Komisi</SelectItem>
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Semua Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="ALL">Semua Status</SelectItem>
                <SelectItem value="SUCCESS">Berhasil</SelectItem>
                <SelectItem value="PENDING">Pending</SelectItem>
                <SelectItem value="FAILED">Gagal</SelectItem>
                <SelectItem value="CANCELLED">Dibatalkan</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Transactions List */}
      <Card>
        <CardHeader>
          <CardTitle>Transaksi</CardTitle>
          <CardDescription>
            {transactions.length} transaksi ditemukan
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
            </div>
          ) : transactions.length === 0 ? (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Tidak ada transaksi
              </h3>
              <p className="text-gray-600 mb-4">
                Belum ada transaksi yang sesuai dengan filter
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-gray-100 dark:bg-gray-800 rounded-lg">
                      {getTransactionIcon(transaction.type, transaction.amount)}
                    </div>
                    <div>
                      <div className="flex items-center gap-2 mb-1">
                        <h4 className="font-medium">{transaction.description}</h4>
                        <Badge variant="outline" className="text-xs">
                          {getTypeLabel(transaction.type)}
                        </Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                        <span>{formatDate(transaction.createdAt)}</span>
                        <span>•</span>
                        <span>{formatRelativeTime(transaction.createdAt)}</span>
                        {transaction.payment && (
                          <>
                            <span>•</span>
                            <span>{transaction.payment.gateway}</span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`font-medium ${
                        transaction.type === 'DEPOSIT' || transaction.amount > 0 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {transaction.type === 'DEPOSIT' || transaction.amount > 0 ? '+' : '-'}
                        {formatCurrency(Math.abs(transaction.amount))}
                      </span>
                      {getStatusIcon(transaction.status)}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={getStatusColor(transaction.status) as any} className="text-xs">
                        {transaction.status}
                      </Badge>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      Saldo: {formatCurrency(transaction.balanceAfter)}
                    </div>
                  </div>
                </div>
              ))}

              {/* Load More Button */}
              {hasMore && (
                <div className="text-center pt-4">
                  <Button
                    variant="outline"
                    onClick={loadMore}
                    disabled={loadingMore}
                  >
                    {loadingMore ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Memuat...
                      </>
                    ) : (
                      'Muat Lebih Banyak'
                    )}
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
