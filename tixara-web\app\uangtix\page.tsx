'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Wallet, 
  Plus, 
  Send, 
  Download,
  ArrowUpRight,
  ArrowDownLeft,
  Clock,
  CheckCircle,
  XCircle,
  Loader2,
  Eye,
  EyeOff,
  RefreshCw
} from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency, formatDate, formatRelativeTime } from '@/lib/utils'

interface UangtiXTransaction {
  id: string
  type: string
  amount: number
  description: string
  status: string
  balanceBefore: number
  balanceAfter: number
  createdAt: string
  payment?: {
    id: string
    gateway: string
    status: string
  }
}

export default function UangtiXPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [balance, setBalance] = useState(0)
  const [transactions, setTransactions] = useState<UangtiXTransaction[]>([])
  const [loading, setLoading] = useState(true)
  const [showBalance, setShowBalance] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  // Redirect jika belum login
  useEffect(() => {
    if (status === 'loading') return
    if (!session?.user) {
      router.push('/auth/login')
      return
    }
  }, [session, status, router])

  // Fetch balance dan transactions
  const fetchData = async () => {
    try {
      setLoading(true)
      
      const [balanceRes, transactionsRes] = await Promise.all([
        fetch('/api/uangtix/balance'),
        fetch('/api/uangtix/transactions?limit=10')
      ])

      const balanceData = await balanceRes.json()
      const transactionsData = await transactionsRes.json()

      if (balanceData.success) {
        setBalance(balanceData.data.balance)
      }

      if (transactionsData.success) {
        setTransactions(transactionsData.data)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Gagal mengambil data UangtiX',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const refreshData = async () => {
    setRefreshing(true)
    await fetchData()
    setRefreshing(false)
  }

  useEffect(() => {
    if (session?.user) {
      fetchData()
    }
  }, [session])

  const getTransactionIcon = (type: string, amount: number) => {
    if (type === 'DEPOSIT' || amount > 0) {
      return <ArrowDownLeft className="h-4 w-4 text-green-500" />
    } else {
      return <ArrowUpRight className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'FAILED':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return <Clock className="h-4 w-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'SUCCESS':
        return 'success'
      case 'PENDING':
        return 'warning'
      case 'FAILED':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user) {
    return null
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      {/* Header */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-primary/10 rounded-lg">
            <Wallet className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              UangtiX
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-2">
              Dompet digital TiXara
            </p>
          </div>
        </div>
        
        <Button
          variant="outline"
          size="sm"
          onClick={refreshData}
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Balance Card */}
      <Card className="mb-8 bg-gradient-to-r from-primary to-primary/80 text-white">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/80 text-sm mb-2">Saldo UangtiX</p>
              <div className="flex items-center gap-3">
                {showBalance ? (
                  <h2 className="text-3xl font-bold">
                    {formatCurrency(balance)}
                  </h2>
                ) : (
                  <h2 className="text-3xl font-bold">
                    Rp ••••••••
                  </h2>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowBalance(!showBalance)}
                  className="text-white hover:bg-white/20"
                >
                  {showBalance ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
              </div>
            </div>
            
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => router.push('/uangtix/deposit')}
              >
                <Plus className="h-4 w-4 mr-1" />
                Top Up
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => router.push('/uangtix/transfer')}
              >
                <Send className="h-4 w-4 mr-1" />
                Transfer
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/uangtix/deposit')}>
          <CardContent className="pt-6 text-center">
            <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-lg w-fit mx-auto mb-3">
              <Plus className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="font-medium">Top Up</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Isi saldo</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/uangtix/transfer')}>
          <CardContent className="pt-6 text-center">
            <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-lg w-fit mx-auto mb-3">
              <Send className="h-6 w-6 text-blue-600" />
            </div>
            <h3 className="font-medium">Transfer</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Kirim uang</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/uangtix/withdraw')}>
          <CardContent className="pt-6 text-center">
            <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-lg w-fit mx-auto mb-3">
              <Download className="h-6 w-6 text-orange-600" />
            </div>
            <h3 className="font-medium">Withdraw</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Tarik dana</p>
          </CardContent>
        </Card>

        <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={() => router.push('/uangtix/history')}>
          <CardContent className="pt-6 text-center">
            <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-lg w-fit mx-auto mb-3">
              <Clock className="h-6 w-6 text-purple-600" />
            </div>
            <h3 className="font-medium">Riwayat</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">Lihat semua</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Transactions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Transaksi Terbaru</CardTitle>
              <CardDescription>
                10 transaksi terakhir
              </CardDescription>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push('/uangtix/history')}
            >
              Lihat Semua
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {transactions.length === 0 ? (
            <div className="text-center py-8">
              <Wallet className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Belum ada transaksi
              </h3>
              <p className="text-gray-600 mb-4">
                Mulai gunakan UangtiX untuk bertransaksi
              </p>
              <Button onClick={() => router.push('/uangtix/deposit')}>
                <Plus className="h-4 w-4 mr-2" />
                Top Up Sekarang
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {transactions.map((transaction) => (
                <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    {getTransactionIcon(transaction.type, transaction.amount)}
                    <div>
                      <h4 className="font-medium">{transaction.description}</h4>
                      <p className="text-sm text-gray-600">
                        {formatRelativeTime(transaction.createdAt)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center gap-2">
                      <span className={`font-medium ${
                        transaction.type === 'DEPOSIT' || transaction.amount > 0 
                          ? 'text-green-600' 
                          : 'text-red-600'
                      }`}>
                        {transaction.type === 'DEPOSIT' || transaction.amount > 0 ? '+' : '-'}
                        {formatCurrency(Math.abs(transaction.amount))}
                      </span>
                      {getStatusIcon(transaction.status)}
                    </div>
                    <Badge variant={getStatusColor(transaction.status) as any} className="text-xs">
                      {transaction.status}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
