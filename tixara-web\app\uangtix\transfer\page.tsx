'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { ArrowLeft, Send, User, Wallet, Loader2, Search } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency } from '@/lib/utils'

const QUICK_AMOUNTS = [25000, 50000, 100000, 250000, 500000]

interface UserSearchResult {
  id: string
  name: string
  email: string
  avatar?: string
}

export default function TransferPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [balance, setBalance] = useState(0)
  const [amount, setAmount] = useState('')
  const [recipientEmail, setRecipientEmail] = useState('')
  const [recipientUser, setRecipientUser] = useState<UserSearchResult | null>(null)
  const [description, setDescription] = useState('')
  const [loading, setLoading] = useState(false)
  const [searching, setSearching] = useState(false)
  const [searchResults, setSearchResults] = useState<UserSearchResult[]>([])

  // Fetch balance
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        const response = await fetch('/api/uangtix/balance')
        const data = await response.json()
        if (data.success) {
          setBalance(data.data.balance)
        }
      } catch (error) {
        console.error('Error fetching balance:', error)
      }
    }

    if (session?.user) {
      fetchBalance()
    }
  }, [session])

  const handleAmountChange = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '')
    setAmount(numericValue)
  }

  const handleQuickAmount = (value: number) => {
    setAmount(value.toString())
  }

  const searchUsers = async (email: string) => {
    if (!email || email.length < 3) {
      setSearchResults([])
      return
    }

    setSearching(true)
    try {
      const response = await fetch(`/api/users/search?email=${encodeURIComponent(email)}`)
      const data = await response.json()
      
      if (data.success) {
        setSearchResults(data.data.filter((user: UserSearchResult) => user.id !== session?.user?.id))
      }
    } catch (error) {
      console.error('Error searching users:', error)
    } finally {
      setSearching(false)
    }
  }

  const handleEmailChange = (value: string) => {
    setRecipientEmail(value)
    setRecipientUser(null)
    
    // Debounce search
    const timeoutId = setTimeout(() => {
      searchUsers(value)
    }, 500)

    return () => clearTimeout(timeoutId)
  }

  const selectRecipient = (user: UserSearchResult) => {
    setRecipientUser(user)
    setRecipientEmail(user.email)
    setSearchResults([])
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!amount || !recipientEmail || !description) {
      toast({
        title: 'Error',
        description: 'Mohon lengkapi semua field',
        variant: 'destructive',
      })
      return
    }

    const numericAmount = parseInt(amount)
    if (numericAmount < 1000) {
      toast({
        title: 'Error',
        description: 'Minimum transfer Rp 1.000',
        variant: 'destructive',
      })
      return
    }

    if (numericAmount > balance) {
      toast({
        title: 'Error',
        description: 'Saldo tidak mencukupi',
        variant: 'destructive',
      })
      return
    }

    if (recipientEmail === session?.user?.email) {
      toast({
        title: 'Error',
        description: 'Tidak dapat transfer ke diri sendiri',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/uangtix/transfer', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: numericAmount,
          recipientEmail,
          description,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Transfer berhasil',
        })
        router.push('/uangtix')
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Transfer gagal',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan server',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Send className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Transfer UangtiX</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Kirim uang ke pengguna lain
            </p>
          </div>
        </div>
      </div>

      {/* Balance Info */}
      <Card className="mb-6 bg-gradient-to-r from-primary to-primary/80 text-white">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/80 text-sm mb-1">Saldo Anda</p>
              <h2 className="text-2xl font-bold">
                {formatCurrency(balance)}
              </h2>
            </div>
            <Wallet className="h-8 w-8 text-white/80" />
          </div>
        </CardContent>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Recipient Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Penerima</CardTitle>
            <CardDescription>
              Masukkan email penerima
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="relative">
              <Label htmlFor="recipient">Email Penerima</Label>
              <div className="relative">
                <Input
                  id="recipient"
                  type="email"
                  placeholder="<EMAIL>"
                  value={recipientEmail}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  className="pr-10"
                />
                {searching && (
                  <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin" />
                )}
              </div>
              
              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border rounded-lg shadow-lg max-h-60 overflow-y-auto">
                  {searchResults.map((user) => (
                    <div
                      key={user.id}
                      className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b last:border-b-0"
                      onClick={() => selectRecipient(user)}
                    >
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-primary/10 rounded-full flex items-center justify-center">
                          <User className="h-4 w-4 text-primary" />
                        </div>
                        <div>
                          <p className="font-medium">{user.name}</p>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{user.email}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Selected Recipient */}
            {recipientUser && (
              <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-green-100 dark:bg-green-900/40 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="font-medium text-green-800 dark:text-green-200">{recipientUser.name}</p>
                    <p className="text-sm text-green-600 dark:text-green-400">{recipientUser.email}</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Amount Input */}
        <Card>
          <CardHeader>
            <CardTitle>Jumlah Transfer</CardTitle>
            <CardDescription>
              Minimum Rp 1.000
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="amount">Jumlah (Rp)</Label>
              <Input
                id="amount"
                type="text"
                placeholder="0"
                value={amount ? parseInt(amount).toLocaleString('id-ID') : ''}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="text-lg"
              />
            </div>

            {/* Quick Amount Buttons */}
            <div>
              <Label>Jumlah Cepat</Label>
              <div className="grid grid-cols-3 gap-2 mt-2">
                {QUICK_AMOUNTS.map((quickAmount) => (
                  <Button
                    key={quickAmount}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAmount(quickAmount)}
                    className={amount === quickAmount.toString() ? 'border-primary' : ''}
                    disabled={quickAmount > balance}
                  >
                    {formatCurrency(quickAmount)}
                  </Button>
                ))}
              </div>
            </div>

            {amount && (
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex justify-between text-sm">
                  <span>Jumlah Transfer:</span>
                  <span className="font-medium">{formatCurrency(parseInt(amount))}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Biaya Admin:</span>
                  <span className="font-medium">Rp 0</span>
                </div>
                <hr className="my-2" />
                <div className="flex justify-between font-medium">
                  <span>Total:</span>
                  <span>{formatCurrency(parseInt(amount))}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Description */}
        <Card>
          <CardHeader>
            <CardTitle>Catatan</CardTitle>
            <CardDescription>
              Tambahkan catatan untuk transfer ini
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="Contoh: Bayar makan siang"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              rows={3}
            />
          </CardContent>
        </Card>

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          size="lg"
          disabled={!amount || !recipientEmail || !description || loading || parseInt(amount) > balance}
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Memproses...
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Transfer {amount ? formatCurrency(parseInt(amount)) : ''}
            </>
          )}
        </Button>
      </form>
    </div>
  )
}
