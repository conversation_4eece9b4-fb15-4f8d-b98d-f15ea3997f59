'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, Download, Wallet, Loader2, CreditCard } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { formatCurrency } from '@/lib/utils'

const QUICK_AMOUNTS = [50000, 100000, 250000, 500000, 1000000]

const BANK_LIST = [
  { code: 'BCA', name: 'Bank Central Asia (BCA)' },
  { code: 'BNI', name: 'Bank Negara Indonesia (BNI)' },
  { code: '<PERSON><PERSON>', name: 'Bank Rakyat Indonesia (BRI)' },
  { code: 'MANDIRI', name: 'Bank Mandiri' },
  { code: 'CIMB', name: 'CIMB Niaga' },
  { code: 'DANAMON', name: 'Bank Danamon' },
  { code: 'PERMATA', name: 'Bank Permata' },
  { code: 'MAYBANK', name: 'Maybank Indonesia' },
  { code: 'BSI', name: 'Bank Syariah Indonesia (BSI)' },
  { code: 'MUAMALAT', name: 'Bank Muamalat' },
]

export default function WithdrawPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()

  const [balance, setBalance] = useState(0)
  const [amount, setAmount] = useState('')
  const [bankCode, setBankCode] = useState('')
  const [accountNumber, setAccountNumber] = useState('')
  const [accountName, setAccountName] = useState('')
  const [loading, setLoading] = useState(false)

  // Fetch balance
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        const response = await fetch('/api/uangtix/balance')
        const data = await response.json()
        if (data.success) {
          setBalance(data.data.balance)
        }
      } catch (error) {
        console.error('Error fetching balance:', error)
      }
    }

    if (session?.user) {
      fetchBalance()
    }
  }, [session])

  const handleAmountChange = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '')
    setAmount(numericValue)
  }

  const handleQuickAmount = (value: number) => {
    setAmount(value.toString())
  }

  const handleAccountNumberChange = (value: string) => {
    const numericValue = value.replace(/[^0-9]/g, '')
    setAccountNumber(numericValue)
  }

  const calculateFee = (amount: number) => {
    // Fee structure: Rp 2.500 for amounts up to 100k, Rp 5.000 for higher amounts
    return amount <= 100000 ? 2500 : 5000
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!amount || !bankCode || !accountNumber || !accountName) {
      toast({
        title: 'Error',
        description: 'Mohon lengkapi semua field',
        variant: 'destructive',
      })
      return
    }

    const numericAmount = parseInt(amount)
    const fee = calculateFee(numericAmount)
    const totalDeduction = numericAmount + fee

    if (numericAmount < 50000) {
      toast({
        title: 'Error',
        description: 'Minimum withdraw Rp 50.000',
        variant: 'destructive',
      })
      return
    }

    if (totalDeduction > balance) {
      toast({
        title: 'Error',
        description: `Saldo tidak mencukupi. Dibutuhkan ${formatCurrency(totalDeduction)} (termasuk biaya admin ${formatCurrency(fee)})`,
        variant: 'destructive',
      })
      return
    }

    if (accountNumber.length < 8) {
      toast({
        title: 'Error',
        description: 'Nomor rekening minimal 8 digit',
        variant: 'destructive',
      })
      return
    }

    setLoading(true)

    try {
      const response = await fetch('/api/uangtix/withdraw', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          amount: numericAmount,
          bankCode,
          accountNumber,
          accountName,
        }),
      })

      const data = await response.json()

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Permintaan withdraw berhasil diajukan. Dana akan diproses dalam 1-3 hari kerja.',
        })
        router.push('/uangtix')
      } else {
        toast({
          title: 'Error',
          description: data.message || 'Withdraw gagal',
          variant: 'destructive',
        })
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Terjadi kesalahan server',
        variant: 'destructive',
      })
    } finally {
      setLoading(false)
    }
  }

  const selectedBank = BANK_LIST.find(bank => bank.code === bankCode)
  const fee = amount ? calculateFee(parseInt(amount)) : 0
  const totalDeduction = amount ? parseInt(amount) + fee : 0

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      {/* Header */}
      <div className="flex items-center gap-4 mb-8">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.back()}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Download className="h-6 w-6 text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold">Withdraw UangtiX</h1>
            <p className="text-gray-600 dark:text-gray-400">
              Tarik dana ke rekening bank
            </p>
          </div>
        </div>
      </div>

      {/* Balance Info */}
      <Card className="mb-6 bg-gradient-to-r from-primary to-primary/80 text-white">
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-white/80 text-sm mb-1">Saldo Anda</p>
              <h2 className="text-2xl font-bold">
                {formatCurrency(balance)}
              </h2>
            </div>
            <Wallet className="h-8 w-8 text-white/80" />
          </div>
        </CardContent>
      </Card>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Bank Selection */}
        <Card>
          <CardHeader>
            <CardTitle>Bank Tujuan</CardTitle>
            <CardDescription>
              Pilih bank untuk transfer dana
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="bank">Bank</Label>
              <Select value={bankCode} onValueChange={setBankCode}>
                <SelectTrigger>
                  <SelectValue placeholder="Pilih bank" />
                </SelectTrigger>
                <SelectContent>
                  {BANK_LIST.map((bank) => (
                    <SelectItem key={bank.code} value={bank.code}>
                      {bank.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="accountNumber">Nomor Rekening</Label>
              <Input
                id="accountNumber"
                type="text"
                placeholder="**********"
                value={accountNumber}
                onChange={(e) => handleAccountNumberChange(e.target.value)}
              />
            </div>

            <div>
              <Label htmlFor="accountName">Nama Pemilik Rekening</Label>
              <Input
                id="accountName"
                type="text"
                placeholder="Nama sesuai rekening bank"
                value={accountName}
                onChange={(e) => setAccountName(e.target.value)}
              />
            </div>

            {selectedBank && accountNumber && accountName && (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center gap-3">
                  <CreditCard className="h-5 w-5 text-blue-600" />
                  <div>
                    <p className="font-medium text-blue-800 dark:text-blue-200">{selectedBank.name}</p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">{accountNumber} - {accountName}</p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Amount Input */}
        <Card>
          <CardHeader>
            <CardTitle>Jumlah Withdraw</CardTitle>
            <CardDescription>
              Minimum Rp 50.000
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="amount">Jumlah (Rp)</Label>
              <Input
                id="amount"
                type="text"
                placeholder="0"
                value={amount ? parseInt(amount).toLocaleString('id-ID') : ''}
                onChange={(e) => handleAmountChange(e.target.value)}
                className="text-lg"
              />
            </div>

            {/* Quick Amount Buttons */}
            <div>
              <Label>Jumlah Cepat</Label>
              <div className="grid grid-cols-3 gap-2 mt-2">
                {QUICK_AMOUNTS.map((quickAmount) => (
                  <Button
                    key={quickAmount}
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAmount(quickAmount)}
                    className={amount === quickAmount.toString() ? 'border-primary' : ''}
                    disabled={quickAmount + calculateFee(quickAmount) > balance}
                  >
                    {formatCurrency(quickAmount)}
                  </Button>
                ))}
              </div>
            </div>

            {amount && (
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex justify-between text-sm">
                  <span>Jumlah Withdraw:</span>
                  <span className="font-medium">{formatCurrency(parseInt(amount))}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Biaya Admin:</span>
                  <span className="font-medium">{formatCurrency(fee)}</span>
                </div>
                <hr className="my-2" />
                <div className="flex justify-between font-medium">
                  <span>Total Dipotong:</span>
                  <span className="text-red-600">{formatCurrency(totalDeduction)}</span>
                </div>
                <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-1">
                  <span>Sisa Saldo:</span>
                  <span>{formatCurrency(balance - totalDeduction)}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Important Notes */}
        <Card className="border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-900/20">
          <CardContent className="pt-6">
            <h3 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Penting:</h3>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
              <li>• Dana akan diproses dalam 1-3 hari kerja</li>
              <li>• Pastikan data rekening sudah benar</li>
              <li>• Biaya admin tidak dapat dikembalikan</li>
              <li>• Withdraw hanya dapat dilakukan ke rekening atas nama sendiri</li>
            </ul>
          </CardContent>
        </Card>

        {/* Submit Button */}
        <Button
          type="submit"
          className="w-full"
          size="lg"
          disabled={!amount || !bankCode || !accountNumber || !accountName || loading || totalDeduction > balance}
        >
          {loading ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Memproses...
            </>
          ) : (
            <>
              <Download className="h-4 w-4 mr-2" />
              Withdraw {amount ? formatCurrency(parseInt(amount)) : ''}
            </>
          )}
        </Button>
      </form>
    </div>
  )
}
