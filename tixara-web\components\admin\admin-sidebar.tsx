'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Users,
  Calendar,
  FileText,
  Settings,
  BarChart3,
  Shield,
  Star,
  Palette,
  TrendingUp,
  CreditCard,
  Bell,
  Tag,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  Megaphone
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: LayoutDashboard,
  },
  {
    name: 'Manajemen User',
    icon: Users,
    children: [
      { name: 'Semua User', href: '/admin/users' },
      { name: 'Veri<PERSON>kasi Organizer', href: '/admin/users/verification' },
      { name: 'Role & Permission', href: '/admin/users/roles' },
    ],
  },
  {
    name: 'Event & Tiket',
    icon: Calendar,
    children: [
      { name: 'Semua Event', href: '/admin/events' },
      { name: 'Kategori Event', href: '/admin/categories' },
      { name: 'Template Tiket', href: '/admin/ticket-templates' },
    ],
  },
  {
    name: '<PERSON><PERSON> & Langganan',
    icon: Star,
    children: [
      { name: 'Badge Plans', href: '/admin/badges/plans' },
      { name: 'Subscription', href: '/admin/badges/subscriptions' },
      { name: 'Pricing Management', href: '/admin/badges/pricing' },
    ],
  },
  {
    name: 'Jasa & Promosi',
    icon: Palette,
    children: [
      { name: 'Artposure Services', href: '/admin/artposure' },
      { name: 'Event Booster', href: '/admin/booster' },
      { name: 'Paket Promosi', href: '/admin/promotion-packages' },
    ],
  },
  {
    name: 'Keuangan',
    icon: CreditCard,
    children: [
      { name: 'UangtiX Transactions', href: '/admin/finance/uangtix' },
      { name: 'Payment Gateway', href: '/admin/finance/payments' },
      { name: 'Commission & Tax', href: '/admin/finance/commission' },
      { name: 'Withdraw Requests', href: '/admin/finance/withdrawals' },
    ],
  },
  {
    name: 'Analytics & Reports',
    icon: BarChart3,
    children: [
      { name: 'Sales Analytics', href: '/admin/analytics/sales' },
      { name: 'User Analytics', href: '/admin/analytics/users' },
      { name: 'Event Performance', href: '/admin/analytics/events' },
      { name: 'Revenue Reports', href: '/admin/analytics/revenue' },
    ],
  },
  {
    name: 'Notifikasi',
    icon: Bell,
    children: [
      { name: 'Broadcast Message', href: '/admin/notifications/broadcast' },
      { name: 'Push Notifications', href: '/admin/notifications/push' },
      { name: 'Email Templates', href: '/admin/notifications/email' },
    ],
  },
  {
    name: 'Pengaturan Sistem',
    icon: Settings,
    children: [
      { name: 'Platform Settings', href: '/admin/settings/platform' },
      { name: 'Maintenance Mode', href: '/admin/settings/maintenance' },
      { name: 'SEO & Branding', href: '/admin/settings/branding' },
      { name: 'API Configuration', href: '/admin/settings/api' },
    ],
  },
]

export function AdminSidebar() {
  const pathname = usePathname()
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    if (href === '/admin') {
      return pathname === '/admin'
    }
    return pathname.startsWith(href)
  }

  const isParentActive = (children: any[]) => {
    return children.some(child => isActive(child.href))
  }

  return (
    <>
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="absolute inset-0 bg-gray-600 opacity-75" />
        </div>
      )}

      {/* Mobile sidebar toggle */}
      <div className="lg:hidden">
        <button
          type="button"
          className="fixed top-4 left-4 z-50 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          {sidebarOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 bg-primary-600 dark:bg-primary-700">
            <Link href="/admin" className="flex items-center">
              <Shield className="h-8 w-8 text-white mr-2" />
              <span className="text-xl font-bold text-white">TiXara Admin</span>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map((item) => (
              <div key={item.name}>
                {item.children ? (
                  <div>
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={cn(
                        "w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                        isParentActive(item.children)
                          ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                          : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                      )}
                    >
                      <div className="flex items-center">
                        <item.icon className="h-5 w-5 mr-3" />
                        {item.name}
                      </div>
                      {expandedItems.includes(item.name) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </button>
                    
                    {expandedItems.includes(item.name) && (
                      <div className="mt-2 ml-6 space-y-1">
                        {item.children.map((child) => (
                          <Link
                            key={child.href}
                            href={child.href}
                            className={cn(
                              "block px-3 py-2 text-sm rounded-lg transition-colors",
                              isActive(child.href)
                                ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                                : "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                            )}
                            onClick={() => setSidebarOpen(false)}
                          >
                            {child.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    className={cn(
                      "flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                      isActive(item.href)
                        ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                        : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                    )}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </nav>
        </div>
      </div>
    </>
  )
}
