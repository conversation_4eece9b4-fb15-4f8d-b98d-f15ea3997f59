'use client'

import { ReactNode } from 'react'
import { UserRole } from '@prisma/client'
import { useAuth } from '@/hooks/use-auth'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Lock } from 'lucide-react'
import Link from 'next/link'

interface RoleGuardProps {
  children: ReactNode
  allowedRoles: UserRole[]
  fallback?: ReactNode
  redirectTo?: string
}

export function RoleGuard({ 
  children, 
  allowedRoles, 
  fallback,
  redirectTo = '/auth/login'
}: RoleGuardProps) {
  const { user, isLoading, isAuthenticated, hasRole } = useAuth()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen p-4">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <Lock className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Login Diperlukan</h2>
              <p className="text-muted-foreground mb-4">
                Anda harus login untuk mengakses halaman ini.
              </p>
              <Button asChild>
                <Link href={redirectTo}>Login Sekarang</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      )
    )
  }

  if (!hasRole(allowedRoles)) {
    return (
      fallback || (
        <div className="flex items-center justify-center min-h-screen p-4">
          <Card className="w-full max-w-md">
            <CardContent className="p-6 text-center">
              <AlertTriangle className="h-12 w-12 text-destructive mx-auto mb-4" />
              <h2 className="text-xl font-semibold mb-2">Akses Ditolak</h2>
              <p className="text-muted-foreground mb-4">
                Anda tidak memiliki izin untuk mengakses halaman ini.
              </p>
              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  Role Anda: <span className="font-medium">{user?.role}</span>
                </p>
                <p className="text-sm text-muted-foreground">
                  Role yang diizinkan: <span className="font-medium">{allowedRoles.join(', ')}</span>
                </p>
              </div>
              <Button asChild className="mt-4">
                <Link href="/dashboard">Kembali ke Dashboard</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      )
    )
  }

  return <>{children}</>
}

// Specific role guards for convenience
export function AdminGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function OrganizerGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN, UserRole.ORGANIZER]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function StaffGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.ADMIN, UserRole.STAFF]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}

export function BuyerGuard({ children, fallback }: { children: ReactNode; fallback?: ReactNode }) {
  return (
    <RoleGuard allowedRoles={[UserRole.BUYER, UserRole.ORGANIZER]} fallback={fallback}>
      {children}
    </RoleGuard>
  )
}
