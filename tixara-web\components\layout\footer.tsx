import Link from 'next/link'
import { Ticket, Facebook, Instagram, Twitter, Youtube, Mail, Phone, MapPin } from 'lucide-react'

export function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    platform: [
      { name: 'Tentang TiX<PERSON>', href: '/about' },
      { name: '<PERSON>', href: '/how-it-works' },
      { name: 'Syarat & Ketentuan', href: '/terms' },
      { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/privacy' },
    ],
    organizer: [
      { name: '<PERSON><PERSON>', href: '/organizer/register' },
      { name: 'Panduan Organizer', href: '/organizer/guide' },
      { name: 'Jasa Artposure', href: '/artposure' },
      { name: 'Boost Event', href: '/boost' },
    ],
    buyer: [
      { name: 'Cari Event', href: '/events' },
      { name: '<PERSON><PERSON><PERSON>', href: '/categories' },
      { name: 'UangtiX Wallet', href: '/wallet' },
      { name: 'Badge System', href: '/badges' },
    ],
    support: [
      { name: '<PERSON><PERSON><PERSON>', href: '/help' },
      { name: 'Hubungi Kami', href: '/contact' },
      { name: 'FAQ', href: '/faq' },
      { name: 'Status Sistem', href: '/status' },
    ],
  }

  return (
    <footer className="bg-slate-900 text-white">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <Link href="/" className="flex items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-primary-500 to-secondary-500 flex items-center justify-center">
                <Ticket className="h-5 w-5 text-white" />
              </div>
              <span className="text-xl font-bold">TiXara</span>
            </Link>
            <p className="text-slate-300 mb-6 max-w-sm">
              Platform e-ticketing terdepan Indonesia untuk event, konser, workshop, dan seminar. 
              Jual beli tiket dengan mudah dan aman.
            </p>
            
            {/* Social Media */}
            <div className="flex space-x-4">
              <Link 
                href="https://facebook.com/tixara-id" 
                className="text-slate-400 hover:text-white transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Facebook className="h-5 w-5" />
              </Link>
              <Link 
                href="https://instagram.com/tixara-id" 
                className="text-slate-400 hover:text-white transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Instagram className="h-5 w-5" />
              </Link>
              <Link 
                href="https://twitter.com/tixara-id" 
                className="text-slate-400 hover:text-white transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Twitter className="h-5 w-5" />
              </Link>
              <Link 
                href="https://youtube.com/tixara-id" 
                className="text-slate-400 hover:text-white transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                <Youtube className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Platform Links */}
          <div>
            <h3 className="font-semibold mb-4">Platform</h3>
            <ul className="space-y-2">
              {footerLinks.platform.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href} 
                    className="text-slate-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Organizer Links */}
          <div>
            <h3 className="font-semibold mb-4">Organizer</h3>
            <ul className="space-y-2">
              {footerLinks.organizer.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href} 
                    className="text-slate-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Buyer Links */}
          <div>
            <h3 className="font-semibold mb-4">Pembeli</h3>
            <ul className="space-y-2">
              {footerLinks.buyer.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href} 
                    className="text-slate-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="font-semibold mb-4">Bantuan</h3>
            <ul className="space-y-2">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href} 
                    className="text-slate-300 hover:text-white transition-colors text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Contact Info */}
        <div className="border-t border-slate-700 mt-8 pt-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="flex items-center space-x-3">
              <Mail className="h-5 w-5 text-primary-400" />
              <div>
                <p className="text-sm text-slate-300">Email</p>
                <p className="text-sm font-medium"><EMAIL></p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Phone className="h-5 w-5 text-primary-400" />
              <div>
                <p className="text-sm text-slate-300">Telepon</p>
                <p className="text-sm font-medium">+62 812-3456-7890</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <MapPin className="h-5 w-5 text-primary-400" />
              <div>
                <p className="text-sm text-slate-300">Alamat</p>
                <p className="text-sm font-medium">Jakarta, Indonesia</p>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-slate-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-sm text-slate-400">
            © {currentYear} TiXara. Semua hak cipta dilindungi.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <Link href="/terms" className="text-sm text-slate-400 hover:text-white transition-colors">
              Syarat & Ketentuan
            </Link>
            <Link href="/privacy" className="text-sm text-slate-400 hover:text-white transition-colors">
              Kebijakan Privasi
            </Link>
            <Link href="/cookies" className="text-sm text-slate-400 hover:text-white transition-colors">
              Kebijakan Cookie
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}
