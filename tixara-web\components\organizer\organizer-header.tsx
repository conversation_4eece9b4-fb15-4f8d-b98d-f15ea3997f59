'use client'

import { useState } from 'react'
import { useSession, signOut } from 'next-auth/react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import {
  Bell,
  Settings,
  LogOut,
  User,
  CreditCard,
  Star,
  Calendar,
  TrendingUp,
  Palette,
  Shield
} from 'lucide-react'

export function OrganizerHeader() {
  const { data: session } = useSession()
  const [notificationCount] = useState(3) // This would come from API

  const getUserInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .substring(0, 2)
  }

  const getBadgeColor = (badge: string) => {
    switch (badge) {
      case 'BRONZE':
        return 'bg-amber-100 text-amber-800'
      case 'SILVER':
        return 'bg-gray-100 text-gray-800'
      case 'GOLD':
        return 'bg-yellow-100 text-yellow-800'
      case 'TITANIUM':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Left side - could add breadcrumbs or search */}
          <div className="flex items-center">
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white">
              Organizer Dashboard
            </h1>
          </div>

          {/* Right side - notifications and user menu */}
          <div className="flex items-center space-x-4">
            {/* Quick Actions */}
            <div className="hidden md:flex items-center space-x-2">
              <Button size="sm" variant="outline" asChild>
                <Link href="/organizer/events/create">
                  <Calendar className="h-4 w-4 mr-1" />
                  Buat Event
                </Link>
              </Button>
              
              <Button size="sm" variant="outline" asChild>
                <Link href="/organizer/boost">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  Boost Event
                </Link>
              </Button>
            </div>

            {/* Notifications */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="relative">
                  <Bell className="h-5 w-5" />
                  {notificationCount > 0 && (
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                      {notificationCount}
                    </span>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-80">
                <DropdownMenuLabel>Notifikasi</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <div className="max-h-64 overflow-y-auto">
                  <DropdownMenuItem className="flex flex-col items-start p-3">
                    <div className="flex items-center gap-2 w-full">
                      <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                      <span className="font-medium text-sm">Order Artposure Baru</span>
                      <span className="text-xs text-gray-500 ml-auto">2 jam lalu</span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      Order untuk desain poster event "Music Festival 2024"
                    </p>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem className="flex flex-col items-start p-3">
                    <div className="flex items-center gap-2 w-full">
                      <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                      <span className="font-medium text-sm">Boost Event Aktif</span>
                      <span className="text-xs text-gray-500 ml-auto">1 hari lalu</span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      Event "Tech Conference" berhasil di-boost
                    </p>
                  </DropdownMenuItem>
                  
                  <DropdownMenuItem className="flex flex-col items-start p-3">
                    <div className="flex items-center gap-2 w-full">
                      <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                      <span className="font-medium text-sm">Badge Upgrade</span>
                      <span className="text-xs text-gray-500 ml-auto">3 hari lalu</span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">
                      Selamat! Badge Anda telah upgrade ke Silver
                    </p>
                  </DropdownMenuItem>
                </div>
                
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link href="/organizer/notifications" className="w-full text-center">
                    Lihat Semua Notifikasi
                  </Link>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* User Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={session?.user?.image || ''} alt={session?.user?.name || ''} />
                    <AvatarFallback>
                      {session?.user?.name ? getUserInitials(session.user.name) : 'OR'}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">
                      {session?.user?.name || 'Organizer'}
                    </p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {session?.user?.email}
                    </p>
                    <div className="flex items-center gap-2 mt-2">
                      <Badge className={getBadgeColor(session?.user?.badge || 'BRONZE')}>
                        <Star className="h-3 w-3 mr-1" />
                        {session?.user?.badge || 'BRONZE'}
                      </Badge>
                      <Badge variant="outline">
                        <Shield className="h-3 w-3 mr-1" />
                        Organizer
                      </Badge>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem asChild>
                  <Link href="/organizer/settings/profile" className="flex items-center">
                    <User className="mr-2 h-4 w-4" />
                    <span>Profil</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <Link href="/organizer/wallet" className="flex items-center">
                    <CreditCard className="mr-2 h-4 w-4" />
                    <span>UangtiX Wallet</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <Link href="/organizer/badges" className="flex items-center">
                    <Star className="mr-2 h-4 w-4" />
                    <span>Badge & Langganan</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <Link href="/organizer/artposure" className="flex items-center">
                    <Palette className="mr-2 h-4 w-4" />
                    <span>Jasa Artposure</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuItem asChild>
                  <Link href="/organizer/boost" className="flex items-center">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    <span>Event Booster</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                
                <DropdownMenuItem asChild>
                  <Link href="/organizer/settings" className="flex items-center">
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Pengaturan</span>
                  </Link>
                </DropdownMenuItem>

                <DropdownMenuSeparator />
                
                <DropdownMenuItem
                  className="text-red-600 dark:text-red-400"
                  onClick={() => signOut({ callbackUrl: '/' })}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Keluar</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  )
}
