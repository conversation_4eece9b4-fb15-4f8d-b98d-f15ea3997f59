'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import {
  LayoutDashboard,
  Calendar,
  Ticket,
  BarChart3,
  Settings,
  Star,
  Palette,
  TrendingUp,
  CreditCard,
  Bell,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  Users,
  FileText,
  Megaphone
} from 'lucide-react'

const navigation = [
  {
    name: 'Dashboard',
    href: '/organizer',
    icon: LayoutDashboard,
  },
  {
    name: 'Event Management',
    icon: Calendar,
    children: [
      { name: 'Semua Event', href: '/organizer/events' },
      { name: 'Buat Event Baru', href: '/organizer/events/create' },
      { name: 'Template <PERSON>ike<PERSON>', href: '/organizer/ticket-templates' },
    ],
  },
  {
    name: '<PERSON>ju<PERSON> & Tiket',
    icon: Ticket,
    children: [
      { name: '<PERSON><PERSON><PERSON> Tiket', href: '/organizer/sales' },
      { name: 'Check-in Event', href: '/organizer/checkin' },
      { name: '<PERSON><PERSON><PERSON>', href: '/organizer/reports' },
    ],
  },
  {
    name: 'J<PERSON> & Promosi',
    icon: <PERSON><PERSON>,
    children: [
      { name: 'Jasa Artposure', href: '/organizer/artposure' },
      { name: 'Event Booster', href: '/organizer/boost' },
      { name: 'Paket Promosi', href: '/organizer/promotion' },
    ],
  },
  {
    name: 'Badge & Langganan',
    icon: Star,
    children: [
      { name: 'Badge Saya', href: '/organizer/badges' },
      { name: 'Upgrade Badge', href: '/organizer/badges/upgrade' },
      { name: 'Riwayat Langganan', href: '/organizer/badges/history' },
    ],
  },
  {
    name: 'UangtiX Wallet',
    href: '/organizer/wallet',
    icon: CreditCard,
  },
  {
    name: 'Analytics',
    href: '/organizer/analytics',
    icon: BarChart3,
  },
  {
    name: 'Notifikasi',
    href: '/organizer/notifications',
    icon: Bell,
  },
  {
    name: 'Pengaturan',
    icon: Settings,
    children: [
      { name: 'Profil Organizer', href: '/organizer/settings/profile' },
      { name: 'Verifikasi Akun', href: '/organizer/settings/verification' },
      { name: 'Keamanan', href: '/organizer/settings/security' },
    ],
  },
]

export function OrganizerSidebar() {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>(['Event Management'])
  const pathname = usePathname()

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev => 
      prev.includes(itemName) 
        ? prev.filter(item => item !== itemName)
        : [...prev, itemName]
    )
  }

  const isActive = (href: string) => {
    return pathname === href
  }

  const isParentActive = (children: { href: string }[]) => {
    return children.some(child => pathname.startsWith(child.href))
  }

  return (
    <>
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div className="absolute inset-0 bg-gray-600 opacity-75" />
        </div>
      )}

      {/* Mobile sidebar toggle */}
      <div className="lg:hidden">
        <button
          type="button"
          className="fixed top-4 left-4 z-50 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
          onClick={() => setSidebarOpen(!sidebarOpen)}
        >
          {sidebarOpen ? (
            <X className="h-6 w-6" />
          ) : (
            <Menu className="h-6 w-6" />
          )}
        </button>
      </div>

      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0",
        sidebarOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-center h-16 px-4 bg-primary-600 dark:bg-primary-700">
            <Link href="/organizer" className="flex items-center">
              <Calendar className="h-8 w-8 text-white mr-2" />
              <span className="text-xl font-bold text-white">TiXara Organizer</span>
            </Link>
          </div>

          {/* Navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
            {navigation.map((item) => (
              <div key={item.name}>
                {item.children ? (
                  <div>
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className={cn(
                        "w-full flex items-center justify-between px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                        isParentActive(item.children)
                          ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                          : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                      )}
                    >
                      <div className="flex items-center">
                        <item.icon className="h-5 w-5 mr-3" />
                        {item.name}
                      </div>
                      {expandedItems.includes(item.name) ? (
                        <ChevronDown className="h-4 w-4" />
                      ) : (
                        <ChevronRight className="h-4 w-4" />
                      )}
                    </button>
                    
                    {expandedItems.includes(item.name) && (
                      <div className="mt-2 space-y-1">
                        {item.children.map((child) => (
                          <Link
                            key={child.name}
                            href={child.href}
                            onClick={() => setSidebarOpen(false)}
                            className={cn(
                              "block px-3 py-2 ml-6 text-sm rounded-lg transition-colors",
                              isActive(child.href)
                                ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                                : "text-gray-600 hover:bg-gray-100 dark:text-gray-400 dark:hover:bg-gray-700"
                            )}
                          >
                            {child.name}
                          </Link>
                        ))}
                      </div>
                    )}
                  </div>
                ) : (
                  <Link
                    href={item.href}
                    onClick={() => setSidebarOpen(false)}
                    className={cn(
                      "flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors",
                      isActive(item.href)
                        ? "bg-primary-100 text-primary-700 dark:bg-primary-900 dark:text-primary-300"
                        : "text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700"
                    )}
                  >
                    <item.icon className="h-5 w-5 mr-3" />
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
              TiXara Organizer Panel
              <br />
              v1.0.0
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
