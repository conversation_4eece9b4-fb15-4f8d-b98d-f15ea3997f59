'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Calendar, MapPin, Users, Star, ArrowRight } from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

// Mock data - in real app, this would come from API
const mockEvents = [
  {
    id: '1',
    title: 'Jakarta Music Festival 2024',
    description: 'Festival musik terbesar di Jakarta dengan lineup artis internasional dan lokal terbaik.',
    image: '/images/events/music-festival.jpg',
    category: 'Konser & Musik',
    location: 'Jakarta International Expo',
    startDate: new Date('2024-08-15'),
    price: 250000,
    soldTickets: 1250,
    totalTickets: 2000,
    rating: 4.8,
    isBoosted: true,
    organizer: 'Jakarta Events Co.',
  },
  {
    id: '2',
    title: 'Workshop Digital Marketing 2024',
    description: 'Pelajari strategi digital marketing terkini dari para ahli industri.',
    image: '/images/events/workshop-digital.jpg',
    category: 'Workshop & Seminar',
    location: 'Hotel Mulia Jakarta',
    startDate: new Date('2024-07-20'),
    price: 150000,
    soldTickets: 85,
    totalTickets: 100,
    rating: 4.9,
    isBoosted: false,
    organizer: 'Digital Academy',
  },
  {
    id: '3',
    title: 'Startup Pitch Competition',
    description: 'Kompetisi pitch startup dengan total hadiah 1 miliar rupiah.',
    image: '/images/events/startup-pitch.jpg',
    category: 'Teknologi',
    location: 'Balai Kartini Jakarta',
    startDate: new Date('2024-09-10'),
    price: 0,
    soldTickets: 450,
    totalTickets: 500,
    rating: 4.7,
    isBoosted: true,
    organizer: 'Startup Indonesia',
  },
]

export function FeaturedEvents() {
  const [events, setEvents] = useState(mockEvents)

  const getAvailabilityStatus = (sold: number, total: number) => {
    const percentage = (sold / total) * 100
    if (percentage >= 90) return { status: 'Hampir Habis', color: 'destructive' }
    if (percentage >= 70) return { status: 'Terbatas', color: 'warning' }
    return { status: 'Tersedia', color: 'success' }
  }

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Event Unggulan
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Jangan lewatkan event-event menarik yang sedang trending dan mendapat rating terbaik dari peserta.
          </p>
        </div>

        {/* Events Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {events.map((event) => {
            const availability = getAvailabilityStatus(event.soldTickets, event.totalTickets)
            
            return (
              <Card key={event.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                <div className="relative">
                  {/* Event Image */}
                  <div className="aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 relative overflow-hidden">
                    {event.image ? (
                      <img
                        src={event.image}
                        alt={event.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <Calendar className="h-16 w-16 text-primary-400" />
                      </div>
                    )}
                  </div>

                  {/* Badges */}
                  <div className="absolute top-3 left-3 flex gap-2">
                    {event.isBoosted && (
                      <Badge variant="default" className="bg-accent-500 text-white">
                        ⚡ Boosted
                      </Badge>
                    )}
                    <Badge variant="outline" className="bg-white/90">
                      {event.category}
                    </Badge>
                  </div>

                  {/* Availability Badge */}
                  <div className="absolute top-3 right-3">
                    <Badge variant={availability.color as any} className="bg-white/90">
                      {availability.status}
                    </Badge>
                  </div>
                </div>

                <CardContent className="p-6">
                  {/* Event Title */}
                  <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                    {event.title}
                  </h3>

                  {/* Event Description */}
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {event.description}
                  </p>

                  {/* Event Details */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-2" />
                      {formatDate(event.startDate)}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <MapPin className="h-4 w-4 mr-2" />
                      {event.location}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Users className="h-4 w-4 mr-2" />
                      {event.soldTickets}/{event.totalTickets} peserta
                    </div>
                  </div>

                  {/* Rating & Organizer */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 mr-1" />
                      <span className="text-sm font-medium">{event.rating}</span>
                    </div>
                    <span className="text-sm text-gray-500">{event.organizer}</span>
                  </div>

                  {/* Price & CTA */}
                  <div className="flex items-center justify-between">
                    <div>
                      {event.price > 0 ? (
                        <div>
                          <span className="text-lg font-bold text-primary-600">
                            {formatCurrency(event.price)}
                          </span>
                          <span className="text-sm text-gray-500 ml-1">/tiket</span>
                        </div>
                      ) : (
                        <span className="text-lg font-bold text-secondary-600">GRATIS</span>
                      )}
                    </div>
                    <Button variant="primary" size="sm" asChild>
                      <Link href={`/events/${event.id}`}>
                        Lihat Detail
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Button variant="outline" size="lg" asChild>
            <Link href="/events" className="inline-flex items-center">
              Lihat Semua Event
              <ArrowRight className="ml-2 h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  )
}
