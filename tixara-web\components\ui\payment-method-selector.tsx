'use client'

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Wallet, CreditCard, Building2, Smartphone } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface PaymentMethodSelectorProps {
  amount: number
  uangtixBalance?: number
  onPaymentMethodSelect: (method: string) => void
  loading?: boolean
  className?: string
}

const PAYMENT_METHODS = [
  {
    id: 'UANGTIX',
    name: 'UangtiX Wallet',
    description: 'Bayar menggunakan saldo UangtiX',
    icon: Wallet,
    color: 'bg-blue-500',
    benefits: ['Instant', 'No fees', 'Secure']
  },
  {
    id: 'TRIPAY',
    name: 'Tripay Gateway',
    description: 'Bank Transfer, E-Wallet, Virtual Account',
    icon: Building2,
    color: 'bg-green-500',
    benefits: ['Multiple options', 'Bank transfer', 'E-wallet']
  },
  {
    id: 'MIDTRANS',
    name: 'Midtrans',
    description: 'Credit Card, Bank Transfer, E-Wallet',
    icon: CreditCard,
    color: 'bg-purple-500',
    benefits: ['Credit card', 'Installment', 'International']
  },
  {
    id: 'XENDIT',
    name: 'Xendit',
    description: 'Bank Transfer, E-Wallet, Retail Outlets',
    icon: Smartphone,
    color: 'bg-orange-500',
    benefits: ['Retail outlets', 'QR code', 'Mobile banking']
  }
]

export default function PaymentMethodSelector({
  amount,
  uangtixBalance = 0,
  onPaymentMethodSelect,
  loading = false,
  className = ''
}: PaymentMethodSelectorProps) {
  const [selectedMethod, setSelectedMethod] = useState<string>('')
  const canUseUangtiX = uangtixBalance >= amount

  const handleMethodSelect = (methodId: string) => {
    if (methodId === 'UANGTIX' && !canUseUangtiX) return
    setSelectedMethod(methodId)
  }

  const handleProceed = () => {
    if (selectedMethod) {
      onPaymentMethodSelect(selectedMethod)
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Pilih Metode Pembayaran
        </h3>
        <p className="text-gray-600">
          Total pembayaran: <span className="font-semibold text-blue-600">{formatCurrency(amount)}</span>
        </p>
      </div>

      <div className="grid gap-4">
        {PAYMENT_METHODS.map((method) => {
          const Icon = method.icon
          const isUangtiX = method.id === 'UANGTIX'
          const isDisabled = isUangtiX && !canUseUangtiX
          const isSelected = selectedMethod === method.id

          return (
            <Card
              key={method.id}
              className={`cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'ring-2 ring-blue-500 border-blue-500'
                  : 'hover:border-gray-300'
              } ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => !isDisabled && handleMethodSelect(method.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start space-x-4">
                  <div className={`p-2 rounded-lg ${method.color} text-white`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-medium text-gray-900">{method.name}</h4>
                      {isUangtiX && (
                        <Badge variant={canUseUangtiX ? 'default' : 'destructive'}>
                          {formatCurrency(uangtixBalance)}
                        </Badge>
                      )}
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">{method.description}</p>
                    
                    {isUangtiX && !canUseUangtiX && (
                      <p className="text-sm text-red-600 mb-2">
                        Saldo tidak mencukupi. Silakan top up terlebih dahulu.
                      </p>
                    )}
                    
                    <div className="flex flex-wrap gap-1">
                      {method.benefits.map((benefit) => (
                        <Badge key={benefit} variant="secondary" className="text-xs">
                          {benefit}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex-shrink-0">
                    <div className={`w-4 h-4 rounded-full border-2 ${
                      isSelected
                        ? 'border-blue-500 bg-blue-500'
                        : 'border-gray-300'
                    }`}>
                      {isSelected && (
                        <div className="w-full h-full rounded-full bg-white scale-50"></div>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>

      {selectedMethod && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-2 mb-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-sm font-medium text-blue-900">
              Metode pembayaran dipilih
            </span>
          </div>
          <p className="text-sm text-blue-700">
            {selectedMethod === 'UANGTIX'
              ? 'Pembayaran akan langsung diproses menggunakan saldo UangtiX Anda.'
              : 'Anda akan diarahkan ke halaman pembayaran untuk menyelesaikan transaksi.'
            }
          </p>
        </div>
      )}

      <div className="flex space-x-3">
        <Button
          onClick={handleProceed}
          disabled={!selectedMethod || loading}
          className="flex-1"
          size="lg"
        >
          {loading ? 'Memproses...' : 'Lanjutkan Pembayaran'}
        </Button>
      </div>

      {selectedMethod === 'UANGTIX' && canUseUangtiX && (
        <div className="text-center">
          <p className="text-sm text-gray-600">
            Sisa saldo setelah pembayaran: {' '}
            <span className="font-medium text-green-600">
              {formatCurrency(uangtixBalance - amount)}
            </span>
          </p>
        </div>
      )}
    </div>
  )
}
