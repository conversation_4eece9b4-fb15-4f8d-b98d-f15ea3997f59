'use client'

import { useSession } from 'next-auth/react'
import { UserRole } from '@prisma/client'

export function useAuth() {
  const { data: session, status } = useSession()

  const isLoading = status === 'loading'
  const isAuthenticated = !!session?.user
  const user = session?.user

  const hasRole = (allowedRoles: UserRole[]) => {
    if (!user) return false
    return allowedRoles.includes(user.role)
  }

  const isAdmin = () => user?.role === UserRole.ADMIN
  const isOrganizer = () => user?.role === UserRole.ORGANIZER
  const isBuyer = () => user?.role === UserRole.BUYER
  const isStaff = () => user?.role === UserRole.STAFF

  const canAccessAdminPanel = () => isAdmin()
  const canCreateEvent = () => hasRole([UserRole.ADMIN, UserRole.ORGANIZER])
  const canValidateTicket = () => hasRole([UserRole.ADMIN, UserRole.STAFF])
  const canPurchaseTicket = () => hasRole([UserRole.BUYER, UserRole.ORGANIZER])

  return {
    user,
    isLoading,
    isAuthenticated,
    hasRole,
    isAdmin,
    isOrganizer,
    isBuyer,
    isStaff,
    canAccessAdminPanel,
    canCreateEvent,
    canValidateTicket,
    canPurchaseTicket,
  }
}
