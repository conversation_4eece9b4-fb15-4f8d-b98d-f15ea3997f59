import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@auth/prisma-adapter'
import CredentialsProvider from 'next-auth/providers/credentials'
import bcrypt from 'bcryptjs'
import { prisma } from '@/lib/prisma'
import { UserRole } from '@prisma/client'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Email dan password harus diisi')
        }

        const user = await prisma.user.findUnique({
          where: { email: credentials.email },
        })

        if (!user) {
          throw new Error('Email atau password salah')
        }

        const isPasswordValid = await bcrypt.compare(credentials.password, user.password)

        if (!isPasswordValid) {
          throw new Error('Email atau password salah')
        }

        // Update last login
        await prisma.user.update({
          where: { id: user.id },
          data: { lastLoginAt: new Date() },
        })

        return {
          id: user.id,
          email: user.email,
          name: user.name,
          role: user.role,
          isVerified: user.isVerified,
          badge: user.badge,
          avatar: user.avatar,
        }
      },
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user, trigger, session }) {
      if (user) {
        token.role = user.role
        token.isVerified = user.isVerified
        token.badge = user.badge
        token.avatar = user.avatar
      }

      // Handle session update
      if (trigger === 'update' && session) {
        token = { ...token, ...session }
      }

      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub!
        session.user.role = token.role as UserRole
        session.user.isVerified = token.isVerified as boolean
        session.user.badge = token.badge
        session.user.avatar = token.avatar as string
      }
      return session
    },
  },
  pages: {
    signIn: '/auth/login',
    signUp: '/auth/register',
    error: '/auth/error',
  },
  events: {
    async signIn({ user, isNewUser }) {
      if (isNewUser) {
        // Send welcome notification for new users
        await prisma.notification.create({
          data: {
            userId: user.id!,
            title: 'Selamat Datang di TiXara!',
            message: 'Terima kasih telah bergabung dengan TiXara. Mulai jelajahi event menarik di sekitar Anda.',
            type: 'SYSTEM_ANNOUNCEMENT',
          },
        })
      }
    },
  },
  debug: process.env.NODE_ENV === 'development',
}

// Helper functions for role-based access control
export const hasRole = (userRole: UserRole, allowedRoles: UserRole[]): boolean => {
  return allowedRoles.includes(userRole)
}

export const isAdmin = (userRole: UserRole): boolean => {
  return userRole === UserRole.ADMIN
}

export const isOrganizer = (userRole: UserRole): boolean => {
  return userRole === UserRole.ORGANIZER
}

export const isBuyer = (userRole: UserRole): boolean => {
  return userRole === UserRole.BUYER
}

export const isStaff = (userRole: UserRole): boolean => {
  return userRole === UserRole.STAFF
}

export const canAccessAdminPanel = (userRole: UserRole): boolean => {
  return userRole === UserRole.ADMIN
}

export const canCreateEvent = (userRole: UserRole): boolean => {
  return [UserRole.ADMIN, UserRole.ORGANIZER].includes(userRole)
}

export const canValidateTicket = (userRole: UserRole): boolean => {
  return [UserRole.ADMIN, UserRole.STAFF].includes(userRole)
}

export const canPurchaseTicket = (userRole: UserRole): boolean => {
  return [UserRole.BUYER, UserRole.ORGANIZER].includes(userRole)
}
