import { withAuth } from 'next-auth/middleware'
import { NextResponse } from 'next/server'
import { UserRole } from '@prisma/client'

export default withAuth(
  function middleware(req) {
    const token = req.nextauth.token
    const { pathname } = req.nextUrl

    // Admin routes protection
    if (pathname.startsWith('/admin')) {
      if (!token || token.role !== UserRole.ADMIN) {
        return NextResponse.redirect(new URL('/auth/login?error=unauthorized', req.url))
      }
    }

    // Organizer routes protection
    if (pathname.startsWith('/organizer')) {
      if (!token || ![UserRole.ADMIN, UserRole.ORGANIZER].includes(token.role as UserRole)) {
        return NextResponse.redirect(new URL('/auth/login?error=unauthorized', req.url))
      }
    }

    // Staff routes protection
    if (pathname.startsWith('/staff')) {
      if (!token || ![UserRole.ADMIN, UserRole.STAFF].includes(token.role as UserRole)) {
        return NextResponse.redirect(new URL('/auth/login?error=unauthorized', req.url))
      }
    }

    // Dashboard routes protection (all authenticated users)
    if (pathname.startsWith('/dashboard')) {
      if (!token) {
        return NextResponse.redirect(new URL('/auth/login', req.url))
      }
    }

    // Redirect authenticated users away from auth pages
    if (pathname.startsWith('/auth/') && token) {
      const redirectPath = getRedirectPath(token.role as UserRole)
      return NextResponse.redirect(new URL(redirectPath, req.url))
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Allow access to public routes
        const publicRoutes = [
          '/',
          '/events',
          '/categories',
          '/about',
          '/contact',
          '/auth/login',
          '/auth/register',
          '/auth/error',
          '/api/auth',
        ]

        const isPublicRoute = publicRoutes.some(route => 
          pathname === route || pathname.startsWith(route + '/')
        )

        if (isPublicRoute) {
          return true
        }

        // Require authentication for protected routes
        return !!token
      },
    },
  }
)

function getRedirectPath(role: UserRole): string {
  switch (role) {
    case UserRole.ADMIN:
      return '/admin/dashboard'
    case UserRole.ORGANIZER:
      return '/organizer/dashboard'
    case UserRole.STAFF:
      return '/staff/dashboard'
    case UserRole.BUYER:
    default:
      return '/dashboard'
  }
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api/auth (NextAuth.js routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    '/((?!api/auth|_next/static|_next/image|favicon.ico|images|icons).*)',
  ],
}
