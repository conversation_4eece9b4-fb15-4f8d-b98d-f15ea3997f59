{"version": 3, "file": "index.d.ts", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,MAAM,MAAM,UAAU,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAA;AAExD;;GAEG;AACH,MAAM,MAAM,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;AAE3D,OAAO,EAAE,OAAO,EAAE,cAAc,EAAmB,MAAM,WAAW,CAAA;AAoEpE;;;GAGG;AACH,MAAM,MAAM,UAAU,CAAC,CAAC,SAAS,UAAU,EAAE,CAAC,SAAS,OAAO,IAAI;IAChE,CAAC;IACD,CAAC;CACF,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,GACtB,MAAM,EAAE,GACR,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC,GAC/B,MAAM,EAAE,GACR,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,IAAI,CAAC,GAChC,OAAO,EAAE,GACT,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,GAChC,MAAM,GACN,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,GAChC,MAAM,GACN,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,KAAK,CAAC,GACjC,OAAO,GACP,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,GAClC,MAAM,GAAG,MAAM,EAAE,GACjB,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,SAAS,EAAE,OAAO,CAAC,GACnC,OAAO,GAAG,OAAO,EAAE,GACnB,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,QAAQ,EAAE,OAAO,CAAC,GAClC,MAAM,GAAG,MAAM,EAAE,GACjB,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,UAAU,EAAE,KAAK,CAAC,GAClC,MAAM,GAAG,MAAM,GAAG,OAAO,GACzB,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,UAAU,EAAE,IAAI,CAAC,GACjC,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,GAC/B,MAAM,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM,EAAE,GAAG,MAAM,EAAE,GAAG,OAAO,EAAE,CAAA;AAE/D;;;GAGG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,SAAS,UAAU,EAAE,CAAC,SAAS,OAAO,IAAI;IACtE,OAAO,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAA;IACtC,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,QAAQ,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,KAAK,OAAO,CAAC,CAAA;IACtE,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC1B,IAAI,CAAC,EAAE,CAAC,CAAA;CACT,GAAG,CAAC,CAAC,SAAS,SAAS,GAAG,EAAE,GAAG;IAAE,IAAI,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAAE,CAAC,GAC5D,CAAC,CAAC,SAAS,KAAK,GACZ,EAAE,GACF;IAAE,QAAQ,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC;IAAC,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAAE,CAAC,CAAA;AAE/D;;;GAGG;AACH,MAAM,MAAM,aAAa,CAAC,CAAC,SAAS,UAAU,EAAE,CAAC,SAAS,OAAO,IAAI;IACnE,CAAC,UAAU,EAAE,MAAM,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAC7C,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,oBAAoB,CAC9B,CAAC,SAAS,UAAU,EACpB,CAAC,SAAS,OAAO,EACjB,CAAC,SAAS,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC,IAC3B;KACD,UAAU,IAAI,MAAM,CAAC,GAAG,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC;CAChD,CAAA;AAED;;;;GAIG;AACH,MAAM,MAAM,SAAS,CAAC,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,IAAI,GACrD;IACE,QAAQ,EAAE,IAAI,CAAA;IACd,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,GACD,CAAC,SAAS,KAAK,GACf;IACE,QAAQ,CAAC,EAAE,KAAK,GAAG,SAAS,CAAA;IAC5B,KAAK,CAAC,EAAE,SAAS,CAAA;CAClB,GACD;IACE,QAAQ,CAAC,EAAE,OAAO,GAAG,SAAS,CAAA;IAC9B,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;CAC3B,CAAA;AAEL;;GAEG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,SAAS,UAAU,EAAE,CAAC,SAAS,OAAO,IAAI;IACtE,IAAI,EAAE,CAAC,CAAA;IACP,KAAK,CAAC,EAAE,MAAM,GAAG,SAAS,CAAA;IAC1B,OAAO,CAAC,EAAE,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAA;IACtC,WAAW,CAAC,EAAE,MAAM,CAAA;IACpB,IAAI,CAAC,EAAE,CAAC,SAAS,SAAS,GAAG,SAAS,GAAG,MAAM,GAAG,SAAS,CAAA;IAC3D,QAAQ,CAAC,EAAE,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;CAC7C,GAAG,SAAS,CAAC,CAAC,CAAC,CAAA;AAEhB,eAAO,MAAM,YAAY,MAAO,MAAM,oBAEiB,CAAA;AA4CvD,eAAO,MAAM,cAAc,+CACtB,GAAG,mDAagB,CAAA;AAExB;;;GAGG;AACH,MAAM,MAAM,SAAS,GAAG;IACtB,CAAC,UAAU,EAAE,MAAM,GAAG,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;CAC5D,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,cAAc,CAAC,CAAC,SAAS,SAAS,IAAI;KAC/C,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAC3D,MAAM,GACN,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,GAC7C,MAAM,EAAE,GACR,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,GAC9C,MAAM,GACN,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,GAC7C,MAAM,EAAE,GACR,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,SAAS,EAAE,KAAK,CAAC,GAC/C,OAAO,GACP,CAAC,CAAC,CAAC,CAAC,SAAS,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC,GAC9C,OAAO,EAAE,GACT,KAAK;CACV,CAAA;AAED;;GAEG;AACH,MAAM,MAAM,MAAM,CAAC,CAAC,SAAS,SAAS,IAAI;IACxC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,CAAA;IACzB,WAAW,EAAE,MAAM,EAAE,CAAA;CACtB,CAAA;AA0LD;;GAEG;AACH,MAAM,WAAW,GAAG;IAClB,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,IAAI,CAAC,EAAE,MAAM,CAAA;CACd;AAED;;;;;;;;GAQG;AACH,MAAM,WAAW,OAAQ,SAAQ,GAAG;IAClC,IAAI,EAAE,SAAS,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,CAAC,EAAE,EAAE,CAAA;IACT,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,KAAK,EAAE,MAAM,CAAA;IACb,GAAG,CAAC,EAAE,OAAO,CAAA;CACd;AAID;;;;;GAKG;AACH,MAAM,WAAW,WAAY,SAAQ,GAAG;IACtC,IAAI,EAAE,aAAa,CAAA;IACnB,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,CAAC,EAAE,EAAE,CAAA;IACT,QAAQ,CAAC,EAAE,OAAO,CAAA;IAClB,GAAG,CAAC,EAAE,OAAO,CAAA;CACd;AAKD;;;GAGG;AACH,MAAM,MAAM,OAAO,GAAG,OAAO,GAAG,WAAW,CAAA;AAE3C;;GAEG;AACH,MAAM,MAAM,UAAU,GAClB,OAAO,GACP;IACE,IAAI,EAAE,QAAQ,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,gBAAgB,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;CAC7C,CAAA;AAEL;;GAEG;AACH,MAAM,WAAW,WAAW;IAC1B;;;;OAIG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAA;IAE1B;;;;OAIG;IACH,SAAS,CAAC,EAAE,MAAM,CAAA;IAElB;;;OAGG;IACH,GAAG,CAAC,EAAE;QAAE,CAAC,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,SAAS,CAAA;KAAE,CAAA;IAEzC;;;;OAIG;IACH,KAAK,CAAC,EAAE,MAAM,CAAA;IAEd;;;;;;;;;;;OAWG;IACH,gBAAgB,CAAC,EAAE,OAAO,CAAA;CAC3B;AAED;;;GAGG;AACH,qBAAa,IAAI,CAAC,CAAC,SAAS,SAAS,GAAG,EAAE;;gBAW5B,OAAO,GAAE,WAAgB;IAarC;;;;;OAKG;IACH,eAAe,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,EAAE,MAAM,SAAK;IAmBtD;;;;;;;;;;OAUG;IACH,KAAK,CAAC,IAAI,GAAE,MAAM,EAAiB,GAAG,MAAM,CAAC,CAAC,CAAC;IA4I/C;;;OAGG;IACH,QAAQ,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO,CAAC,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;IAwClD;;OAEG;IACH,OAAO,CACL,IAAI,EAAE,MAAM,EACZ,KAAK,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAC7B,EAAE,GAAW,EAAE,GAAE;QAAE,GAAG,CAAC,EAAE,OAAO,CAAA;KAAO,GACtC,IAAI,CAAC,CAAC,CAAC;IAQV;;OAEG;IACH,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE,GAAG,EAAE,GAAE;QAAE,GAAG,CAAC,EAAE,OAAO,CAAA;KAAO,GAAG,IAAI,CAAC,CAAC,CAAC;IAKnE;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,EAC1C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAIrD;;OAEG;IACH,OAAO,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,EAC7C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAIpD;;OAEG;IACH,GAAG,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,EAAE,KAAK,CAAC,EAC1C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAIrD;;OAEG;IACH,OAAO,CAAC,CAAC,SAAS,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,EAC7C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAIpD;;OAEG;IACH,IAAI,CAAC,CAAC,SAAS,aAAa,CAAC,SAAS,EAAE,KAAK,CAAC,EAC5C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,SAAS,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IAItD;;OAEG;IACH,QAAQ,CAAC,CAAC,SAAS,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,EAC/C,MAAM,EAAE,CAAC,GACR,IAAI,CAAC,CAAC,GAAG,oBAAoB,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAIrD;;;;OAIG;IACH,SAAS,CAAC,CAAC,SAAS,SAAS,EAAE,MAAM,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;IA4EtD;;OAEG;IACH,KAAK,IAAI,MAAM;IAgGf;;OAEG;IACH,aAAa,IAAI,MAAM;IA4HvB;;OAEG;IACH,MAAM;;;;;;;;;;;IAmBN;;OAEG;IACH,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc;CAGpD;AAuCD;;GAEG;AACH,eAAO,MAAM,IAAI,aAAa,WAAW,aAA2B,CAAA"}