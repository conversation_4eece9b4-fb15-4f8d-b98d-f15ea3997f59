{"version": 3, "file": "parse-args-cjs.cjs", "sourceRoot": "", "sources": ["../../src/parse-args-cjs.cts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,2CAA4B;AAE5B,MAAM,EAAE,GACN,OAAO,OAAO,KAAK,QAAQ;IAC3B,CAAC,CAAC,OAAO;IACT,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ;IACjC,CAAC,CAAC,OAAO,CAAC,OAAO;IACjB,CAAC,CAAC,QAAQ,CAAA;AACd,MAAM,GAAG,GAAG,EAAE;KACX,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC;KACjB,KAAK,CAAC,GAAG,CAAC;KACV,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAA;AAE5B,qBAAqB;AACrB,MAAM,CAAC,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC,GAAG,GAAG,CAAA;AAClC,oBAAoB;AAEpB,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,GAAG,IAAI,CAAA;AAC5B,qBAAqB;AACrB,IACE,CAAC,EAAE;IACH,KAAK,GAAG,EAAE;IACV,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC;IAC5B,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,GAAG,EAAE,CAAC,EAC5B;IACA,oBAAoB;IACpB,EAAE,GAAG,OAAO,CAAC,kBAAkB,CAAC,CAAC,SAAS,CAAA;CAC3C;AAEY,QAAA,SAAS,GAAG,EAAE,CAAA", "sourcesContent": ["import * as util from 'util'\n\nconst pv =\n  typeof process === 'object' &&\n  !!process &&\n  typeof process.version === 'string'\n    ? process.version\n    : 'v0.0.0'\nconst pvs = pv\n  .replace(/^v/, '')\n  .split('.')\n  .map(s => parseInt(s, 10))\n\n/* c8 ignore start */\nconst [major = 0, minor = 0] = pvs\n/* c8 ignore stop */\n\nlet { parseArgs: pa } = util\n/* c8 ignore start */\nif (\n  !pa ||\n  major < 16 ||\n  (major === 18 && minor < 11) ||\n  (major === 16 && minor < 19)\n) {\n  /* c8 ignore stop */\n  pa = require('@pkgjs/parseargs').parseArgs\n}\n\nexport const parseArgs = pa\n"]}