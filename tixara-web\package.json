{"name": "tixara-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:push": "prisma db push", "db:generate": "prisma generate", "db:studio": "prisma studio"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "dependencies": {"@auth/prisma-adapter": "^1.4.0", "@hookform/resolvers": "^3.3.4", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.10.2", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@types/puppeteer": "^7.0.4", "@types/qrcode": "^1.5.5", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "crypto-js": "^4.2.0", "date-fns": "^3.3.1", "lucide-react": "^0.344.0", "nanoid": "^5.1.5", "next": "14.1.0", "next-auth": "^4.24.6", "next-themes": "^0.2.1", "puppeteer": "^24.11.2", "qrcode": "^1.5.4", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.50.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/node": "^24.0.10", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.1.0", "postcss": "^8", "prisma": "^5.10.2", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}