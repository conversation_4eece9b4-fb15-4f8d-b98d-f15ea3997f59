{"name": "tixara-web", "version": "1.0.0", "description": "TiXara E-Ticketing Platform - Web Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.2.0", "react": "^18.3.0", "react-dom": "^18.3.0", "typescript": "^5.4.0", "@types/node": "^20.12.0", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "tailwind-merge": "^2.3.0", "lucide-react": "^0.376.0", "@prisma/client": "^5.13.0", "prisma": "^5.13.0", "mysql2": "^3.9.0", "next-auth": "^4.24.0", "@auth/prisma-adapter": "^2.0.0", "bcryptjs": "^2.4.3", "@types/bcryptjs": "^2.4.6", "jsonwebtoken": "^9.0.2", "@types/jsonwebtoken": "^9.0.6", "zod": "^3.23.0", "react-hook-form": "^7.51.0", "@hookform/resolvers": "^3.3.0", "axios": "^1.6.8", "swr": "^2.2.5", "date-fns": "^3.6.0", "react-datepicker": "^6.9.0", "@types/react-datepicker": "^6.2.0", "qrcode": "^1.5.3", "@types/qrcode": "^1.5.5", "puppeteer": "^22.6.0", "pdfkit": "^0.15.0", "@types/pdfkit": "^0.13.4", "nodemailer": "^6.9.13", "@types/nodemailer": "^6.4.14", "resend": "^3.2.0", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "react-dropzone": "^14.2.3", "sharp": "^0.33.3", "stripe": "^15.5.0", "midtrans-client": "^1.3.1", "react-hot-toast": "^2.4.1", "sonner": "^1.4.41", "zustand": "^4.5.2", "immer": "^10.1.1"}, "devDependencies": {"eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eslint-plugin-react": "^7.34.0", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.14", "jest": "^29.7.0", "@testing-library/react": "^15.0.2", "@testing-library/jest-dom": "^6.4.2", "jest-environment-jsdom": "^29.7.0", "tsx": "^4.7.2", "@faker-js/faker": "^8.4.1"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}