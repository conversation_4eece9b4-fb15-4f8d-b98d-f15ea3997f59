// TiXara Database Schema
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id              String    @id @default(cuid())
  email           String    @unique
  name            String
  phone           String?
  avatar          String?
  password        String
  role            UserRole  @default(BUYER)
  isVerified      Boolean   @default(false)
  badge           BadgeType?
  uangtixBalance  Float     @default(0)
  emailVerifiedAt DateTime?
  lastLoginAt     DateTime?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  organizedEvents     Event[]
  purchasedTickets    Ticket[]
  validatedTickets    Ticket[]             @relation("TicketValidator")
  createdTemplates    TicketTemplate[]     @relation("TemplateCreator")
  payments            Payment[]
  uangtixTransactions UangtiXTransaction[]
  artposureOrders     ArtposureOrder[]
  eventBoosts         EventBoost[]
  reviews             Review[]             @relation("ReviewBuyer")
  receivedReviews     Review[]             @relation("ReviewOrganizer")
  notifications       Notification[]
  staffAssignments    EventStaff[]
  accounts            Account[]
  sessions            Session[]
  badgeSubscription   BadgeSubscription?

  @@map("users")
}

enum UserRole {
  ADMIN
  ORGANIZER
  BUYER
  STAFF
}

enum BadgeType {
  BRONZE
  SILVER
  GOLD
  TITANIUM
}

// Event Management
model Event {
  id           String        @id @default(cuid())
  title        String
  description  String        @db.Text
  image        String
  slug         String        @unique
  category     EventCategory @relation(fields: [categoryId], references: [id])
  categoryId   String
  organizer    User          @relation(fields: [organizerId], references: [id])
  organizerId  String
  location     String
  startDate    DateTime
  endDate      DateTime
  price        Float
  totalTickets Int
  soldTickets  Int           @default(0)
  isActive     Boolean       @default(true)
  isBoosted    Boolean       @default(false)
  boostEndDate DateTime?
  template     TicketTemplate? @relation(fields: [templateId], references: [id])
  templateId   String?
  adminFee     Float         @default(0)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  tickets     Ticket[]
  boosts      EventBoost[]
  reviews     Review[]
  staff       EventStaff[]
  artposureOrders ArtposureOrder[]

  @@map("events")
}

model EventCategory {
  id          String  @id @default(cuid())
  name        String  @unique
  slug        String  @unique
  description String?
  icon        String?
  color       String?
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  events Event[]

  @@map("event_categories")
}

model EventStaff {
  id        String   @id @default(cuid())
  event     Event    @relation(fields: [eventId], references: [id], onDelete: Cascade)
  eventId   String
  staff     User     @relation(fields: [staffId], references: [id])
  staffId   String
  createdAt DateTime @default(now())

  @@unique([eventId, staffId])
  @@map("event_staff")
}

// Ticket Management
model Ticket {
  id            String        @id @default(cuid())
  event         Event         @relation(fields: [eventId], references: [id])
  eventId       String
  buyer         User          @relation(fields: [buyerId], references: [id])
  buyerId       String
  template      TicketTemplate? @relation(fields: [templateId], references: [id])
  templateId    String?
  payment       Payment?      @relation(fields: [paymentId], references: [id])
  paymentId     String?
  qrCode        String        @unique
  ticketCode    String        @unique // Human readable code like TIX-ABC123
  isUsed        Boolean       @default(false)
  usedAt        DateTime?
  validatedBy   String?
  validator     User?         @relation("TicketValidator", fields: [validatedBy], references: [id])
  price         Float
  adminFee      Float
  totalPaid     Float
  paymentMethod PaymentMethod
  pdfUrl        String?       // Generated PDF URL
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  @@map("tickets")
}

enum PaymentMethod {
  UANGTIX
  TRIPAY
  MIDTRANS
  XENDIT
  MANUAL
}

model TicketTemplate {
  id           String     @id @default(cuid())
  name         String
  description  String?
  templateCode String     @db.Text // .temptix format
  preview      String?
  category     String     @default("general") // general, music, sports, etc
  isPremium    Boolean    @default(false)
  requiredBadge BadgeType?
  price        Float      @default(0)
  isActive     Boolean    @default(true)
  createdBy    String?
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  // Relations
  creator User?    @relation("TemplateCreator", fields: [createdBy], references: [id])
  events  Event[]
  tickets Ticket[]

  @@map("ticket_templates")
}

// Payment Gateway System
model Payment {
  id              String        @id @default(cuid())
  user            User          @relation(fields: [userId], references: [id])
  userId          String
  gateway         PaymentGateway
  externalId      String        // ID dari payment gateway
  amount          Float
  adminFee        Float         @default(0)
  totalAmount     Float
  currency        String        @default("IDR")
  description     String
  status          PaymentStatus @default(PENDING)
  paymentUrl      String?       // URL untuk pembayaran
  expiredAt       DateTime?
  paidAt          DateTime?
  metadata        Json?         // data tambahan dari gateway
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  tickets         Ticket[]
  uangtiXTransaction UangtiXTransaction?
  badgeSubscriptions BadgeSubscription[]

  @@unique([gateway, externalId])
  @@map("payments")
}

enum PaymentGateway {
  TRIPAY
  MIDTRANS
  XENDIT
  MANUAL
  UANGTIX
}

enum PaymentStatus {
  PENDING
  PAID
  EXPIRED
  FAILED
  CANCELLED
  REFUNDED
}

// UangtiX Wallet System
model UangtiXTransaction {
  id          String            @id @default(cuid())
  user        User              @relation(fields: [userId], references: [id])
  userId      String
  type        TransactionType
  amount      Float
  description String
  reference   String?           // reference ke payment, ticket, dll
  status      TransactionStatus @default(PENDING)
  balanceBefore Float           @default(0)
  balanceAfter  Float           @default(0)
  payment     Payment?          @relation(fields: [paymentId], references: [id])
  paymentId   String?           @unique
  createdAt   DateTime          @default(now())

  @@map("uangtix_transactions")
}

enum TransactionType {
  DEPOSIT
  WITHDRAW
  PAYMENT
  REFUND
  TRANSFER
  COMMISSION
}

enum TransactionStatus {
  PENDING
  SUCCESS
  FAILED
  CANCELLED
}

// Artposure Service System
model ArtposureService {
  id          String             @id @default(cuid())
  name        String
  description String             @db.Text
  price       Float
  duration    Int                // in days
  category    ArtposureCategory
  samples     Json               // array of image URLs
  isActive    Boolean            @default(true)
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt

  // Relations
  orders ArtposureOrder[]

  @@map("artposure_services")
}

enum ArtposureCategory {
  POSTER
  VIDEO
  SOCIAL_MEDIA
  BANNER
  LOGO
  BRANDING
}

model ArtposureOrder {
  id           String           @id @default(cuid())
  service      ArtposureService @relation(fields: [serviceId], references: [id])
  serviceId    String
  organizer    User             @relation(fields: [organizerId], references: [id])
  organizerId  String
  event        Event?           @relation(fields: [eventId], references: [id])
  eventId      String?
  requirements String           @db.Text
  status       ArtposureStatus  @default(PENDING)
  price        Float
  deliveryDate DateTime
  result       String?          // file URL
  feedback     String?
  rating       Int?             // 1-5
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  @@map("artposure_orders")
}

enum ArtposureStatus {
  PENDING
  IN_PROGRESS
  REVIEW
  REVISION
  COMPLETED
  CANCELLED
}

// Event Booster System
model BoosterPackage {
  id          String  @id @default(cuid())
  name        String
  description String  @db.Text
  duration    Int     // in days
  price       Float
  features    Json    // array of features
  priority    Int     // higher = more priority
  isActive    Boolean @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  boosts EventBoost[]

  @@map("booster_packages")
}

model EventBoost {
  id          String         @id @default(cuid())
  event       Event          @relation(fields: [eventId], references: [id])
  eventId     String
  package     BoosterPackage @relation(fields: [packageId], references: [id])
  packageId   String
  organizer   User           @relation(fields: [organizerId], references: [id])
  organizerId String
  startDate   DateTime
  endDate     DateTime
  price       Float
  status      BoostStatus    @default(ACTIVE)
  createdAt   DateTime       @default(now())

  @@map("event_boosts")
}

enum BoostStatus {
  ACTIVE
  EXPIRED
  CANCELLED
}

// Review System
model Review {
  id          String   @id @default(cuid())
  event       Event    @relation(fields: [eventId], references: [id])
  eventId     String
  organizer   User     @relation("ReviewOrganizer", fields: [organizerId], references: [id])
  organizerId String
  buyer       User     @relation("ReviewBuyer", fields: [buyerId], references: [id])
  buyerId     String
  rating      Int      // 1-5
  comment     String?  @db.Text
  isVisible   Boolean  @default(true)
  createdAt   DateTime @default(now())

  @@unique([eventId, buyerId])
  @@map("reviews")
}

// Notification System
model Notification {
  id        String           @id @default(cuid())
  user      User             @relation(fields: [userId], references: [id])
  userId    String
  title     String
  message   String           @db.Text
  type      NotificationType
  isRead    Boolean          @default(false)
  data      Json?            // additional data
  createdAt DateTime         @default(now())

  @@map("notifications")
}

enum NotificationType {
  TICKET_PURCHASED
  EVENT_REMINDER
  PAYMENT_SUCCESS
  PAYMENT_FAILED
  ARTPOSURE_UPDATE
  BOOST_ACTIVATED
  SYSTEM_ANNOUNCEMENT
  VERIFICATION_UPDATE
}

// System Settings
model SystemSettings {
  id                       String   @id @default(cuid())
  maintenanceMode          Boolean  @default(false)
  platformName             String   @default("TiXara")
  platformLogo             String?
  primaryColor             String   @default("#1890ff")
  secondaryColor           String   @default("#52c41a")
  adminCommissionRate      Float    @default(5.0)
  taxRate                  Float    @default(0.0)
  allowRegistration        Boolean  @default(true)
  requireEmailVerification Boolean  @default(true)
  maxFileSize              Int      @default(10485760) // 10MB in bytes
  allowedFileTypes         Json     @default("[\"image/jpeg\",\"image/png\",\"image/webp\",\"application/pdf\"]")
  updatedAt                DateTime @updatedAt

  @@map("system_settings")
}

// Platform Settings (Key-Value Store)
model PlatformSetting {
  id          String                @id @default(cuid())
  key         String                @unique
  value       String
  type        PlatformSettingType   @default(STRING)
  description String?
  createdAt   DateTime              @default(now())
  updatedAt   DateTime              @updatedAt

  @@map("platform_settings")
}

enum PlatformSettingType {
  STRING
  BOOLEAN
  NUMBER
  INTEGER
  JSON
}

// Badge Subscription System
model BadgePlan {
  id          String    @id @default(cuid())
  badge       BadgeType
  name        String
  description String?
  features    Json      // Array of features
  monthlyPrice Float
  yearlyPrice Float
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  subscriptions BadgeSubscription[]

  @@unique([badge])
  @@map("badge_plans")
}

model BadgeSubscription {
  id          String           @id @default(cuid())
  userId      String           @unique
  user        User             @relation(fields: [userId], references: [id])
  planId      String
  plan        BadgePlan        @relation(fields: [planId], references: [id])
  badge       BadgeType
  type        SubscriptionType
  startDate   DateTime
  endDate     DateTime
  price       Float
  paymentId   String?
  payment     Payment?         @relation(fields: [paymentId], references: [id])
  isActive    Boolean          @default(true)
  autoRenew   Boolean          @default(false)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  @@map("badge_subscriptions")
}

enum SubscriptionType {
  MONTHLY
  YEARLY
}

// Session Management (for NextAuth)
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

