import { PrismaClient, UserRole, BadgeType, ArtposureCategory } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create System Settings
  const systemSettings = await prisma.systemSettings.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      maintenanceMode: false,
      platformName: 'TiXara',
      platformLogo: '/images/logo.png',
      primaryColor: '#1890ff',
      secondaryColor: '#52c41a',
      adminCommissionRate: 5.0,
      taxRate: 0.0,
      allowRegistration: true,
      requireEmailVerification: true,
      maxFileSize: 10485760, // 10MB
      allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
    },
  })

  console.log('✅ System settings created')

  // Create Admin User
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'TiXara Admin',
      password: adminPassword,
      role: UserRole.ADMIN,
      isVerified: true,
      badge: BadgeType.TITANIUM,
      uangtixBalance: 1000000,
    },
  })

  console.log('✅ Admin user created')

  // Create Sample Organizer
  const organizerPassword = await bcrypt.hash('organizer123', 12)
  const organizer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Event Organizer',
      password: organizerPassword,
      role: UserRole.ORGANIZER,
      isVerified: true,
      badge: BadgeType.GOLD,
      uangtixBalance: 500000,
    },
  })

  console.log('✅ Sample organizer created')

  // Create Sample Buyer
  const buyerPassword = await bcrypt.hash('buyer123', 12)
  const buyer = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Event Buyer',
      password: buyerPassword,
      role: UserRole.BUYER,
      isVerified: true,
      badge: BadgeType.BRONZE,
      uangtixBalance: 100000,
    },
  })

  console.log('✅ Sample buyer created')

  // Create Event Categories
  const categories = [
    {
      name: 'Konser & Musik',
      slug: 'konser-musik',
      description: 'Konser musik, festival, dan pertunjukan musik',
      icon: '🎵',
      color: '#ff6b6b',
    },
    {
      name: 'Workshop & Seminar',
      slug: 'workshop-seminar',
      description: 'Workshop, seminar, dan pelatihan',
      icon: '📚',
      color: '#4ecdc4',
    },
    {
      name: 'Olahraga',
      slug: 'olahraga',
      description: 'Event olahraga dan kompetisi',
      icon: '⚽',
      color: '#45b7d1',
    },
    {
      name: 'Teknologi',
      slug: 'teknologi',
      description: 'Event teknologi, startup, dan inovasi',
      icon: '💻',
      color: '#96ceb4',
    },
    {
      name: 'Seni & Budaya',
      slug: 'seni-budaya',
      description: 'Pameran seni, pertunjukan budaya',
      icon: '🎨',
      color: '#feca57',
    },
    {
      name: 'Kuliner',
      slug: 'kuliner',
      description: 'Festival kuliner dan food event',
      icon: '🍕',
      color: '#ff9ff3',
    },
  ]

  for (const category of categories) {
    await prisma.eventCategory.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
  }

  console.log('✅ Event categories created')

  // Create Ticket Templates
  const templates = [
    {
      name: 'Template Klasik',
      description: 'Template tiket klasik dengan desain sederhana',
      templateCode: `<temptix>
  <ticket-header>🎫 {{platformName}}</ticket-header>
  <ticket-body>
    <event>{{eventName}}</event>
    <category>{{category}}</category>
    <buyer>{{buyerName}}</buyer>
    <qr-code src="{{qr}}" />
    <verified>{{isVerified}}</verified>
    <admin-fee>{{adminFee}}</admin-fee>
  </ticket-body>
</temptix>`,
      preview: '/images/templates/classic.png',
      isPremium: false,
      createdBy: admin.id,
    },
    {
      name: 'Template Premium Blue',
      description: 'Template premium dengan tema biru elegan',
      templateCode: `<temptix theme="blue">
  <ticket-header gradient="true">🎫 {{platformName}}</ticket-header>
  <ticket-body>
    <event style="font-size: 24px; font-weight: bold;">{{eventName}}</event>
    <category badge="true">{{category}}</category>
    <buyer>{{buyerName}}</buyer>
    <date-time>{{eventDate}} - {{eventTime}}</date-time>
    <location>{{eventLocation}}</location>
    <qr-code src="{{qr}}" size="large" />
    <verified badge="premium">{{isVerified}}</verified>
    <admin-fee>{{adminFee}}</admin-fee>
  </ticket-body>
</temptix>`,
      preview: '/images/templates/premium-blue.png',
      isPremium: true,
      requiredBadge: BadgeType.GOLD,
      price: 50000,
      createdBy: admin.id,
    },
  ]

  for (const template of templates) {
    await prisma.ticketTemplate.upsert({
      where: { name: template.name },
      update: {},
      create: template,
    })
  }

  console.log('✅ Ticket templates created')

  // Create Artposure Services
  const artposureServices = [
    {
      name: 'Desain Poster Event',
      description: 'Desain poster profesional untuk promosi event Anda',
      price: 150000,
      duration: 3,
      category: ArtposureCategory.POSTER,
      samples: [
        '/images/samples/poster1.jpg',
        '/images/samples/poster2.jpg',
        '/images/samples/poster3.jpg',
      ],
    },
    {
      name: 'Video Promosi Event',
      description: 'Video promosi singkat untuk media sosial',
      price: 500000,
      duration: 7,
      category: ArtposureCategory.VIDEO,
      samples: [
        '/images/samples/video1.mp4',
        '/images/samples/video2.mp4',
      ],
    },
    {
      name: 'Konten Media Sosial',
      description: 'Paket konten untuk Instagram, Facebook, dan Twitter',
      price: 200000,
      duration: 2,
      category: ArtposureCategory.SOCIAL_MEDIA,
      samples: [
        '/images/samples/social1.jpg',
        '/images/samples/social2.jpg',
        '/images/samples/social3.jpg',
      ],
    },
  ]

  for (const service of artposureServices) {
    await prisma.artposureService.upsert({
      where: { name: service.name },
      update: {},
      create: service,
    })
  }

  console.log('✅ Artposure services created')

  // Create Booster Packages
  const boosterPackages = [
    {
      name: 'Boost Basic',
      description: 'Tampilkan event Anda di halaman utama selama 3 hari',
      duration: 3,
      price: 100000,
      features: ['Tampil di halaman utama', 'Badge "Featured"', 'Prioritas pencarian'],
      priority: 1,
    },
    {
      name: 'Boost Premium',
      description: 'Promosi maksimal selama 7 hari dengan berbagai keuntungan',
      duration: 7,
      price: 250000,
      features: [
        'Tampil di halaman utama',
        'Badge "Premium Featured"',
        'Prioritas pencarian tinggi',
        'Highlight di kategori',
        'Push notification ke user',
      ],
      priority: 2,
    },
    {
      name: 'Boost Ultimate',
      description: 'Paket promosi terlengkap selama 14 hari',
      duration: 14,
      price: 500000,
      features: [
        'Semua fitur Premium',
        'Banner di homepage',
        'Email newsletter',
        'Social media mention',
        'Dedicated support',
      ],
      priority: 3,
    },
  ]

  for (const package_ of boosterPackages) {
    await prisma.boosterPackage.upsert({
      where: { name: package_.name },
      update: {},
      create: package_,
    })
  }

  console.log('✅ Booster packages created')

  console.log('🎉 Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
