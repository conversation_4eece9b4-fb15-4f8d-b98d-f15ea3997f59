@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700;800&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 210 100% 56%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 98%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 98%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 56%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 100% 56%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-primary-300 dark:bg-primary-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-primary-400 dark:bg-primary-500;
  }
}

@layer components {
  /* TiXara Custom Components */
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg active:scale-95;
  }

  .btn-secondary {
    @apply bg-secondary-500 hover:bg-secondary-600 text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg active:scale-95;
  }

  .btn-outline {
    @apply border-2 border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white font-medium px-6 py-3 rounded-lg transition-all duration-200 active:scale-95;
  }

  .btn-ghost {
    @apply text-primary-500 hover:bg-primary-50 dark:hover:bg-primary-900/20 font-medium px-6 py-3 rounded-lg transition-all duration-200 active:scale-95;
  }

  .card-elevated {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 border border-gray-100 dark:border-gray-700;
  }

  .input-field {
    @apply w-full px-4 py-3 border border-gray-200 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }

  .badge-verified {
    @apply inline-flex items-center gap-1 bg-secondary-100 text-secondary-700 px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-bronze {
    @apply inline-flex items-center gap-1 bg-amber-100 text-amber-700 px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-silver {
    @apply inline-flex items-center gap-1 bg-gray-100 text-gray-700 px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-gold {
    @apply inline-flex items-center gap-1 bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full text-xs font-medium;
  }

  .badge-titanium {
    @apply inline-flex items-center gap-1 bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-medium;
  }

  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500;
  }

  .gradient-card {
    @apply bg-gradient-to-br from-primary-50 to-secondary-50 dark:from-primary-900/20 dark:to-secondary-900/20;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }

  /* Loading animations */
  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-200 border-t-primary-500;
  }

  .loading-pulse {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
  }

  /* Dana-style mobile bottom navigation */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 px-4 py-2 z-50;
  }

  .mobile-nav-item {
    @apply flex flex-col items-center justify-center py-2 px-3 rounded-lg transition-all duration-200 text-gray-600 dark:text-gray-400;
  }

  .mobile-nav-item.active {
    @apply text-primary-500 bg-primary-50 dark:bg-primary-900/20;
  }

  /* Event card styles */
  .event-card {
    @apply card-elevated overflow-hidden hover:scale-[1.02] transition-all duration-300 cursor-pointer;
  }

  .event-card-image {
    @apply w-full h-48 object-cover;
  }

  .event-card-content {
    @apply p-4;
  }

  /* Ticket styles */
  .ticket-card {
    @apply bg-white dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600 p-4 relative overflow-hidden;
  }

  .ticket-card::before {
    content: '';
    @apply absolute top-0 left-0 w-full h-full bg-gradient-to-r from-primary-500/5 to-secondary-500/5 pointer-events-none;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Animation utilities */
  .animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out;
  }

  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.4s ease-out;
  }

  @keyframes slide-in-right {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}