// Test API endpoints directly
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testAPIEndpoints() {
  console.log('🧪 Testing API Endpoints...')
  
  try {
    // Test Badge Plans API logic
    console.log('\n1. Testing Badge Plans API Logic...')
    
    // Simulate GET /api/badges/plans
    const badgePlans = await prisma.badgePlan.findMany({
      where: { isActive: true },
      orderBy: { monthlyPrice: 'asc' }
    })
    
    if (badgePlans.length === 0) {
      console.log('⚠️  No badge plans found, creating default plans...')
      
      const DEFAULT_BADGE_PLANS = [
        {
          badge: 'BRONZE',
          name: 'Bronze Badge',
          description: 'Badge gratis dengan fitur dasar untuk organizer pemula',
          features: [
            'Maksimal 3 event per bulan',
            'Maksimal 100 tiket per event',
            'Template tiket dasar',
            'Komisi 5%',
            'Support email'
          ],
          monthlyPrice: 0,
          yearlyPrice: 0,
          isActive: true,
        },
        {
          badge: 'SILVER',
          name: 'Silver Badge',
          description: 'Badge premium untuk organizer yang berkembang',
          features: [
            'Maksimal 10 event per bulan',
            'Maksimal 500 tiket per event',
            'Template tiket premium',
            'Komisi 4%',
            'Support prioritas',
            'Analytics dasar'
          ],
          monthlyPrice: 99000,
          yearlyPrice: 990000,
          isActive: true,
        }
      ]
      
      for (const plan of DEFAULT_BADGE_PLANS) {
        await prisma.badgePlan.create({ data: plan })
      }
      
      console.log('✅ Default badge plans created')
    } else {
      console.log(`✅ Found ${badgePlans.length} badge plans`)
      badgePlans.forEach(plan => {
        console.log(`   - ${plan.name}: Rp${plan.monthlyPrice.toLocaleString()}/month`)
      })
    }
    
    // Test User Badge Subscription
    console.log('\n2. Testing User Badge Subscription...')
    
    const organizer = await prisma.user.findFirst({
      where: { role: 'ORGANIZER' },
      include: {
        badgeSubscription: {
          include: { plan: true }
        }
      }
    })
    
    if (organizer) {
      console.log(`✅ Found organizer: ${organizer.name}`)
      
      if (organizer.badgeSubscription) {
        console.log(`   - Current badge: ${organizer.badgeSubscription.plan.badge}`)
        console.log(`   - Expires: ${organizer.badgeSubscription.endDate}`)
        console.log(`   - Auto-renewal: ${organizer.badgeSubscription.autoRenew}`)
      } else {
        console.log('   - No badge subscription found')
        
        // Create default Bronze subscription
        const bronzePlan = await prisma.badgePlan.findFirst({
          where: { badge: 'BRONZE' }
        })
        
        if (bronzePlan) {
          const subscription = await prisma.badgeSubscription.create({
            data: {
              userId: organizer.id,
              planId: bronzePlan.id,
              badge: 'BRONZE',
              type: 'MONTHLY',
              startDate: new Date(),
              endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
              price: bronzePlan.monthlyPrice,
              isActive: true,
              autoRenew: false
            }
          })

          console.log(`✅ Created Bronze subscription for ${organizer.name}`)
        }
      }
    }
    
    // Test UangtiX Wallet
    console.log('\n3. Testing UangtiX Wallet...')

    if (!organizer) {
      console.log('⚠️  No organizer found, skipping wallet test')
      return
    }

    console.log(`✅ UangtiX balance: Rp${organizer.uangtixBalance.toLocaleString()}`)

    // Update balance if it's 0
    if (organizer.uangtixBalance === 0) {
      const updatedUser = await prisma.user.update({
        where: { id: organizer.id },
        data: { uangtixBalance: 500000 }
      })
      console.log(`✅ Updated UangtiX balance to: Rp${updatedUser.uangtixBalance.toLocaleString()}`)
    }
    
    console.log('\n🎉 All API endpoint tests passed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testAPIEndpoints()
