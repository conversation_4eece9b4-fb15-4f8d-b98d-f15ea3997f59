// Test Badge API endpoints
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testBadgeAPI() {
  console.log('🧪 Testing Badge API...')
  
  try {
    // Test 1: Get Badge Plans
    console.log('\n1. Testing Badge Plans retrieval...')
    const badgePlans = await prisma.badgePlan.findMany({
      where: { isActive: true },
      orderBy: { monthlyPrice: 'asc' }
    })
    
    console.log(`✅ Found ${badgePlans.length} badge plans:`)
    badgePlans.forEach(plan => {
      console.log(`   - ${plan.name}: Rp${plan.monthlyPrice.toLocaleString()}/month`)
    })
    
    // Test 2: Get Users
    console.log('\n2. Testing Users retrieval...')
    const users = await prisma.user.findMany({
      where: { role: 'ORGANIZER' },
      include: {
        badgeSubscription: {
          include: {
            plan: true
          }
        }
      }
    })
    
    console.log(`✅ Found ${users.length} organizer users:`)
    users.forEach(user => {
      const badge = user.badgeSubscription?.plan?.badge || 'BRONZE'
      console.log(`   - ${user.name} (${user.email}): ${badge} badge`)
    })
    
    // Test 3: Badge Configuration
    console.log('\n3. Testing Badge Configuration...')

    // Test badge configuration directly
    const BADGE_CONFIG = {
      BRONZE: {
        name: 'Bronze',
        color: '#CD7F32',
        features: ['Maksimal 3 event per bulan', 'Maksimal 100 tiket per event'],
        limits: { maxEventsPerMonth: 3, maxTicketsPerEvent: 100 },
        commissionRate: 5
      },
      SILVER: {
        name: 'Silver',
        color: '#C0C0C0',
        features: ['Maksimal 10 event per bulan', 'Maksimal 500 tiket per event'],
        limits: { maxEventsPerMonth: 10, maxTicketsPerEvent: 500 },
        commissionRate: 4
      },
      GOLD: {
        name: 'Gold',
        color: '#FFD700',
        features: ['Maksimal 25 event per bulan', 'Maksimal 1000 tiket per event'],
        limits: { maxEventsPerMonth: 25, maxTicketsPerEvent: 1000 },
        commissionRate: 3
      }
    }

    console.log(`✅ Bronze features: ${BADGE_CONFIG.BRONZE.features.length} features`)
    console.log(`✅ Silver limits: ${BADGE_CONFIG.SILVER.limits.maxEventsPerMonth} events/month`)
    console.log(`✅ Gold commission: ${BADGE_CONFIG.GOLD.commissionRate}%`)
    
    console.log('\n🎉 All badge API tests passed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testBadgeAPI()
