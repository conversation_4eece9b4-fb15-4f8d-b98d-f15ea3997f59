const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testBoosterAPI() {
  console.log('🧪 Testing Event Booster API...\n')

  try {
    // 1. Test Booster Packages
    console.log('1. Testing Booster Packages...')
    const packages = await prisma.boosterPackage.findMany({
      include: {
        _count: {
          select: {
            boosts: true
          }
        }
      },
      orderBy: {
        priority: 'desc'
      }
    })

    console.log(`✅ Found ${packages.length} booster packages`)
    packages.forEach(pkg => {
      console.log(`   - ${pkg.name}: Rp${pkg.price.toLocaleString('id-ID')} (${pkg.duration} days, Priority: ${pkg.priority})`)
      console.log(`     Features: ${pkg.features.join(', ')}`)
      console.log(`     Active boosts: ${pkg._count.boosts}`)
    })

    // 2. Test Event Boosts
    console.log('\n2. Testing Event Boosts...')
    const boosts = await prisma.eventBoost.findMany({
      include: {
        event: {
          select: {
            title: true,
            startDate: true
          }
        },
        package: {
          select: {
            name: true,
            priority: true
          }
        },
        organizer: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 5
    })

    console.log(`✅ Found ${boosts.length} event boosts`)
    boosts.forEach(boost => {
      console.log(`   - Event: ${boost.event.title}`)
      console.log(`     Package: ${boost.package.name} (Priority: ${boost.package.priority})`)
      console.log(`     Organizer: ${boost.organizer.name}`)
      console.log(`     Status: ${boost.status}`)
      console.log(`     Period: ${boost.startDate.toLocaleDateString()} - ${boost.endDate.toLocaleDateString()}`)
      console.log(`     Price: Rp${boost.price.toLocaleString('id-ID')}`)
    })

    // 3. Test Boosted Events
    console.log('\n3. Testing Boosted Events...')
    const boostedEvents = await prisma.event.findMany({
      where: {
        isBoosted: true
      },
      include: {
        boosts: {
          where: {
            status: 'ACTIVE'
          },
          include: {
            package: {
              select: {
                name: true,
                priority: true
              }
            }
          }
        },
        organizer: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        boostEndDate: 'desc'
      }
    })

    console.log(`✅ Found ${boostedEvents.length} boosted events`)
    boostedEvents.forEach(event => {
      console.log(`   - ${event.title}`)
      console.log(`     Organizer: ${event.organizer.name}`)
      console.log(`     Boost ends: ${event.boostEndDate ? event.boostEndDate.toLocaleDateString() : 'N/A'}`)
      if (event.boosts.length > 0) {
        event.boosts.forEach(boost => {
          console.log(`     Active boost: ${boost.package.name} (Priority: ${boost.package.priority})`)
        })
      }
    })

    // 4. Test Analytics
    console.log('\n4. Testing Boost Analytics...')
    
    // Total revenue from boosts
    const totalRevenue = await prisma.eventBoost.aggregate({
      _sum: {
        price: true
      }
    })

    // Boosts by status
    const boostsByStatus = await prisma.eventBoost.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    })

    // Most popular packages
    const popularPackages = await prisma.eventBoost.groupBy({
      by: ['packageId'],
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      },
      take: 3
    })

    console.log(`✅ Total boost revenue: Rp${(totalRevenue._sum.price || 0).toLocaleString('id-ID')}`)
    
    console.log('✅ Boosts by status:')
    boostsByStatus.forEach(stat => {
      console.log(`   - ${stat.status}: ${stat._count.id} boosts`)
    })

    if (popularPackages.length > 0) {
      console.log('✅ Most popular packages:')
      for (const stat of popularPackages) {
        const pkg = await prisma.boosterPackage.findUnique({
          where: { id: stat.packageId },
          select: { name: true }
        })
        console.log(`   - ${pkg?.name || 'Unknown'}: ${stat._count.id} boosts`)
      }
    }

    console.log('\n🎉 All Event Booster API tests passed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

testBoosterAPI()
