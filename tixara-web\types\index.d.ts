// TiXara Platform Types
export interface User {
  id: string;
  email: string;
  name: string;
  phone?: string;
  avatar?: string;
  role: UserRole;
  isVerified: boolean;
  badge?: BadgeType;
  uangtixBalance: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  ORGANIZER = 'ORGANIZER',
  BUYER = 'BUYER',
  STAFF = 'STAFF',
}

export enum BadgeType {
  BRONZE = 'BRONZE',
  SILVER = 'SILVER',
  GOLD = 'GOLD',
  TITANIUM = 'TITANIUM',
}

export interface Event {
  id: string;
  title: string;
  description: string;
  image: string;
  category: EventCategory;
  organizer: User;
  organizerId: string;
  location: string;
  startDate: Date;
  endDate: Date;
  price: number;
  totalTickets: number;
  soldTickets: number;
  isActive: boolean;
  isBoosted: boolean;
  boostEndDate?: Date;
  templateId?: string;
  adminFee: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface EventCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  icon?: string;
  color?: string;
  isActive: boolean;
}

export interface Ticket {
  id: string;
  event: Event;
  eventId: string;
  buyer: User;
  buyerId: string;
  qrCode: string;
  isUsed: boolean;
  usedAt?: Date;
  validatedBy?: string;
  price: number;
  adminFee: number;
  totalPaid: number;
  paymentMethod: PaymentMethod;
  createdAt: Date;
}

export enum PaymentMethod {
  UANGTIX = 'UANGTIX',
  TRIPAY = 'TRIPAY',
  MIDTRANS = 'MIDTRANS',
  XENDIT = 'XENDIT',
  MANUAL = 'MANUAL',
}

export interface TicketTemplate {
  id: string;
  name: string;
  description?: string;
  templateCode: string; // .temptix format
  preview: string;
  isPremium: boolean;
  requiredBadge?: BadgeType;
  price?: number;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
}

export interface UangtiXTransaction {
  id: string;
  user: User;
  userId: string;
  type: TransactionType;
  amount: number;
  description: string;
  reference?: string;
  status: TransactionStatus;
  createdAt: Date;
}

export enum TransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAW = 'WITHDRAW',
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  TRANSFER = 'TRANSFER',
  COMMISSION = 'COMMISSION',
}

export enum TransactionStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export interface ArtposureService {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number; // in days
  category: ArtposureCategory;
  samples: string[]; // image URLs
  isActive: boolean;
  createdAt: Date;
}

export enum ArtposureCategory {
  POSTER = 'POSTER',
  VIDEO = 'VIDEO',
  SOCIAL_MEDIA = 'SOCIAL_MEDIA',
  BANNER = 'BANNER',
  LOGO = 'LOGO',
  BRANDING = 'BRANDING',
}

export interface ArtposureOrder {
  id: string;
  service: ArtposureService;
  serviceId: string;
  organizer: User;
  organizerId: string;
  event?: Event;
  eventId?: string;
  requirements: string;
  status: ArtposureStatus;
  price: number;
  deliveryDate: Date;
  result?: string; // file URL
  feedback?: string;
  rating?: number;
  createdAt: Date;
  updatedAt: Date;
}

export enum ArtposureStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  REVIEW = 'REVIEW',
  REVISION = 'REVISION',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export interface BoosterPackage {
  id: string;
  name: string;
  description: string;
  duration: number; // in days
  price: number;
  features: string[];
  priority: number; // higher = more priority
  isActive: boolean;
  createdAt: Date;
}

export interface EventBoost {
  id: string;
  event: Event;
  eventId: string;
  package: BoosterPackage;
  packageId: string;
  organizer: User;
  organizerId: string;
  startDate: Date;
  endDate: Date;
  price: number;
  status: BoostStatus;
  createdAt: Date;
}

export enum BoostStatus {
  ACTIVE = 'ACTIVE',
  EXPIRED = 'EXPIRED',
  CANCELLED = 'CANCELLED',
}

export interface Review {
  id: string;
  event: Event;
  eventId: string;
  organizer: User;
  organizerId: string;
  buyer: User;
  buyerId: string;
  rating: number; // 1-5
  comment?: string;
  isVisible: boolean;
  createdAt: Date;
}

export interface Notification {
  id: string;
  user: User;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  data?: any; // additional data
  createdAt: Date;
}

export enum NotificationType {
  TICKET_PURCHASED = 'TICKET_PURCHASED',
  EVENT_REMINDER = 'EVENT_REMINDER',
  PAYMENT_SUCCESS = 'PAYMENT_SUCCESS',
  PAYMENT_FAILED = 'PAYMENT_FAILED',
  ARTPOSURE_UPDATE = 'ARTPOSURE_UPDATE',
  BOOST_ACTIVATED = 'BOOST_ACTIVATED',
  SYSTEM_ANNOUNCEMENT = 'SYSTEM_ANNOUNCEMENT',
  VERIFICATION_UPDATE = 'VERIFICATION_UPDATE',
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  role: UserRole;
}

export interface EventForm {
  title: string;
  description: string;
  categoryId: string;
  location: string;
  startDate: Date;
  endDate: Date;
  price: number;
  totalTickets: number;
  image?: File;
  templateId?: string;
}

// Component Props Types
export interface ButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export interface CardProps {
  children: React.ReactNode;
  className?: string;
  elevated?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg';
}

// Theme Types
export interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  logo: string;
  name: string;
  favicon: string;
}

// Settings Types
export interface SystemSettings {
  id: string;
  maintenanceMode: boolean;
  platformName: string;
  platformLogo: string;
  primaryColor: string;
  secondaryColor: string;
  adminCommissionRate: number;
  taxRate: number;
  allowRegistration: boolean;
  requireEmailVerification: boolean;
  maxFileSize: number; // in MB
  allowedFileTypes: string[];
  updatedAt: Date;
}