import { UserRole, BadgeType } from '@prisma/client'
import <PERSON><PERSON><PERSON> from 'next-auth'

declare module 'next-auth' {
  interface Session {
    user: {
      id: string
      email: string
      name: string
      role: UserRole
      isVerified: boolean
      badge?: BadgeType
      avatar?: string
    }
  }

  interface User {
    id: string
    email: string
    name: string
    role: UserRole
    isVerified: boolean
    badge?: BadgeType
    avatar?: string
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: UserRole
    isVerified: boolean
    badge?: BadgeType
    avatar?: string
  }
}
